{"name": "timemachine", "version": "0.3.2", "description": "Test your time critical app by overriding the native Javascript Date function", "main": "timemachine.js", "type": "index.d.ts", "directories": {"test": "test"}, "scripts": {"test": "grunt test --verbose", "build": "grunt build", "bump": "grunt bump"}, "repository": {"type": "git", "url": "https://github.com/schickling/timemachine.git"}, "keywords": ["time", "date", "override", "overwrite", "global", "manipulation", "testing", "test"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/schickling/timemachine/issues"}, "devDependencies": {"coveralls": "~2.12.0", "grunt": "~1.0.1", "grunt-bump": "~0.8.0", "grunt-cli": "~1.2.0", "grunt-contrib-copy": "~1.0.0", "grunt-contrib-jshint": "~1.1.0", "grunt-contrib-uglify": "~2.2.0", "grunt-karma": "~2.0.0", "jasmine-core": "~2.5.2", "karma": "~1.5.0", "karma-coverage": "~1.1.1", "karma-jasmine": "~1.1.0", "karma-phantomjs-launcher": "~1.0.4", "load-grunt-tasks": "~3.5.2", "phantomjs": "~2.1.7"}}