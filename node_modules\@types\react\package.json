{"name": "@types/react", "version": "18.0.29", "description": "TypeScript definitions for React", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://asana.com"}, {"name": "AssureSign", "url": "http://www.assuresign.com"}, {"name": "Microsoft", "url": "https://microsoft.com"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/bbenezech", "githubUsername": "bbenezech"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pza<PERSON><PERSON>ky", "githubUsername": "pza<PERSON>linsky"}, {"name": "<PERSON>", "url": "https://github.com/ericanderson", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/theruther4d", "githubUsername": "theruther4d"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/guil<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ferdaber", "githubUsername": "ferd<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jrakotoharisoa", "githubUsername": "jrakotoharisoa"}, {"name": "<PERSON>", "url": "https://github.com/pascaloliv", "githubUsername": "pascal<PERSON>v"}, {"name": "<PERSON>", "url": "https://github.com/hotell", "githubUsername": "hotell"}, {"name": "<PERSON>", "url": "https://github.com/franklixuefei", "githubUsername": "frank<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/Jessidhia", "githubUsername": "Jessidhia"}, {"name": "Saransh Kataria", "url": "https://github.com/saranshkataria", "githubUsername": "saranshkataria"}, {"name": "Kanitkorn Sujautra", "url": "https://github.com/lukyth", "githubUsername": "luk<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/zieka", "githubUsername": "zieka"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/dancerphil", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dimitrop<PERSON>los", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/disjukr", "githubUsername": "disju<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/vhfmag", "githubUsername": "vhfmag"}, {"name": "<PERSON>", "url": "https://github.com/hellatan", "githubUsername": "hellatan"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/priyanshurav", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/Semigradsky", "githubUsername": "Semigradsky"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react"}, "scripts": {}, "dependencies": {"@types/prop-types": "*", "@types/scheduler": "*", "csstype": "^3.0.2"}, "typesPublisherContentHash": "0b57cd9152ecf4ff05a80413cd6550a1943c8b944b1241e16cf8cc351aba79e0", "typeScriptVersion": "4.3", "exports": {".": {"types": {"default": "./index.d.ts"}}, "./next": {"types": {"default": "./next.d.ts"}}, "./experimental": {"types": {"default": "./experimental.d.ts"}}, "./jsx-runtime": {"types": {"default": "./jsx-runtime.d.ts"}}, "./jsx-dev-runtime": {"types": {"default": "./jsx-dev-runtime.d.ts"}}, "./package.json": "./package.json"}}