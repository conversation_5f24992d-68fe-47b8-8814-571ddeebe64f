{"name": "rollup", "version": "3.20.2", "description": "Next-generation ES module bundler", "main": "dist/rollup.js", "module": "dist/es/rollup.js", "types": "dist/rollup.d.ts", "bin": {"rollup": "dist/bin/rollup"}, "scripts": {"build": "rollup --config rollup.config.ts --configPlugin typescript", "dev": "vitepress dev docs", "build:cjs": "rollup --config rollup.config.ts --configPlugin typescript --configTest", "build:bootstrap": "node dist/bin/rollup --config rollup.config.ts --configPlugin typescript", "build:docs": "vitepress build docs", "preview:docs": "vitepress preview docs", "ci:lint": "concurrently 'npm:lint:js:nofix' 'npm:lint:markdown:nofix'", "ci:test": "npm run build:cjs && npm run build:bootstrap && npm run test:all", "ci:test:only": "npm run build:cjs && npm run build:bootstrap && npm run test:only", "ci:coverage": "npm run build:cjs && npm run build:bootstrap && nyc --reporter l<PERSON><PERSON><PERSON> mocha", "lint": "concurrently -c red,green 'npm:lint:js' 'npm:lint:markdown'", "lint:js": "eslint . --fix --cache", "lint:js:nofix": "eslint . --cache", "lint:markdown": "prettier --write \"**/*.md\"", "lint:markdown:nofix": "prettier --check \"**/*.md\"", "perf": "npm run build:cjs && node --expose-gc scripts/perf.js", "perf:init": "node scripts/perf-init.js", "prepare": "husky install && node scripts/check-release.js || npm run build", "prepublishOnly": "node scripts/check-release.js", "release": "node scripts/release.js", "release:docs": "git fetch --update-head-ok origin master:master && git branch --force documentation-published master && git push origin documentation-published", "test": "npm run build && npm run test:all", "test:update-snapshots": "node scripts/update-snapshots.js", "test:cjs": "npm run build:cjs && npm run test:only", "test:quick": "mocha -b test/test.js", "test:all": "concurrently --kill-others-on-fail -c green,blue,magenta,cyan,red 'npm:test:only' 'npm:test:browser' 'npm:test:typescript' 'npm:test:leak' 'npm:test:package' 'npm:test:options'", "test:coverage": "npm run build:cjs && shx rm -rf coverage/* && nyc --reporter html mocha test/test.js", "test:coverage:browser": "npm run build && shx rm -rf coverage/* && nyc mocha test/browser/index.js", "test:leak": "node --expose-gc test/leak/index.js", "test:package": "node scripts/test-package.js", "test:options": "node scripts/test-options.js", "test:only": "mocha test/test.js", "test:typescript": "shx rm -rf test/typescript/dist && shx cp -r dist test/typescript/ && tsc --noEmit -p test/typescript && tsc --noEmit", "test:browser": "mocha test/browser/index.js", "watch": "rollup --config rollup.config.ts --configPlugin typescript --watch"}, "repository": "rollup/rollup", "keywords": ["modules", "bundler", "bundling", "es6", "optimizer"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "homepage": "https://rollupjs.org/", "optionalDependencies": {"fsevents": "~2.3.2"}, "devDependencies": {"@codemirror/commands": "^6.2.1", "@codemirror/lang-javascript": "^6.1.4", "@codemirror/language": "^6.6.0", "@codemirror/search": "^6.2.3", "@codemirror/state": "^6.2.0", "@codemirror/view": "^6.9.2", "@jridgewell/sourcemap-codec": "^1.4.14", "@mermaid-js/mermaid-cli": "^10.0.2", "@rollup/plugin-alias": "^4.0.3", "@rollup/plugin-buble": "^1.0.2", "@rollup/plugin-commonjs": "^24.0.1", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.0.1", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-terser": "^0.4.0", "@rollup/plugin-typescript": "^11.0.0", "@rollup/pluginutils": "^5.0.2", "@types/estree": "1.0.0", "@types/node": "^14.18.36", "@types/signal-exit": "^3.0.1", "@types/yargs-parser": "^21.0.0", "@typescript-eslint/eslint-plugin": "^5.54.1", "@typescript-eslint/parser": "^5.54.1", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.2", "acorn": "^8.8.2", "acorn-import-assertions": "^1.8.0", "acorn-jsx": "^5.3.2", "acorn-walk": "^8.2.0", "buble": "^0.20.0", "builtin-modules": "^3.3.0", "chokidar": "^3.5.3", "colorette": "^2.0.19", "concurrently": "^7.6.0", "core-js": "^3.29.0", "date-time": "^4.0.0", "es5-shim": "^4.6.7", "es6-shim": "^0.35.7", "eslint": "^8.35.0", "eslint-config-prettier": "^8.7.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-unicorn": "^46.0.0", "eslint-plugin-vue": "^9.9.0", "fixturify": "^3.0.0", "flru": "^1.0.2", "fs-extra": "^11.1.0", "github-api": "^3.4.0", "hash.js": "^1.1.7", "husky": "^8.0.3", "inquirer": "^9.1.4", "is-reference": "^3.0.1", "lint-staged": "^13.1.2", "locate-character": "^2.0.5", "magic-string": "^0.30.0", "mocha": "^10.2.0", "nyc": "^15.1.0", "pinia": "^2.0.33", "prettier": "^2.8.4", "pretty-bytes": "^6.1.0", "pretty-ms": "^8.0.0", "requirejs": "^2.3.6", "rollup": "^3.18.0", "rollup-plugin-license": "^3.0.1", "rollup-plugin-string": "^3.0.0", "rollup-plugin-thatworks": "^1.0.4", "semver": "^7.3.8", "shx": "^0.3.4", "signal-exit": "^3.0.7", "source-map": "^0.7.4", "source-map-support": "^0.5.21", "systemjs": "^6.14.0", "terser": "^5.16.5", "tslib": "^2.5.0", "typescript": "^4.9.5", "vitepress": "^1.0.0-alpha.50", "vue": "^3.2.47", "weak-napi": "^2.0.2", "yargs-parser": "^21.1.1"}, "overrides": {"d3": "7.8.0"}, "files": ["dist/**/*.js", "dist/*.d.ts", "dist/bin/rollup", "dist/es/package.json"], "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "exports": {".": {"types": "./dist/rollup.d.ts", "require": "./dist/rollup.js", "import": "./dist/es/rollup.js"}, "./loadConfigFile": {"types": "./dist/loadConfigFile.d.ts", "require": "./dist/loadConfigFile.js", "default": "./dist/loadConfigFile.js"}, "./dist/*": "./dist/*"}}