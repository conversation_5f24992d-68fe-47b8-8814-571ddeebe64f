{"version": 3, "names": ["path", "WeakMap", "exports", "scope", "clear", "clear<PERSON>ath", "clearScope"], "sources": ["../src/cache.ts"], "sourcesContent": ["export let path = new WeakMap();\nexport let scope = new WeakMap();\n\nexport function clear() {\n  clearPath();\n  clearScope();\n}\n\nexport function clearPath() {\n  path = new WeakMap();\n}\n\nexport function clearScope() {\n  scope = new WeakMap();\n}\n"], "mappings": ";;;;;;;;;AAAO,IAAIA,IAAI,GAAG,IAAIC,OAAO,EAAE;AAACC,OAAA,CAAAF,IAAA,GAAAA,IAAA;AACzB,IAAIG,KAAK,GAAG,IAAIF,OAAO,EAAE;AAACC,OAAA,CAAAC,KAAA,GAAAA,KAAA;AAE1B,SAASC,KAAKA,CAAA,EAAG;EACtBC,SAAS,EAAE;EACXC,UAAU,EAAE;AACd;AAEO,SAASD,SAASA,CAAA,EAAG;EAC1BH,OAAA,CAAAF,IAAA,GAAAA,IAAI,GAAG,IAAIC,OAAO,EAAE;AACtB;AAEO,SAASK,UAAUA,CAAA,EAAG;EAC3BJ,OAAA,CAAAC,KAAA,GAAAA,KAAK,GAAG,IAAIF,OAAO,EAAE;AACvB"}