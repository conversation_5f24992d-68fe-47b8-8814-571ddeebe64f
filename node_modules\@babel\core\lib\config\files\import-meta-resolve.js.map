{"version": 3, "names": ["_importMetaResolve", "require", "asyncGeneratorStep", "gen", "resolve", "reject", "_next", "_throw", "key", "arg", "info", "value", "error", "done", "Promise", "then", "_asyncToGenerator", "fn", "self", "args", "arguments", "apply", "err", "undefined", "import_", "_unused", "importMetaResolveP", "process", "execArgv", "includes", "m", "default", "polyfill", "_x", "_x2", "_resolve", "specifier", "parent"], "sources": ["../../../src/config/files/import-meta-resolve.ts"], "sourcesContent": ["import { createRequire } from \"module\";\nimport { resolve as polyfill } from \"../../vendor/import-meta-resolve\";\n\nconst require = createRequire(import.meta.url);\n\nlet import_;\ntry {\n  // Node < 13.3 doesn't support import() syntax.\n  import_ = require(\"./import.cjs\");\n} catch {}\n\n// import.meta.resolve is only available in ESM, but this file is compiled to CJS.\n// We can extract it using dynamic import.\nconst importMetaResolveP: Promise<ImportMeta[\"resolve\"]> =\n  import_ &&\n  // Due to a Node.js/V8 bug (https://github.com/nodejs/node/issues/35889), we cannot\n  // use always dynamic import because it segfaults when running in a Node.js `vm` context,\n  // which is used by the default Jest environment and by webpack-cli.\n  //\n  // However, import.meta.resolve is experimental and only enabled when Node.js is run\n  // with the `--experimental-import-meta-resolve` flag: we can avoid calling import()\n  // when that flag is not enabled, so that the default behavior never segfaults.\n  //\n  // Hopefully, before Node.js unflags import.meta.resolve, either:\n  // - we will move to ESM, so that we have direct access to import.meta.resolve, or\n  // - the V8 bug will be fixed so that we can safely use dynamic import by default.\n  //\n  // I (@nicolo-ribaudo) am really annoyed by this bug, because there is no known\n  // work-around other than \"don't use dynamic import if you are running in a `vm` context\",\n  // but there is no reliable way to detect it (you cannot try/catch segfaults).\n  //\n  // This is the only place where we *need* to use dynamic import because we need to access\n  // an ES module. All the other places will first try using require() and *then*, if\n  // it throws because it's a module, will fallback to import().\n  process.execArgv.includes(\"--experimental-import-meta-resolve\")\n    ? import_(\"data:text/javascript,export default import.meta.resolve\").then(\n        (m: { default: ImportMeta[\"resolve\"] | undefined }) =>\n          m.default || polyfill,\n        () => polyfill,\n      )\n    : Promise.resolve(polyfill);\n\nexport default async function resolve(\n  specifier: Parameters<ImportMeta[\"resolve\"]>[0],\n  parent?: Parameters<ImportMeta[\"resolve\"]>[1],\n): ReturnType<ImportMeta[\"resolve\"]> {\n  return (await importMetaResolveP)(specifier, parent);\n}\n"], "mappings": ";;;;;;AACA,IAAAA,kBAAA,GAAAC,OAAA;AAAuE,SAAAC,mBAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,EAAAC,GAAA,EAAAC,GAAA,cAAAC,IAAA,GAAAP,GAAA,CAAAK,GAAA,EAAAC,GAAA,OAAAE,KAAA,GAAAD,IAAA,CAAAC,KAAA,WAAAC,KAAA,IAAAP,MAAA,CAAAO,KAAA,iBAAAF,IAAA,CAAAG,IAAA,IAAAT,OAAA,CAAAO,KAAA,YAAAG,OAAA,CAAAV,OAAA,CAAAO,KAAA,EAAAI,IAAA,CAAAT,KAAA,EAAAC,MAAA;AAAA,SAAAS,kBAAAC,EAAA,6BAAAC,IAAA,SAAAC,IAAA,GAAAC,SAAA,aAAAN,OAAA,WAAAV,OAAA,EAAAC,MAAA,QAAAF,GAAA,GAAAc,EAAA,CAAAI,KAAA,CAAAH,IAAA,EAAAC,IAAA,YAAAb,MAAAK,KAAA,IAAAT,kBAAA,CAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,UAAAI,KAAA,cAAAJ,OAAAe,GAAA,IAAApB,kBAAA,CAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,WAAAe,GAAA,KAAAhB,KAAA,CAAAiB,SAAA;AAIvE,IAAIC,OAAO;AACX,IAAI;EAEFA,OAAO,GAAGvB,OAAO,CAAC,cAAc,CAAC;AACnC,CAAC,CAAC,OAAAwB,OAAA,EAAM,CAAC;AAIT,MAAMC,kBAAkD,GACtDF,OAAO,IAoBPG,OAAO,CAACC,QAAQ,CAACC,QAAQ,CAAC,oCAAoC,CAAC,GAC3DL,OAAO,CAAC,yDAAyD,CAAC,CAACT,IAAI,CACpEe,CAAiD,IAChDA,CAAC,CAACC,OAAO,IAAIC,0BAAQ,EACvB,MAAMA,0BAAQ,CACf,GACDlB,OAAO,CAACV,OAAO,CAAC4B,0BAAQ,CAAC;AAAC,SAEF5B,OAAOA,CAAA6B,EAAA,EAAAC,GAAA;EAAA,OAAAC,QAAA,CAAAd,KAAA,OAAAD,SAAA;AAAA;AAAA,SAAAe,SAAA;EAAAA,QAAA,GAAAnB,iBAAA,CAAtB,WACboB,SAA+C,EAC/CC,MAA6C,EACV;IACnC,OAAO,OAAOX,kBAAkB,EAAEU,SAAS,EAAEC,MAAM,CAAC;EACtD,CAAC;EAAA,OAAAF,QAAA,CAAAd,KAAA,OAAAD,SAAA;AAAA;AAAA"}