{"version": 3, "file": "socket.io.min.js", "sources": ["../node_modules/engine.io-parser/build/esm/commons.js", "../node_modules/engine.io-parser/build/esm/contrib/base64-arraybuffer.js", "../node_modules/engine.io-parser/build/esm/encodePacket.browser.js", "../node_modules/engine.io-parser/build/esm/decodePacket.browser.js", "../node_modules/engine.io-parser/build/esm/index.js", "../node_modules/@socket.io/component-emitter/index.mjs", "../node_modules/engine.io-client/build/esm/globalThis.browser.js", "../node_modules/engine.io-client/build/esm/util.js", "../node_modules/engine.io-client/build/esm/contrib/yeast.js", "../node_modules/engine.io-client/build/esm/transport.js", "../node_modules/engine.io-client/build/esm/contrib/parseqs.js", "../node_modules/engine.io-client/build/esm/contrib/has-cors.js", "../node_modules/engine.io-client/build/esm/transports/xmlhttprequest.browser.js", "../node_modules/engine.io-client/build/esm/transports/polling.js", "../node_modules/engine.io-client/build/esm/transports/websocket-constructor.browser.js", "../node_modules/engine.io-client/build/esm/transports/websocket.js", "../node_modules/engine.io-client/build/esm/transports/index.js", "../node_modules/engine.io-client/build/esm/contrib/parseuri.js", "../node_modules/engine.io-client/build/esm/socket.js", "../node_modules/engine.io-client/build/esm/index.js", "../node_modules/socket.io-parser/build/esm/is-binary.js", "../node_modules/socket.io-parser/build/esm/binary.js", "../node_modules/socket.io-parser/build/esm/index.js", "../build/esm/on.js", "../build/esm/socket.js", "../build/esm/contrib/backo2.js", "../build/esm/manager.js", "../build/esm/index.js", "../build/esm/url.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = (arraybuffer) => {\n    let bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nexport const decode = (base64) => {\n    let bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    const arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + content);\n    };\n    return fileReader.readAsDataURL(data);\n};\nexport default encodePacket;\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType)\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1)\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type]\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            return data instanceof ArrayBuffer ? new Blob([data]) : data;\n        case \"arraybuffer\":\n        default:\n            return data; // assuming the data is already an ArrayBuffer\n    }\n};\nexport default decodePacket;\n", "import encodePacket from \"./encodePacket.js\";\nimport decodePacket from \"./decodePacket.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, encodedPacket => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload };\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\n", "import { globalThisShim as globalThis } from \"./globalThis.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n", "// imported from https://github.com/unshiftio/yeast\n'use strict';\nconst alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split(''), length = 64, map = {};\nlet seed = 0, i = 0, prev;\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nexport function encode(num) {\n    let encoded = '';\n    do {\n        encoded = alphabet[num % length] + encoded;\n        num = Math.floor(num / length);\n    } while (num > 0);\n    return encoded;\n}\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nexport function decode(str) {\n    let decoded = 0;\n    for (i = 0; i < str.length; i++) {\n        decoded = decoded * length + map[str.charAt(i)];\n    }\n    return decoded;\n}\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nexport function yeast() {\n    const now = encode(+new Date());\n    if (now !== prev)\n        return seed = 0, prev = now;\n    return now + '.' + encode(seed++);\n}\n//\n// Map each character to its index.\n//\nfor (; i < length; i++)\n    map[alphabet[i]] = i;\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nclass TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n}\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "// browser shim for xmlhttprequest module\nimport { hasCORS } from \"../contrib/has-cors.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nexport function XHR(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { encode } from \"../contrib/parseqs.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nimport { XHR as XMLHttpRequest } from \"./xmlhttprequest.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nfunction empty() { }\nconst hasXHR2 = (function () {\n    const xhr = new XMLHttpRequest({\n        xdomain: false,\n    });\n    return null != xhr.responseType;\n})();\nexport class Polling extends Transport {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        this.polling = false;\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n            this.xs = opts.secure !== isSSL;\n        }\n        /**\n         * XHR supports binary\n         */\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this.poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this.polling || !this.writable) {\n            let total = 0;\n            if (this.polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    poll() {\n        this.polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this.polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this.poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        let query = this.query || {};\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        let port = \"\";\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        // avoid port if default for schema\n        if (this.opts.port &&\n            ((\"https\" === schema && Number(this.opts.port) !== 443) ||\n                (\"http\" === schema && Number(this.opts.port) !== 80))) {\n            port = \":\" + this.opts.port;\n        }\n        const encodedQuery = encode(query);\n        const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n        return (schema +\n            \"://\" +\n            (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n            port +\n            this.opts.path +\n            (encodedQuery.length ? \"?\" + encodedQuery : \"\"));\n    }\n    /**\n     * Creates a request.\n     *\n     * @param {String} method\n     * @private\n     */\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd, xs: this.xs }, this.opts);\n        return new Request(this.uri(), opts);\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(uri, opts) {\n        super();\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.method = opts.method || \"GET\";\n        this.uri = uri;\n        this.async = false !== opts.async;\n        this.data = undefined !== opts.data ? opts.data : null;\n        this.create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    create() {\n        const opts = pick(this.opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this.opts.xd;\n        opts.xscheme = !!this.opts.xs;\n        const xhr = (this.xhr = new XMLHttpRequest(opts));\n        try {\n            xhr.open(this.method, this.uri, this.async);\n            try {\n                if (this.opts.extraHeaders) {\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this.opts.extraHeaders) {\n                        if (this.opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this.method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this.opts.withCredentials;\n            }\n            if (this.opts.requestTimeout) {\n                xhr.timeout = this.opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this.onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this.data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this.onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this.index = Request.requestsCount++;\n            Request.requests[this.index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    onError(err) {\n        this.emitReserved(\"error\", err, this.xhr);\n        this.cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    cleanup(fromError) {\n        if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n            return;\n        }\n        this.xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this.xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this.index];\n        }\n        this.xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    onLoad() {\n        const data = this.xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this.cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this.cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\n", "import { globalThisShim as globalThis } from \"../globalThis.js\";\nexport const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const WebSocket = globalThis.WebSocket || globalThis.MozWebSocket;\nexport const usingBrowserWebSocket = true;\nexport const defaultBinaryType = \"arraybuffer\";\n", "import { Transport } from \"../transport.js\";\nimport { encode } from \"../contrib/parseqs.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { pick } from \"../util.js\";\nimport { defaultBinaryType, nextTick, usingBrowserWebSocket, WebSocket, } from \"./websocket-constructor.js\";\nimport { encodePacket } from \"engine.io-parser\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class WS extends Transport {\n    /**\n     * WebSocket transport constructor.\n     *\n     * @param {Object} opts - connection options\n     * @protected\n     */\n    constructor(opts) {\n        super(opts);\n        this.supportsBinary = !opts.forceBase64;\n    }\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        if (!this.check()) {\n            // let probe timeout\n            return;\n        }\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws =\n                usingBrowserWebSocket && !isReactNative\n                    ? protocols\n                        ? new WebSocket(uri, protocols)\n                        : new WebSocket(uri)\n                    : new WebSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType || defaultBinaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // always create a new object (GH-437)\n                const opts = {};\n                if (!usingBrowserWebSocket) {\n                    if (packet.options) {\n                        opts.compress = packet.options.compress;\n                    }\n                    if (this.opts.perMessageDeflate) {\n                        const len = \n                        // @ts-ignore\n                        \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n                        if (len < this.opts.perMessageDeflate.threshold) {\n                            opts.compress = false;\n                        }\n                    }\n                }\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    if (usingBrowserWebSocket) {\n                        // TypeError is thrown when passing the second argument on Safari\n                        this.ws.send(data);\n                    }\n                    else {\n                        this.ws.send(data, opts);\n                    }\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        let query = this.query || {};\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        let port = \"\";\n        // avoid port if default for schema\n        if (this.opts.port &&\n            ((\"wss\" === schema && Number(this.opts.port) !== 443) ||\n                (\"ws\" === schema && Number(this.opts.port) !== 80))) {\n            port = \":\" + this.opts.port;\n        }\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        const encodedQuery = encode(query);\n        const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n        return (schema +\n            \"://\" +\n            (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n            port +\n            this.opts.path +\n            (encodedQuery.length ? \"?\" + encodedQuery : \"\"));\n    }\n    /**\n     * Feature detection for WebSocket.\n     *\n     * @return {Boolean} whether this transport is available.\n     * @private\n     */\n    check() {\n        return !!WebSocket;\n    }\n}\n", "import { Polling } from \"./polling.js\";\nimport { WS } from \"./websocket.js\";\nexport const transports = {\n    websocket: WS,\n    polling: Polling,\n};\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nexport class Socket extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts = {}) {\n        super();\n        this.writeBuffer = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            uri = parse(uri);\n            opts.hostname = uri.host;\n            opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n            opts.port = uri.port;\n            if (uri.query)\n                opts.query = uri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = opts.transports || [\"polling\", \"websocket\"];\n        this.writeBuffer = [];\n        this.prevBufferLen = 0;\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: true,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        // set on handshake\n        this.id = null;\n        this.upgrades = null;\n        this.pingInterval = null;\n        this.pingTimeout = null;\n        // set on heartbeat\n        this.pingTimeoutTimer = null;\n        if (typeof addEventListener === \"function\") {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this.beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this.offlineEventListener = () => {\n                    this.onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                addEventListener(\"offline\", this.offlineEventListener, false);\n            }\n        }\n        this.open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts.transportOptions[name], this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        });\n        return new transports[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    open() {\n        let transport;\n        if (this.opts.rememberUpgrade &&\n            Socket.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1) {\n            transport = \"websocket\";\n        }\n        else if (0 === this.transports.length) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        else {\n            transport = this.transports[0];\n        }\n        this.readyState = \"opening\";\n        // Retry with the next transport if the transport is disabled (jsonp: false)\n        try {\n            transport = this.createTransport(transport);\n        }\n        catch (e) {\n            this.transports.shift();\n            this.open();\n            return;\n        }\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this.onDrain.bind(this))\n            .on(\"packet\", this.onPacket.bind(this))\n            .on(\"error\", this.onError.bind(this))\n            .on(\"close\", (reason) => this.onClose(\"transport close\", reason));\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        Socket.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        transport.open();\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n        // we check for `readyState` in case an `open`\n        // listener already closed the socket\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            let i = 0;\n            const l = this.upgrades.length;\n            for (; i < l; i++) {\n                this.probe(this.upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this.resetPingTimeout();\n                    this.sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this.onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this.upgrades = this.filterUpgrades(data.upgrades);\n        this.pingInterval = data.pingInterval;\n        this.pingTimeout = data.pingTimeout;\n        this.maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this.resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    resetPingTimeout() {\n        this.clearTimeoutFn(this.pingTimeoutTimer);\n        this.pingTimeoutTimer = this.setTimeoutFn(() => {\n            this.onClose(\"ping timeout\");\n        }, this.pingInterval + this.pingTimeout);\n        if (this.opts.autoUnref) {\n            this.pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    onDrain() {\n        this.writeBuffer.splice(0, this.prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this.prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this.getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this.prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    getWritablePackets() {\n        const shouldCheckPayloadSize = this.maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this.maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    send(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this.onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    onError(err) {\n        Socket.priorWebsocketSuccess = false;\n        this.emitReserved(\"error\", err);\n        this.onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this.pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (typeof removeEventListener === \"function\") {\n                removeEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n                removeEventListener(\"offline\", this.offlineEventListener, false);\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this.prevBufferLen = 0;\n        }\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        let i = 0;\n        const j = upgrades.length;\n        for (; i < j; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\nSocket.protocol = protocol;\n", "import { Socket } from \"./socket.js\";\nexport { Socket };\nexport const protocol = Socket.protocol;\nexport { Transport } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\nexport { nextTick } from \"./transports/websocket-constructor.js\";\n", "const withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeFile = typeof File === \"function\" ||\n    (typeof File !== \"undefined\" &&\n        toString.call(File) === \"[object FileConstructor]\");\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nexport function isBinary(obj) {\n    return ((withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj))) ||\n        (withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File));\n}\nexport function hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for (let i = 0, l = obj.length; i < l; i++) {\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON &&\n        typeof obj.toJSON === \"function\" &&\n        arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\n", "import { isBinary } from \"./is-binary.js\";\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nexport function deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return { packet: pack, buffers: buffers };\n}\nfunction _deconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (isBinary(data)) {\n        const placeholder = { _placeholder: true, num: buffers.length };\n        buffers.push(data);\n        return placeholder;\n    }\n    else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for (let i = 0; i < data.length; i++) {\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    }\n    else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nexport function reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    delete packet.attachments; // no longer useful\n    return packet;\n}\nfunction _reconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (data && data._placeholder === true) {\n        const isIndexValid = typeof data.num === \"number\" &&\n            data.num >= 0 &&\n            data.num < buffers.length;\n        if (isIndexValid) {\n            return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n        }\n        else {\n            throw new Error(\"illegal attachments\");\n        }\n    }\n    else if (Array.isArray(data)) {\n        for (let i = 0; i < data.length; i++) {\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    }\n    else if (typeof data === \"object\") {\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n", "import { Emitter } from \"@socket.io/component-emitter\";\nimport { deconstructPacket, reconstructPacket } from \"./binary.js\";\nimport { isBinary, hasBinary } from \"./is-binary.js\";\n/**\n * Protocol version.\n *\n * @public\n */\nexport const protocol = 5;\nexport var PacketType;\n(function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType || (PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nexport class Encoder {\n    /**\n     * Encoder constructor\n     *\n     * @param {function} replacer - custom replacer to pass down to JSON.parse\n     */\n    constructor(replacer) {\n        this.replacer = replacer;\n    }\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */\n    encode(obj) {\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if (hasBinary(obj)) {\n                return this.encodeAsBinary({\n                    type: obj.type === PacketType.EVENT\n                        ? PacketType.BINARY_EVENT\n                        : PacketType.BINARY_ACK,\n                    nsp: obj.nsp,\n                    data: obj.data,\n                    id: obj.id,\n                });\n            }\n        }\n        return [this.encodeAsString(obj)];\n    }\n    /**\n     * Encode packet as string.\n     */\n    encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT ||\n            obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data, this.replacer);\n        }\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */\n    encodeAsBinary(obj) {\n        const deconstruction = deconstructPacket(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nexport class Decoder extends Emitter {\n    /**\n     * Decoder constructor\n     *\n     * @param {function} reviver - custom reviver to pass down to JSON.stringify\n     */\n    constructor(reviver) {\n        super();\n        this.reviver = reviver;\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */\n    add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            if (this.reconstructor) {\n                throw new Error(\"got plaintext data when reconstructing a packet\");\n            }\n            packet = this.decodeString(obj);\n            const isBinaryEvent = packet.type === PacketType.BINARY_EVENT;\n            if (isBinaryEvent || packet.type === PacketType.BINARY_ACK) {\n                packet.type = isBinaryEvent ? PacketType.EVENT : PacketType.ACK;\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n            else {\n                // non-binary full packet\n                super.emitReserved(\"decoded\", packet);\n            }\n        }\n        else if (isBinary(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            }\n            else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n        }\n        else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */\n    decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0)),\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT ||\n            p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while (str.charAt(++i) !== \"-\" && i != str.length) { }\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (\",\" === c)\n                    break;\n                if (i === str.length)\n                    break;\n            }\n            p.nsp = str.substring(start, i);\n        }\n        else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length)\n                    break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = this.tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            }\n            else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n        return p;\n    }\n    tryParse(str) {\n        try {\n            return JSON.parse(str, this.reviver);\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    static isPayloadValid(type, payload) {\n        switch (type) {\n            case PacketType.CONNECT:\n                return typeof payload === \"object\";\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || typeof payload === \"object\";\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return Array.isArray(payload) && payload.length > 0;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */\n    destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n            this.reconstructor = null;\n        }\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n    constructor(packet) {\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */\n    takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = reconstructPacket(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */\n    finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\n", "export function on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n", "import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */\nexport class Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     */\n    constructor(io, nsp, opts) {\n        super();\n        /**\n         * Whether the socket is currently connected to the server.\n         *\n         * @example\n         * const socket = io();\n         *\n         * socket.on(\"connect\", () => {\n         *   console.log(socket.connected); // true\n         * });\n         *\n         * socket.on(\"disconnect\", () => {\n         *   console.log(socket.connected); // false\n         * });\n         */\n        this.connected = false;\n        /**\n         * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n         * be transmitted by the server.\n         */\n        this.recovered = false;\n        /**\n         * Buffer for packets received before the CONNECT packet\n         */\n        this.receiveBuffer = [];\n        /**\n         * Buffer for packets that will be sent once the socket is connected\n         */\n        this.sendBuffer = [];\n        /**\n         * The queue of packets to be sent with retry in case of failure.\n         *\n         * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n         * @private\n         */\n        this._queue = [];\n        /**\n         * A sequence to generate the ID of the {@link QueuedPacket}.\n         * @private\n         */\n        this._queueSeq = 0;\n        this.ids = 0;\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        this._opts = Object.assign({}, opts);\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.disconnected); // false\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.disconnected); // true\n     * });\n     */\n    get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on(io, \"open\", this.onopen.bind(this)),\n            on(io, \"packet\", this.onpacket.bind(this)),\n            on(io, \"error\", this.onerror.bind(this)),\n            on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n     *\n     * @example\n     * const socket = io();\n     *\n     * console.log(socket.active); // true\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   if (reason === \"io server disconnect\") {\n     *     // the disconnection was initiated by the server, you need to manually reconnect\n     *     console.log(socket.active); // false\n     *   }\n     *   // else the socket will automatically try to reconnect\n     *   console.log(socket.active); // true\n     * });\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @example\n     * const socket = io({\n     *   autoConnect: false\n     * });\n     *\n     * socket.connect();\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for {@link connect()}.\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * This method mimics the WebSocket.send() method.\n     *\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n     *\n     * @example\n     * socket.send(\"hello\");\n     *\n     * // this is equivalent to\n     * socket.emit(\"message\", \"hello\");\n     *\n     * @return self\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @example\n     * socket.emit(\"hello\", \"world\");\n     *\n     * // all serializable datastructures are supported (no need to call JSON.stringify)\n     * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n     *\n     * // with an acknowledgement from the server\n     * socket.emit(\"hello\", \"world\", (val) => {\n     *   // ...\n     * });\n     *\n     * @return self\n     */\n    emit(ev, ...args) {\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n            this._addToQueue(args);\n            return this;\n        }\n        const packet = {\n            type: PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = this.io.engine &&\n            this.io.engine.transport &&\n            this.io.engine.transport.writable;\n        const discardPacket = this.flags.volatile && (!isTransportWritable || !this.connected);\n        if (discardPacket) {\n        }\n        else if (this.connected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */\n    _registerAckCallback(id, ack) {\n        var _a;\n        const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(() => {\n            delete this.acks[id];\n            for (let i = 0; i < this.sendBuffer.length; i++) {\n                if (this.sendBuffer[i].id === id) {\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        this.acks[id] = (...args) => {\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, [null, ...args]);\n        };\n    }\n    /**\n     * Emits an event and waits for an acknowledgement\n     *\n     * @example\n     * // without timeout\n     * const response = await socket.emitWithAck(\"hello\", \"world\");\n     *\n     * // with a specific timeout\n     * try {\n     *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n     * } catch (err) {\n     *   // the server did not acknowledge the event in the given delay\n     * }\n     *\n     * @return a Promise that will be fulfilled when the server acknowledges the event\n     */\n    emitWithAck(ev, ...args) {\n        // the timeout flag is optional\n        const withErr = this.flags.timeout !== undefined || this._opts.ackTimeout !== undefined;\n        return new Promise((resolve, reject) => {\n            args.push((arg1, arg2) => {\n                if (withErr) {\n                    return arg1 ? reject(arg1) : resolve(arg2);\n                }\n                else {\n                    return resolve(arg1);\n                }\n            });\n            this.emit(ev, ...args);\n        });\n    }\n    /**\n     * Add the packet to the queue.\n     * @param args\n     * @private\n     */\n    _addToQueue(args) {\n        let ack;\n        if (typeof args[args.length - 1] === \"function\") {\n            ack = args.pop();\n        }\n        const packet = {\n            id: this._queueSeq++,\n            tryCount: 0,\n            pending: false,\n            args,\n            flags: Object.assign({ fromQueue: true }, this.flags),\n        };\n        args.push((err, ...responseArgs) => {\n            if (packet !== this._queue[0]) {\n                // the packet has already been acknowledged\n                return;\n            }\n            const hasError = err !== null;\n            if (hasError) {\n                if (packet.tryCount > this._opts.retries) {\n                    this._queue.shift();\n                    if (ack) {\n                        ack(err);\n                    }\n                }\n            }\n            else {\n                this._queue.shift();\n                if (ack) {\n                    ack(null, ...responseArgs);\n                }\n            }\n            packet.pending = false;\n            return this._drainQueue();\n        });\n        this._queue.push(packet);\n        this._drainQueue();\n    }\n    /**\n     * Send the first packet of the queue, and wait for an acknowledgement from the server.\n     * @param force - whether to resend a packet that has not been acknowledged yet\n     *\n     * @private\n     */\n    _drainQueue(force = false) {\n        if (!this.connected || this._queue.length === 0) {\n            return;\n        }\n        const packet = this._queue[0];\n        if (packet.pending && !force) {\n            return;\n        }\n        packet.pending = true;\n        packet.tryCount++;\n        this.flags = packet.flags;\n        this.emit.apply(this, packet.args);\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this._sendConnectPacket(data);\n            });\n        }\n        else {\n            this._sendConnectPacket(this.auth);\n        }\n    }\n    /**\n     * Sends a CONNECT packet to initiate the Socket.IO session.\n     *\n     * @param data\n     * @private\n     */\n    _sendConnectPacket(data) {\n        this.packet({\n            type: PacketType.CONNECT,\n            data: this._pid\n                ? Object.assign({ pid: this._pid, offset: this._lastOffset }, data)\n                : data,\n        });\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */\n    onclose(reason, description) {\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    this.onconnect(packet.data.sid, packet.data.pid);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        if (null != packet.id) {\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n        if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n            this._lastOffset = args[args.length - 1];\n        }\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            self.packet({\n                type: PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowlegement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (\"function\" === typeof ack) {\n            ack.apply(this, packet.data);\n            delete this.acks[packet.id];\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id, pid) {\n        this.id = id;\n        this.recovered = pid && this._pid === pid;\n        this._pid = pid; // defined only if connection state recovery is enabled\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n        this._drainQueue(true);\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n     *\n     * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   // console.log(reason); prints \"io client disconnect\"\n     * });\n     *\n     * socket.disconnect();\n     *\n     * @return self\n     */\n    disconnect() {\n        if (this.connected) {\n            this.packet({ type: PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for {@link disconnect()}.\n     *\n     * @return self\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @example\n     * socket.compress(false).emit(\"hello\");\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @example\n     * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n     *\n     * @returns self\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * @example\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     *\n     * @returns self\n     */\n    timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @example\n     * socket.onAny((event, ...args) => {\n     *   console.log(`got ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @example\n     * socket.prependAny((event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * }\n     *\n     * socket.onAny(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAny(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAny();\n     *\n     * @param listener\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * }\n     *\n     * socket.onAnyOutgoing(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAnyOutgoing(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAnyOutgoing();\n     *\n     * @param [listener] - the catch-all listener (optional)\n     */\n    offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */\n    notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n", "/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\nexport function Backoff(opts) {\n    opts = opts || {};\n    this.ms = opts.min || 100;\n    this.max = opts.max || 10000;\n    this.factor = opts.factor || 2;\n    this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n    this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\nBackoff.prototype.duration = function () {\n    var ms = this.ms * Math.pow(this.factor, this.attempts++);\n    if (this.jitter) {\n        var rand = Math.random();\n        var deviation = Math.floor(rand * this.jitter * ms);\n        ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n    }\n    return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\nBackoff.prototype.reset = function () {\n    this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */\nBackoff.prototype.setMin = function (min) {\n    this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */\nBackoff.prototype.setMax = function (max) {\n    this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */\nBackoff.prototype.setJitter = function (jitter) {\n    this.jitter = jitter;\n};\n", "import { Socket as Engine, installTimerFunctions, nextTick, } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\nexport class Manager extends Emitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        installTimerFunctions(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n        this.engine = new Engine(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        // emit `error`\n        const errorSub = on(socket, \"error\", (err) => {\n            self.cleanup();\n            self._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                self.maybeReconnectOnOpen();\n            }\n        });\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            if (timeout === 0) {\n                openSubDestroy(); // prevents a race condition with the 'open' event\n            }\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n                openSubDestroy();\n                socket.close();\n                // @ts-ignore\n                socket.emit(\"error\", new Error(\"timeout\"));\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        try {\n            this.decoder.add(data);\n        }\n        catch (e) {\n            this.onclose(\"parse error\", e);\n        }\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n        nextTick(() => {\n            this.emitReserved(\"packet\", packet);\n        }, this.setTimeoutFn);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        else if (this._autoConnect && !socket.active) {\n            socket.connect();\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n        if (this.engine)\n            this.engine.close();\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called upon engine close.\n     *\n     * @private\n     */\n    onclose(reason, description) {\n        this.cleanup();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n", "import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\n/**\n * Managers cache.\n */\nconst cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n        io = new Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n            cache[id] = new Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager,\n    Socket,\n    io: lookup,\n    connect: lookup,\n});\n/**\n * Protocol version.\n *\n * @public\n */\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default, };\n", "import { parse } from \"engine.io-client\";\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nexport function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        obj = parse(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "encodePacket", "supportsBinary", "callback", "obj", "encodeBlobAsBase64", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "chars", "lookup", "Uint8Array", "i", "length", "charCodeAt", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "decoded", "base64", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "len", "p", "arraybuffer", "bytes", "decode", "SEPARATOR", "String", "fromCharCode", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "this", "_callbacks", "push", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "cb", "callbacks", "splice", "emit", "args", "Array", "slice", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "globalThisShim", "self", "window", "Function", "pick", "_len", "attr", "_key", "reduce", "acc", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "globalThis", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "setTimeoutFn", "bind", "clearTimeoutFn", "prev", "TransportError", "reason", "description", "context", "_this", "_classCallCheck", "_super", "Error", "Transport", "_Emitter", "_inherits", "_super2", "_createSuper", "_this2", "writable", "_assertThisInitialized", "query", "socket", "_createClass", "value", "_get", "_getPrototypeOf", "readyState", "doOpen", "doClose", "onClose", "packets", "write", "packet", "onPacket", "details", "onPause", "alphabet", "map", "seed", "encode", "num", "encoded", "Math", "floor", "yeast", "now", "Date", "str", "encodeURIComponent", "qs", "qry", "pairs", "l", "pair", "decodeURIComponent", "XMLHttpRequest", "err", "hasCORS", "XHR", "xdomain", "e", "concat", "join", "empty", "hasXHR2", "responseType", "Polling", "_Transport", "polling", "location", "isSSL", "protocol", "port", "xd", "hostname", "xs", "secure", "forceBase64", "get", "poll", "pause", "total", "doPoll", "_this3", "encodedPayload", "encodedPackets", "decodedPacket", "decodePayload", "onOpen", "_this4", "close", "_this5", "count", "encodePayload", "doWrite", "schema", "timestampRequests", "timestampParam", "sid", "b64", "Number", "<PERSON><PERSON><PERSON><PERSON>", "indexOf", "path", "_extends", "Request", "uri", "_this6", "req", "request", "method", "xhrStatus", "onError", "_this7", "onData", "pollXhr", "_this8", "async", "undefined", "_this9", "xscheme", "xhr", "open", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "withCredentials", "requestTimeout", "timeout", "onreadystatechange", "status", "onLoad", "send", "document", "index", "requestsCount", "requests", "cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "nextTick", "Promise", "resolve", "then", "WebSocket", "MozWebSocket", "isReactNative", "navigator", "product", "toLowerCase", "WS", "check", "protocols", "headers", "ws", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "_loop", "lastPacket", "transports", "websocket", "re", "parts", "parse", "src", "b", "replace", "m", "exec", "source", "host", "authority", "ipv6uri", "pathNames", "regx", "names", "query<PERSON><PERSON>", "$0", "$1", "$2", "Socket", "writeBuffer", "prevBufferLen", "agent", "upgrade", "rememberUpgrade", "addTrailingSlash", "rejectUnauthorized", "perMessageDeflate", "threshold", "transportOptions", "closeOnBeforeunload", "id", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "beforeunloadEventListener", "transport", "offlineEventListener", "name", "EIO", "priorWebsocketSuccess", "createTransport", "shift", "setTransport", "onDrain", "failed", "onTransportOpen", "msg", "upgrading", "flush", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "probe", "onHandshake", "JSON", "resetPingTimeout", "sendPacket", "code", "filterUpgrades", "maxPayload", "getWritablePackets", "payloadSize", "c", "utf8Length", "ceil", "byteLength", "size", "options", "compress", "cleanupAndClose", "waitForUpgrade", "filteredUpgrades", "j", "Socket$1", "withNativeFile", "File", "isBinary", "hasBinary", "toJSON", "_typeof", "isArray", "deconstructPacket", "buffers", "packetData", "pack", "_deconstructPacket", "attachments", "placeholder", "_placeholder", "newData", "reconstructPacket", "_reconstructPacket", "PacketType", "Encoder", "replacer", "EVENT", "ACK", "encodeAsString", "encodeAsBinary", "BINARY_EVENT", "BINARY_ACK", "nsp", "stringify", "deconstruction", "unshift", "Decoder", "reviver", "reconstructor", "isBinaryEvent", "decodeString", "BinaryReconstructor", "takeBinaryData", "start", "buf", "next", "payload", "try<PERSON><PERSON><PERSON>", "substr", "isPayloadValid", "finishedReconstruction", "CONNECT", "DISCONNECT", "CONNECT_ERROR", "reconPack", "binData", "RESERVED_EVENTS", "freeze", "connect", "connect_error", "disconnect", "disconnecting", "newListener", "io", "connected", "recovered", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "_queue", "_queueSeq", "ids", "acks", "flags", "auth", "_opts", "_autoConnect", "subs", "onpacket", "subEvents", "_readyState", "_len2", "_key2", "retries", "fromQueue", "_addToQueue", "ack", "pop", "_registerAckCallback", "isTransportWritable", "engine", "discardPacket", "notifyOutgoingListeners", "_a", "ackTimeout", "timer", "_len3", "_key3", "_len4", "_key4", "withErr", "reject", "arg1", "arg2", "tryCount", "pending", "<PERSON><PERSON><PERSON><PERSON>", "_len5", "responseArgs", "_key5", "_drainQueue", "force", "_packet", "_sendConnectPacket", "_pid", "pid", "offset", "_lastOffset", "onconnect", "onevent", "onack", "ondisconnect", "destroy", "message", "emitEvent", "_anyListeners", "_step", "_iterator", "_createForOfIteratorHelper", "s", "n", "done", "f", "sent", "_len6", "_key6", "emitBuffered", "subDestroy", "listener", "_anyOutgoingListeners", "_step2", "_iterator2", "Backoff", "ms", "min", "max", "factor", "jitter", "attempts", "duration", "pow", "rand", "random", "deviation", "reset", "setMin", "setMax", "setJitter", "Manager", "nsps", "reconnection", "reconnectionAttempts", "Infinity", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "_parser", "parser", "encoder", "decoder", "autoConnect", "v", "_reconnection", "_reconnectionAttempts", "_reconnectionDelay", "_randomizationFactor", "_reconnectionDelayMax", "_timeout", "_reconnecting", "reconnect", "Engine", "skipReconnect", "openSubDestroy", "errorSub", "maybeReconnectOnOpen", "onping", "ondata", "ondecoded", "add", "active", "_i", "_nsps", "_close", "delay", "onreconnect", "attempt", "cache", "parsed", "loc", "test", "href", "url", "sameNamespace", "forceNew", "multiplex"], "mappings": ";;;;;0xIAAA,IAAMA,EAAeC,OAAOC,OAAO,MACnCF,EAAY,KAAW,IACvBA,EAAY,MAAY,IACxBA,EAAY,KAAW,IACvBA,EAAY,KAAW,IACvBA,EAAY,QAAc,IAC1BA,EAAY,QAAc,IAC1BA,EAAY,KAAW,IACvB,IAAMG,EAAuBF,OAAOC,OAAO,MAC3CD,OAAOG,KAAKJ,GAAcK,SAAQ,SAAAC,GAC9BH,EAAqBH,EAAaM,IAAQA,CAC7C,ICRD,IDSA,IAAMC,EAAe,CAAEC,KAAM,QAASC,KAAM,gBEXtCC,EAAiC,mBAATC,MACT,oBAATA,MACqC,6BAAzCV,OAAOW,UAAUC,SAASC,KAAKH,MACjCI,EAA+C,mBAAhBC,YAO/BC,EAAe,WAAiBC,EAAgBC,GAAa,IALpDC,EAKSZ,IAAAA,KAAMC,IAAAA,KAC1B,OAAIC,GAAkBD,aAAgBE,KAC9BO,EACOC,EAASV,GAGTY,EAAmBZ,EAAMU,GAG/BJ,IACJN,aAAgBO,cAfVI,EAegCX,EAdN,mBAAvBO,YAAYM,OACpBN,YAAYM,OAAOF,GACnBA,GAAOA,EAAIG,kBAAkBP,cAa3BE,EACOC,EAASV,GAGTY,EAAmB,IAAIV,KAAK,CAACF,IAAQU,GAI7CA,EAASnB,EAAaQ,IAASC,GAAQ,IACjD,EACKY,EAAqB,SAACZ,EAAMU,GAC9B,IAAMK,EAAa,IAAIC,WAKvB,OAJAD,EAAWE,OAAS,WAChB,IAAMC,EAAUH,EAAWI,OAAOC,MAAM,KAAK,GAC7CV,EAAS,IAAMQ,IAEZH,EAAWM,cAAcrB,EACnC,EDvCKsB,EAAQ,mEAERC,EAA+B,oBAAfC,WAA6B,GAAK,IAAIA,WAAW,KAC9DC,EAAI,EAAGA,EAAIH,EAAMI,OAAQD,IAC9BF,EAAOD,EAAMK,WAAWF,IAAMA,EAkB3B,IEpBDnB,EAA+C,mBAAhBC,YAC/BqB,EAAe,SAACC,EAAeC,GACjC,GAA6B,iBAAlBD,EACP,MAAO,CACH9B,KAAM,UACNC,KAAM+B,EAAUF,EAAeC,IAGvC,IAAM/B,EAAO8B,EAAcG,OAAO,GAClC,MAAa,MAATjC,EACO,CACHA,KAAM,UACNC,KAAMiC,EAAmBJ,EAAcK,UAAU,GAAIJ,IAG1CpC,EAAqBK,GAIjC8B,EAAcH,OAAS,EACxB,CACE3B,KAAML,EAAqBK,GAC3BC,KAAM6B,EAAcK,UAAU,IAEhC,CACEnC,KAAML,EAAqBK,IARxBD,CAUd,EACKmC,EAAqB,SAACjC,EAAM8B,GAC9B,GAAIxB,EAAuB,CACvB,IAAM6B,EFVQ,SAACC,GACnB,IAA8DX,EAAUY,EAAUC,EAAUC,EAAUC,EAAlGC,EAA+B,IAAhBL,EAAOV,OAAegB,EAAMN,EAAOV,OAAWiB,EAAI,EACnC,MAA9BP,EAAOA,EAAOV,OAAS,KACvBe,IACkC,MAA9BL,EAAOA,EAAOV,OAAS,IACvBe,KAGR,IAAMG,EAAc,IAAIrC,YAAYkC,GAAeI,EAAQ,IAAIrB,WAAWoB,GAC1E,IAAKnB,EAAI,EAAGA,EAAIiB,EAAKjB,GAAK,EACtBY,EAAWd,EAAOa,EAAOT,WAAWF,IACpCa,EAAWf,EAAOa,EAAOT,WAAWF,EAAI,IACxCc,EAAWhB,EAAOa,EAAOT,WAAWF,EAAI,IACxCe,EAAWjB,EAAOa,EAAOT,WAAWF,EAAI,IACxCoB,EAAMF,KAAQN,GAAY,EAAMC,GAAY,EAC5CO,EAAMF,MAAoB,GAAXL,IAAkB,EAAMC,GAAY,EACnDM,EAAMF,MAAoB,EAAXJ,IAAiB,EAAiB,GAAXC,EAE1C,OAAOI,CACV,CETuBE,CAAO9C,GACvB,OAAO+B,EAAUI,EAASL,EAC7B,CAEG,MAAO,CAAEM,QAAQ,EAAMpC,KAAAA,EAE9B,EACK+B,EAAY,SAAC/B,EAAM8B,GACrB,MACS,SADDA,GAEO9B,aAAgBO,YAAc,IAAIL,KAAK,CAACF,IAGxCA,CAElB,EC7CK+C,EAAYC,OAAOC,aAAa,ICI/B,SAASC,EAAQvC,GACtB,GAAIA,EAAK,OAWX,SAAeA,GACb,IAAK,IAAId,KAAOqD,EAAQ/C,UACtBQ,EAAId,GAAOqD,EAAQ/C,UAAUN,GAE/B,OAAOc,CACR,CAhBiBwC,CAAMxC,EACvB,CA0BDuC,EAAQ/C,UAAUiD,GAClBF,EAAQ/C,UAAUkD,iBAAmB,SAASC,EAAOC,GAInD,OAHAC,KAAKC,WAAaD,KAAKC,YAAc,CAAA,GACpCD,KAAKC,WAAW,IAAMH,GAASE,KAAKC,WAAW,IAAMH,IAAU,IAC7DI,KAAKH,GACDC,IACR,EAYDN,EAAQ/C,UAAUwD,KAAO,SAASL,EAAOC,GACvC,SAASH,IACPI,KAAKI,IAAIN,EAAOF,GAChBG,EAAGM,MAAML,KAAMM,UAChB,CAID,OAFAV,EAAGG,GAAKA,EACRC,KAAKJ,GAAGE,EAAOF,GACRI,IACR,EAYDN,EAAQ/C,UAAUyD,IAClBV,EAAQ/C,UAAU4D,eAClBb,EAAQ/C,UAAU6D,mBAClBd,EAAQ/C,UAAU8D,oBAAsB,SAASX,EAAOC,GAItD,GAHAC,KAAKC,WAAaD,KAAKC,YAAc,CAAA,EAGjC,GAAKK,UAAUpC,OAEjB,OADA8B,KAAKC,WAAa,GACXD,KAIT,IAUIU,EAVAC,EAAYX,KAAKC,WAAW,IAAMH,GACtC,IAAKa,EAAW,OAAOX,KAGvB,GAAI,GAAKM,UAAUpC,OAEjB,cADO8B,KAAKC,WAAW,IAAMH,GACtBE,KAKT,IAAK,IAAI/B,EAAI,EAAGA,EAAI0C,EAAUzC,OAAQD,IAEpC,IADAyC,EAAKC,EAAU1C,MACJ8B,GAAMW,EAAGX,KAAOA,EAAI,CAC7BY,EAAUC,OAAO3C,EAAG,GACpB,KACD,CASH,OAJyB,IAArB0C,EAAUzC,eACL8B,KAAKC,WAAW,IAAMH,GAGxBE,IACR,EAUDN,EAAQ/C,UAAUkE,KAAO,SAASf,GAChCE,KAAKC,WAAaD,KAAKC,YAAc,CAAA,EAKrC,IAHA,IAAIa,EAAO,IAAIC,MAAMT,UAAUpC,OAAS,GACpCyC,EAAYX,KAAKC,WAAW,IAAMH,GAE7B7B,EAAI,EAAGA,EAAIqC,UAAUpC,OAAQD,IACpC6C,EAAK7C,EAAI,GAAKqC,UAAUrC,GAG1B,GAAI0C,EAEG,CAAI1C,EAAI,EAAb,IAAK,IAAWiB,GADhByB,EAAYA,EAAUK,MAAM,IACI9C,OAAQD,EAAIiB,IAAOjB,EACjD0C,EAAU1C,GAAGoC,MAAML,KAAMc,EADK5C,CAKlC,OAAO8B,IACR,EAGDN,EAAQ/C,UAAUsE,aAAevB,EAAQ/C,UAAUkE,KAUnDnB,EAAQ/C,UAAUuE,UAAY,SAASpB,GAErC,OADAE,KAAKC,WAAaD,KAAKC,YAAc,CAAA,EAC9BD,KAAKC,WAAW,IAAMH,IAAU,EACxC,EAUDJ,EAAQ/C,UAAUwE,aAAe,SAASrB,GACxC,QAAUE,KAAKkB,UAAUpB,GAAO5B,MACjC,ECxKM,IAAMkD,EACW,oBAATC,KACAA,KAEgB,oBAAXC,OACLA,OAGAC,SAAS,cAATA,GCPR,SAASC,EAAKrE,GAAc,IAAA,IAAAsE,EAAAnB,UAAApC,OAANwD,EAAM,IAAAX,MAAAU,EAAA,EAAAA,EAAA,EAAA,GAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAND,EAAMC,EAAA,GAAArB,UAAAqB,GAC/B,OAAOD,EAAKE,QAAO,SAACC,EAAKC,GAIrB,OAHI3E,EAAI4E,eAAeD,KACnBD,EAAIC,GAAK3E,EAAI2E,IAEVD,CAJJ,GAKJ,CALI,EAMV,CAED,IAAMG,EAAqBC,EAAWC,WAChCC,EAAuBF,EAAWG,aACjC,SAASC,EAAsBlF,EAAKmF,GACnCA,EAAKC,iBACLpF,EAAIqF,aAAeR,EAAmBS,KAAKR,GAC3C9E,EAAIuF,eAAiBP,EAAqBM,KAAKR,KAG/C9E,EAAIqF,aAAeP,EAAWC,WAAWO,KAAKR,GAC9C9E,EAAIuF,eAAiBT,EAAWG,aAAaK,KAAKR,GAEzD,KClBoBU,ECAfC,gCACF,SAAAA,EAAYC,EAAQC,EAAaC,GAAS,IAAAC,EAAA,OAAAC,EAAAjD,KAAA4C,IACtCI,EAAAE,EAAArG,KAAAmD,KAAM6C,IACDC,YAAcA,EACnBE,EAAKD,QAAUA,EACfC,EAAKzG,KAAO,iBAJ0ByG,CAKzC,gBANwBG,QAQhBC,EAAb,SAAAC,GAAAC,EAAAF,EAAAC,GAAA,IAAAE,EAAAC,EAAAJ,GAOI,SAAAA,EAAYd,GAAM,IAAAmB,EAAA,OAAAR,EAAAjD,KAAAoD,IACdK,EAAAF,EAAA1G,KAAAmD,OACK0D,UAAW,EAChBrB,EAAqBsB,EAAAF,GAAOnB,GAC5BmB,EAAKnB,KAAOA,EACZmB,EAAKG,MAAQtB,EAAKsB,MAClBH,EAAKI,OAASvB,EAAKuB,OANLJ,CAOjB,CAdL,OAAAK,EAAAV,EAAA,CAAA,CAAA/G,IAAA,UAAA0H,MAwBI,SAAQlB,EAAQC,EAAaC,GAEzB,OADAiB,EAAmBC,EAAAb,EAAAzG,WAAA,eAAAqD,MAAAnD,KAAAmD,KAAA,QAAS,IAAI4C,EAAeC,EAAQC,EAAaC,IAC7D/C,IACV,GA3BL,CAAA3D,IAAA,OAAA0H,MA+BI,WAGI,OAFA/D,KAAKkE,WAAa,UAClBlE,KAAKmE,SACEnE,IACV,GAnCL,CAAA3D,IAAA,QAAA0H,MAuCI,WAKI,MAJwB,YAApB/D,KAAKkE,YAAgD,SAApBlE,KAAKkE,aACtClE,KAAKoE,UACLpE,KAAKqE,WAEFrE,IACV,GA7CL,CAAA3D,IAAA,OAAA0H,MAmDI,SAAKO,GACuB,SAApBtE,KAAKkE,YACLlE,KAAKuE,MAAMD,EAKlB,GA1DL,CAAAjI,IAAA,SAAA0H,MAgEI,WACI/D,KAAKkE,WAAa,OAClBlE,KAAK0D,UAAW,EAChBM,EAAAC,EAAAb,EAAAzG,WAAA,eAAAqD,MAAAnD,KAAAmD,KAAmB,OACtB,GApEL,CAAA3D,IAAA,SAAA0H,MA2EI,SAAOvH,GACH,IAAMgI,EAASpG,EAAa5B,EAAMwD,KAAK6D,OAAOvF,YAC9C0B,KAAKyE,SAASD,EACjB,GA9EL,CAAAnI,IAAA,WAAA0H,MAoFI,SAASS,GACLR,EAAmBC,EAAAb,EAAAzG,WAAA,eAAAqD,MAAAnD,KAAAmD,KAAA,SAAUwE,EAChC,GAtFL,CAAAnI,IAAA,UAAA0H,MA4FI,SAAQW,GACJ1E,KAAKkE,WAAa,SAClBF,EAAmBC,EAAAb,EAAAzG,WAAA,eAAAqD,MAAAnD,KAAAmD,KAAA,QAAS0E,EAC/B,GA/FL,CAAArI,IAAA,QAAA0H,MAqGI,SAAMY,GAAY,KArGtBvB,CAAA,CAAA,CAA+B1D,GDTzBkF,EAAW,mEAAmEhH,MAAM,IAAkBiH,EAAM,CAAA,EAC9GC,EAAO,EAAG7G,EAAI,EAQX,SAAS8G,EAAOC,GACnB,IAAIC,EAAU,GACd,GACIA,EAAUL,EAASI,EAZ6E,IAY7DC,EACnCD,EAAME,KAAKC,MAAMH,EAb+E,UAc3FA,EAAM,GACf,OAAOC,CACV,CAqBM,SAASG,IACZ,IAAMC,EAAMN,GAAQ,IAAIO,MACxB,OAAID,IAAQ1C,GACDmC,EAAO,EAAGnC,EAAO0C,GACrBA,EAAM,IAAMN,EAAOD,IAC7B,CAID,KAAO7G,EA9CiG,GA8CrFA,IACf4G,EAAID,EAAS3G,IAAMA,EEzChB,SAAS8G,EAAO5H,GACnB,IAAIoI,EAAM,GACV,IAAK,IAAItH,KAAKd,EACNA,EAAI4E,eAAe9D,KACfsH,EAAIrH,SACJqH,GAAO,KACXA,GAAOC,mBAAmBvH,GAAK,IAAMuH,mBAAmBrI,EAAIc,KAGpE,OAAOsH,CACV,CAOM,SAASjG,EAAOmG,GAGnB,IAFA,IAAIC,EAAM,CAAA,EACNC,EAAQF,EAAG7H,MAAM,KACZK,EAAI,EAAG2H,EAAID,EAAMzH,OAAQD,EAAI2H,EAAG3H,IAAK,CAC1C,IAAI4H,EAAOF,EAAM1H,GAAGL,MAAM,KAC1B8H,EAAII,mBAAmBD,EAAK,KAAOC,mBAAmBD,EAAK,GAC9D,CACD,OAAOH,CACV,CChCD,IAAI3B,GAAQ,EACZ,IACIA,EAAkC,oBAAnBgC,gBACX,oBAAqB,IAAIA,cAKhC,CAHD,MAAOC,GAGN,CACM,IAAMC,EAAUlC,ECPhB,SAASmC,EAAI5D,GAChB,IAAM6D,EAAU7D,EAAK6D,QAErB,IACI,GAAI,oBAAuBJ,kBAAoBI,GAAWF,GACtD,OAAO,IAAIF,cAGN,CAAb,MAAOK,GAAM,CACb,IAAKD,EACD,IACI,OAAO,IAAIlE,EAAW,CAAC,UAAUoE,OAAO,UAAUC,KAAK,OAAM,oBAEpD,CAAb,MAAOF,GAAM,CAEpB,CCVD,SAASG,IAAW,CACpB,IAAMC,GAIK,MAHK,IAAIT,EAAe,CAC3BI,SAAS,IAEMM,aAEVC,GAAb,SAAAC,GAAArD,EAAAoD,EAAAC,GAAA,IAAAzD,EAAAM,EAAAkD,GAOI,SAAAA,EAAYpE,GAAM,IAAAU,EAGd,GAHcC,EAAAjD,KAAA0G,IACd1D,EAAAE,EAAArG,KAAAmD,KAAMsC,IACDsE,SAAU,EACS,oBAAbC,SAA0B,CACjC,IAAMC,EAAQ,WAAaD,SAASE,SAChCC,EAAOH,SAASG,KAEfA,IACDA,EAAOF,EAAQ,MAAQ,MAE3B9D,EAAKiE,GACoB,oBAAbJ,UACJvE,EAAK4E,WAAaL,SAASK,UAC3BF,IAAS1E,EAAK0E,KACtBhE,EAAKmE,GAAK7E,EAAK8E,SAAWN,CAC7B,CAID,IAAMO,EAAc/E,GAAQA,EAAK+E,YAnBnB,OAoBdrE,EAAK/F,eAAiBuJ,KAAYa,EApBpBrE,CAqBjB,CA5BL,OAAAc,EAAA4C,EAAA,CAAA,CAAArK,IAAA,OAAAiL,IA6BI,WACI,MAAO,SACV,GA/BL,CAAAjL,IAAA,SAAA0H,MAsCI,WACI/D,KAAKuH,MACR,GAxCL,CAAAlL,IAAA,QAAA0H,MA+CI,SAAMY,GAAS,IAAAlB,EAAAzD,KACXA,KAAKkE,WAAa,UAClB,IAAMsD,EAAQ,WACV/D,EAAKS,WAAa,SAClBS,KAEJ,GAAI3E,KAAK4G,UAAY5G,KAAK0D,SAAU,CAChC,IAAI+D,EAAQ,EACRzH,KAAK4G,UACLa,IACAzH,KAAKG,KAAK,gBAAgB,aACpBsH,GAASD,QAGdxH,KAAK0D,WACN+D,IACAzH,KAAKG,KAAK,SAAS,aACbsH,GAASD,OAGtB,MAEGA,GAEP,GAvEL,CAAAnL,IAAA,OAAA0H,MA6EI,WACI/D,KAAK4G,SAAU,EACf5G,KAAK0H,SACL1H,KAAKiB,aAAa,OACrB,GAjFL,CAAA5E,IAAA,SAAA0H,MAuFI,SAAOvH,GAAM,IAAAmL,EAAA3H,MTpFK,SAAC4H,EAAgBtJ,GAGnC,IAFA,IAAMuJ,EAAiBD,EAAehK,MAAM2B,GACtC+E,EAAU,GACPrG,EAAI,EAAGA,EAAI4J,EAAe3J,OAAQD,IAAK,CAC5C,IAAM6J,EAAgB1J,EAAayJ,EAAe5J,GAAIK,GAEtD,GADAgG,EAAQpE,KAAK4H,GACc,UAAvBA,EAAcvL,KACd,KAEP,CACD,OAAO+H,CACV,ESwFOyD,CAAcvL,EAAMwD,KAAK6D,OAAOvF,YAAYlC,SAd3B,SAACoI,GAMd,GAJI,YAAcmD,EAAKzD,YAA8B,SAAhBM,EAAOjI,MACxCoL,EAAKK,SAGL,UAAYxD,EAAOjI,KAEnB,OADAoL,EAAKtD,QAAQ,CAAEvB,YAAa,oCACrB,EAGX6E,EAAKlD,SAASD,EACjB,IAIG,WAAaxE,KAAKkE,aAElBlE,KAAK4G,SAAU,EACf5G,KAAKiB,aAAa,gBACd,SAAWjB,KAAKkE,YAChBlE,KAAKuH,OAKhB,GAlHL,CAAAlL,IAAA,UAAA0H,MAwHI,WAAU,IAAAkE,EAAAjI,KACAkI,EAAQ,WACVD,EAAK1D,MAAM,CAAC,CAAEhI,KAAM,YAEpB,SAAWyD,KAAKkE,WAChBgE,IAKAlI,KAAKG,KAAK,OAAQ+H,EAEzB,GApIL,CAAA7L,IAAA,QAAA0H,MA2II,SAAMO,GAAS,IAAA6D,EAAAnI,KACXA,KAAK0D,UAAW,ETxJF,SAACY,EAASpH,GAE5B,IAAMgB,EAASoG,EAAQpG,OACjB2J,EAAiB,IAAI9G,MAAM7C,GAC7BkK,EAAQ,EACZ9D,EAAQlI,SAAQ,SAACoI,EAAQvG,GAErBjB,EAAawH,GAAQ,GAAO,SAAAnG,GACxBwJ,EAAe5J,GAAKI,IACd+J,IAAUlK,GACZhB,EAAS2K,EAAevB,KAAK/G,GAEpC,MAER,CS2IO8I,CAAc/D,GAAS,SAAC9H,GACpB2L,EAAKG,QAAQ9L,GAAM,WACf2L,EAAKzE,UAAW,EAChByE,EAAKlH,aAAa,WAEzB,GACJ,GAnJL,CAAA5E,IAAA,MAAA0H,MAyJI,WACI,IAAIH,EAAQ5D,KAAK4D,OAAS,GACpB2E,EAASvI,KAAKsC,KAAK8E,OAAS,QAAU,OACxCJ,EAAO,IAEP,IAAUhH,KAAKsC,KAAKkG,oBACpB5E,EAAM5D,KAAKsC,KAAKmG,gBAAkBrD,KAEjCpF,KAAK/C,gBAAmB2G,EAAM8E,MAC/B9E,EAAM+E,IAAM,GAGZ3I,KAAKsC,KAAK0E,OACR,UAAYuB,GAAqC,MAA3BK,OAAO5I,KAAKsC,KAAK0E,OACpC,SAAWuB,GAAqC,KAA3BK,OAAO5I,KAAKsC,KAAK0E,SAC3CA,EAAO,IAAMhH,KAAKsC,KAAK0E,MAE3B,IAAM6B,EAAe9D,EAAOnB,GAE5B,OAAQ2E,EACJ,QAF8C,IAArCvI,KAAKsC,KAAK4E,SAAS4B,QAAQ,KAG5B,IAAM9I,KAAKsC,KAAK4E,SAAW,IAAMlH,KAAKsC,KAAK4E,UACnDF,EACAhH,KAAKsC,KAAKyG,MACTF,EAAa3K,OAAS,IAAM2K,EAAe,GACnD,GAlLL,CAAAxM,IAAA,UAAA0H,MAyLI,WAAmB,IAAXzB,yDAAO,CAAA,EAEX,OADA0G,EAAc1G,EAAM,CAAE2E,GAAIjH,KAAKiH,GAAIE,GAAInH,KAAKmH,IAAMnH,KAAKsC,MAChD,IAAI2G,GAAQjJ,KAAKkJ,MAAO5G,EAClC,GA5LL,CAAAjG,IAAA,UAAA0H,MAoMI,SAAQvH,EAAMuD,GAAI,IAAAoJ,EAAAnJ,KACRoJ,EAAMpJ,KAAKqJ,QAAQ,CACrBC,OAAQ,OACR9M,KAAMA,IAEV4M,EAAIxJ,GAAG,UAAWG,GAClBqJ,EAAIxJ,GAAG,SAAS,SAAC2J,EAAWxG,GACxBoG,EAAKK,QAAQ,iBAAkBD,EAAWxG,KAEjD,GA7ML,CAAA1G,IAAA,SAAA0H,MAmNI,WAAS,IAAA0F,EAAAzJ,KACCoJ,EAAMpJ,KAAKqJ,UACjBD,EAAIxJ,GAAG,OAAQI,KAAK0J,OAAOjH,KAAKzC,OAChCoJ,EAAIxJ,GAAG,SAAS,SAAC2J,EAAWxG,GACxB0G,EAAKD,QAAQ,iBAAkBD,EAAWxG,MAE9C/C,KAAK2J,QAAUP,CAClB,KA1NL1C,CAAA,CAAA,CAA6BtD,GA4NhB6F,GAAb,SAAA5F,GAAAC,EAAA2F,EAAA5F,GAAA,IAAAE,EAAAC,EAAAyF,GAOI,SAAYC,EAAAA,EAAK5G,GAAM,IAAAsH,EAAA,OAAA3G,EAAAjD,KAAAiJ,GAEnB5G,EAAqBsB,EADrBiG,EAAArG,EAAA1G,KAAAmD,OAC4BsC,GAC5BsH,EAAKtH,KAAOA,EACZsH,EAAKN,OAAShH,EAAKgH,QAAU,MAC7BM,EAAKV,IAAMA,EACXU,EAAKC,OAAQ,IAAUvH,EAAKuH,MAC5BD,EAAKpN,UAAOsN,IAAcxH,EAAK9F,KAAO8F,EAAK9F,KAAO,KAClDoN,EAAK3N,SARc2N,CAStB,CAhBL,OAAA9F,EAAAmF,EAAA,CAAA,CAAA5M,IAAA,SAAA0H,MAsBI,WAAS,IAAAgG,EAAA/J,KACCsC,EAAOd,EAAKxB,KAAKsC,KAAM,QAAS,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,aACjHA,EAAK6D,UAAYnG,KAAKsC,KAAK2E,GAC3B3E,EAAK0H,UAAYhK,KAAKsC,KAAK6E,GAC3B,IAAM8C,EAAOjK,KAAKiK,IAAM,IAAIlE,EAAezD,GAC3C,IACI2H,EAAIC,KAAKlK,KAAKsJ,OAAQtJ,KAAKkJ,IAAKlJ,KAAK6J,OACrC,IACI,GAAI7J,KAAKsC,KAAK6H,aAEV,IAAK,IAAIlM,KADTgM,EAAIG,uBAAyBH,EAAIG,uBAAsB,GACzCpK,KAAKsC,KAAK6H,aAChBnK,KAAKsC,KAAK6H,aAAapI,eAAe9D,IACtCgM,EAAII,iBAAiBpM,EAAG+B,KAAKsC,KAAK6H,aAAalM,GAKlD,CAAb,MAAOmI,GAAM,CACb,GAAI,SAAWpG,KAAKsJ,OAChB,IACIW,EAAII,iBAAiB,eAAgB,2BAE5B,CAAb,MAAOjE,GAAM,CAEjB,IACI6D,EAAII,iBAAiB,SAAU,MApBnC,CAsBA,MAAOjE,GAtBP,CAwBI,oBAAqB6D,IACrBA,EAAIK,gBAAkBtK,KAAKsC,KAAKgI,iBAEhCtK,KAAKsC,KAAKiI,iBACVN,EAAIO,QAAUxK,KAAKsC,KAAKiI,gBAE5BN,EAAIQ,mBAAqB,WACjB,IAAMR,EAAI/F,aAEV,MAAQ+F,EAAIS,QAAU,OAAST,EAAIS,OACnCX,EAAKY,SAKLZ,EAAKvH,cAAa,WACduH,EAAKP,QAA8B,iBAAfS,EAAIS,OAAsBT,EAAIS,OAAS,EAD/D,GAEG,KAGXT,EAAIW,KAAK5K,KAAKxD,KAUjB,CARD,MAAO4J,GAOH,YAHApG,KAAKwC,cAAa,WACduH,EAAKP,QAAQpD,EADjB,GAEG,EAEN,CACuB,oBAAbyE,WACP7K,KAAK8K,MAAQ7B,EAAQ8B,gBACrB9B,EAAQ+B,SAAShL,KAAK8K,OAAS9K,KAEtC,GAtFL,CAAA3D,IAAA,UAAA0H,MA4FI,SAAQiC,GACJhG,KAAKiB,aAAa,QAAS+E,EAAKhG,KAAKiK,KACrCjK,KAAKiL,SAAQ,EAChB,GA/FL,CAAA5O,IAAA,UAAA0H,MAqGI,SAAQmH,GACJ,QAAI,IAAuBlL,KAAKiK,KAAO,OAASjK,KAAKiK,IAArD,CAIA,GADAjK,KAAKiK,IAAIQ,mBAAqBlE,EAC1B2E,EACA,IACIlL,KAAKiK,IAAIkB,OAEA,CAAb,MAAO/E,GAAM,CAEO,oBAAbyE,iBACA5B,EAAQ+B,SAAShL,KAAK8K,OAEjC9K,KAAKiK,IAAM,IAXV,CAYJ,GApHL,CAAA5N,IAAA,SAAA0H,MA0HI,WACI,IAAMvH,EAAOwD,KAAKiK,IAAImB,aACT,OAAT5O,IACAwD,KAAKiB,aAAa,OAAQzE,GAC1BwD,KAAKiB,aAAa,WAClBjB,KAAKiL,UAEZ,GAjIL,CAAA5O,IAAA,QAAA0H,MAuII,WACI/D,KAAKiL,SACR,KAzILhC,CAAA,CAAA,CAA6BvJ,GAkJ7B,GAPAuJ,GAAQ8B,cAAgB,EACxB9B,GAAQ+B,SAAW,CAAA,EAMK,oBAAbH,SAEP,GAA2B,mBAAhBQ,YAEPA,YAAY,WAAYC,SAEvB,GAAgC,mBAArBzL,iBAAiC,CAE7CA,iBADyB,eAAgBoC,EAAa,WAAa,SAChCqJ,IAAe,EACrD,CAEL,SAASA,KACL,IAAK,IAAIrN,KAAKgL,GAAQ+B,SACd/B,GAAQ+B,SAASjJ,eAAe9D,IAChCgL,GAAQ+B,SAAS/M,GAAGkN,OAG/B,CC7YM,IAAMI,GACqC,mBAAZC,SAAqD,mBAApBA,QAAQC,QAEhE,SAAC/K,GAAD,OAAQ8K,QAAQC,UAAUC,KAAKhL,IAG/B,SAACA,EAAI8B,GAAL,OAAsBA,EAAa9B,EAAI,IAGzCiL,GAAY1J,EAAW0J,WAAa1J,EAAW2J,aCHtDC,GAAqC,oBAAdC,WACI,iBAAtBA,UAAUC,SACmB,gBAApCD,UAAUC,QAAQC,cACTC,GAAb,SAAAtF,GAAArD,EAAA2I,EAAAtF,GAAA,IAAAzD,EAAAM,EAAAyI,GAOI,SAAAA,EAAY3J,GAAM,IAAAU,EAAA,OAAAC,EAAAjD,KAAAiM,IACdjJ,EAAAE,EAAArG,KAAAmD,KAAMsC,IACDrF,gBAAkBqF,EAAK+E,YAFdrE,CAGjB,CAVL,OAAAc,EAAAmI,EAAA,CAAA,CAAA5P,IAAA,OAAAiL,IAWI,WACI,MAAO,WACV,GAbL,CAAAjL,IAAA,SAAA0H,MAcI,WACI,GAAK/D,KAAKkM,QAAV,CAIA,IAAMhD,EAAMlJ,KAAKkJ,MACXiD,EAAYnM,KAAKsC,KAAK6J,UAEtB7J,EAAOuJ,GACP,CAAA,EACArK,EAAKxB,KAAKsC,KAAM,QAAS,oBAAqB,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,eAAgB,kBAAmB,SAAU,aAAc,SAAU,uBAChMtC,KAAKsC,KAAK6H,eACV7H,EAAK8J,QAAUpM,KAAKsC,KAAK6H,cAE7B,IACInK,KAAKqM,GACyBR,GAIpB,IAAIF,GAAUzC,EAAKiD,EAAW7J,GAH9B6J,EACI,IAAIR,GAAUzC,EAAKiD,GACnB,IAAIR,GAAUzC,EAK/B,CAFD,MAAOlD,GACH,OAAOhG,KAAKiB,aAAa,QAAS+E,EACrC,CACDhG,KAAKqM,GAAG/N,WAAa0B,KAAK6D,OAAOvF,YDrCR,cCsCzB0B,KAAKsM,mBAtBJ,CAuBJ,GAzCL,CAAAjQ,IAAA,oBAAA0H,MA+CI,WAAoB,IAAAN,EAAAzD,KAChBA,KAAKqM,GAAGE,OAAS,WACT9I,EAAKnB,KAAKkK,WACV/I,EAAK4I,GAAGI,QAAQC,QAEpBjJ,EAAKuE,UAEThI,KAAKqM,GAAGM,QAAU,SAACC,GAAD,OAAgBnJ,EAAKY,QAAQ,CAC3CvB,YAAa,8BACbC,QAAS6J,KAEb5M,KAAKqM,GAAGQ,UAAY,SAACC,GAAD,OAAQrJ,EAAKiG,OAAOoD,EAAGtQ,OAC3CwD,KAAKqM,GAAGU,QAAU,SAAC3G,GAAD,OAAO3C,EAAK+F,QAAQ,kBAAmBpD,GAC5D,GA5DL,CAAA/J,IAAA,QAAA0H,MA6DI,SAAMO,GAAS,IAAAqD,EAAA3H,KACXA,KAAK0D,UAAW,EAGhB,IAJW,IAAAsJ,EAAA,SAIF/O,GACL,IAAMuG,EAASF,EAAQrG,GACjBgP,EAAahP,IAAMqG,EAAQpG,OAAS,EAC1ClB,EAAawH,EAAQmD,EAAK1K,gBAAgB,SAACT,GAmBvC,IAGQmL,EAAK0E,GAAGzB,KAAKpO,EAOpB,CADD,MAAO4J,GACN,CACG6G,GAGA1B,IAAS,WACL5D,EAAKjE,UAAW,EAChBiE,EAAK1G,aAAa,QACrB,GAAE0G,EAAKnF,aAEf,GA7CM,EAIFvE,EAAI,EAAGA,EAAIqG,EAAQpG,OAAQD,IAAK+O,EAAhC/O,EA2CZ,GA5GL,CAAA5B,IAAA,UAAA0H,MA6GI,gBAC2B,IAAZ/D,KAAKqM,KACZrM,KAAKqM,GAAGnE,QACRlI,KAAKqM,GAAK,KAEjB,GAlHL,CAAAhQ,IAAA,MAAA0H,MAwHI,WACI,IAAIH,EAAQ5D,KAAK4D,OAAS,GACpB2E,EAASvI,KAAKsC,KAAK8E,OAAS,MAAQ,KACtCJ,EAAO,GAEPhH,KAAKsC,KAAK0E,OACR,QAAUuB,GAAqC,MAA3BK,OAAO5I,KAAKsC,KAAK0E,OAClC,OAASuB,GAAqC,KAA3BK,OAAO5I,KAAKsC,KAAK0E,SACzCA,EAAO,IAAMhH,KAAKsC,KAAK0E,MAGvBhH,KAAKsC,KAAKkG,oBACV5E,EAAM5D,KAAKsC,KAAKmG,gBAAkBrD,KAGjCpF,KAAK/C,iBACN2G,EAAM+E,IAAM,GAEhB,IAAME,EAAe9D,EAAOnB,GAE5B,OAAQ2E,EACJ,QAF8C,IAArCvI,KAAKsC,KAAK4E,SAAS4B,QAAQ,KAG5B,IAAM9I,KAAKsC,KAAK4E,SAAW,IAAMlH,KAAKsC,KAAK4E,UACnDF,EACAhH,KAAKsC,KAAKyG,MACTF,EAAa3K,OAAS,IAAM2K,EAAe,GACnD,GAlJL,CAAAxM,IAAA,QAAA0H,MAyJI,WACI,QAAS4H,EACZ,KA3JLM,CAAA,CAAA,CAAwB7I,GCRX8J,GAAa,CACtBC,UAAWlB,GACXrF,QAASF,ICeP0G,GAAK,sPACLC,GAAQ,CACV,SAAU,WAAY,YAAa,WAAY,OAAQ,WAAY,OAAQ,OAAQ,WAAY,OAAQ,YAAa,OAAQ,QAAS,UAElI,SAASC,GAAM/H,GAClB,IAAMgI,EAAMhI,EAAKiI,EAAIjI,EAAIuD,QAAQ,KAAM1C,EAAIb,EAAIuD,QAAQ,MAC7C,GAAN0E,IAAiB,GAANpH,IACXb,EAAMA,EAAI7G,UAAU,EAAG8O,GAAKjI,EAAI7G,UAAU8O,EAAGpH,GAAGqH,QAAQ,KAAM,KAAOlI,EAAI7G,UAAU0H,EAAGb,EAAIrH,SAG9F,IADA,IAwBmB0F,EACbpH,EAzBFkR,EAAIN,GAAGO,KAAKpI,GAAO,IAAK2D,EAAM,CAAlC,EAAsCjL,EAAI,GACnCA,KACHiL,EAAImE,GAAMpP,IAAMyP,EAAEzP,IAAM,GAU5B,OARU,GAANuP,IAAiB,GAANpH,IACX8C,EAAI0E,OAASL,EACbrE,EAAI2E,KAAO3E,EAAI2E,KAAKnP,UAAU,EAAGwK,EAAI2E,KAAK3P,OAAS,GAAGuP,QAAQ,KAAM,KACpEvE,EAAI4E,UAAY5E,EAAI4E,UAAUL,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAAIA,QAAQ,KAAM,KAC9EvE,EAAI6E,SAAU,GAElB7E,EAAI8E,UAIR,SAAmB7Q,EAAK4L,GACpB,IAAMkF,EAAO,WAAYC,EAAQnF,EAAK0E,QAAQQ,EAAM,KAAKrQ,MAAM,KACvC,KAApBmL,EAAK/H,MAAM,EAAG,IAA6B,IAAhB+H,EAAK7K,QAChCgQ,EAAMtN,OAAO,EAAG,GAEE,KAAlBmI,EAAK/H,OAAO,IACZkN,EAAMtN,OAAOsN,EAAMhQ,OAAS,EAAG,GAEnC,OAAOgQ,CACV,CAbmBF,CAAU9E,EAAKA,EAAG,MAClCA,EAAIiF,UAaevK,EAbUsF,EAAG,MAc1B1M,EAAO,CAAA,EACboH,EAAM6J,QAAQ,6BAA6B,SAAUW,EAAIC,EAAIC,GACrDD,IACA7R,EAAK6R,GAAMC,MAGZ9R,GAnBA0M,CACV,CCnCD,IAAaqF,GAAb,SAAAlL,GAAAC,EAAAiL,EAAAlL,GAAA,IAAAH,EAAAM,EAAA+K,GAOI,SAAAA,EAAYrF,GAAgB,IAAAlG,EAAXV,yDAAO,CAAA,EAAI,OAAAW,EAAAjD,KAAAuO,IACxBvL,EAAAE,EAAArG,KAAAmD,OACKwO,YAAc,GACftF,GAAO,WAAoBA,EAAAA,KAC3B5G,EAAO4G,EACPA,EAAM,MAENA,GACAA,EAAMoE,GAAMpE,GACZ5G,EAAK4E,SAAWgC,EAAI2E,KACpBvL,EAAK8E,OAA0B,UAAjB8B,EAAInC,UAAyC,QAAjBmC,EAAInC,SAC9CzE,EAAK0E,KAAOkC,EAAIlC,KACZkC,EAAItF,QACJtB,EAAKsB,MAAQsF,EAAItF,QAEhBtB,EAAKuL,OACVvL,EAAK4E,SAAWoG,GAAMhL,EAAKuL,MAAMA,MAErCxL,EAAqBsB,EAAAX,GAAOV,GAC5BU,EAAKoE,OACD,MAAQ9E,EAAK8E,OACP9E,EAAK8E,OACe,oBAAbP,UAA4B,WAAaA,SAASE,SAC/DzE,EAAK4E,WAAa5E,EAAK0E,OAEvB1E,EAAK0E,KAAOhE,EAAKoE,OAAS,MAAQ,MAEtCpE,EAAKkE,SACD5E,EAAK4E,WACoB,oBAAbL,SAA2BA,SAASK,SAAW,aAC/DlE,EAAKgE,KACD1E,EAAK0E,OACoB,oBAAbH,UAA4BA,SAASG,KACvCH,SAASG,KACThE,EAAKoE,OACD,MACA,MAClBpE,EAAKkK,WAAa5K,EAAK4K,YAAc,CAAC,UAAW,aACjDlK,EAAKwL,YAAc,GACnBxL,EAAKyL,cAAgB,EACrBzL,EAAKV,KAAO0G,EAAc,CACtBD,KAAM,aACN2F,OAAO,EACPpE,iBAAiB,EACjBqE,SAAS,EACTlG,eAAgB,IAChBmG,iBAAiB,EACjBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAmB,CACfC,UAAW,MAEfC,iBAAkB,CAZI,EAatBC,qBAAqB,GACtB5M,GACHU,EAAKV,KAAKyG,KACN/F,EAAKV,KAAKyG,KAAK0E,QAAQ,MAAO,KACzBzK,EAAKV,KAAKuM,iBAAmB,IAAM,IACb,iBAApB7L,EAAKV,KAAKsB,QACjBZ,EAAKV,KAAKsB,MAAQtE,EAAO0D,EAAKV,KAAKsB,QAGvCZ,EAAKmM,GAAK,KACVnM,EAAKoM,SAAW,KAChBpM,EAAKqM,aAAe,KACpBrM,EAAKsM,YAAc,KAEnBtM,EAAKuM,iBAAmB,KACQ,mBAArB1P,mBACHmD,EAAKV,KAAK4M,sBAIVlM,EAAKwM,0BAA4B,WACzBxM,EAAKyM,YAELzM,EAAKyM,UAAUjP,qBACfwC,EAAKyM,UAAUvH,UAGvBrI,iBAAiB,eAAgBmD,EAAKwM,2BAA2B,IAE/C,cAAlBxM,EAAKkE,WACLlE,EAAK0M,qBAAuB,WACxB1M,EAAKqB,QAAQ,kBAAmB,CAC5BvB,YAAa,6BAGrBjD,iBAAiB,UAAWmD,EAAK0M,sBAAsB,KAG/D1M,EAAKkH,OA3FmBlH,CA4F3B,CAnGL,OAAAc,EAAAyK,EAAA,CAAA,CAAAlS,IAAA,kBAAA0H,MA2GI,SAAgB4L,GACZ,IAAM/L,EAAQoF,EAAc,CAAA,EAAIhJ,KAAKsC,KAAKsB,OAE1CA,EAAMgM,IdtFU,EcwFhBhM,EAAM6L,UAAYE,EAEd3P,KAAKmP,KACLvL,EAAM8E,IAAM1I,KAAKmP,IACrB,IAAM7M,EAAO0G,EAAc,CAAA,EAAIhJ,KAAKsC,KAAK2M,iBAAiBU,GAAO3P,KAAKsC,KAAM,CACxEsB,MAAAA,EACAC,OAAQ7D,KACRkH,SAAUlH,KAAKkH,SACfE,OAAQpH,KAAKoH,OACbJ,KAAMhH,KAAKgH,OAEf,OAAO,IAAIkG,GAAWyC,GAAMrN,EAC/B,GA5HL,CAAAjG,IAAA,OAAA0H,MAkII,WAAO,IACC0L,EADDhM,EAAAzD,KAEH,GAAIA,KAAKsC,KAAKsM,iBACVL,EAAOsB,wBACmC,IAA1C7P,KAAKkN,WAAWpE,QAAQ,aACxB2G,EAAY,gBAEX,IAAI,IAAMzP,KAAKkN,WAAWhP,OAK3B,YAHA8B,KAAKwC,cAAa,WACdiB,EAAKxC,aAAa,QAAS,0BAD/B,GAEG,GAIHwO,EAAYzP,KAAKkN,WAAW,EAC/B,CACDlN,KAAKkE,WAAa,UAElB,IACIuL,EAAYzP,KAAK8P,gBAAgBL,EAMpC,CAJD,MAAOrJ,GAGH,OAFApG,KAAKkN,WAAW6C,aAChB/P,KAAKkK,MAER,CACDuF,EAAUvF,OACVlK,KAAKgQ,aAAaP,EACrB,GA/JL,CAAApT,IAAA,eAAA0H,MAqKI,SAAa0L,GAAW,IAAA9H,EAAA3H,KAChBA,KAAKyP,WACLzP,KAAKyP,UAAUjP,qBAGnBR,KAAKyP,UAAYA,EAEjBA,EACK7P,GAAG,QAASI,KAAKiQ,QAAQxN,KAAKzC,OAC9BJ,GAAG,SAAUI,KAAKyE,SAAShC,KAAKzC,OAChCJ,GAAG,QAASI,KAAKwJ,QAAQ/G,KAAKzC,OAC9BJ,GAAG,SAAS,SAACiD,GAAD,OAAY8E,EAAKtD,QAAQ,kBAAmBxB,KAChE,GAjLL,CAAAxG,IAAA,QAAA0H,MAwLI,SAAM4L,GAAM,IAAA1H,EAAAjI,KACJyP,EAAYzP,KAAK8P,gBAAgBH,GACjCO,GAAS,EACb3B,EAAOsB,uBAAwB,EAC/B,IAAMM,EAAkB,WAChBD,IAEJT,EAAU7E,KAAK,CAAC,CAAErO,KAAM,OAAQC,KAAM,WACtCiT,EAAUtP,KAAK,UAAU,SAACiQ,GACtB,IAAIF,EAEJ,GAAI,SAAWE,EAAI7T,MAAQ,UAAY6T,EAAI5T,KAAM,CAG7C,GAFAyL,EAAKoI,WAAY,EACjBpI,EAAKhH,aAAa,YAAawO,IAC1BA,EACD,OACJlB,EAAOsB,sBAAwB,cAAgBJ,EAAUE,KACzD1H,EAAKwH,UAAUjI,OAAM,WACb0I,GAEA,WAAajI,EAAK/D,aAEtB+G,IACAhD,EAAK+H,aAAaP,GAClBA,EAAU7E,KAAK,CAAC,CAAErO,KAAM,aACxB0L,EAAKhH,aAAa,UAAWwO,GAC7BA,EAAY,KACZxH,EAAKoI,WAAY,EACjBpI,EAAKqI,WAEZ,KACI,CACD,IAAMtK,EAAM,IAAI7C,MAAM,eAEtB6C,EAAIyJ,UAAYA,EAAUE,KAC1B1H,EAAKhH,aAAa,eAAgB+E,EACrC,OAGT,SAASuK,IACDL,IAGJA,GAAS,EACTjF,IACAwE,EAAUvH,QACVuH,EAAY,KA9CR,CAiDR,IAAM1C,EAAU,SAAC/G,GACb,IAAMwK,EAAQ,IAAIrN,MAAM,gBAAkB6C,GAE1CwK,EAAMf,UAAYA,EAAUE,KAC5BY,IACAtI,EAAKhH,aAAa,eAAgBuP,IAEtC,SAASC,IACL1D,EAAQ,mBAzDJ,CA4DR,SAASJ,IACLI,EAAQ,gBA7DJ,CAgER,SAAS2D,EAAUC,GACXlB,GAAakB,EAAGhB,OAASF,EAAUE,MACnCY,GAlEA,CAsER,IAAMtF,EAAU,WACZwE,EAAUlP,eAAe,OAAQ4P,GACjCV,EAAUlP,eAAe,QAASwM,GAClC0C,EAAUlP,eAAe,QAASkQ,GAClCxI,EAAK7H,IAAI,QAASuM,GAClB1E,EAAK7H,IAAI,YAAasQ,IAE1BjB,EAAUtP,KAAK,OAAQgQ,GACvBV,EAAUtP,KAAK,QAAS4M,GACxB0C,EAAUtP,KAAK,QAASsQ,GACxBzQ,KAAKG,KAAK,QAASwM,GACnB3M,KAAKG,KAAK,YAAauQ,GACvBjB,EAAUvF,MACb,GA3QL,CAAA7N,IAAA,SAAA0H,MAiRI,WAOI,GANA/D,KAAKkE,WAAa,OAClBqK,EAAOsB,sBAAwB,cAAgB7P,KAAKyP,UAAUE,KAC9D3P,KAAKiB,aAAa,QAClBjB,KAAKsQ,QAGD,SAAWtQ,KAAKkE,YAAclE,KAAKsC,KAAKqM,QAGxC,IAFA,IAAI1Q,EAAI,EACF2H,EAAI5F,KAAKoP,SAASlR,OACjBD,EAAI2H,EAAG3H,IACV+B,KAAK4Q,MAAM5Q,KAAKoP,SAASnR,GAGpC,GA/RL,CAAA5B,IAAA,WAAA0H,MAqSI,SAASS,GACL,GAAI,YAAcxE,KAAKkE,YACnB,SAAWlE,KAAKkE,YAChB,YAAclE,KAAKkE,WAInB,OAHAlE,KAAKiB,aAAa,SAAUuD,GAE5BxE,KAAKiB,aAAa,aACVuD,EAAOjI,MACX,IAAK,OACDyD,KAAK6Q,YAAYC,KAAKxD,MAAM9I,EAAOhI,OACnC,MACJ,IAAK,OACDwD,KAAK+Q,mBACL/Q,KAAKgR,WAAW,QAChBhR,KAAKiB,aAAa,QAClBjB,KAAKiB,aAAa,QAClB,MACJ,IAAK,QACD,IAAM+E,EAAM,IAAI7C,MAAM,gBAEtB6C,EAAIiL,KAAOzM,EAAOhI,KAClBwD,KAAKwJ,QAAQxD,GACb,MACJ,IAAK,UACDhG,KAAKiB,aAAa,OAAQuD,EAAOhI,MACjCwD,KAAKiB,aAAa,UAAWuD,EAAOhI,MAMnD,GApUL,CAAAH,IAAA,cAAA0H,MA2UI,SAAYvH,GACRwD,KAAKiB,aAAa,YAAazE,GAC/BwD,KAAKmP,GAAK3S,EAAKkM,IACf1I,KAAKyP,UAAU7L,MAAM8E,IAAMlM,EAAKkM,IAChC1I,KAAKoP,SAAWpP,KAAKkR,eAAe1U,EAAK4S,UACzCpP,KAAKqP,aAAe7S,EAAK6S,aACzBrP,KAAKsP,YAAc9S,EAAK8S,YACxBtP,KAAKmR,WAAa3U,EAAK2U,WACvBnR,KAAKgI,SAED,WAAahI,KAAKkE,YAEtBlE,KAAK+Q,kBACR,GAxVL,CAAA1U,IAAA,mBAAA0H,MA8VI,WAAmB,IAAAoE,EAAAnI,KACfA,KAAK0C,eAAe1C,KAAKuP,kBACzBvP,KAAKuP,iBAAmBvP,KAAKwC,cAAa,WACtC2F,EAAK9D,QAAQ,eADO,GAErBrE,KAAKqP,aAAerP,KAAKsP,aACxBtP,KAAKsC,KAAKkK,WACVxM,KAAKuP,iBAAiB7C,OAE7B,GAtWL,CAAArQ,IAAA,UAAA0H,MA4WI,WACI/D,KAAKwO,YAAY5N,OAAO,EAAGZ,KAAKyO,eAIhCzO,KAAKyO,cAAgB,EACjB,IAAMzO,KAAKwO,YAAYtQ,OACvB8B,KAAKiB,aAAa,SAGlBjB,KAAKsQ,OAEZ,GAxXL,CAAAjU,IAAA,QAAA0H,MA8XI,WACI,GAAI,WAAa/D,KAAKkE,YAClBlE,KAAKyP,UAAU/L,WACd1D,KAAKqQ,WACNrQ,KAAKwO,YAAYtQ,OAAQ,CACzB,IAAMoG,EAAUtE,KAAKoR,qBACrBpR,KAAKyP,UAAU7E,KAAKtG,GAGpBtE,KAAKyO,cAAgBnK,EAAQpG,OAC7B8B,KAAKiB,aAAa,QACrB,CACJ,GA1YL,CAAA5E,IAAA,qBAAA0H,MAiZI,WAII,KAH+B/D,KAAKmR,YACR,YAAxBnR,KAAKyP,UAAUE,MACf3P,KAAKwO,YAAYtQ,OAAS,GAE1B,OAAO8B,KAAKwO,YAGhB,IADA,IXrYmBrR,EWqYfkU,EAAc,EACTpT,EAAI,EAAGA,EAAI+B,KAAKwO,YAAYtQ,OAAQD,IAAK,CAC9C,IAAMzB,EAAOwD,KAAKwO,YAAYvQ,GAAGzB,KAIjC,GAHIA,IACA6U,GXxYO,iBADIlU,EWyYeX,GXlY1C,SAAoB+I,GAEhB,IADA,IAAI+L,EAAI,EAAGpT,EAAS,EACXD,EAAI,EAAG2H,EAAIL,EAAIrH,OAAQD,EAAI2H,EAAG3H,KACnCqT,EAAI/L,EAAIpH,WAAWF,IACX,IACJC,GAAU,EAELoT,EAAI,KACTpT,GAAU,EAELoT,EAAI,OAAUA,GAAK,MACxBpT,GAAU,GAGVD,IACAC,GAAU,GAGlB,OAAOA,CACV,CAxBcqT,CAAWpU,GAGf+H,KAAKsM,KAPQ,MAOFrU,EAAIsU,YAActU,EAAIuU,QWsY5BzT,EAAI,GAAKoT,EAAcrR,KAAKmR,WAC5B,OAAOnR,KAAKwO,YAAYxN,MAAM,EAAG/C,GAErCoT,GAAe,CAClB,CACD,OAAOrR,KAAKwO,WACf,GApaL,CAAAnS,IAAA,QAAA0H,MA6aI,SAAMqM,EAAKuB,EAAS5R,GAEhB,OADAC,KAAKgR,WAAW,UAAWZ,EAAKuB,EAAS5R,GAClCC,IACV,GAhbL,CAAA3D,IAAA,OAAA0H,MAibI,SAAKqM,EAAKuB,EAAS5R,GAEf,OADAC,KAAKgR,WAAW,UAAWZ,EAAKuB,EAAS5R,GAClCC,IACV,GApbL,CAAA3D,IAAA,aAAA0H,MA8bI,SAAWxH,EAAMC,EAAMmV,EAAS5R,GAS5B,GARI,mBAAsBvD,IACtBuD,EAAKvD,EACLA,OAAOsN,GAEP,mBAAsB6H,IACtB5R,EAAK4R,EACLA,EAAU,MAEV,YAAc3R,KAAKkE,YAAc,WAAalE,KAAKkE,WAAvD,EAGAyN,EAAUA,GAAW,IACbC,UAAW,IAAUD,EAAQC,SACrC,IAAMpN,EAAS,CACXjI,KAAMA,EACNC,KAAMA,EACNmV,QAASA,GAEb3R,KAAKiB,aAAa,eAAgBuD,GAClCxE,KAAKwO,YAAYtO,KAAKsE,GAClBzE,GACAC,KAAKG,KAAK,QAASJ,GACvBC,KAAKsQ,OAZJ,CAaJ,GAtdL,CAAAjU,IAAA,QAAA0H,MA0dI,WAAQ,IAAAoF,EAAAnJ,KACEkI,EAAQ,WACViB,EAAK9E,QAAQ,gBACb8E,EAAKsG,UAAUvH,SAEb2J,EAAkB,SAAlBA,IACF1I,EAAK/I,IAAI,UAAWyR,GACpB1I,EAAK/I,IAAI,eAAgByR,GACzB3J,KAEE4J,EAAiB,WAEnB3I,EAAKhJ,KAAK,UAAW0R,GACrB1I,EAAKhJ,KAAK,eAAgB0R,IAqB9B,MAnBI,YAAc7R,KAAKkE,YAAc,SAAWlE,KAAKkE,aACjDlE,KAAKkE,WAAa,UACdlE,KAAKwO,YAAYtQ,OACjB8B,KAAKG,KAAK,SAAS,WACXgJ,EAAKkH,UACLyB,IAGA5J,OAIHlI,KAAKqQ,UACVyB,IAGA5J,KAGDlI,IACV,GA7fL,CAAA3D,IAAA,UAAA0H,MAmgBI,SAAQiC,GACJuI,EAAOsB,uBAAwB,EAC/B7P,KAAKiB,aAAa,QAAS+E,GAC3BhG,KAAKqE,QAAQ,kBAAmB2B,EACnC,GAvgBL,CAAA3J,IAAA,UAAA0H,MA6gBI,SAAQlB,EAAQC,GACR,YAAc9C,KAAKkE,YACnB,SAAWlE,KAAKkE,YAChB,YAAclE,KAAKkE,aAEnBlE,KAAK0C,eAAe1C,KAAKuP,kBAEzBvP,KAAKyP,UAAUjP,mBAAmB,SAElCR,KAAKyP,UAAUvH,QAEflI,KAAKyP,UAAUjP,qBACoB,mBAAxBC,sBACPA,oBAAoB,eAAgBT,KAAKwP,2BAA2B,GACpE/O,oBAAoB,UAAWT,KAAK0P,sBAAsB,IAG9D1P,KAAKkE,WAAa,SAElBlE,KAAKmP,GAAK,KAEVnP,KAAKiB,aAAa,QAAS4B,EAAQC,GAGnC9C,KAAKwO,YAAc,GACnBxO,KAAKyO,cAAgB,EAE5B,GAxiBL,CAAApS,IAAA,iBAAA0H,MA+iBI,SAAeqL,GAIX,IAHA,IAAM2C,EAAmB,GACrB9T,EAAI,EACF+T,EAAI5C,EAASlR,OACZD,EAAI+T,EAAG/T,KACL+B,KAAKkN,WAAWpE,QAAQsG,EAASnR,KAClC8T,EAAiB7R,KAAKkP,EAASnR,IAEvC,OAAO8T,CACV,KAxjBLxD,CAAA,CAAA,CAA4B7O,GA0jBtBuS,GAAClL,SdliBiB,Ee5BAwH,GAAOxH,SCF/B,IAAMjK,GAA+C,mBAAhBC,YAM/BH,GAAWZ,OAAOW,UAAUC,SAC5BH,GAAiC,mBAATC,MACT,oBAATA,MACoB,6BAAxBE,GAASC,KAAKH,MAChBwV,GAAiC,mBAATC,MACT,oBAATA,MACoB,6BAAxBvV,GAASC,KAAKsV,MAMf,SAASC,GAASjV,GACrB,OAASL,KAA0BK,aAAeJ,aAlBvC,SAACI,GACZ,MAAqC,mBAAvBJ,YAAYM,OACpBN,YAAYM,OAAOF,GACnBA,EAAIG,kBAAkBP,WAC/B,CAcoEM,CAAOF,KACnEV,IAAkBU,aAAeT,MACjCwV,IAAkB/U,aAAegV,IACzC,CACM,SAASE,GAAUlV,EAAKmV,GAC3B,IAAKnV,GAAsB,WAAfoV,EAAOpV,GACf,OAAO,EAEX,GAAI4D,MAAMyR,QAAQrV,GAAM,CACpB,IAAK,IAAIc,EAAI,EAAG2H,EAAIzI,EAAIe,OAAQD,EAAI2H,EAAG3H,IACnC,GAAIoU,GAAUlV,EAAIc,IACd,OAAO,EAGf,OAAO,CACV,CACD,GAAImU,GAASjV,GACT,OAAO,EAEX,GAAIA,EAAImV,QACkB,mBAAfnV,EAAImV,QACU,IAArBhS,UAAUpC,OACV,OAAOmU,GAAUlV,EAAImV,UAAU,GAEnC,IAAK,IAAMjW,KAAOc,EACd,GAAInB,OAAOW,UAAUoF,eAAelF,KAAKM,EAAKd,IAAQgW,GAAUlV,EAAId,IAChE,OAAO,EAGf,OAAO,CACV,CCzCM,SAASoW,GAAkBjO,GAC9B,IAAMkO,EAAU,GACVC,EAAanO,EAAOhI,KACpBoW,EAAOpO,EAGb,OAFAoO,EAAKpW,KAAOqW,GAAmBF,EAAYD,GAC3CE,EAAKE,YAAcJ,EAAQxU,OACpB,CAAEsG,OAAQoO,EAAMF,QAASA,EACnC,CACD,SAASG,GAAmBrW,EAAMkW,GAC9B,IAAKlW,EACD,OAAOA,EACX,GAAI4V,GAAS5V,GAAO,CAChB,IAAMuW,EAAc,CAAEC,cAAc,EAAMhO,IAAK0N,EAAQxU,QAEvD,OADAwU,EAAQxS,KAAK1D,GACNuW,CAHX,CAKK,GAAIhS,MAAMyR,QAAQhW,GAAO,CAE1B,IADA,IAAMyW,EAAU,IAAIlS,MAAMvE,EAAK0B,QACtBD,EAAI,EAAGA,EAAIzB,EAAK0B,OAAQD,IAC7BgV,EAAQhV,GAAK4U,GAAmBrW,EAAKyB,GAAIyU,GAE7C,OAAOO,CACV,CACI,GAAoB,WAAhBV,EAAO/V,MAAuBA,aAAgB8I,MAAO,CAC1D,IAAM2N,EAAU,CAAA,EAChB,IAAK,IAAM5W,KAAOG,EACVR,OAAOW,UAAUoF,eAAelF,KAAKL,EAAMH,KAC3C4W,EAAQ5W,GAAOwW,GAAmBrW,EAAKH,GAAMqW,IAGrD,OAAOO,CACV,CACD,OAAOzW,CACV,CASM,SAAS0W,GAAkB1O,EAAQkO,GAGtC,OAFAlO,EAAOhI,KAAO2W,GAAmB3O,EAAOhI,KAAMkW,UACvClO,EAAOsO,YACPtO,CACV,CACD,SAAS2O,GAAmB3W,EAAMkW,GAC9B,IAAKlW,EACD,OAAOA,EACX,GAAIA,IAA8B,IAAtBA,EAAKwW,aAAuB,CAIpC,GAHyC,iBAAbxW,EAAKwI,KAC7BxI,EAAKwI,KAAO,GACZxI,EAAKwI,IAAM0N,EAAQxU,OAEnB,OAAOwU,EAAQlW,EAAKwI,KAGpB,MAAM,IAAI7B,MAAM,sBARxB,CAWK,GAAIpC,MAAMyR,QAAQhW,GACnB,IAAK,IAAIyB,EAAI,EAAGA,EAAIzB,EAAK0B,OAAQD,IAC7BzB,EAAKyB,GAAKkV,GAAmB3W,EAAKyB,GAAIyU,QAGzC,GAAoB,WAAhBH,EAAO/V,GACZ,IAAK,IAAMH,KAAOG,EACVR,OAAOW,UAAUoF,eAAelF,KAAKL,EAAMH,KAC3CG,EAAKH,GAAO8W,GAAmB3W,EAAKH,GAAMqW,IAItD,OAAOlW,CACV,CC1EM,IACI4W,IACX,SAAWA,GACPA,EAAWA,EAAU,QAAc,GAAK,UACxCA,EAAWA,EAAU,WAAiB,GAAK,aAC3CA,EAAWA,EAAU,MAAY,GAAK,QACtCA,EAAWA,EAAU,IAAU,GAAK,MACpCA,EAAWA,EAAU,cAAoB,GAAK,gBAC9CA,EAAWA,EAAU,aAAmB,GAAK,eAC7CA,EAAWA,EAAU,WAAiB,GAAK,YAP/C,CAAA,CAQGA,KAAeA,GAAa,CAAlB,IAIb,IAAaC,GAAb,WAMI,SAAAA,EAAYC,GAAUrQ,EAAAjD,KAAAqT,GAClBrT,KAAKsT,SAAWA,CACnB,CARL,OAAAxP,EAAAuP,EAAA,CAAA,CAAAhX,IAAA,SAAA0H,MAeI,SAAO5G,GACH,OAAIA,EAAIZ,OAAS6W,GAAWG,OAASpW,EAAIZ,OAAS6W,GAAWI,MACrDnB,GAAUlV,GAWX,CAAC6C,KAAKyT,eAAetW,IAVb6C,KAAK0T,eAAe,CACvBnX,KAAMY,EAAIZ,OAAS6W,GAAWG,MACxBH,GAAWO,aACXP,GAAWQ,WACjBC,IAAK1W,EAAI0W,IACTrX,KAAMW,EAAIX,KACV2S,GAAIhS,EAAIgS,IAKvB,GA7BL,CAAA9S,IAAA,iBAAA0H,MAiCI,SAAe5G,GAEX,IAAIoI,EAAM,GAAKpI,EAAIZ,KAmBnB,OAjBIY,EAAIZ,OAAS6W,GAAWO,cACxBxW,EAAIZ,OAAS6W,GAAWQ,aACxBrO,GAAOpI,EAAI2V,YAAc,KAIzB3V,EAAI0W,KAAO,MAAQ1W,EAAI0W,MACvBtO,GAAOpI,EAAI0W,IAAM,KAGjB,MAAQ1W,EAAIgS,KACZ5J,GAAOpI,EAAIgS,IAGX,MAAQhS,EAAIX,OACZ+I,GAAOuL,KAAKgD,UAAU3W,EAAIX,KAAMwD,KAAKsT,WAElC/N,CACV,GAvDL,CAAAlJ,IAAA,iBAAA0H,MA6DI,SAAe5G,GACX,IAAM4W,EAAiBtB,GAAkBtV,GACnCyV,EAAO5S,KAAKyT,eAAeM,EAAevP,QAC1CkO,EAAUqB,EAAerB,QAE/B,OADAA,EAAQsB,QAAQpB,GACTF,CACV,KAnELW,CAAA,CAAA,GA0EaY,GAAb,SAAA5Q,GAAAC,EAAA2Q,EAAA5Q,GAAA,IAAAH,EAAAM,EAAAyQ,GAMI,SAAAA,EAAYC,GAAS,IAAAlR,EAAA,OAAAC,EAAAjD,KAAAiU,IACjBjR,EAAAE,EAAArG,KAAAmD,OACKkU,QAAUA,EAFElR,CAGpB,CATL,OAAAc,EAAAmQ,EAAA,CAAA,CAAA5X,IAAA,MAAA0H,MAeI,SAAI5G,GACA,IAAIqH,EACJ,GAAmB,iBAARrH,EAAkB,CACzB,GAAI6C,KAAKmU,cACL,MAAM,IAAIhR,MAAM,mDAGpB,IAAMiR,GADN5P,EAASxE,KAAKqU,aAAalX,IACEZ,OAAS6W,GAAWO,aAC7CS,GAAiB5P,EAAOjI,OAAS6W,GAAWQ,YAC5CpP,EAAOjI,KAAO6X,EAAgBhB,GAAWG,MAAQH,GAAWI,IAE5DxT,KAAKmU,cAAgB,IAAIG,GAAoB9P,GAElB,IAAvBA,EAAOsO,aACP9O,EAAmBC,EAAAgQ,EAAAtX,WAAA,eAAAqD,MAAAnD,KAAAmD,KAAA,UAAWwE,IAKlCR,EAAmBC,EAAAgQ,EAAAtX,WAAA,eAAAqD,MAAAnD,KAAAmD,KAAA,UAAWwE,EAjBtC,KAoBK,KAAI4N,GAASjV,KAAQA,EAAIyB,OAe1B,MAAM,IAAIuE,MAAM,iBAAmBhG,GAbnC,IAAK6C,KAAKmU,cACN,MAAM,IAAIhR,MAAM,qDAGhBqB,EAASxE,KAAKmU,cAAcI,eAAepX,MAGvC6C,KAAKmU,cAAgB,KACrBnQ,EAAmBC,EAAAgQ,EAAAtX,WAAA,eAAAqD,MAAAnD,KAAAmD,KAAA,UAAWwE,GAMzC,CACJ,GAtDL,CAAAnI,IAAA,eAAA0H,MA6DI,SAAawB,GACT,IAAItH,EAAI,EAEFkB,EAAI,CACN5C,KAAMqM,OAAOrD,EAAI/G,OAAO,KAE5B,QAA2BsL,IAAvBsJ,GAAWjU,EAAE5C,MACb,MAAM,IAAI4G,MAAM,uBAAyBhE,EAAE5C,MAG/C,GAAI4C,EAAE5C,OAAS6W,GAAWO,cACtBxU,EAAE5C,OAAS6W,GAAWQ,WAAY,CAElC,IADA,IAAMY,EAAQvW,EAAI,EACS,MAApBsH,EAAI/G,SAASP,IAAcA,GAAKsH,EAAIrH,SAC3C,IAAMuW,EAAMlP,EAAI7G,UAAU8V,EAAOvW,GACjC,GAAIwW,GAAO7L,OAAO6L,IAA0B,MAAlBlP,EAAI/G,OAAOP,GACjC,MAAM,IAAIkF,MAAM,uBAEpBhE,EAAE2T,YAAclK,OAAO6L,EAlBb,CAqBd,GAAI,MAAQlP,EAAI/G,OAAOP,EAAI,GAAI,CAE3B,IADA,IAAMuW,EAAQvW,EAAI,IACTA,GAAG,CAER,GAAI,MADMsH,EAAI/G,OAAOP,GAEjB,MACJ,GAAIA,IAAMsH,EAAIrH,OACV,KACP,CACDiB,EAAE0U,IAAMtO,EAAI7G,UAAU8V,EAAOvW,EAChC,MAEGkB,EAAE0U,IAAM,IAGZ,IAAMa,EAAOnP,EAAI/G,OAAOP,EAAI,GAC5B,GAAI,KAAOyW,GAAQ9L,OAAO8L,IAASA,EAAM,CAErC,IADA,IAAMF,EAAQvW,EAAI,IACTA,GAAG,CACR,IAAMqT,EAAI/L,EAAI/G,OAAOP,GACrB,GAAI,MAAQqT,GAAK1I,OAAO0I,IAAMA,EAAG,GAC3BrT,EACF,KACH,CACD,GAAIA,IAAMsH,EAAIrH,OACV,KACP,CACDiB,EAAEgQ,GAAKvG,OAAOrD,EAAI7G,UAAU8V,EAAOvW,EAAI,GAhD7B,CAmDd,GAAIsH,EAAI/G,SAASP,GAAI,CACjB,IAAM0W,EAAU3U,KAAK4U,SAASrP,EAAIsP,OAAO5W,IACzC,IAAIgW,EAAQa,eAAe3V,EAAE5C,KAAMoY,GAI/B,MAAM,IAAIxR,MAAM,mBAHhBhE,EAAE3C,KAAOmY,CAKhB,CACD,OAAOxV,CACV,GA1HL,CAAA9C,IAAA,WAAA0H,MA2HI,SAASwB,GACL,IACI,OAAOuL,KAAKxD,MAAM/H,EAAKvF,KAAKkU,QAI/B,CAFD,MAAO9N,GACH,OAAO,CACV,CACJ,GAlIL,CAAA/J,IAAA,UAAA0H,MAsJI,WACQ/D,KAAKmU,gBACLnU,KAAKmU,cAAcY,yBACnB/U,KAAKmU,cAAgB,KAE5B,IA3JL,CAAA,CAAA9X,IAAA,iBAAA0H,MAmII,SAAsBxH,EAAMoY,GACxB,OAAQpY,GACJ,KAAK6W,GAAW4B,QACZ,MAA0B,WAAnBzC,EAAOoC,GAClB,KAAKvB,GAAW6B,WACZ,YAAmBnL,IAAZ6K,EACX,KAAKvB,GAAW8B,cACZ,MAA0B,iBAAZP,GAA2C,WAAnBpC,EAAOoC,GACjD,KAAKvB,GAAWG,MAChB,KAAKH,GAAWO,aACZ,OAAO5S,MAAMyR,QAAQmC,IAAYA,EAAQzW,OAAS,EACtD,KAAKkV,GAAWI,IAChB,KAAKJ,GAAWQ,WACZ,OAAO7S,MAAMyR,QAAQmC,GAEhC,KAlJLV,CAAA,CAAA,CAA6BvU,GAqKvB4U,cACF,SAAAA,EAAY9P,GAAQvB,EAAAjD,KAAAsU,GAChBtU,KAAKwE,OAASA,EACdxE,KAAK0S,QAAU,GACf1S,KAAKmV,UAAY3Q,CACpB,mCASDT,MAAA,SAAeqR,GAEX,GADApV,KAAK0S,QAAQxS,KAAKkV,GACdpV,KAAK0S,QAAQxU,SAAW8B,KAAKmV,UAAUrC,YAAa,CAEpD,IAAMtO,EAAS0O,GAAkBlT,KAAKmV,UAAWnV,KAAK0S,SAEtD,OADA1S,KAAK+U,yBACEvQ,CACV,CACD,OAAO,IACV,uCAID,WACIxE,KAAKmV,UAAY,KACjBnV,KAAK0S,QAAU,EAClB,oDA3RmB,sDCRjB,SAAS9S,GAAGzC,EAAK2P,EAAI/M,GAExB,OADA5C,EAAIyC,GAAGkN,EAAI/M,GACJ,WACH5C,EAAIiD,IAAI0M,EAAI/M,GAEnB,CCED,IAAMsV,GAAkBrZ,OAAOsZ,OAAO,CAClCC,QAAS,EACTC,cAAe,EACfC,WAAY,EACZC,cAAe,EAEfC,YAAa,EACbpV,eAAgB,IA0BPgO,GAAb,SAAAlL,GAAAC,EAAAiL,EAAAlL,GAAA,IAAAH,EAAAM,EAAA+K,GAII,SAAAA,EAAYqH,EAAI/B,EAAKvR,GAAM,IAAAU,EAAA,OAAAC,EAAAjD,KAAAuO,IACvBvL,EAAAE,EAAArG,KAAAmD,OAeK6V,WAAY,EAKjB7S,EAAK8S,WAAY,EAIjB9S,EAAK+S,cAAgB,GAIrB/S,EAAKgT,WAAa,GAOlBhT,EAAKiT,OAAS,GAKdjT,EAAKkT,UAAY,EACjBlT,EAAKmT,IAAM,EACXnT,EAAKoT,KAAO,GACZpT,EAAKqT,MAAQ,GACbrT,EAAK4S,GAAKA,EACV5S,EAAK6Q,IAAMA,EACPvR,GAAQA,EAAKgU,OACbtT,EAAKsT,KAAOhU,EAAKgU,MAErBtT,EAAKuT,MAAQvN,EAAc,CAAd,EAAkB1G,GAC3BU,EAAK4S,GAAGY,cACRxT,EAAKkH,OApDclH,CAqD1B,CAzDL,OAAAc,EAAAyK,EAAA,CAAA,CAAAlS,IAAA,eAAAiL,IAwEI,WACI,OAAQtH,KAAK6V,SAChB,GA1EL,CAAAxZ,IAAA,YAAA0H,MAgFI,WACI,IAAI/D,KAAKyW,KAAT,CAEA,IAAMb,EAAK5V,KAAK4V,GAChB5V,KAAKyW,KAAO,CACR7W,GAAGgW,EAAI,OAAQ5V,KAAKuM,OAAO9J,KAAKzC,OAChCJ,GAAGgW,EAAI,SAAU5V,KAAK0W,SAASjU,KAAKzC,OACpCJ,GAAGgW,EAAI,QAAS5V,KAAK+M,QAAQtK,KAAKzC,OAClCJ,GAAGgW,EAAI,QAAS5V,KAAK2M,QAAQlK,KAAKzC,OANlC,CAQP,GA1FL,CAAA3D,IAAA,SAAAiL,IA4GI,WACI,QAAStH,KAAKyW,IACjB,GA9GL,CAAApa,IAAA,UAAA0H,MAyHI,WACI,OAAI/D,KAAK6V,YAET7V,KAAK2W,YACA3W,KAAK4V,GAAL,eACD5V,KAAK4V,GAAG1L,OACR,SAAWlK,KAAK4V,GAAGgB,aACnB5W,KAAKuM,UALEvM,IAOd,GAlIL,CAAA3D,IAAA,OAAA0H,MAsII,WACI,OAAO/D,KAAKuV,SACf,GAxIL,CAAAlZ,IAAA,OAAA0H,MAwJI,WAAc,IAAA,IAAAtC,EAAAnB,UAAApC,OAAN4C,EAAM,IAAAC,MAAAU,GAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAANb,EAAMa,GAAArB,UAAAqB,GAGV,OAFAb,EAAKkT,QAAQ,WACbhU,KAAKa,KAAKR,MAAML,KAAMc,GACfd,IACV,GA5JL,CAAA3D,IAAA,OAAA0H,MA8KI,SAAK+I,GACD,GAAIuI,GAAgBtT,eAAe+K,GAC/B,MAAM,IAAI3J,MAAM,IAAM2J,EAAGlQ,WAAa,8BAF5B,IAAA,IAAAia,EAAAvW,UAAApC,OAAN4C,EAAM,IAAAC,MAAA8V,EAAA,EAAAA,EAAA,EAAA,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAANhW,EAAMgW,EAAA,GAAAxW,UAAAwW,GAKd,GADAhW,EAAKkT,QAAQlH,GACT9M,KAAKuW,MAAMQ,UAAY/W,KAAKqW,MAAMW,YAAchX,KAAKqW,eAErD,OADArW,KAAKiX,YAAYnW,GACVd,KAEX,IAAMwE,EAAS,CACXjI,KAAM6W,GAAWG,MACjB/W,KAAMsE,EAEV0D,QAAiB,IAGjB,GAFAA,EAAOmN,QAAQC,UAAmC,IAAxB5R,KAAKqW,MAAMzE,SAEjC,mBAAsB9Q,EAAKA,EAAK5C,OAAS,GAAI,CAC7C,IAAMiR,EAAKnP,KAAKmW,MACVe,EAAMpW,EAAKqW,MACjBnX,KAAKoX,qBAAqBjI,EAAI+H,GAC9B1S,EAAO2K,GAAKA,CACf,CACD,IAAMkI,EAAsBrX,KAAK4V,GAAG0B,QAChCtX,KAAK4V,GAAG0B,OAAO7H,WACfzP,KAAK4V,GAAG0B,OAAO7H,UAAU/L,SACvB6T,EAAgBvX,KAAKqW,MAAL,YAAyBgB,IAAwBrX,KAAK6V,WAW5E,OAVI0B,IAEKvX,KAAK6V,WACV7V,KAAKwX,wBAAwBhT,GAC7BxE,KAAKwE,OAAOA,IAGZxE,KAAKgW,WAAW9V,KAAKsE,IAEzBxE,KAAKqW,MAAQ,GACNrW,IACV,GAnNL,CAAA3D,IAAA,uBAAA0H,MAuNI,SAAqBoL,EAAI+H,GAAK,IACtBO,EADsBhU,EAAAzD,KAEpBwK,EAAwC,QAA7BiN,EAAKzX,KAAKqW,MAAM7L,eAA4B,IAAPiN,EAAgBA,EAAKzX,KAAKuW,MAAMmB,WACtF,QAAgB5N,IAAZU,EAAJ,CAKA,IAAMmN,EAAQ3X,KAAK4V,GAAGpT,cAAa,kBACxBiB,EAAK2S,KAAKjH,GACjB,IAAK,IAAIlR,EAAI,EAAGA,EAAIwF,EAAKuS,WAAW9X,OAAQD,IACpCwF,EAAKuS,WAAW/X,GAAGkR,KAAOA,GAC1B1L,EAAKuS,WAAWpV,OAAO3C,EAAG,GAGlCiZ,EAAIra,KAAK4G,EAAM,IAAIN,MAAM,2BAPf,GAQXqH,GACHxK,KAAKoW,KAAKjH,GAAM,WAEZ1L,EAAKmS,GAAGlT,eAAeiV,GAFE,IAAA,IAAAC,EAAAtX,UAAApC,OAAT4C,EAAS,IAAAC,MAAA6W,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAT/W,EAAS+W,GAAAvX,UAAAuX,GAGzBX,EAAI7W,MAAMoD,EAAO,CAAA,aAAS3C,IApBJ,MAItBd,KAAKoW,KAAKjH,GAAM+H,CAkBvB,GA7OL,CAAA7a,IAAA,cAAA0H,MA8PI,SAAY+I,GAAa,IAAA,IAAAnF,EAAA3H,KAAA8X,EAAAxX,UAAApC,OAAN4C,EAAM,IAAAC,MAAA+W,EAAA,EAAAA,EAAA,EAAA,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAANjX,EAAMiX,EAAA,GAAAzX,UAAAyX,GAErB,IAAMC,OAAiClO,IAAvB9J,KAAKqW,MAAM7L,cAAmDV,IAA1B9J,KAAKuW,MAAMmB,WAC/D,OAAO,IAAIlM,SAAQ,SAACC,EAASwM,GACzBnX,EAAKZ,MAAK,SAACgY,EAAMC,GACb,OAAIH,EACOE,EAAOD,EAAOC,GAAQzM,EAAQ0M,GAG9B1M,EAAQyM,MAGvBvQ,EAAK9G,KAALR,MAAAsH,GAAUmF,GAANzG,OAAavF,GACpB,GACJ,GA5QL,CAAAzE,IAAA,cAAA0H,MAkRI,SAAYjD,GAAM,IACVoW,EADUjP,EAAAjI,KAEuB,mBAA1Bc,EAAKA,EAAK5C,OAAS,KAC1BgZ,EAAMpW,EAAKqW,OAEf,IAAM3S,EAAS,CACX2K,GAAInP,KAAKkW,YACTkC,SAAU,EACVC,SAAS,EACTvX,KAAAA,EACAuV,MAAOrN,EAAc,CAAEgO,WAAW,GAAQhX,KAAKqW,QAEnDvV,EAAKZ,MAAK,SAAC8F,GACP,GAAIxB,IAAWyD,EAAKgO,OAAO,GAA3B,CAIA,IAAMqC,EAAmB,OAARtS,EACjB,GAAIsS,EACI9T,EAAO4T,SAAWnQ,EAAKsO,MAAMQ,UAC7B9O,EAAKgO,OAAOlG,QACRmH,GACAA,EAAIlR,SAMZ,GADAiC,EAAKgO,OAAOlG,QACRmH,EAAK,CAAA,IAAA,IAAAqB,EAAAjY,UAAApC,OAhBEsa,EAgBF,IAAAzX,MAAAwX,EAAA,EAAAA,EAAA,EAAA,GAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAhBED,EAgBFC,EAAA,GAAAnY,UAAAmY,GACLvB,EAAA7W,WAAA,EAAA,CAAI,MAAJgG,OAAamS,GAChB,CAGL,OADAhU,EAAO6T,SAAU,EACVpQ,EAAKyQ,aAjBX,KAmBL1Y,KAAKiW,OAAO/V,KAAKsE,GACjBxE,KAAK0Y,aACR,GAvTL,CAAArc,IAAA,cAAA0H,MA8TI,WAA2B,IAAf4U,0DACR,GAAK3Y,KAAK6V,WAAoC,IAAvB7V,KAAKiW,OAAO/X,OAAnC,CAGA,IAAMsG,EAASxE,KAAKiW,OAAO,GACvBzR,EAAO6T,UAAYM,IAGvBnU,EAAO6T,SAAU,EACjB7T,EAAO4T,WACPpY,KAAKqW,MAAQ7R,EAAO6R,MACpBrW,KAAKa,KAAKR,MAAML,KAAMwE,EAAO1D,MAR5B,CASJ,GA1UL,CAAAzE,IAAA,SAAA0H,MAiVI,SAAOS,GACHA,EAAOqP,IAAM7T,KAAK6T,IAClB7T,KAAK4V,GAAGgD,QAAQpU,EACnB,GApVL,CAAAnI,IAAA,SAAA0H,MA0VI,WAAS,IAAAoE,EAAAnI,KACmB,mBAAbA,KAAKsW,KACZtW,KAAKsW,MAAK,SAAC9Z,GACP2L,EAAK0Q,mBAAmBrc,MAI5BwD,KAAK6Y,mBAAmB7Y,KAAKsW,KAEpC,GAnWL,CAAAja,IAAA,qBAAA0H,MA0WI,SAAmBvH,GACfwD,KAAKwE,OAAO,CACRjI,KAAM6W,GAAW4B,QACjBxY,KAAMwD,KAAK8Y,KACL9P,EAAc,CAAE+P,IAAK/Y,KAAK8Y,KAAME,OAAQhZ,KAAKiZ,aAAezc,GAC5DA,GAEb,GAjXL,CAAAH,IAAA,UAAA0H,MAwXI,SAAQiC,GACChG,KAAK6V,WACN7V,KAAKiB,aAAa,gBAAiB+E,EAE1C,GA5XL,CAAA3J,IAAA,UAAA0H,MAoYI,SAAQlB,EAAQC,GACZ9C,KAAK6V,WAAY,SACV7V,KAAKmP,GACZnP,KAAKiB,aAAa,aAAc4B,EAAQC,EAC3C,GAxYL,CAAAzG,IAAA,WAAA0H,MA+YI,SAASS,GAEL,GADsBA,EAAOqP,MAAQ7T,KAAK6T,IAG1C,OAAQrP,EAAOjI,MACX,KAAK6W,GAAW4B,QACRxQ,EAAOhI,MAAQgI,EAAOhI,KAAKkM,IAC3B1I,KAAKkZ,UAAU1U,EAAOhI,KAAKkM,IAAKlE,EAAOhI,KAAKuc,KAG5C/Y,KAAKiB,aAAa,gBAAiB,IAAIkC,MAAM,8LAEjD,MACJ,KAAKiQ,GAAWG,MAChB,KAAKH,GAAWO,aACZ3T,KAAKmZ,QAAQ3U,GACb,MACJ,KAAK4O,GAAWI,IAChB,KAAKJ,GAAWQ,WACZ5T,KAAKoZ,MAAM5U,GACX,MACJ,KAAK4O,GAAW6B,WACZjV,KAAKqZ,eACL,MACJ,KAAKjG,GAAW8B,cACZlV,KAAKsZ,UACL,IAAMtT,EAAM,IAAI7C,MAAMqB,EAAOhI,KAAK+c,SAElCvT,EAAIxJ,KAAOgI,EAAOhI,KAAKA,KACvBwD,KAAKiB,aAAa,gBAAiB+E,GAG9C,GA/aL,CAAA3J,IAAA,UAAA0H,MAsbI,SAAQS,GACJ,IAAM1D,EAAO0D,EAAOhI,MAAQ,GACxB,MAAQgI,EAAO2K,IACfrO,EAAKZ,KAAKF,KAAKkX,IAAI1S,EAAO2K,KAE1BnP,KAAK6V,UACL7V,KAAKwZ,UAAU1Y,GAGfd,KAAK+V,cAAc7V,KAAKlE,OAAOsZ,OAAOxU,GAE7C,GAjcL,CAAAzE,IAAA,YAAA0H,MAkcI,SAAUjD,GACN,GAAId,KAAKyZ,eAAiBzZ,KAAKyZ,cAAcvb,OAAQ,CACjD,IADiDwb,EAAAC,EAAAC,EAC/B5Z,KAAKyZ,cAAczY,SADY,IAEjD,IAAkC2Y,EAAAE,MAAAH,EAAAC,EAAAG,KAAAC,MAAA,CAAAL,EAAA3V,MACrB1D,MAAML,KAAMc,EACxB,CAJgD,CAAA,MAAAkF,GAAA2T,EAAAvT,EAAAJ,EAAA,CAAA,QAAA2T,EAAAK,GAAA,CAKpD,CACDhW,EAAAC,EAAAsK,EAAA5R,WAAA,OAAAqD,MAAWK,MAAML,KAAMc,GACnBd,KAAK8Y,MAAQhY,EAAK5C,QAA2C,iBAA1B4C,EAAKA,EAAK5C,OAAS,KACtD8B,KAAKiZ,YAAcnY,EAAKA,EAAK5C,OAAS,GAE7C,GA7cL,CAAA7B,IAAA,MAAA0H,MAmdI,SAAIoL,GACA,IAAM9N,EAAOrB,KACTia,GAAO,EACX,OAAO,WAEH,IAAIA,EAAJ,CAEAA,GAAO,EAJe,IAAA,IAAAC,EAAA5Z,UAAApC,OAAN4C,EAAM,IAAAC,MAAAmZ,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAANrZ,EAAMqZ,GAAA7Z,UAAA6Z,GAKtB9Y,EAAKmD,OAAO,CACRjI,KAAM6W,GAAWI,IACjBrE,GAAIA,EACJ3S,KAAMsE,GALN,EAQX,GAjeL,CAAAzE,IAAA,QAAA0H,MAweI,SAAMS,GACF,IAAM0S,EAAMlX,KAAKoW,KAAK5R,EAAO2K,IACzB,mBAAsB+H,IACtBA,EAAI7W,MAAML,KAAMwE,EAAOhI,aAChBwD,KAAKoW,KAAK5R,EAAO2K,IAI/B,GAhfL,CAAA9S,IAAA,YAAA0H,MAsfI,SAAUoL,EAAI4J,GACV/Y,KAAKmP,GAAKA,EACVnP,KAAK8V,UAAYiD,GAAO/Y,KAAK8Y,OAASC,EACtC/Y,KAAK8Y,KAAOC,EACZ/Y,KAAK6V,WAAY,EACjB7V,KAAKoa,eACLpa,KAAKiB,aAAa,WAClBjB,KAAK0Y,aAAY,EACpB,GA9fL,CAAArc,IAAA,eAAA0H,MAogBI,WAAe,IAAAoF,EAAAnJ,KACXA,KAAK+V,cAAc3Z,SAAQ,SAAC0E,GAAD,OAAUqI,EAAKqQ,UAAU1Y,MACpDd,KAAK+V,cAAgB,GACrB/V,KAAKgW,WAAW5Z,SAAQ,SAACoI,GACrB2E,EAAKqO,wBAAwBhT,GAC7B2E,EAAK3E,OAAOA,MAEhBxE,KAAKgW,WAAa,EACrB,GA5gBL,CAAA3Z,IAAA,eAAA0H,MAkhBI,WACI/D,KAAKsZ,UACLtZ,KAAK2M,QAAQ,uBAChB,GArhBL,CAAAtQ,IAAA,UAAA0H,MA6hBI,WACQ/D,KAAKyW,OAELzW,KAAKyW,KAAKra,SAAQ,SAACie,GAAD,OAAgBA,OAClCra,KAAKyW,UAAO3M,GAEhB9J,KAAK4V,GAAL,SAAoB5V,KACvB,GApiBL,CAAA3D,IAAA,aAAA0H,MAqjBI,WAUI,OATI/D,KAAK6V,WACL7V,KAAKwE,OAAO,CAAEjI,KAAM6W,GAAW6B,aAGnCjV,KAAKsZ,UACDtZ,KAAK6V,WAEL7V,KAAK2M,QAAQ,wBAEV3M,IACV,GAhkBL,CAAA3D,IAAA,QAAA0H,MAskBI,WACI,OAAO/D,KAAKyV,YACf,GAxkBL,CAAApZ,IAAA,WAAA0H,MAklBI,SAAS6N,GAEL,OADA5R,KAAKqW,MAAMzE,SAAWA,EACf5R,IACV,GArlBL,CAAA3D,IAAA,WAAAiL,IA+lBI,WAEI,OADAtH,KAAKqW,gBAAiB,EACfrW,IACV,GAlmBL,CAAA3D,IAAA,UAAA0H,MAgnBI,SAAQyG,GAEJ,OADAxK,KAAKqW,MAAM7L,QAAUA,EACdxK,IACV,GAnnBL,CAAA3D,IAAA,QAAA0H,MA+nBI,SAAMuW,GAGF,OAFAta,KAAKyZ,cAAgBzZ,KAAKyZ,eAAiB,GAC3CzZ,KAAKyZ,cAAcvZ,KAAKoa,GACjBta,IACV,GAnoBL,CAAA3D,IAAA,aAAA0H,MA+oBI,SAAWuW,GAGP,OAFAta,KAAKyZ,cAAgBzZ,KAAKyZ,eAAiB,GAC3CzZ,KAAKyZ,cAAczF,QAAQsG,GACpBta,IACV,GAnpBL,CAAA3D,IAAA,SAAA0H,MAsqBI,SAAOuW,GACH,IAAKta,KAAKyZ,cACN,OAAOzZ,KAEX,GAAIsa,GAEA,IADA,IAAMpZ,EAAYlB,KAAKyZ,cACdxb,EAAI,EAAGA,EAAIiD,EAAUhD,OAAQD,IAClC,GAAIqc,IAAapZ,EAAUjD,GAEvB,OADAiD,EAAUN,OAAO3C,EAAG,GACb+B,UAKfA,KAAKyZ,cAAgB,GAEzB,OAAOzZ,IACV,GAvrBL,CAAA3D,IAAA,eAAA0H,MA4rBI,WACI,OAAO/D,KAAKyZ,eAAiB,EAChC,GA9rBL,CAAApd,IAAA,gBAAA0H,MA4sBI,SAAcuW,GAGV,OAFAta,KAAKua,sBAAwBva,KAAKua,uBAAyB,GAC3Dva,KAAKua,sBAAsBra,KAAKoa,GACzBta,IACV,GAhtBL,CAAA3D,IAAA,qBAAA0H,MA8tBI,SAAmBuW,GAGf,OAFAta,KAAKua,sBAAwBva,KAAKua,uBAAyB,GAC3Dva,KAAKua,sBAAsBvG,QAAQsG,GAC5Bta,IACV,GAluBL,CAAA3D,IAAA,iBAAA0H,MAqvBI,SAAeuW,GACX,IAAKta,KAAKua,sBACN,OAAOva,KAEX,GAAIsa,GAEA,IADA,IAAMpZ,EAAYlB,KAAKua,sBACdtc,EAAI,EAAGA,EAAIiD,EAAUhD,OAAQD,IAClC,GAAIqc,IAAapZ,EAAUjD,GAEvB,OADAiD,EAAUN,OAAO3C,EAAG,GACb+B,UAKfA,KAAKua,sBAAwB,GAEjC,OAAOva,IACV,GAtwBL,CAAA3D,IAAA,uBAAA0H,MA2wBI,WACI,OAAO/D,KAAKua,uBAAyB,EACxC,GA7wBL,CAAAle,IAAA,0BAAA0H,MAqxBI,SAAwBS,GACpB,GAAIxE,KAAKua,uBAAyBva,KAAKua,sBAAsBrc,OAAQ,CACjE,IADiEsc,EAAAC,EAAAb,EAC/C5Z,KAAKua,sBAAsBvZ,SADoB,IAEjE,IAAkCyZ,EAAAZ,MAAAW,EAAAC,EAAAX,KAAAC,MAAA,CAAAS,EAAAzW,MACrB1D,MAAML,KAAMwE,EAAOhI,KAC/B,CAJgE,CAAA,MAAAwJ,GAAAyU,EAAArU,EAAAJ,EAAA,CAAA,QAAAyU,EAAAT,GAAA,CAKpE,CACJ,KA5xBLzL,CAAA,CAAA,CAA4B7O,GC7BrB,SAASgb,GAAQpY,GACpBA,EAAOA,GAAQ,GACftC,KAAK2a,GAAKrY,EAAKsY,KAAO,IACtB5a,KAAK6a,IAAMvY,EAAKuY,KAAO,IACvB7a,KAAK8a,OAASxY,EAAKwY,QAAU,EAC7B9a,KAAK+a,OAASzY,EAAKyY,OAAS,GAAKzY,EAAKyY,QAAU,EAAIzY,EAAKyY,OAAS,EAClE/a,KAAKgb,SAAW,CACnB,CAODN,GAAQ/d,UAAUse,SAAW,WACzB,IAAIN,EAAK3a,KAAK2a,GAAKzV,KAAKgW,IAAIlb,KAAK8a,OAAQ9a,KAAKgb,YAC9C,GAAIhb,KAAK+a,OAAQ,CACb,IAAII,EAAOjW,KAAKkW,SACZC,EAAYnW,KAAKC,MAAMgW,EAAOnb,KAAK+a,OAASJ,GAChDA,EAAoC,IAAN,EAAxBzV,KAAKC,MAAa,GAAPgW,IAAuBR,EAAKU,EAAYV,EAAKU,CACjE,CACD,OAAgC,EAAzBnW,KAAK0V,IAAID,EAAI3a,KAAK6a,IAC5B,EAMDH,GAAQ/d,UAAU2e,MAAQ,WACtBtb,KAAKgb,SAAW,CACnB,EAMDN,GAAQ/d,UAAU4e,OAAS,SAAUX,GACjC5a,KAAK2a,GAAKC,CACb,EAMDF,GAAQ/d,UAAU6e,OAAS,SAAUX,GACjC7a,KAAK6a,IAAMA,CACd,EAMDH,GAAQ/d,UAAU8e,UAAY,SAAUV,GACpC/a,KAAK+a,OAASA,CACjB,EC3DD,IAAaW,GAAb,SAAArY,GAAAC,EAAAoY,EAAArY,GAAA,IAAAH,EAAAM,EAAAkY,GACI,SAAYxS,EAAAA,EAAK5G,GAAM,IAAAU,EACfyU,EADexU,EAAAjD,KAAA0b,IAEnB1Y,EAAAE,EAAArG,KAAAmD,OACK2b,KAAO,GACZ3Y,EAAKyT,KAAO,GACRvN,GAAO,WAAoBA,EAAAA,KAC3B5G,EAAO4G,EACPA,OAAMY,IAEVxH,EAAOA,GAAQ,IACVyG,KAAOzG,EAAKyG,MAAQ,aACzB/F,EAAKV,KAAOA,EACZD,EAAqBsB,EAAAX,GAAOV,GAC5BU,EAAK4Y,cAAmC,IAAtBtZ,EAAKsZ,cACvB5Y,EAAK6Y,qBAAqBvZ,EAAKuZ,sBAAwBC,KACvD9Y,EAAK+Y,kBAAkBzZ,EAAKyZ,mBAAqB,KACjD/Y,EAAKgZ,qBAAqB1Z,EAAK0Z,sBAAwB,KACvDhZ,EAAKiZ,oBAAwD,QAAnCxE,EAAKnV,EAAK2Z,2BAAwC,IAAPxE,EAAgBA,EAAK,IAC1FzU,EAAKkZ,QAAU,IAAIxB,GAAQ,CACvBE,IAAK5X,EAAK+Y,oBACVlB,IAAK7X,EAAKgZ,uBACVjB,OAAQ/X,EAAKiZ,wBAEjBjZ,EAAKwH,QAAQ,MAAQlI,EAAKkI,QAAU,IAAQlI,EAAKkI,SACjDxH,EAAK4T,YAAc,SACnB5T,EAAKkG,IAAMA,EACX,IAAMiT,EAAU7Z,EAAK8Z,QAAUA,GA1BZ,OA2BnBpZ,EAAKqZ,QAAU,IAAIF,EAAQ9I,QAC3BrQ,EAAKsZ,QAAU,IAAIH,EAAQlI,QAC3BjR,EAAKwT,cAAoC,IAArBlU,EAAKia,YACrBvZ,EAAKwT,cACLxT,EAAKkH,OA/BUlH,CAgCtB,CAjCL,OAAAc,EAAA4X,EAAA,CAAA,CAAArf,IAAA,eAAA0H,MAkCI,SAAayY,GACT,OAAKlc,UAAUpC,QAEf8B,KAAKyc,gBAAkBD,EAChBxc,MAFIA,KAAKyc,aAGnB,GAvCL,CAAApgB,IAAA,uBAAA0H,MAwCI,SAAqByY,GACjB,YAAU1S,IAAN0S,EACOxc,KAAK0c,uBAChB1c,KAAK0c,sBAAwBF,EACtBxc,KACV,GA7CL,CAAA3D,IAAA,oBAAA0H,MA8CI,SAAkByY,GACd,IAAI/E,EACJ,YAAU3N,IAAN0S,EACOxc,KAAK2c,oBAChB3c,KAAK2c,mBAAqBH,EACF,QAAvB/E,EAAKzX,KAAKkc,eAA4B,IAAPzE,GAAyBA,EAAG8D,OAAOiB,GAC5Dxc,KACV,GArDL,CAAA3D,IAAA,sBAAA0H,MAsDI,SAAoByY,GAChB,IAAI/E,EACJ,YAAU3N,IAAN0S,EACOxc,KAAK4c,sBAChB5c,KAAK4c,qBAAuBJ,EACJ,QAAvB/E,EAAKzX,KAAKkc,eAA4B,IAAPzE,GAAyBA,EAAGgE,UAAUe,GAC/Dxc,KACV,GA7DL,CAAA3D,IAAA,uBAAA0H,MA8DI,SAAqByY,GACjB,IAAI/E,EACJ,YAAU3N,IAAN0S,EACOxc,KAAK6c,uBAChB7c,KAAK6c,sBAAwBL,EACL,QAAvB/E,EAAKzX,KAAKkc,eAA4B,IAAPzE,GAAyBA,EAAG+D,OAAOgB,GAC5Dxc,KACV,GArEL,CAAA3D,IAAA,UAAA0H,MAsEI,SAAQyY,GACJ,OAAKlc,UAAUpC,QAEf8B,KAAK8c,SAAWN,EACTxc,MAFIA,KAAK8c,QAGnB,GA3EL,CAAAzgB,IAAA,uBAAA0H,MAkFI,YAES/D,KAAK+c,eACN/c,KAAKyc,eACqB,IAA1Bzc,KAAKkc,QAAQlB,UAEbhb,KAAKgd,WAEZ,GA1FL,CAAA3gB,IAAA,OAAA0H,MAkGI,SAAKhE,GAAI,IAAA0D,EAAAzD,KACL,IAAKA,KAAK4W,YAAY9N,QAAQ,QAC1B,OAAO9I,KACXA,KAAKsX,OAAS,IAAI2F,GAAOjd,KAAKkJ,IAAKlJ,KAAKsC,MACxC,IAAMuB,EAAS7D,KAAKsX,OACdjW,EAAOrB,KACbA,KAAK4W,YAAc,UACnB5W,KAAKkd,eAAgB,EAErB,IAAMC,EAAiBvd,GAAGiE,EAAQ,QAAQ,WACtCxC,EAAKkL,SACLxM,GAAMA,OAGJqd,EAAWxd,GAAGiE,EAAQ,SAAS,SAACmC,GAClC3E,EAAK4J,UACL5J,EAAKuV,YAAc,SACnBnT,EAAKxC,aAAa,QAAS+E,GACvBjG,EACAA,EAAGiG,GAIH3E,EAAKgc,sBAEZ,IACD,IAAI,IAAUrd,KAAK8c,SAAU,CACzB,IAAMtS,EAAUxK,KAAK8c,SACL,IAAZtS,GACA2S,IAGJ,IAAMxF,EAAQ3X,KAAKwC,cAAa,WAC5B2a,IACAtZ,EAAOqE,QAEPrE,EAAOhD,KAAK,QAAS,IAAIsC,MAAM,WAJrB,GAKXqH,GACCxK,KAAKsC,KAAKkK,WACVmL,EAAMjL,QAEV1M,KAAKyW,KAAKvW,MAAK,WACXkC,aAAauV,KAEpB,CAGD,OAFA3X,KAAKyW,KAAKvW,KAAKid,GACfnd,KAAKyW,KAAKvW,KAAKkd,GACRpd,IACV,GAlJL,CAAA3D,IAAA,UAAA0H,MAyJI,SAAQhE,GACJ,OAAOC,KAAKkK,KAAKnK,EACpB,GA3JL,CAAA1D,IAAA,SAAA0H,MAiKI,WAEI/D,KAAKiL,UAELjL,KAAK4W,YAAc,OACnB5W,KAAKiB,aAAa,QAElB,IAAM4C,EAAS7D,KAAKsX,OACpBtX,KAAKyW,KAAKvW,KAAKN,GAAGiE,EAAQ,OAAQ7D,KAAKsd,OAAO7a,KAAKzC,OAAQJ,GAAGiE,EAAQ,OAAQ7D,KAAKud,OAAO9a,KAAKzC,OAAQJ,GAAGiE,EAAQ,QAAS7D,KAAK+M,QAAQtK,KAAKzC,OAAQJ,GAAGiE,EAAQ,QAAS7D,KAAK2M,QAAQlK,KAAKzC,OAAQJ,GAAGI,KAAKsc,QAAS,UAAWtc,KAAKwd,UAAU/a,KAAKzC,OACtP,GA1KL,CAAA3D,IAAA,SAAA0H,MAgLI,WACI/D,KAAKiB,aAAa,OACrB,GAlLL,CAAA5E,IAAA,SAAA0H,MAwLI,SAAOvH,GACH,IACIwD,KAAKsc,QAAQmB,IAAIjhB,EAIpB,CAFD,MAAO4J,GACHpG,KAAK2M,QAAQ,cAAevG,EAC/B,CACJ,GA/LL,CAAA/J,IAAA,YAAA0H,MAqMI,SAAUS,GAAQ,IAAAmD,EAAA3H,KAEduL,IAAS,WACL5D,EAAK1G,aAAa,SAAUuD,KAC7BxE,KAAKwC,aACX,GA1ML,CAAAnG,IAAA,UAAA0H,MAgNI,SAAQiC,GACJhG,KAAKiB,aAAa,QAAS+E,EAC9B,GAlNL,CAAA3J,IAAA,SAAA0H,MAyNI,SAAO8P,EAAKvR,GACR,IAAIuB,EAAS7D,KAAK2b,KAAK9H,GAQvB,OAPKhQ,EAII7D,KAAKwW,eAAiB3S,EAAO6Z,QAClC7Z,EAAO0R,WAJP1R,EAAS,IAAI0K,GAAOvO,KAAM6T,EAAKvR,GAC/BtC,KAAK2b,KAAK9H,GAAOhQ,GAKdA,CACV,GAnOL,CAAAxH,IAAA,WAAA0H,MA0OI,SAASF,GAEL,IADA,IACA8Z,EAAA,EAAAC,EADa5hB,OAAOG,KAAK6D,KAAK2b,MACNgC,EAAAC,EAAA1f,OAAAyf,IAAA,CAAnB,IAAM9J,EAAN+J,EAAAD,GAED,GADe3d,KAAK2b,KAAK9H,GACd6J,OACP,MAEP,CACD1d,KAAK6d,QACR,GAnPL,CAAAxhB,IAAA,UAAA0H,MA0PI,SAAQS,GAEJ,IADA,IAAMqD,EAAiB7H,KAAKqc,QAAQtX,OAAOP,GAClCvG,EAAI,EAAGA,EAAI4J,EAAe3J,OAAQD,IACvC+B,KAAKsX,OAAO/S,MAAMsD,EAAe5J,GAAIuG,EAAOmN,QAEnD,GA/PL,CAAAtV,IAAA,UAAA0H,MAqQI,WACI/D,KAAKyW,KAAKra,SAAQ,SAACie,GAAD,OAAgBA,OAClCra,KAAKyW,KAAKvY,OAAS,EACnB8B,KAAKsc,QAAQhD,SAChB,GAzQL,CAAAjd,IAAA,SAAA0H,MA+QI,WACI/D,KAAKkd,eAAgB,EACrBld,KAAK+c,eAAgB,EACrB/c,KAAK2M,QAAQ,gBACT3M,KAAKsX,QACLtX,KAAKsX,OAAOpP,OACnB,GArRL,CAAA7L,IAAA,aAAA0H,MA2RI,WACI,OAAO/D,KAAK6d,QACf,GA7RL,CAAAxhB,IAAA,UAAA0H,MAmSI,SAAQlB,EAAQC,GACZ9C,KAAKiL,UACLjL,KAAKkc,QAAQZ,QACbtb,KAAK4W,YAAc,SACnB5W,KAAKiB,aAAa,QAAS4B,EAAQC,GAC/B9C,KAAKyc,gBAAkBzc,KAAKkd,eAC5Bld,KAAKgd,WAEZ,GA3SL,CAAA3gB,IAAA,YAAA0H,MAiTI,WAAY,IAAAkE,EAAAjI,KACR,GAAIA,KAAK+c,eAAiB/c,KAAKkd,cAC3B,OAAOld,KACX,IAAMqB,EAAOrB,KACb,GAAIA,KAAKkc,QAAQlB,UAAYhb,KAAK0c,sBAC9B1c,KAAKkc,QAAQZ,QACbtb,KAAKiB,aAAa,oBAClBjB,KAAK+c,eAAgB,MAEpB,CACD,IAAMe,EAAQ9d,KAAKkc,QAAQjB,WAC3Bjb,KAAK+c,eAAgB,EACrB,IAAMpF,EAAQ3X,KAAKwC,cAAa,WACxBnB,EAAK6b,gBAETjV,EAAKhH,aAAa,oBAAqBI,EAAK6a,QAAQlB,UAEhD3Z,EAAK6b,eAET7b,EAAK6I,MAAK,SAAClE,GACHA,GACA3E,EAAK0b,eAAgB,EACrB1b,EAAK2b,YACL/U,EAAKhH,aAAa,kBAAmB+E,IAGrC3E,EAAK0c,iBAdH,GAiBXD,GACC9d,KAAKsC,KAAKkK,WACVmL,EAAMjL,QAEV1M,KAAKyW,KAAKvW,MAAK,WACXkC,aAAauV,KAEpB,CACJ,GAtVL,CAAAtb,IAAA,cAAA0H,MA4VI,WACI,IAAMia,EAAUhe,KAAKkc,QAAQlB,SAC7Bhb,KAAK+c,eAAgB,EACrB/c,KAAKkc,QAAQZ,QACbtb,KAAKiB,aAAa,YAAa+c,EAClC,KAjWLtC,CAAA,CAAA,CAA6Bhc,GCAvBue,GAAQ,CAAA,EACd,SAASlgB,GAAOmL,EAAK5G,GACE,WAAfiQ,EAAOrJ,KACP5G,EAAO4G,EACPA,OAAMY,GAGV,IASI8L,EATEsI,ECHH,SAAahV,GAAqB,IAAhBH,yDAAO,GAAIoV,EAAK7d,UAAApC,OAAA,EAAAoC,UAAA,QAAAwJ,EACjC3M,EAAM+L,EAEViV,EAAMA,GAA4B,oBAAbtX,UAA4BA,SAC7C,MAAQqC,IACRA,EAAMiV,EAAIpX,SAAW,KAAOoX,EAAItQ,MAEjB,iBAAR3E,IACH,MAAQA,EAAI1K,OAAO,KAEf0K,EADA,MAAQA,EAAI1K,OAAO,GACb2f,EAAIpX,SAAWmC,EAGfiV,EAAItQ,KAAO3E,GAGpB,sBAAsBkV,KAAKlV,KAExBA,OADA,IAAuBiV,EACjBA,EAAIpX,SAAW,KAAOmC,EAGtB,WAAaA,GAI3B/L,EAAMmQ,GAAMpE,IAGX/L,EAAI6J,OACD,cAAcoX,KAAKjhB,EAAI4J,UACvB5J,EAAI6J,KAAO,KAEN,eAAeoX,KAAKjhB,EAAI4J,YAC7B5J,EAAI6J,KAAO,QAGnB7J,EAAI4L,KAAO5L,EAAI4L,MAAQ,IACvB,IACM8E,GADkC,IAA3B1Q,EAAI0Q,KAAK/E,QAAQ,KACV,IAAM3L,EAAI0Q,KAAO,IAAM1Q,EAAI0Q,KAS/C,OAPA1Q,EAAIgS,GAAKhS,EAAI4J,SAAW,MAAQ8G,EAAO,IAAM1Q,EAAI6J,KAAO+B,EAExD5L,EAAIkhB,KACAlhB,EAAI4J,SACA,MACA8G,GACCsQ,GAAOA,EAAInX,OAAS7J,EAAI6J,KAAO,GAAK,IAAM7J,EAAI6J,MAChD7J,CACV,CD7CkBmhB,CAAIpV,GADnB5G,EAAOA,GAAQ,IACcyG,MAAQ,cAC/B6E,EAASsQ,EAAOtQ,OAChBuB,EAAK+O,EAAO/O,GACZpG,EAAOmV,EAAOnV,KACdwV,EAAgBN,GAAM9O,IAAOpG,KAAQkV,GAAM9O,GAAN,KAkB3C,OAjBsB7M,EAAKkc,UACvBlc,EAAK,0BACL,IAAUA,EAAKmc,WACfF,EAGA3I,EAAK,IAAI8F,GAAQ9N,EAAQtL,IAGpB2b,GAAM9O,KACP8O,GAAM9O,GAAM,IAAIuM,GAAQ9N,EAAQtL,IAEpCsT,EAAKqI,GAAM9O,IAEX+O,EAAOta,QAAUtB,EAAKsB,QACtBtB,EAAKsB,MAAQsa,EAAO/P,UAEjByH,EAAG/R,OAAOqa,EAAOnV,KAAMzG,EACjC,QAGD0G,EAAcjL,GAAQ,CAClB2d,QAAAA,GACAnN,OAAAA,GACAqH,GAAI7X,GACJwX,QAASxX"}