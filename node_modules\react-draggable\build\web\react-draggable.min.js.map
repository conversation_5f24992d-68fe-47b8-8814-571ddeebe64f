{"version": 3, "file": "react-draggable.min.js", "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;;;;;;;;;;;;;;;;;;;;;;;;;ACVA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA,aAAa,gBAAgB;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,6BAAe,SAAS,SAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;ACtCA;AACO,SAASA,WAAT,CAAqBC;AAArB;AAAA,EAAoDC;AAApD;AAAA;AAAA;AAA6E;AAClF,OAAK,IAAIC,CAAC,GAAG,CAAR,EAAWC,MAAM,GAAGH,KAAK,CAACG,MAA/B,EAAuCD,CAAC,GAAGC,MAA3C,EAAmDD,CAAC,EAApD,EAAwD;AACtD,QAAID,QAAQ,CAACG,KAAT,CAAeH,QAAf,EAAyB,CAACD,KAAK,CAACE,CAAD,CAAN,EAAWA,CAAX,EAAcF,KAAd,CAAzB,CAAJ,EAAoD,OAAOA,KAAK,CAACE,CAAD,CAAZ;AACrD;AACF;AAEM,SAASG,UAAT,CAAoBC;AAApB;AAAA;AAAA;AAAgD;AACrD;AACA,SAAO,OAAOA,IAAP,KAAgB,UAAhB,IAA8BC,MAAM,CAACC,SAAP,CAAiBC,QAAjB,CAA0BC,IAA1B,CAA+BJ,IAA/B,MAAyC,mBAA9E;AACD;AAEM,SAASK,KAAT,CAAeC;AAAf;AAAA;AAAA;AAA0C;AAC/C,SAAO,OAAOA,GAAP,KAAe,QAAf,IAA2B,CAACC,KAAK,CAACD,GAAD,CAAxC;AACD;AAEM,SAASE,SAAT,CAAaC;AAAb;AAAA;AAAA;AAAgC;AACrC,SAAOC,QAAQ,CAACD,CAAD,EAAI,EAAJ,CAAf;AACD;AAEM,SAASE,SAAT,CAAmBC;AAAnB;AAAA,EAAkCC;AAAlC;AAAA,EAAoDC;AAApD;AAAA;AAAA;AAAmF;AACxF,MAAIF,KAAK,CAACC,QAAD,CAAT,EAAqB;AACnB,WAAO,IAAIE,KAAJ,wBAA0BF,QAA1B,wBAAgDC,aAAhD,8CAAP;AACD;AACF;;ACxBD,IAAME,QAAQ,GAAG,CAAC,KAAD,EAAQ,QAAR,EAAkB,GAAlB,EAAuB,IAAvB,CAAjB;AACO,SAASC,SAAT;AAAA;AAAqD;AAAA;;AAAA,MAAlCC;AAAkC;AAAA,wEAArB,WAAqB;AAC1D;AACA;AACA,MAAI,OAAOC,MAAP,KAAkB,WAAtB,EAAmC,OAAO,EAAP,CAHuB,CAK1D;AACA;;AACA,MAAMC,KAAK,uBAAGD,MAAM,CAACE,QAAV,8EAAG,iBAAiBC,eAApB,0DAAG,sBAAkCF,KAAhD;AACA,MAAI,CAACA,KAAL,EAAY,OAAO,EAAP;AAEZ,MAAIF,IAAI,IAAIE,KAAZ,EAAmB,OAAO,EAAP;;AAEnB,OAAK,IAAIxB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoB,QAAQ,CAACnB,MAA7B,EAAqCD,CAAC,EAAtC,EAA0C;AACxC,QAAI2B,kBAAkB,CAACL,IAAD,EAAOF,QAAQ,CAACpB,CAAD,CAAf,CAAlB,IAAyCwB,KAA7C,EAAoD,OAAOJ,QAAQ,CAACpB,CAAD,CAAf;AACrD;;AAED,SAAO,EAAP;AACD;AAEM,SAAS2B,kBAAT,CAA4BL;AAA5B;AAAA,EAA0CM;AAA1C;AAAA;AAAA;AAAkE;AACvE,SAAOA,MAAM,aAAMA,MAAN,SAAeC,gBAAgB,CAACP,IAAD,CAA/B,IAA0CA,IAAvD;AACD;AAEM,SAASQ,oBAAT,CAA8BR;AAA9B;AAAA,EAA4CM;AAA5C;AAAA;AAAA;AAAoE;AACzE,SAAOA,MAAM,cAAOA,MAAM,CAACG,WAAP,EAAP,cAA+BT,IAA/B,IAAwCA,IAArD;AACD;;AAED,SAASO,gBAAT,CAA0BG;AAA1B;AAAA;AAAA;AAA+C;AAC7C,MAAIC,GAAG,GAAG,EAAV;AACA,MAAIC,gBAAgB,GAAG,IAAvB;;AACA,OAAK,IAAIlC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgC,GAAG,CAAC/B,MAAxB,EAAgCD,CAAC,EAAjC,EAAqC;AACnC,QAAIkC,gBAAJ,EAAsB;AACpBD,MAAAA,GAAG,IAAID,GAAG,CAAChC,CAAD,CAAH,CAAOmC,WAAP,EAAP;AACAD,MAAAA,gBAAgB,GAAG,KAAnB;AACD,KAHD,MAGO,IAAIF,GAAG,CAAChC,CAAD,CAAH,KAAW,GAAf,EAAoB;AACzBkC,MAAAA,gBAAgB,GAAG,IAAnB;AACD,KAFM,MAEA;AACLD,MAAAA,GAAG,IAAID,GAAG,CAAChC,CAAD,CAAV;AACD;AACF;;AACD,SAAOiC,GAAP;AACD,EAED;AACA;AACA;;;AACA,oDAAgBZ,SAAS,EAAE;;;;;;;;AC/C3B;AACA;;;AAIA,IAAIgB,mBAAmB,GAAG,EAA1B;AACO,SAASC,eAAT,CAAyBC;AAAzB;AAAA,EAAmCC;AAAnC;AAAA;AAAA;AAA8D;AACnE,MAAI,CAACH,mBAAL,EAA0B;AACxBA,IAAAA,mBAAmB,GAAGxC,WAAW,CAAC,CAChC,SADgC,EAEhC,uBAFgC,EAGhC,oBAHgC,EAIhC,mBAJgC,EAKhC,kBALgC,CAAD,EAM9B,UAAS4C,MAAT,EAAgB;AACjB;AACA,aAAOtC,UAAU,CAACoC,EAAE,CAACE,MAAD,CAAH,CAAjB;AACD,KATgC,CAAjC;AAUD,GAZkE,CAcnE;AACA;;;AACA,MAAI,CAACtC,UAAU,CAACoC,EAAE,CAACF,mBAAD,CAAH,CAAf,EAA0C,OAAO,KAAP,CAhByB,CAkBnE;;AACA,SAAOE,EAAE,CAACF,mBAAD,CAAF,CAAwBG,QAAxB,CAAP;AACD,EAED;;AACO,SAASE,2BAAT,CAAqCH;AAArC;AAAA,EAA+CC;AAA/C;AAAA,EAAiEG;AAAjE;AAAA;AAAA;AAA0F;AAC/F,MAAIC,IAAI,GAAGL,EAAX;;AACA,KAAG;AACD,QAAID,eAAe,CAACM,IAAD,EAAOJ,QAAP,CAAnB,EAAqC,OAAO,IAAP;AACrC,QAAII,IAAI,KAAKD,QAAb,EAAuB,OAAO,KAAP;AACvBC,IAAAA,IAAI,GAAGA,IAAI,CAACC,UAAZ;AACD,GAJD,QAISD,IAJT;;AAMA,SAAO,KAAP;AACD;AAEM,SAASE,QAAT,CAAkBP;AAAlB;AAAA,EAA6BQ;AAA7B;AAAA,EAA4CC;AAA5C;AAAA,EAA+DC;AAA/D;AAAA;AAAA;AAA4F;AACjG,MAAI,CAACV,EAAL,EAAS;;AACT,MAAMW,OAAO;AAAIC,IAAAA,OAAO,EAAE;AAAb,KAAsBF,YAAtB,CAAb,CAFiG,CAGjG;;;AACA,MAAIV,EAAE,CAACa,gBAAP,EAAyB;AACvBb,IAAAA,EAAE,CAACa,gBAAH,CAAoBL,KAApB,EAA2BC,OAA3B,EAAoCE,OAApC;AACD,GAFD,MAEO,IAAIX,EAAE,CAACc,WAAP,EAAoB;AACzBd,IAAAA,EAAE,CAACc,WAAH,CAAe,OAAON,KAAtB,EAA6BC,OAA7B;AACD,GAFM,MAEA;AACL;AACAT,IAAAA,EAAE,CAAC,OAAOQ,KAAR,CAAF,GAAmBC,OAAnB;AACD;AACF;AAEM,SAASM,WAAT,CAAqBf;AAArB;AAAA,EAAgCQ;AAAhC;AAAA,EAA+CC;AAA/C;AAAA,EAAkEC;AAAlE;AAAA;AAAA;AAA+F;AACpG,MAAI,CAACV,EAAL,EAAS;;AACT,MAAMW,OAAO;AAAIC,IAAAA,OAAO,EAAE;AAAb,KAAsBF,YAAtB,CAAb,CAFoG,CAGpG;;;AACA,MAAIV,EAAE,CAACgB,mBAAP,EAA4B;AAC1BhB,IAAAA,EAAE,CAACgB,mBAAH,CAAuBR,KAAvB,EAA8BC,OAA9B,EAAuCE,OAAvC;AACD,GAFD,MAEO,IAAIX,EAAE,CAACiB,WAAP,EAAoB;AACzBjB,IAAAA,EAAE,CAACiB,WAAH,CAAe,OAAOT,KAAtB,EAA6BC,OAA7B;AACD,GAFM,MAEA;AACL;AACAT,IAAAA,EAAE,CAAC,OAAOQ,KAAR,CAAF,GAAmB,IAAnB;AACD;AACF;AAEM,SAASU,kBAAT,CAAqBb;AAArB;AAAA;AAAA;AAAgD;AACrD;AACA;AACA,MAAIc,MAAM,GAAGd,IAAI,CAACe,YAAlB;AACA,MAAMC,aAAa,GAAGhB,IAAI,CAACiB,aAAL,CAAmBC,WAAnB,CAA+BC,gBAA/B,CAAgDnB,IAAhD,CAAtB;AACAc,EAAAA,MAAM,IAAI9C,SAAG,CAACgD,aAAa,CAACI,cAAf,CAAb;AACAN,EAAAA,MAAM,IAAI9C,SAAG,CAACgD,aAAa,CAACK,iBAAf,CAAb;AACA,SAAOP,MAAP;AACD;AAEM,SAASQ,iBAAT,CAAoBtB;AAApB;AAAA;AAAA;AAA+C;AACpD;AACA;AACA,MAAIuB,KAAK,GAAGvB,IAAI,CAACwB,WAAjB;AACA,MAAMR,aAAa,GAAGhB,IAAI,CAACiB,aAAL,CAAmBC,WAAnB,CAA+BC,gBAA/B,CAAgDnB,IAAhD,CAAtB;AACAuB,EAAAA,KAAK,IAAIvD,SAAG,CAACgD,aAAa,CAACS,eAAf,CAAZ;AACAF,EAAAA,KAAK,IAAIvD,SAAG,CAACgD,aAAa,CAACU,gBAAf,CAAZ;AACA,SAAOH,KAAP;AACD;AACM,SAASI,kBAAT,CAAqB3B;AAArB;AAAA;AAAA;AAAgD;AACrD,MAAIc,MAAM,GAAGd,IAAI,CAACe,YAAlB;AACA,MAAMC,aAAa,GAAGhB,IAAI,CAACiB,aAAL,CAAmBC,WAAnB,CAA+BC,gBAA/B,CAAgDnB,IAAhD,CAAtB;AACAc,EAAAA,MAAM,IAAI9C,SAAG,CAACgD,aAAa,CAACY,UAAf,CAAb;AACAd,EAAAA,MAAM,IAAI9C,SAAG,CAACgD,aAAa,CAACa,aAAf,CAAb;AACA,SAAOf,MAAP;AACD;AAEM,SAASgB,iBAAT,CAAoB9B;AAApB;AAAA;AAAA;AAA+C;AACpD,MAAIuB,KAAK,GAAGvB,IAAI,CAACwB,WAAjB;AACA,MAAMR,aAAa,GAAGhB,IAAI,CAACiB,aAAL,CAAmBC,WAAnB,CAA+BC,gBAA/B,CAAgDnB,IAAhD,CAAtB;AACAuB,EAAAA,KAAK,IAAIvD,SAAG,CAACgD,aAAa,CAACe,WAAf,CAAZ;AACAR,EAAAA,KAAK,IAAIvD,SAAG,CAACgD,aAAa,CAACgB,YAAf,CAAZ;AACA,SAAOT,KAAP;AACD;;AAtGD;AACA;;AA2GA;AACO,SAASU,kBAAT,CAA4BC;AAA5B;AAAA,EAAkDC;AAAlD;AAAA,EAA6EC;AAA7E;AAAA;AAAA;AAA6G;AAClH,MAAMC,MAAM,GAAGF,YAAY,KAAKA,YAAY,CAAClB,aAAb,CAA2BqB,IAA3D;AACA,MAAMC,gBAAgB,GAAGF,MAAM,GAAG;AAACG,IAAAA,IAAI,EAAE,CAAP;AAAUC,IAAAA,GAAG,EAAE;AAAf,GAAH,GAAuBN,YAAY,CAACO,qBAAb,EAAtD;AAEA,MAAMC,CAAC,GAAG,CAACT,GAAG,CAACU,OAAJ,GAAcT,YAAY,CAACU,UAA3B,GAAwCN,gBAAgB,CAACC,IAA1D,IAAkEJ,KAA5E;AACA,MAAMU,CAAC,GAAG,CAACZ,GAAG,CAACa,OAAJ,GAAcZ,YAAY,CAACa,SAA3B,GAAuCT,gBAAgB,CAACE,GAAzD,IAAgEL,KAA1E;AAEA,SAAO;AAACO,IAAAA,CAAC,EAADA,CAAD;AAAIG,IAAAA,CAAC,EAADA;AAAJ,GAAP;AACD;AAEM,SAASG,kBAAT,CAA4BC;AAA5B;AAAA,EAAyDC;AAAzD;AAAA;AAAA;AAAgH;AACrH,MAAMC,WAAW,GAAGC,cAAc,CAACH,UAAD,EAAaC,cAAb,EAA6B,IAA7B,CAAlC;AACA,6BAASpE,kBAAkB,CAAC,WAAD,EAAcS,eAAd,CAA3B,EAA0D4D,WAA1D;AACD;AAEM,SAASE,kBAAT,CAA4BJ;AAA5B;AAAA,EAAyDC;AAAzD;AAAA;AAAA;AAAgH;AACrH,MAAMC,WAAW,GAAGC,cAAc,CAACH,UAAD,EAAaC,cAAb,EAA6B,EAA7B,CAAlC;AACA,SAAOC,WAAP;AACD;AACM,SAASC,cAAT,QAAiDF;AAAjD;AAAA,EAAgGI;AAAhG;AAAA;AAAA;AAA4H;AAAA,MAAnGZ,CAAmG,SAAnGA,CAAmG;AAAA,MAAhGG,CAAgG,SAAhGA,CAAgG;AACjI,MAAIM,WAAW,uBAAgBT,CAAhB,SAAoBY,UAApB,cAAkCT,CAAlC,SAAsCS,UAAtC,MAAf;;AACA,MAAIJ,cAAJ,EAAoB;AAClB,QAAMK,QAAQ,aAAO,OAAOL,cAAc,CAACR,CAAtB,KAA4B,QAA7B,GAAyCQ,cAAc,CAACR,CAAxD,GAA4DQ,cAAc,CAACR,CAAf,GAAmBY,UAArF,CAAd;AACA,QAAME,QAAQ,aAAO,OAAON,cAAc,CAACL,CAAtB,KAA4B,QAA7B,GAAyCK,cAAc,CAACL,CAAxD,GAA4DK,cAAc,CAACL,CAAf,GAAmBS,UAArF,CAAd;AACAH,IAAAA,WAAW,GAAG,oBAAaI,QAAb,eAA0BC,QAA1B,SAAwCL,WAAtD;AACD;;AACD,SAAOA,WAAP;AACD;AAEM,SAASM,QAAT,CAAkBC;AAAlB;AAAA,EAAsCC;AAAtC;AAAA;AAAA;AAA+F;AACpG,SAAQD,CAAC,CAACE,aAAF,IAAmB5G,WAAW,CAAC0G,CAAC,CAACE,aAAH,EAAkB,UAAAC,CAAC;AAAA,WAAIF,UAAU,KAAKE,CAAC,CAACF,UAArB;AAAA,GAAnB,CAA/B,IACCD,CAAC,CAACI,cAAF,IAAoB9G,WAAW,CAAC0G,CAAC,CAACI,cAAH,EAAmB,UAAAD,CAAC;AAAA,WAAIF,UAAU,KAAKE,CAAC,CAACF,UAArB;AAAA,GAApB,CADvC;AAED;AAEM,SAASI,kBAAT,CAA4BL;AAA5B;AAAA;AAAA;AAAyD;AAC9D,MAAIA,CAAC,CAACE,aAAF,IAAmBF,CAAC,CAACE,aAAF,CAAgB,CAAhB,CAAvB,EAA2C,OAAOF,CAAC,CAACE,aAAF,CAAgB,CAAhB,EAAmBD,UAA1B;AAC3C,MAAID,CAAC,CAACI,cAAF,IAAoBJ,CAAC,CAACI,cAAF,CAAiB,CAAjB,CAAxB,EAA6C,OAAOJ,CAAC,CAACI,cAAF,CAAiB,CAAjB,EAAoBH,UAA3B;AAC9C,EAED;AACA;AACA;AAEA;;AACO,SAASK,mBAAT,CAA6BC;AAA7B;AAAA,EAA6C;AAClD,MAAI,CAACA,GAAL,EAAU;AACV,MAAIC,OAAO,GAAGD,GAAG,CAACE,cAAJ,CAAmB,0BAAnB,CAAd;;AACA,MAAI,CAACD,OAAL,EAAc;AACZA,IAAAA,OAAO,GAAGD,GAAG,CAACG,aAAJ,CAAkB,OAAlB,CAAV;AACAF,IAAAA,OAAO,CAACG,IAAR,GAAe,UAAf;AACAH,IAAAA,OAAO,CAACI,EAAR,GAAa,0BAAb;AACAJ,IAAAA,OAAO,CAACK,SAAR,GAAoB,4EAApB;AACAL,IAAAA,OAAO,CAACK,SAAR,IAAqB,uEAArB;AACAN,IAAAA,GAAG,CAACO,oBAAJ,CAAyB,MAAzB,EAAiC,CAAjC,EAAoCC,WAApC,CAAgDP,OAAhD;AACD;;AACD,MAAID,GAAG,CAAC5B,IAAR,EAAcqC,YAAY,CAACT,GAAG,CAAC5B,IAAL,EAAW,uCAAX,CAAZ;AACf;AAEM,SAASsC,sBAAT,CAAgCV;AAAhC;AAAA,EAAgD;AACrD,MAAI,CAACA,GAAL,EAAU;;AACV,MAAI;AACF,QAAIA,GAAG,CAAC5B,IAAR,EAAcuC,eAAe,CAACX,GAAG,CAAC5B,IAAL,EAAW,uCAAX,CAAf,CADZ,CAEF;;AACA,QAAI4B,GAAG,CAACY,SAAR,EAAmB;AACjB;AACAZ,MAAAA,GAAG,CAACY,SAAJ,CAAcC,KAAd;AACD,KAHD,MAGO;AACL;AACA;AACA,UAAMD,SAAS,GAAG,CAACZ,GAAG,CAAChD,WAAJ,IAAmBvC,MAApB,EAA4BqG,YAA5B,EAAlB;;AACA,UAAIF,SAAS,IAAIA,SAAS,CAACR,IAAV,KAAmB,OAApC,EAA6C;AAC3CQ,QAAAA,SAAS,CAACG,eAAV;AACD;AACF;AACF,GAdD,CAcE,OAAOtB,CAAP,EAAU,CACV;AACD;AACF;AAEM,SAASgB,YAAT,CAAsBhF;AAAtB;AAAA,EAAuCuF;AAAvC;AAAA,EAA0D;AAC/D,MAAIvF,EAAE,CAACwF,SAAP,EAAkB;AAChBxF,IAAAA,EAAE,CAACwF,SAAH,CAAaC,GAAb,CAAiBF,SAAjB;AACD,GAFD,MAEO;AACL,QAAI,CAACvF,EAAE,CAACuF,SAAH,CAAaG,KAAb,CAAmB,IAAIC,MAAJ,oBAAuBJ,SAAvB,aAAnB,CAAL,EAAqE;AACnEvF,MAAAA,EAAE,CAACuF,SAAH,eAAoBA,SAApB;AACD;AACF;AACF;AAEM,SAASL,eAAT,CAAyBlF;AAAzB;AAAA,EAA0CuF;AAA1C;AAAA,EAA6D;AAClE,MAAIvF,EAAE,CAACwF,SAAP,EAAkB;AAChBxF,IAAAA,EAAE,CAACwF,SAAH,CAAaI,MAAb,CAAoBL,SAApB;AACD,GAFD,MAEO;AACLvF,IAAAA,EAAE,CAACuF,SAAH,GAAevF,EAAE,CAACuF,SAAH,CAAaM,OAAb,CAAqB,IAAIF,MAAJ,oBAAuBJ,SAAvB,cAA2C,GAA3C,CAArB,EAAsE,EAAtE,CAAf;AACD;AACF;;AC3MD;AACA;;;;;;;AAMO,SAASO,gBAAT,CAA0BC;AAA1B;AAAA,EAAgD/C;AAAhD;AAAA,EAA2DG;AAA3D;AAAA;AAAA;AAAwF;AAC7F;AACA,MAAI,CAAC4C,SAAS,CAACtH,KAAV,CAAgBuH,MAArB,EAA6B,OAAO,CAAChD,CAAD,EAAIG,CAAJ,CAAP,CAFgE,CAI7F;;AACA,MAAK6C,MAAL,GAAeD,SAAS,CAACtH,KAAzB,CAAKuH,MAAL;AACAA,EAAAA,MAAM,GAAG,OAAOA,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsCC,WAAW,CAACD,MAAD,CAA1D;AACA,MAAM3F,IAAI,GAAG6F,WAAW,CAACH,SAAD,CAAxB;;AAEA,MAAI,OAAOC,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,QAAO1E,aAAP,GAAwBjB,IAAxB,CAAOiB,aAAP;AACA,QAAM6E,WAAW,GAAG7E,aAAa,CAACC,WAAlC;AACA,QAAI6E,SAAJ;;AACA,QAAIJ,MAAM,KAAK,QAAf,EAAyB;AACvBI,MAAAA,SAAS,GAAG/F,IAAI,CAACC,UAAjB;AACD,KAFD,MAEO;AACL8F,MAAAA,SAAS,GAAG9E,aAAa,CAAC+E,aAAd,CAA4BL,MAA5B,CAAZ;AACD;;AACD,QAAI,EAAEI,SAAS,YAAYD,WAAW,CAACG,WAAnC,CAAJ,EAAqD;AACnD,YAAM,IAAI1H,KAAJ,CAAU,sBAAsBoH,MAAtB,GAA+B,8BAAzC,CAAN;AACD;;AACD,QAAMO;AAAwB;AAAA,MAAGH,SAAjC,CAZ8B,CAYc;;AAC5C,QAAMI,SAAS,GAAGL,WAAW,CAAC3E,gBAAZ,CAA6BnB,IAA7B,CAAlB;AACA,QAAMoG,cAAc,GAAGN,WAAW,CAAC3E,gBAAZ,CAA6B+E,WAA7B,CAAvB,CAd8B,CAe9B;;AACAP,IAAAA,MAAM,GAAG;AACPnD,MAAAA,IAAI,EAAE,CAACxC,IAAI,CAACqG,UAAN,GAAmBrI,SAAG,CAACoI,cAAc,CAACrE,WAAhB,CAAtB,GAAqD/D,SAAG,CAACmI,SAAS,CAACG,UAAX,CADvD;AAEP7D,MAAAA,GAAG,EAAE,CAACzC,IAAI,CAACuG,SAAN,GAAkBvI,SAAG,CAACoI,cAAc,CAACxE,UAAhB,CAArB,GAAmD5D,SAAG,CAACmI,SAAS,CAACK,SAAX,CAFpD;AAGPC,MAAAA,KAAK,EAAE3E,iBAAU,CAACoE,WAAD,CAAV,GAA0B5E,iBAAU,CAACtB,IAAD,CAApC,GAA6CA,IAAI,CAACqG,UAAlD,GACLrI,SAAG,CAACoI,cAAc,CAACpE,YAAhB,CADE,GAC8BhE,SAAG,CAACmI,SAAS,CAACO,WAAX,CAJjC;AAKPC,MAAAA,MAAM,EAAEhF,kBAAW,CAACuE,WAAD,CAAX,GAA2BrF,kBAAW,CAACb,IAAD,CAAtC,GAA+CA,IAAI,CAACuG,SAApD,GACNvI,SAAG,CAACoI,cAAc,CAACvE,aAAhB,CADG,GAC8B7D,SAAG,CAACmI,SAAS,CAACS,YAAX;AANlC,KAAT;AAQD,GAjC4F,CAmC7F;;;AACA,MAAI/I,KAAK,CAAC8H,MAAM,CAACc,KAAR,CAAT,EAAyB9D,CAAC,GAAGkE,IAAI,CAACC,GAAL,CAASnE,CAAT,EAAYgD,MAAM,CAACc,KAAnB,CAAJ;AACzB,MAAI5I,KAAK,CAAC8H,MAAM,CAACgB,MAAR,CAAT,EAA0B7D,CAAC,GAAG+D,IAAI,CAACC,GAAL,CAAShE,CAAT,EAAY6C,MAAM,CAACgB,MAAnB,CAAJ,CArCmE,CAuC7F;;AACA,MAAI9I,KAAK,CAAC8H,MAAM,CAACnD,IAAR,CAAT,EAAwBG,CAAC,GAAGkE,IAAI,CAACE,GAAL,CAASpE,CAAT,EAAYgD,MAAM,CAACnD,IAAnB,CAAJ;AACxB,MAAI3E,KAAK,CAAC8H,MAAM,CAAClD,GAAR,CAAT,EAAuBK,CAAC,GAAG+D,IAAI,CAACE,GAAL,CAASjE,CAAT,EAAY6C,MAAM,CAAClD,GAAnB,CAAJ;AAEvB,SAAO,CAACE,CAAD,EAAIG,CAAJ,CAAP;AACD;AAEM,SAASkE,UAAT,CAAoBC;AAApB;AAAA,EAA4CC;AAA5C;AAAA,EAA8DC;AAA9D;AAAA;AAAA;AAAkG;AACvG,MAAMxE,CAAC,GAAGkE,IAAI,CAACO,KAAL,CAAWF,QAAQ,GAAGD,IAAI,CAAC,CAAD,CAA1B,IAAiCA,IAAI,CAAC,CAAD,CAA/C;AACA,MAAMnE,CAAC,GAAG+D,IAAI,CAACO,KAAL,CAAWD,QAAQ,GAAGF,IAAI,CAAC,CAAD,CAA1B,IAAiCA,IAAI,CAAC,CAAD,CAA/C;AACA,SAAO,CAACtE,CAAD,EAAIG,CAAJ,CAAP;AACD;AAEM,SAASuE,QAAT,CAAkB3B;AAAlB;AAAA;AAAA;AAAiD;AACtD,SAAOA,SAAS,CAACtH,KAAV,CAAgBkJ,IAAhB,KAAyB,MAAzB,IAAmC5B,SAAS,CAACtH,KAAV,CAAgBkJ,IAAhB,KAAyB,GAAnE;AACD;AAEM,SAASC,QAAT,CAAkB7B;AAAlB;AAAA;AAAA;AAAiD;AACtD,SAAOA,SAAS,CAACtH,KAAV,CAAgBkJ,IAAhB,KAAyB,MAAzB,IAAmC5B,SAAS,CAACtH,KAAV,CAAgBkJ,IAAhB,KAAyB,GAAnE;AACD,EAED;;AACO,SAASE,kBAAT,CAA4B7D;AAA5B;AAAA,EAAgD8D;AAAhD;AAAA,EAA0EC;AAA1E;AAAA;AAAA;AAA0H;AAC/H,MAAMC,QAAQ,GAAG,OAAOF,eAAP,KAA2B,QAA3B,GAAsC/D,QAAQ,CAACC,CAAD,EAAI8D,eAAJ,CAA9C,GAAqE,IAAtF;AACA,MAAI,OAAOA,eAAP,KAA2B,QAA3B,IAAuC,CAACE,QAA5C,EAAsD,OAAO,IAAP,CAFyE,CAE5D;;AACnE,MAAM3H,IAAI,GAAG6F,WAAW,CAAC6B,aAAD,CAAxB,CAH+H,CAI/H;;AACA,MAAMvF,YAAY,GAAGuF,aAAa,CAACtJ,KAAd,CAAoB+D,YAApB,IAAoCnC,IAAI,CAACmC,YAAzC,IAAyDnC,IAAI,CAACiB,aAAL,CAAmBqB,IAAjG;AACA,SAAOL,kBAAkB,CAAC0F,QAAQ,IAAIhE,CAAb,EAAgBxB,YAAhB,EAA8BuF,aAAa,CAACtJ,KAAd,CAAoBgE,KAAlD,CAAzB;AACD,EAED;;AACO,SAASwF,cAAT,CAAwBlC;AAAxB;AAAA,EAAkD/C;AAAlD;AAAA,EAA6DG;AAA7D;AAAA;AAAA;AAAuF;AAC5F,MAAM+E,KAAK,GAAGnC,SAAS,CAACmC,KAAxB;AACA,MAAMC,OAAO,GAAG,CAACjK,KAAK,CAACgK,KAAK,CAACE,KAAP,CAAtB;AACA,MAAM/H,IAAI,GAAG6F,WAAW,CAACH,SAAD,CAAxB;;AAEA,MAAIoC,OAAJ,EAAa;AACX;AACA,WAAO;AACL9H,MAAAA,IAAI,EAAJA,IADK;AAELgI,MAAAA,MAAM,EAAE,CAFH;AAEMC,MAAAA,MAAM,EAAE,CAFd;AAGLF,MAAAA,KAAK,EAAEpF,CAHF;AAGKuF,MAAAA,KAAK,EAAEpF,CAHZ;AAILH,MAAAA,CAAC,EAADA,CAJK;AAIFG,MAAAA,CAAC,EAADA;AAJE,KAAP;AAMD,GARD,MAQO;AACL;AACA,WAAO;AACL9C,MAAAA,IAAI,EAAJA,IADK;AAELgI,MAAAA,MAAM,EAAErF,CAAC,GAAGkF,KAAK,CAACE,KAFb;AAEoBE,MAAAA,MAAM,EAAEnF,CAAC,GAAG+E,KAAK,CAACK,KAFtC;AAGLH,MAAAA,KAAK,EAAEF,KAAK,CAACE,KAHR;AAGeG,MAAAA,KAAK,EAAEL,KAAK,CAACK,KAH5B;AAILvF,MAAAA,CAAC,EAADA,CAJK;AAIFG,MAAAA,CAAC,EAADA;AAJE,KAAP;AAMD;AACF,EAED;;AACO,SAASqF,mBAAT,CAA6BzC;AAA7B;AAAA,EAAmD0C;AAAnD;AAAA;AAAA;AAA2F;AAChG,MAAMhG,KAAK,GAAGsD,SAAS,CAACtH,KAAV,CAAgBgE,KAA9B;AACA,SAAO;AACLpC,IAAAA,IAAI,EAAEoI,QAAQ,CAACpI,IADV;AAEL2C,IAAAA,CAAC,EAAE+C,SAAS,CAACmC,KAAV,CAAgBlF,CAAhB,GAAqByF,QAAQ,CAACJ,MAAT,GAAkB5F,KAFrC;AAGLU,IAAAA,CAAC,EAAE4C,SAAS,CAACmC,KAAV,CAAgB/E,CAAhB,GAAqBsF,QAAQ,CAACH,MAAT,GAAkB7F,KAHrC;AAIL4F,IAAAA,MAAM,EAAGI,QAAQ,CAACJ,MAAT,GAAkB5F,KAJtB;AAKL6F,IAAAA,MAAM,EAAGG,QAAQ,CAACH,MAAT,GAAkB7F,KALtB;AAML2F,IAAAA,KAAK,EAAErC,SAAS,CAACmC,KAAV,CAAgBlF,CANlB;AAOLuF,IAAAA,KAAK,EAAExC,SAAS,CAACmC,KAAV,CAAgB/E;AAPlB,GAAP;AASD,EAED;;AACA,SAAS8C,WAAT,CAAqBD;AAArB;AAAA;AAAA;AAA6C;AAC3C,SAAO;AACLnD,IAAAA,IAAI,EAAEmD,MAAM,CAACnD,IADR;AAELC,IAAAA,GAAG,EAAEkD,MAAM,CAAClD,GAFP;AAGLgE,IAAAA,KAAK,EAAEd,MAAM,CAACc,KAHT;AAILE,IAAAA,MAAM,EAAEhB,MAAM,CAACgB;AAJV,GAAP;AAMD;;AAED,SAASd,WAAT,CAAqBH;AAArB;AAAA;AAAA;AAAwE;AACtE,MAAM1F,IAAI,GAAG0F,SAAS,CAACG,WAAV,EAAb;;AACA,MAAI,CAAC7F,IAAL,EAAW;AACT,UAAM,IAAIzB,KAAJ,CAAU,0CAAV,CAAN;AACD,GAJqE,CAKtE;;;AACA,SAAOyB,IAAP;AACD;;ACrID;AACe,SAASqI,GAAT,GAA2B;AAAA;;AACxC,aAAiC;AAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACHD;AACA;AACA;AACA;AAEA;AACA;AACA;;;;;AAKA;AACA,IAAMK,SAAS,GAAG;AAChBC,EAAAA,KAAK,EAAE;AACLC,IAAAA,KAAK,EAAE,YADF;AAELC,IAAAA,IAAI,EAAE,WAFD;AAGLC,IAAAA,IAAI,EAAE;AAHD,GADS;AAMhBC,EAAAA,KAAK,EAAE;AACLH,IAAAA,KAAK,EAAE,WADF;AAELC,IAAAA,IAAI,EAAE,WAFD;AAGLC,IAAAA,IAAI,EAAE;AAHD;AANS,CAAlB,EAaA;;AACA,IAAIE,YAAY,GAAGN,SAAS,CAACK,KAA7B;;AA5BA;AACA;AACA;AACA;AACA;;;AAJA;AACA;AACA;AACA;AACA;;;;;;;;;AAJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AA+DA;AACA;AACA;AACA;AACA;AACA;IAEqBE;;;;;;;;;;;;;;;;yEAwJS;AAC1BC,MAAAA,QAAQ,EAAE,KADgB;AAE1B;AACAnB,MAAAA,KAAK,EAAEoB,GAHmB;AAGdjB,MAAAA,KAAK,EAAEiB,GAHO;AAI1B1B,MAAAA,eAAe,EAAE;AAJS;;2EAOT;;mFAkC8B,UAAC9D,CAAD,EAAO;AACtD;AACA,YAAKvF,KAAL,CAAWgL,WAAX,CAAuBzF,CAAvB,EAFsD,CAItD;;;AACA,UAAI,CAAC,MAAKvF,KAAL,CAAWiL,aAAZ,IAA6B,OAAO1F,CAAC,CAAC2F,MAAT,KAAoB,QAAjD,IAA6D3F,CAAC,CAAC2F,MAAF,KAAa,CAA9E,EAAiF,OAAO,KAAP,CAL3B,CAOtD;;AACA,UAAMC,QAAQ,GAAG,MAAK1D,WAAL,EAAjB;;AACA,UAAI,CAAC0D,QAAD,IAAa,CAACA,QAAQ,CAACtI,aAAvB,IAAwC,CAACsI,QAAQ,CAACtI,aAAT,CAAuBqB,IAApE,EAA0E;AACxE,cAAM,IAAI/D,KAAJ,CAAU,2CAAV,CAAN;AACD;;AACD,UAAO0C,aAAP,GAAwBsI,QAAxB,CAAOtI,aAAP,CAZsD,CActD;;AACA,UAAI,MAAK7C,KAAL,CAAWoL,QAAX,IACD,EAAE7F,CAAC,CAAC8F,MAAF,YAAoBxI,aAAa,CAACC,WAAd,CAA0BwI,IAAhD,CADC,IAED,MAAKtL,KAAL,CAAWuL,MAAX,IAAqB,CAAC7J,2BAA2B,CAAC6D,CAAC,CAAC8F,MAAH,EAAW,MAAKrL,KAAL,CAAWuL,MAAtB,EAA8BJ,QAA9B,CAFhD,IAGD,MAAKnL,KAAL,CAAWwL,MAAX,IAAqB9J,2BAA2B,CAAC6D,CAAC,CAAC8F,MAAH,EAAW,MAAKrL,KAAL,CAAWwL,MAAtB,EAA8BL,QAA9B,CAHnD,EAG6F;AAC3F;AACD,OApBqD,CAsBtD;AACA;;;AACA,UAAI5F,CAAC,CAACW,IAAF,KAAW,YAAf,EAA6BX,CAAC,CAACkG,cAAF,GAxByB,CA0BtD;AACA;AACA;;AACA,UAAMpC,eAAe,GAAGzD,kBAAkB,CAACL,CAAD,CAA1C;;AACA,YAAKmG,QAAL,CAAc;AAACrC,QAAAA,eAAe,EAAfA;AAAD,OAAd,EA9BsD,CAgCtD;;;AACA,UAAMsC,QAAQ,GAAGvC,kBAAkB,CAAC7D,CAAD,EAAI8D,eAAJ,gCAAnC;AACA,UAAIsC,QAAQ,IAAI,IAAhB,EAAsB,OAlCgC,CAkCxB;;AAC9B,UAAOpH,CAAP,GAAeoH,QAAf,CAAOpH,CAAP;AAAA,UAAUG,CAAV,GAAeiH,QAAf,CAAUjH,CAAV,CAnCsD,CAqCtD;;AACA,UAAMkH,SAAS,GAAGpC,cAAc,gCAAOjF,CAAP,EAAUG,CAAV,CAAhC;AAEAuF,MAAAA,GAAG,CAAC,oCAAD,EAAuC2B,SAAvC,CAAH,CAxCsD,CA0CtD;;AACA3B,MAAAA,GAAG,CAAC,SAAD,EAAY,MAAKjK,KAAL,CAAW6L,OAAvB,CAAH;;AACA,UAAMC,YAAY,GAAG,MAAK9L,KAAL,CAAW6L,OAAX,CAAmBtG,CAAnB,EAAsBqG,SAAtB,CAArB;;AACA,UAAIE,YAAY,KAAK,KAAjB,IAA0B,MAAKC,OAAL,KAAiB,KAA/C,EAAsD,OA7CA,CA+CtD;AACA;;AACA,UAAI,MAAK/L,KAAL,CAAWgM,oBAAf,EAAqCnG,mBAAmB,CAAChD,aAAD,CAAnB,CAjDiB,CAmDtD;AACA;AACA;;AACA,YAAK6I,QAAL,CAAc;AACZZ,QAAAA,QAAQ,EAAE,IADE;AAGZnB,QAAAA,KAAK,EAAEpF,CAHK;AAIZuF,QAAAA,KAAK,EAAEpF;AAJK,OAAd,EAtDsD,CA6DtD;AACA;AACA;;;AACA5C,MAAAA,QAAQ,CAACe,aAAD,EAAgB+H,YAAY,CAACH,IAA7B,EAAmC,MAAKwB,UAAxC,CAAR;AACAnK,MAAAA,QAAQ,CAACe,aAAD,EAAgB+H,YAAY,CAACF,IAA7B,EAAmC,MAAKwB,cAAxC,CAAR;AACD;;8EAE2C,UAAC3G,CAAD,EAAO;AAEjD;AACA,UAAMoG,QAAQ,GAAGvC,kBAAkB,CAAC7D,CAAD,EAAI,MAAKkE,KAAL,CAAWJ,eAAf,gCAAnC;AACA,UAAIsC,QAAQ,IAAI,IAAhB,EAAsB;AACtB,UAAKpH,CAAL,GAAaoH,QAAb,CAAKpH,CAAL;AAAA,UAAQG,CAAR,GAAaiH,QAAb,CAAQjH,CAAR,CALiD,CAOjD;;AACA,UAAIyH,KAAK,CAACC,OAAN,CAAc,MAAKpM,KAAL,CAAW6I,IAAzB,CAAJ,EAAoC;AAClC,YAAIe,MAAM,GAAGrF,CAAC,GAAG,MAAKkF,KAAL,CAAWE,KAA5B;AAAA,YAAmCE,MAAM,GAAGnF,CAAC,GAAG,MAAK+E,KAAL,CAAWK,KAA3D;;AADkC,0BAEflB,UAAU,CAAC,MAAK5I,KAAL,CAAW6I,IAAZ,EAAkBe,MAAlB,EAA0BC,MAA1B,CAFK;;AAAA;;AAEjCD,QAAAA,MAFiC;AAEzBC,QAAAA,MAFyB;AAGlC,YAAI,CAACD,MAAD,IAAW,CAACC,MAAhB,EAAwB,OAHU,CAGF;;AAChCtF,QAAAA,CAAC,GAAG,MAAKkF,KAAL,CAAWE,KAAX,GAAmBC,MAAvB,EAA+BlF,CAAC,GAAG,MAAK+E,KAAL,CAAWK,KAAX,GAAmBD,MAAtD;AACD;;AAED,UAAM+B,SAAS,GAAGpC,cAAc,gCAAOjF,CAAP,EAAUG,CAAV,CAAhC;AAEAuF,MAAAA,GAAG,CAAC,+BAAD,EAAkC2B,SAAlC,CAAH,CAjBiD,CAmBjD;;AACA,UAAME,YAAY,GAAG,MAAK9L,KAAL,CAAWqM,MAAX,CAAkB9G,CAAlB,EAAqBqG,SAArB,CAArB;;AACA,UAAIE,YAAY,KAAK,KAAjB,IAA0B,MAAKC,OAAL,KAAiB,KAA/C,EAAsD;AACpD,YAAI;AACF;AACA,gBAAKG,cAAL,CAAoB,IAAII,UAAJ,CAAe,SAAf,CAApB;AACD,SAHD,CAGE,OAAOC,GAAP,EAAY;AACZ;AACA,cAAMxK,KAAK,KAAKtB,QAAQ,CAAC+L,WAAT,CAAqB,aAArB;AAAL;AAAA;AAAA;AAAA,WAAX,CAFY,CAGZ;AACA;;AACAzK,UAAAA,KAAK,CAAC0K,cAAN,CAAqB,SAArB,EAAgC,IAAhC,EAAsC,IAAtC,EAA4ClM,MAA5C,EAAoD,CAApD,EAAuD,CAAvD,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,CAAhE,EAAmE,KAAnE,EAA0E,KAA1E,EAAiF,KAAjF,EAAwF,KAAxF,EAA+F,CAA/F,EAAkG,IAAlG;;AACA,gBAAK2L,cAAL,CAAoBnK,KAApB;AACD;;AACD;AACD;;AAED,YAAK2J,QAAL,CAAc;AACZ/B,QAAAA,KAAK,EAAEpF,CADK;AAEZuF,QAAAA,KAAK,EAAEpF;AAFK,OAAd;AAID;;kFAE+C,UAACa,CAAD,EAAO;AACrD,UAAI,CAAC,MAAKkE,KAAL,CAAWqB,QAAhB,EAA0B;AAE1B,UAAMa,QAAQ,GAAGvC,kBAAkB,CAAC7D,CAAD,EAAI,MAAKkE,KAAL,CAAWJ,eAAf,gCAAnC;AACA,UAAIsC,QAAQ,IAAI,IAAhB,EAAsB;AACtB,UAAKpH,CAAL,GAAaoH,QAAb,CAAKpH,CAAL;AAAA,UAAQG,CAAR,GAAaiH,QAAb,CAAQjH,CAAR,CALqD,CAOrD;;AACA,UAAIyH,KAAK,CAACC,OAAN,CAAc,MAAKpM,KAAL,CAAW6I,IAAzB,CAAJ,EAAoC;AAClC,YAAIe,MAAM,GAAGrF,CAAC,GAAG,MAAKkF,KAAL,CAAWE,KAAf,IAAwB,CAArC;AACA,YAAIE,MAAM,GAAGnF,CAAC,GAAG,MAAK+E,KAAL,CAAWK,KAAf,IAAwB,CAArC;;AAFkC,2BAGflB,UAAU,CAAC,MAAK5I,KAAL,CAAW6I,IAAZ,EAAkBe,MAAlB,EAA0BC,MAA1B,CAHK;;AAAA;;AAGjCD,QAAAA,MAHiC;AAGzBC,QAAAA,MAHyB;AAIlCtF,QAAAA,CAAC,GAAG,MAAKkF,KAAL,CAAWE,KAAX,GAAmBC,MAAvB,EAA+BlF,CAAC,GAAG,MAAK+E,KAAL,CAAWK,KAAX,GAAmBD,MAAtD;AACD;;AAED,UAAM+B,SAAS,GAAGpC,cAAc,gCAAOjF,CAAP,EAAUG,CAAV,CAAhC,CAfqD,CAiBrD;;AACA,UAAMgI,cAAc,GAAG,MAAK1M,KAAL,CAAW2M,MAAX,CAAkBpH,CAAlB,EAAqBqG,SAArB,CAAvB;;AACA,UAAIc,cAAc,KAAK,KAAnB,IAA4B,MAAKX,OAAL,KAAiB,KAAjD,EAAwD,OAAO,KAAP;;AAExD,UAAMZ,QAAQ,GAAG,MAAK1D,WAAL,EAAjB;;AACA,UAAI0D,QAAJ,EAAc;AACZ;AACA,YAAI,MAAKnL,KAAL,CAAWgM,oBAAf,EAAqCxF,sBAAsB,CAAC2E,QAAQ,CAACtI,aAAV,CAAtB;AACtC;;AAEDoH,MAAAA,GAAG,CAAC,mCAAD,EAAsC2B,SAAtC,CAAH,CA3BqD,CA6BrD;;AACA,YAAKF,QAAL,CAAc;AACZZ,QAAAA,QAAQ,EAAE,KADE;AAEZnB,QAAAA,KAAK,EAAEoB,GAFK;AAGZjB,QAAAA,KAAK,EAAEiB;AAHK,OAAd;;AAMA,UAAII,QAAJ,EAAc;AACZ;AACAlB,QAAAA,GAAG,CAAC,kCAAD,CAAH;AACA3H,QAAAA,WAAW,CAAC6I,QAAQ,CAACtI,aAAV,EAAyB+H,YAAY,CAACH,IAAtC,EAA4C,MAAKwB,UAAjD,CAAX;AACA3J,QAAAA,WAAW,CAAC6I,QAAQ,CAACtI,aAAV,EAAyB+H,YAAY,CAACF,IAAtC,EAA4C,MAAKwB,cAAjD,CAAX;AACD;AACF;;+EAE4C,UAAC3G,CAAD,EAAO;AAClDqF,MAAAA,YAAY,GAAGN,SAAS,CAACK,KAAzB,CADkD,CAClB;;AAEhC,aAAO,MAAKiC,eAAL,CAAqBrH,CAArB,CAAP;AACD;;6EAE0C,UAACA,CAAD,EAAO;AAChDqF,MAAAA,YAAY,GAAGN,SAAS,CAACK,KAAzB;AAEA,aAAO,MAAKuB,cAAL,CAAoB3G,CAApB,CAAP;AACD;;gFAG6C,UAACA,CAAD,EAAO;AACnD;AACAqF,MAAAA,YAAY,GAAGN,SAAS,CAACC,KAAzB;AAEA,aAAO,MAAKqC,eAAL,CAAqBrH,CAArB,CAAP;AACD;;8EAE2C,UAACA,CAAD,EAAO;AACjD;AACAqF,MAAAA,YAAY,GAAGN,SAAS,CAACC,KAAzB;AAEA,aAAO,MAAK2B,cAAL,CAAoB3G,CAApB,CAAP;AACD;;;;;;;WAnND,6BAAoB;AAClB,WAAKwG,OAAL,GAAe,IAAf,CADkB,CAElB;AACA;;AACA,UAAMZ,QAAQ,GAAG,KAAK1D,WAAL,EAAjB;;AACA,UAAI0D,QAAJ,EAAc;AACZrJ,QAAAA,QAAQ,CAACqJ,QAAD,EAAWb,SAAS,CAACC,KAAV,CAAgBC,KAA3B,EAAkC,KAAKqC,YAAvC,EAAqD;AAACC,UAAAA,OAAO,EAAE;AAAV,SAArD,CAAR;AACD;AACF;;;WAED,gCAAuB;AACrB,WAAKf,OAAL,GAAe,KAAf,CADqB,CAErB;AACA;;AACA,UAAMZ,QAAQ,GAAG,KAAK1D,WAAL,EAAjB;;AACA,UAAI0D,QAAJ,EAAc;AACZ,YAAOtI,aAAP,GAAwBsI,QAAxB,CAAOtI,aAAP;AACAP,QAAAA,WAAW,CAACO,aAAD,EAAgByH,SAAS,CAACK,KAAV,CAAgBF,IAAhC,EAAsC,KAAKwB,UAA3C,CAAX;AACA3J,QAAAA,WAAW,CAACO,aAAD,EAAgByH,SAAS,CAACC,KAAV,CAAgBE,IAAhC,EAAsC,KAAKwB,UAA3C,CAAX;AACA3J,QAAAA,WAAW,CAACO,aAAD,EAAgByH,SAAS,CAACK,KAAV,CAAgBD,IAAhC,EAAsC,KAAKwB,cAA3C,CAAX;AACA5J,QAAAA,WAAW,CAACO,aAAD,EAAgByH,SAAS,CAACC,KAAV,CAAgBG,IAAhC,EAAsC,KAAKwB,cAA3C,CAAX;AACA5J,QAAAA,WAAW,CAAC6I,QAAD,EAAWb,SAAS,CAACC,KAAV,CAAgBC,KAA3B,EAAkC,KAAKqC,YAAvC,EAAqD;AAACC,UAAAA,OAAO,EAAE;AAAV,SAArD,CAAX;AACA,YAAI,KAAK9M,KAAL,CAAWgM,oBAAf,EAAqCxF,sBAAsB,CAAC3D,aAAD,CAAtB;AACtC;AACF,MAED;AACA;;;;WACA;AAAA;AAA4B;AAAA;;AAC1B,aAAO,oBAAK7C,KAAL,oDAAY+M,OAAZ,mBAAsB,KAAK/M,KAA3B,yEAAsB,aAAY+M,OAAlC,yDAAsB,qBAAqBC,OAA3C,GAAqD3C,iGAAA,CAAqB,IAArB,CAA5D;AACD;;;WAuLD;AAAA;AAA6B;AAC3B;AACA;AACA,0BAAOF,0EAAA,CAAmBA,2EAAA,CAAoB,KAAKnK,KAAL,CAAWoN,QAA/B,CAAnB,EAA6D;AAClE;AACA;AACApC,QAAAA,WAAW,EAAE,KAAKA,WAHgD;AAIlEqC,QAAAA,SAAS,EAAE,KAAKA,SAJkD;AAKlE;AACA;AACA;AACAC,QAAAA,UAAU,EAAE,KAAKA;AARiD,OAA7D,CAAP;AAUD;;;;EAnYwCnD;;6BAAtBU,8BAEW;;6BAFXA,4BAIA;AACjB;AACJ;AACA;AACA;AACA;AACA;AACII,EAAAA,aAAa,EAAEb,2BAPE;;AASjB;AACJ;AACA;AACA;AACIgB,EAAAA,QAAQ,EAAEhB,2BAbO;;AAejB;AACJ;AACA;AACA;AACA;AACI4B,EAAAA,oBAAoB,EAAE5B,2BApBL;;AAsBjB;AACJ;AACA;AACA;AACIrG,EAAAA,YAAY,EAAE,sBAAS/D;AAAT;AAAA,IAAoCC;AAApC;AAAA,IAAyE;AACrF,QAAID,KAAK,CAACC,QAAD,CAAL,IAAmBD,KAAK,CAACC,QAAD,CAAL,CAAgBwN,QAAhB,KAA6B,CAApD,EAAuD;AACrD,YAAM,IAAItN,KAAJ,CAAU,+CAAV,CAAN;AACD;AACF,GA9BgB;;AAgCjB;AACJ;AACA;AACI0I,EAAAA,IAAI,EAAEuB,4BAAA,CAAkBA,6BAAlB,CAnCW;;AAqCjB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACImB,EAAAA,MAAM,EAAEnB,6BAzDS;;AA2DjB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACIoB,EAAAA,MAAM,EAAEpB,6BA/ES;;AAiFjB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI2C,EAAAA,OAAO,EAAE3C,6BAlGQ;;AAoGjB;AACJ;AACA;AACA;AACIyB,EAAAA,OAAO,EAAEzB,2BAxGQ;;AA0GjB;AACJ;AACA;AACA;AACIiC,EAAAA,MAAM,EAAEjC,2BA9GS;;AAgHjB;AACJ;AACA;AACA;AACIuC,EAAAA,MAAM,EAAEvC,2BApHS;;AAsHjB;AACJ;AACA;AACA;AACIY,EAAAA,WAAW,EAAEZ,2BA1HI;;AA4HjB;AACJ;AACA;AACIpG,EAAAA,KAAK,EAAEoG,6BA/HU;;AAiIjB;AACJ;AACA;AACItD,EAAAA,SAAS,EAAE/G,SApIM;AAqIjBS,EAAAA,KAAK,EAAET,SArIU;AAsIjB+N,EAAAA,SAAS,EAAE/N,SAASA;AAtIH;;6BAJA8K,+BA6I8B;AAC/CI,EAAAA,aAAa,EAAE,KADgC;AACzB;AACtBG,EAAAA,QAAQ,EAAE,KAFqC;AAG/CY,EAAAA,oBAAoB,EAAE,IAHyB;AAI/CH,EAAAA,OAAO,EAAE,mBAAU,CAAE,CAJ0B;AAK/CQ,EAAAA,MAAM,EAAE,kBAAU,CAAE,CAL2B;AAM/CM,EAAAA,MAAM,EAAE,kBAAU,CAAE,CAN2B;AAO/C3B,EAAAA,WAAW,EAAE,uBAAU,CAAE,CAPsB;AAQ/ChH,EAAAA,KAAK,EAAE;AARwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzNnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEA;;;;;;AAVA;AACA;AACA;AACA;AACA;AACA;AACA;;;AANA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;AACA;AACA;AACA;AACA;;AAqCA;AACA;AACA;IAEMgK;;;;;AA0JJ,qBAAYhO;AAAZ;AAAA,IAAmC;AAAA;;AAAA;;AACjC,8BAAMA,KAAN;;AADiC,oFAgDE,UAACuF,CAAD,EAAIyE,QAAJ,EAAiB;AACpDC,MAAAA,GAAG,CAAC,4BAAD,EAA+BD,QAA/B,CAAH,CADoD,CAGpD;;AACA,UAAMiE,WAAW,GAAG,MAAKjO,KAAL,CAAW6L,OAAX,CAAmBtG,CAAnB,EAAsBwE,mBAAmB,CAAC,wCAAMC,QAAP,CAAzC,CAApB,CAJoD,CAKpD;;;AACA,UAAIiE,WAAW,KAAK,KAApB,EAA2B,OAAO,KAAP;;AAE3B,YAAKvC,QAAL,CAAc;AAACZ,QAAAA,QAAQ,EAAE,IAAX;AAAiBoD,QAAAA,OAAO,EAAE;AAA1B,OAAd;AACD,KAzDkC;;AAAA,+EA2DH,UAAC3I,CAAD,EAAIyE,QAAJ,EAAiB;AAC/C,UAAI,CAAC,MAAKP,KAAL,CAAWqB,QAAhB,EAA0B,OAAO,KAAP;AAC1Bb,MAAAA,GAAG,CAAC,uBAAD,EAA0BD,QAA1B,CAAH;AAEA,UAAMmE,MAAM,GAAGpE,mBAAmB,CAAC,wCAAMC,QAAP,CAAlC;AAEA,UAAMoE;AAAgC;AAAA,QAAG;AACvC7J,QAAAA,CAAC,EAAE4J,MAAM,CAAC5J,CAD6B;AAEvCG,QAAAA,CAAC,EAAEyJ,MAAM,CAACzJ;AAF6B,OAAzC,CAN+C,CAW/C;;AACA,UAAI,MAAK1E,KAAL,CAAWuH,MAAf,EAAuB;AACrB;AACA,YAAOhD,CAAP,GAAe6J,QAAf,CAAO7J,CAAP;AAAA,YAAUG,CAAV,GAAe0J,QAAf,CAAU1J,CAAV,CAFqB,CAIrB;AACA;AACA;;AACA0J,QAAAA,QAAQ,CAAC7J,CAAT,IAAc,MAAKkF,KAAL,CAAW4E,MAAzB;AACAD,QAAAA,QAAQ,CAAC1J,CAAT,IAAc,MAAK+E,KAAL,CAAW6E,MAAzB,CARqB,CAUrB;;AACA,gCAA+BjH,gBAAgB,CAAC,wCAAM+G,QAAQ,CAAC7J,CAAhB,EAAmB6J,QAAQ,CAAC1J,CAA5B,CAA/C;AAAA;AAAA,YAAO6J,SAAP;AAAA,YAAkBC,SAAlB;;AACAJ,QAAAA,QAAQ,CAAC7J,CAAT,GAAagK,SAAb;AACAH,QAAAA,QAAQ,CAAC1J,CAAT,GAAa8J,SAAb,CAbqB,CAerB;;AACAJ,QAAAA,QAAQ,CAACC,MAAT,GAAkB,MAAK5E,KAAL,CAAW4E,MAAX,IAAqB9J,CAAC,GAAG6J,QAAQ,CAAC7J,CAAlC,CAAlB;AACA6J,QAAAA,QAAQ,CAACE,MAAT,GAAkB,MAAK7E,KAAL,CAAW6E,MAAX,IAAqB5J,CAAC,GAAG0J,QAAQ,CAAC1J,CAAlC,CAAlB,CAjBqB,CAmBrB;;AACAyJ,QAAAA,MAAM,CAAC5J,CAAP,GAAW6J,QAAQ,CAAC7J,CAApB;AACA4J,QAAAA,MAAM,CAACzJ,CAAP,GAAW0J,QAAQ,CAAC1J,CAApB;AACAyJ,QAAAA,MAAM,CAACvE,MAAP,GAAgBwE,QAAQ,CAAC7J,CAAT,GAAa,MAAKkF,KAAL,CAAWlF,CAAxC;AACA4J,QAAAA,MAAM,CAACtE,MAAP,GAAgBuE,QAAQ,CAAC1J,CAAT,GAAa,MAAK+E,KAAL,CAAW/E,CAAxC;AACD,OApC8C,CAsC/C;;;AACA,UAAMoH,YAAY,GAAG,MAAK9L,KAAL,CAAWqM,MAAX,CAAkB9G,CAAlB,EAAqB4I,MAArB,CAArB;;AACA,UAAIrC,YAAY,KAAK,KAArB,EAA4B,OAAO,KAAP;;AAE5B,YAAKJ,QAAL,CAAc0C,QAAd;AACD,KAtGkC;;AAAA,mFAwGC,UAAC7I,CAAD,EAAIyE,QAAJ,EAAiB;AACnD,UAAI,CAAC,MAAKP,KAAL,CAAWqB,QAAhB,EAA0B,OAAO,KAAP,CADyB,CAGnD;;AACA,UAAM4B,cAAc,GAAG,MAAK1M,KAAL,CAAW2M,MAAX,CAAkBpH,CAAlB,EAAqBwE,mBAAmB,CAAC,wCAAMC,QAAP,CAAxC,CAAvB;;AACA,UAAI0C,cAAc,KAAK,KAAvB,EAA8B,OAAO,KAAP;AAE9BzC,MAAAA,GAAG,CAAC,2BAAD,EAA8BD,QAA9B,CAAH;AAEA,UAAMoE;AAAgC;AAAA,QAAG;AACvCtD,QAAAA,QAAQ,EAAE,KAD6B;AAEvCuD,QAAAA,MAAM,EAAE,CAF+B;AAGvCC,QAAAA,MAAM,EAAE;AAH+B,OAAzC,CATmD,CAenD;AACA;;AACA,UAAMG,UAAU,GAAGC,OAAO,CAAC,MAAK1O,KAAL,CAAW2L,QAAZ,CAA1B;;AACA,UAAI8C,UAAJ,EAAgB;AACd,mCAAe,MAAKzO,KAAL,CAAW2L,QAA1B;AAAA,YAAOpH,CAAP,wBAAOA,CAAP;AAAA,YAAUG,CAAV,wBAAUA,CAAV;AACA0J,QAAAA,QAAQ,CAAC7J,CAAT,GAAaA,CAAb;AACA6J,QAAAA,QAAQ,CAAC1J,CAAT,GAAaA,CAAb;AACD;;AAED,YAAKgH,QAAL,CAAc0C,QAAd;AACD,KAjIkC;;AAGjC,UAAK3E,KAAL,GAAa;AACX;AACAqB,MAAAA,QAAQ,EAAE,KAFC;AAIX;AACAoD,MAAAA,OAAO,EAAE,KALE;AAOX;AACA3J,MAAAA,CAAC,EAAEvE,KAAK,CAAC2L,QAAN,GAAiB3L,KAAK,CAAC2L,QAAN,CAAepH,CAAhC,GAAoCvE,KAAK,CAAC2O,eAAN,CAAsBpK,CARlD;AASXG,MAAAA,CAAC,EAAE1E,KAAK,CAAC2L,QAAN,GAAiB3L,KAAK,CAAC2L,QAAN,CAAejH,CAAhC,GAAoC1E,KAAK,CAAC2O,eAAN,CAAsBjK,CATlD;AAWXkK,MAAAA,iBAAiB,EAAE,2BAAI5O,KAAK,CAAC2L,QAAZ,CAXN;AAaX;AACA0C,MAAAA,MAAM,EAAE,CAdG;AAcAC,MAAAA,MAAM,EAAE,CAdR;AAgBX;AACAO,MAAAA,YAAY,EAAE;AAjBH,KAAb;;AAoBA,QAAI7O,KAAK,CAAC2L,QAAN,IAAkB,EAAE3L,KAAK,CAACqM,MAAN,IAAgBrM,KAAK,CAAC2M,MAAxB,CAAtB,EAAuD;AACrD;AACAzC,MAAAA,OAAO,CAAC4E,IAAR,CAAa,8FACX,uGADW,GAEX,6BAFF;AAGD;;AA5BgC;AA6BlC;;;;WAED,6BAAoB;AAClB;AACA,UAAG,OAAOvO,MAAM,CAACwO,UAAd,KAA6B,WAA7B,IAA4C,KAAKtH,WAAL,cAA8BlH,MAAM,CAACwO,UAApF,EAAgG;AAC9F,aAAKrD,QAAL,CAAc;AAACmD,UAAAA,YAAY,EAAE;AAAf,SAAd;AACD;AACF;;;WAED,gCAAuB;AACrB,WAAKnD,QAAL,CAAc;AAACZ,QAAAA,QAAQ,EAAE;AAAX,OAAd,EADqB,CACa;AACnC,MAED;AACA;;;;WACA;AAAA;AAA4B;AAAA;;AAC1B,qDAAO,KAAK9K,KAAZ,uEAAO,YAAY+M,OAAnB,wDAAO,oBAAqBC,OAA5B,yEAAuC3C,iGAAA,CAAqB,IAArB,CAAvC;AACD;;;WAqFD;AAAA;AAA4B;AAAA;;AAC1B,yBAYI,KAAKrK,KAZT;AAAA,UACEkJ,IADF,gBACEA,IADF;AAAA,UAEE3B,MAFF,gBAEEA,MAFF;AAAA,UAGE6F,QAHF,gBAGEA,QAHF;AAAA,UAIEuB,eAJF,gBAIEA,eAJF;AAAA,UAKEK,gBALF,gBAKEA,gBALF;AAAA,UAMEC,wBANF,gBAMEA,wBANF;AAAA,UAOEC,uBAPF,gBAOEA,uBAPF;AAAA,UAQEvD,QARF,gBAQEA,QARF;AAAA,UASE5G,cATF,gBASEA,cATF;AAAA,UAUEf,KAVF,gBAUEA,KAVF;AAAA,UAWKmL,kBAXL;;AAcA,UAAI3O,KAAK,GAAG,EAAZ;AACA,UAAI4O,YAAY,GAAG,IAAnB,CAhB0B,CAkB1B;;AACA,UAAMX,UAAU,GAAGC,OAAO,CAAC/C,QAAD,CAA1B;AACA,UAAMrE,SAAS,GAAG,CAACmH,UAAD,IAAe,KAAKhF,KAAL,CAAWqB,QAA5C;AAEA,UAAMuE,aAAa,GAAG1D,QAAQ,IAAIgD,eAAlC;AACA,UAAMW,aAAa,GAAG;AACpB;AACA/K,QAAAA,CAAC,EAAE0E,QAAQ,CAAC,IAAD,CAAR,IAAkB3B,SAAlB,GACD,KAAKmC,KAAL,CAAWlF,CADV,GAED8K,aAAa,CAAC9K,CAJI;AAMpB;AACAG,QAAAA,CAAC,EAAEyE,QAAQ,CAAC,IAAD,CAAR,IAAkB7B,SAAlB,GACD,KAAKmC,KAAL,CAAW/E,CADV,GAED2K,aAAa,CAAC3K;AATI,OAAtB,CAvB0B,CAmC1B;;AACA,UAAI,KAAK+E,KAAL,CAAWoF,YAAf,EAA6B;AAC3BO,QAAAA,YAAY,GAAGlK,kBAAkB,CAACoK,aAAD,EAAgBvK,cAAhB,CAAjC;AACD,OAFD,MAEO;AACL;AACA;AACA;AACA;AACAvE,QAAAA,KAAK,GAAGqE,kBAAkB,CAACyK,aAAD,EAAgBvK,cAAhB,CAA1B;AACD,OA5CyB,CA8C1B;;;AACA,UAAM+B,SAAS,GAAGiH,MAAI,CAAEX,QAAQ,CAACpN,KAAT,CAAe8G,SAAf,IAA4B,EAA9B,EAAmCkI,gBAAnC,+CACnBC,wBADmB,EACQ,KAAKxF,KAAL,CAAWqB,QADnB,mCAEnBoE,uBAFmB,EAEO,KAAKzF,KAAL,CAAWyE,OAFlB,UAAtB,CA/C0B,CAoD1B;AACA;;AACA,0BACE,4EAAC,aAAD,eAAmBiB,kBAAnB;AAAuC,eAAO,EAAE,KAAKI,WAArD;AAAkE,cAAM,EAAE,KAAKlD,MAA/E;AAAuF,cAAM,EAAE,KAAKmD;AAApG,uBACGrF,0EAAA,CAAmBA,2EAAA,CAAoBiD,QAApB,CAAnB,EAAkD;AACjDtG,QAAAA,SAAS,EAAEA,SADsC;AAEjDtG,QAAAA,KAAK,EAAE,kDAAI4M,QAAQ,CAACpN,KAAT,CAAeQ,KAArB,GAA+BA,KAA/B,CAF4C;AAGjDsN,QAAAA,SAAS,EAAEsB;AAHsC,OAAlD,CADH,CADF;AASD;;;WAtND;AACA;AACA;AAAA;AAA0H;AAAA,UAAzFzD,QAAyF,QAAzFA,QAAyF;AAAA,UAA7DiD,iBAA6D,SAA7DA,iBAA6D;;AACxH;AACA,UACEjD,QAAQ,KACP,CAACiD,iBAAD,IACCjD,QAAQ,CAACpH,CAAT,KAAeqK,iBAAiB,CAACrK,CADlC,IACuCoH,QAAQ,CAACjH,CAAT,KAAekK,iBAAiB,CAAClK,CAFjE,CADV,EAKE;AACAuF,QAAAA,GAAG,CAAC,wCAAD,EAA2C;AAAC0B,UAAAA,QAAQ,EAARA,QAAD;AAAWiD,UAAAA,iBAAiB,EAAjBA;AAAX,SAA3C,CAAH;AACA,eAAO;AACLrK,UAAAA,CAAC,EAAEoH,QAAQ,CAACpH,CADP;AAELG,UAAAA,CAAC,EAAEiH,QAAQ,CAACjH,CAFP;AAGLkK,UAAAA,iBAAiB,EAAE,2BAAIjD,QAAN;AAHZ,SAAP;AAKD;;AACD,aAAO,IAAP;AACD;;;;EAxJqBxB;;yBAAlB6D,0BAE0B;;yBAF1BA,0EAMCnD;AAEH;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI3B,EAAAA,IAAI,EAAEkB,0BAAA,CAAgB,CAAC,MAAD,EAAS,GAAT,EAAc,GAAd,EAAmB,MAAnB,CAAhB;;AAEN;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI7C,EAAAA,MAAM,EAAE6C,8BAAA,CAAoB,CAC1BA,0BAAA,CAAgB;AACdhG,IAAAA,IAAI,EAAEgG,6BADQ;AAEd/B,IAAAA,KAAK,EAAE+B,6BAFO;AAGd/F,IAAAA,GAAG,EAAE+F,6BAHS;AAId7B,IAAAA,MAAM,EAAE6B,6BAAgBuD;AAJV,GAAhB,CAD0B,EAO1BvD,6BAP0B,EAQ1BA,0BAAA,CAAgB,CAAC,KAAD,CAAhB,CAR0B,CAApB;AAWR4E,EAAAA,gBAAgB,EAAE5E;AAClB6E,EAAAA,wBAAwB,EAAE7E;AAC1B8E,EAAAA,uBAAuB,EAAE9E;;AAEzB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACIuE,EAAAA,eAAe,EAAEvE,0BAAA,CAAgB;AAC/B7F,IAAAA,CAAC,EAAE6F,6BAD4B;AAE/B1F,IAAAA,CAAC,EAAE0F,6BAAgBuD;AAFY,GAAhB;AAIjB5I,EAAAA,cAAc,EAAEqF,0BAAA,CAAgB;AAC9B7F,IAAAA,CAAC,EAAE6F,8BAAA,CAAoB,CAACA,6BAAD,EAAmBA,6BAAnB,CAApB,CAD2B;AAE9B1F,IAAAA,CAAC,EAAE0F,8BAAA,CAAoB,CAACA,6BAAD,EAAmBA,6BAAnB,CAApB;AAF2B,GAAhB;;AAKhB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACIuB,EAAAA,QAAQ,EAAEvB,0BAAA,CAAgB;AACxB7F,IAAAA,CAAC,EAAE6F,6BADqB;AAExB1F,IAAAA,CAAC,EAAE0F,6BAAgBuD;AAFK,GAAhB;;AAKV;AACJ;AACA;AACI7G,EAAAA,SAAS,EAAE/G;AACXS,EAAAA,KAAK,EAAET;AACP+N,EAAAA,SAAS,EAAE/N,SAASA;;;yBAxHlBiO,6EA4HCnD;AACH3B,EAAAA,IAAI,EAAE;AACN3B,EAAAA,MAAM,EAAE;AACRyH,EAAAA,gBAAgB,EAAE;AAClBC,EAAAA,wBAAwB,EAAE;AAC1BC,EAAAA,uBAAuB,EAAE;AACzBP,EAAAA,eAAe,EAAE;AAACpK,IAAAA,CAAC,EAAE,CAAJ;AAAOG,IAAAA,CAAC,EAAE;AAAV;AACjBV,EAAAA,KAAK,EAAE;;;;;;;;;;AChLX,eAA4C+L,mBAAO,CAAC,GAAD,CAAnD;AAAA,IAAgB/B,SAAhB,YAAO8B,OAAP;AAAA,IAA2BjF,aAA3B,YAA2BA,aAA3B,EAEA;AACA;AACA;;;AACAmF,MAAM,CAACC,OAAP,GAAiBjC,SAAjB;AACAgC,yBAAA,GAAyBhC,SAAzB;AACAgC,4BAAA,GAA+BnF,aAA/B;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;;AAEa;;AAEb,2BAA2B,mBAAO,CAAC,GAA4B;;AAE/D;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;;;;;;;AChEA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAI,KAAqC,EAAE,qCAO1C,CAAC;AACF;AACA;AACA,mBAAmB,mBAAO,CAAC,GAA4B;AACvD;;;;;;;;;AClBA;AACA;AACA;AACA;AACA;AACA;;AAEa;;AAEb;;AAEA;;;;;;;;;ACXA;;;;;;;;ACAA;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA,eAAe,4BAA4B;WAC3C,eAAe;WACf,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA,8CAA8C;;;;;WCA9C;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;UENA;UACA;UACA;UACA", "sources": ["../webpack/universalModuleDefinition", ".././node_modules/clsx/dist/clsx.m.js", ".././lib/utils/shims.js", ".././lib/utils/getPrefix.js", ".././lib/utils/domFns.js", ".././lib/utils/positionFns.js", ".././lib/utils/log.js", ".././lib/DraggableCore.js", ".././lib/Draggable.js", ".././lib/cjs.js", ".././node_modules/prop-types/factoryWithThrowingShims.js", ".././node_modules/prop-types/index.js", ".././node_modules/prop-types/lib/ReactPropTypesSecret.js", "../external umd {\"commonjs\":\"react\",\"commonjs2\":\"react\",\"amd\":\"react\",\"root\":\"React\"}", "../external umd {\"commonjs\":\"react-dom\",\"commonjs2\":\"react-dom\",\"amd\":\"react-dom\",\"root\":\"ReactDOM\"}", "../webpack/bootstrap", "../webpack/runtime/compat get default export", "../webpack/runtime/define property getters", "../webpack/runtime/hasOwnProperty shorthand", "../webpack/runtime/make namespace object", "../webpack/before-startup", "../webpack/startup", "../webpack/after-startup"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"react\"), require(\"react-dom\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"react\", \"react-dom\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ReactDraggable\"] = factory(require(\"react\"), require(\"react-dom\"));\n\telse\n\t\troot[\"ReactDraggable\"] = factory(root[\"React\"], root[\"ReactDOM\"]);\n})(self, function(__WEBPACK_EXTERNAL_MODULE__359__, __WEBPACK_EXTERNAL_MODULE__318__) {\nreturn ", "function toVal(mix) {\n\tvar k, y, str='';\n\n\tif (typeof mix === 'string' || typeof mix === 'number') {\n\t\tstr += mix;\n\t} else if (typeof mix === 'object') {\n\t\tif (Array.isArray(mix)) {\n\t\t\tfor (k=0; k < mix.length; k++) {\n\t\t\t\tif (mix[k]) {\n\t\t\t\t\tif (y = toVal(mix[k])) {\n\t\t\t\t\t\tstr && (str += ' ');\n\t\t\t\t\t\tstr += y;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tfor (k in mix) {\n\t\t\t\tif (mix[k]) {\n\t\t\t\t\tstr && (str += ' ');\n\t\t\t\t\tstr += k;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn str;\n}\n\nexport default function () {\n\tvar i=0, tmp, x, str='';\n\twhile (i < arguments.length) {\n\t\tif (tmp = arguments[i++]) {\n\t\t\tif (x = toVal(tmp)) {\n\t\t\t\tstr && (str += ' ');\n\t\t\t\tstr += x\n\t\t\t}\n\t\t}\n\t}\n\treturn str;\n}\n", "// @flow\n// @credits https://gist.github.com/rogozhnikoff/a43cfed27c41e4e68cdc\nexport function findInArray(array: Array<any> | TouchList, callback: Function): any {\n  for (let i = 0, length = array.length; i < length; i++) {\n    if (callback.apply(callback, [array[i], i, array])) return array[i];\n  }\n}\n\nexport function isFunction(func: any): boolean %checks {\n  // $FlowIgnore[method-unbinding]\n  return typeof func === 'function' || Object.prototype.toString.call(func) === '[object Function]';\n}\n\nexport function isNum(num: any): boolean %checks {\n  return typeof num === 'number' && !isNaN(num);\n}\n\nexport function int(a: string): number {\n  return parseInt(a, 10);\n}\n\nexport function dontSetMe(props: Object, propName: string, componentName: string): ?Error {\n  if (props[propName]) {\n    return new Error(`Invalid prop ${propName} passed to ${componentName} - do not set this, set it on the child.`);\n  }\n}\n", "// @flow\nconst prefixes = ['Moz', 'Webkit', 'O', 'ms'];\nexport function getPrefix(prop: string='transform'): string {\n  // Ensure we're running in an environment where there is actually a global\n  // `window` obj\n  if (typeof window === 'undefined') return '';\n\n  // If we're in a pseudo-browser server-side environment, this access\n  // path may not exist, so bail out if it doesn't.\n  const style = window.document?.documentElement?.style;\n  if (!style) return '';\n\n  if (prop in style) return '';\n\n  for (let i = 0; i < prefixes.length; i++) {\n    if (browserPrefixToKey(prop, prefixes[i]) in style) return prefixes[i];\n  }\n\n  return '';\n}\n\nexport function browserPrefixToKey(prop: string, prefix: string): string {\n  return prefix ? `${prefix}${kebabToTitleCase(prop)}` : prop;\n}\n\nexport function browserPrefixToStyle(prop: string, prefix: string): string {\n  return prefix ? `-${prefix.toLowerCase()}-${prop}` : prop;\n}\n\nfunction kebabToTitleCase(str: string): string {\n  let out = '';\n  let shouldCapitalize = true;\n  for (let i = 0; i < str.length; i++) {\n    if (shouldCapitalize) {\n      out += str[i].toUpperCase();\n      shouldCapitalize = false;\n    } else if (str[i] === '-') {\n      shouldCapitalize = true;\n    } else {\n      out += str[i];\n    }\n  }\n  return out;\n}\n\n// Default export is the prefix itself, like 'Moz', 'Webkit', etc\n// Note that you may have to re-test for certain things; for instance, Chrome 50\n// can handle unprefixed `transform`, but not unprefixed `user-select`\nexport default (getPrefix(): string);\n", "// @flow\nimport {findInArray, isFunction, int} from './shims';\nimport browserPrefix, {browserPrefixToKey} from './getPrefix';\n\nimport type {ControlPosition, PositionOffsetControlPosition, MouseTouchEvent} from './types';\n\nlet matchesSelectorFunc = '';\nexport function matchesSelector(el: Node, selector: string): boolean {\n  if (!matchesSelectorFunc) {\n    matchesSelectorFunc = findInArray([\n      'matches',\n      'webkitMatchesSelector',\n      'mozMatchesSelector',\n      'msMatchesSelector',\n      'oMatchesSelector'\n    ], function(method){\n      // $FlowIgnore: Doesn't think elements are indexable\n      return isFunction(el[method]);\n    });\n  }\n\n  // Might not be found entirely (not an Element?) - in that case, bail\n  // $FlowIgnore: Doesn't think elements are indexable\n  if (!isFunction(el[matchesSelectorFunc])) return false;\n\n  // $FlowIgnore: Doesn't think elements are indexable\n  return el[matchesSelectorFunc](selector);\n}\n\n// Works up the tree to the draggable itself attempting to match selector.\nexport function matchesSelectorAndParentsTo(el: Node, selector: string, baseNode: Node): boolean {\n  let node = el;\n  do {\n    if (matchesSelector(node, selector)) return true;\n    if (node === baseNode) return false;\n    node = node.parentNode;\n  } while (node);\n\n  return false;\n}\n\nexport function addEvent(el: ?Node, event: string, handler: Function, inputOptions?: Object): void {\n  if (!el) return;\n  const options = {capture: true, ...inputOptions};\n  // $FlowIgnore[method-unbinding]\n  if (el.addEventListener) {\n    el.addEventListener(event, handler, options);\n  } else if (el.attachEvent) {\n    el.attachEvent('on' + event, handler);\n  } else {\n    // $FlowIgnore: Doesn't think elements are indexable\n    el['on' + event] = handler;\n  }\n}\n\nexport function removeEvent(el: ?Node, event: string, handler: Function, inputOptions?: Object): void {\n  if (!el) return;\n  const options = {capture: true, ...inputOptions};\n  // $FlowIgnore[method-unbinding]\n  if (el.removeEventListener) {\n    el.removeEventListener(event, handler, options);\n  } else if (el.detachEvent) {\n    el.detachEvent('on' + event, handler);\n  } else {\n    // $FlowIgnore: Doesn't think elements are indexable\n    el['on' + event] = null;\n  }\n}\n\nexport function outerHeight(node: HTMLElement): number {\n  // This is deliberately excluding margin for our calculations, since we are using\n  // offsetTop which is including margin. See getBoundPosition\n  let height = node.clientHeight;\n  const computedStyle = node.ownerDocument.defaultView.getComputedStyle(node);\n  height += int(computedStyle.borderTopWidth);\n  height += int(computedStyle.borderBottomWidth);\n  return height;\n}\n\nexport function outerWidth(node: HTMLElement): number {\n  // This is deliberately excluding margin for our calculations, since we are using\n  // offsetLeft which is including margin. See getBoundPosition\n  let width = node.clientWidth;\n  const computedStyle = node.ownerDocument.defaultView.getComputedStyle(node);\n  width += int(computedStyle.borderLeftWidth);\n  width += int(computedStyle.borderRightWidth);\n  return width;\n}\nexport function innerHeight(node: HTMLElement): number {\n  let height = node.clientHeight;\n  const computedStyle = node.ownerDocument.defaultView.getComputedStyle(node);\n  height -= int(computedStyle.paddingTop);\n  height -= int(computedStyle.paddingBottom);\n  return height;\n}\n\nexport function innerWidth(node: HTMLElement): number {\n  let width = node.clientWidth;\n  const computedStyle = node.ownerDocument.defaultView.getComputedStyle(node);\n  width -= int(computedStyle.paddingLeft);\n  width -= int(computedStyle.paddingRight);\n  return width;\n}\n\ninterface EventWithOffset {\n  clientX: number, clientY: number\n}\n\n// Get from offsetParent\nexport function offsetXYFromParent(evt: EventWithOffset, offsetParent: HTMLElement, scale: number): ControlPosition {\n  const isBody = offsetParent === offsetParent.ownerDocument.body;\n  const offsetParentRect = isBody ? {left: 0, top: 0} : offsetParent.getBoundingClientRect();\n\n  const x = (evt.clientX + offsetParent.scrollLeft - offsetParentRect.left) / scale;\n  const y = (evt.clientY + offsetParent.scrollTop - offsetParentRect.top) / scale;\n\n  return {x, y};\n}\n\nexport function createCSSTransform(controlPos: ControlPosition, positionOffset: PositionOffsetControlPosition): Object {\n  const translation = getTranslation(controlPos, positionOffset, 'px');\n  return {[browserPrefixToKey('transform', browserPrefix)]: translation };\n}\n\nexport function createSVGTransform(controlPos: ControlPosition, positionOffset: PositionOffsetControlPosition): string {\n  const translation = getTranslation(controlPos, positionOffset, '');\n  return translation;\n}\nexport function getTranslation({x, y}: ControlPosition, positionOffset: PositionOffsetControlPosition, unitSuffix: string): string {\n  let translation = `translate(${x}${unitSuffix},${y}${unitSuffix})`;\n  if (positionOffset) {\n    const defaultX = `${(typeof positionOffset.x === 'string') ? positionOffset.x : positionOffset.x + unitSuffix}`;\n    const defaultY = `${(typeof positionOffset.y === 'string') ? positionOffset.y : positionOffset.y + unitSuffix}`;\n    translation = `translate(${defaultX}, ${defaultY})` + translation;\n  }\n  return translation;\n}\n\nexport function getTouch(e: MouseTouchEvent, identifier: number): ?{clientX: number, clientY: number} {\n  return (e.targetTouches && findInArray(e.targetTouches, t => identifier === t.identifier)) ||\n         (e.changedTouches && findInArray(e.changedTouches, t => identifier === t.identifier));\n}\n\nexport function getTouchIdentifier(e: MouseTouchEvent): ?number {\n  if (e.targetTouches && e.targetTouches[0]) return e.targetTouches[0].identifier;\n  if (e.changedTouches && e.changedTouches[0]) return e.changedTouches[0].identifier;\n}\n\n// User-select Hacks:\n//\n// Useful for preventing blue highlights all over everything when dragging.\n\n// Note we're passing `document` b/c we could be iframed\nexport function addUserSelectStyles(doc: ?Document) {\n  if (!doc) return;\n  let styleEl = doc.getElementById('react-draggable-style-el');\n  if (!styleEl) {\n    styleEl = doc.createElement('style');\n    styleEl.type = 'text/css';\n    styleEl.id = 'react-draggable-style-el';\n    styleEl.innerHTML = '.react-draggable-transparent-selection *::-moz-selection {all: inherit;}\\n';\n    styleEl.innerHTML += '.react-draggable-transparent-selection *::selection {all: inherit;}\\n';\n    doc.getElementsByTagName('head')[0].appendChild(styleEl);\n  }\n  if (doc.body) addClassName(doc.body, 'react-draggable-transparent-selection');\n}\n\nexport function removeUserSelectStyles(doc: ?Document) {\n  if (!doc) return;\n  try {\n    if (doc.body) removeClassName(doc.body, 'react-draggable-transparent-selection');\n    // $FlowIgnore: IE\n    if (doc.selection) {\n      // $FlowIgnore: IE\n      doc.selection.empty();\n    } else {\n      // Remove selection caused by scroll, unless it's a focused input\n      // (we use doc.defaultView in case we're in an iframe)\n      const selection = (doc.defaultView || window).getSelection();\n      if (selection && selection.type !== 'Caret') {\n        selection.removeAllRanges();\n      }\n    }\n  } catch (e) {\n    // probably IE\n  }\n}\n\nexport function addClassName(el: HTMLElement, className: string) {\n  if (el.classList) {\n    el.classList.add(className);\n  } else {\n    if (!el.className.match(new RegExp(`(?:^|\\\\s)${className}(?!\\\\S)`))) {\n      el.className += ` ${className}`;\n    }\n  }\n}\n\nexport function removeClassName(el: HTMLElement, className: string) {\n  if (el.classList) {\n    el.classList.remove(className);\n  } else {\n    el.className = el.className.replace(new RegExp(`(?:^|\\\\s)${className}(?!\\\\S)`, 'g'), '');\n  }\n}\n", "// @flow\nimport {isNum, int} from './shims';\nimport {getTouch, innerWidth, innerHeight, offsetXYFromParent, outerWidth, outerHeight} from './domFns';\n\nimport type Draggable from '../Draggable';\nimport type {Bo<PERSON>, ControlPosition, DraggableData, MouseTouchEvent} from './types';\nimport type DraggableCore from '../DraggableCore';\n\nexport function getBoundPosition(draggable: Draggable, x: number, y: number): [number, number] {\n  // If no bounds, short-circuit and move on\n  if (!draggable.props.bounds) return [x, y];\n\n  // Clone new bounds\n  let {bounds} = draggable.props;\n  bounds = typeof bounds === 'string' ? bounds : cloneBounds(bounds);\n  const node = findDOMNode(draggable);\n\n  if (typeof bounds === 'string') {\n    const {ownerDocument} = node;\n    const ownerWindow = ownerDocument.defaultView;\n    let boundNode;\n    if (bounds === 'parent') {\n      boundNode = node.parentNode;\n    } else {\n      boundNode = ownerDocument.querySelector(bounds);\n    }\n    if (!(boundNode instanceof ownerWindow.HTMLElement)) {\n      throw new Error('Bounds selector \"' + bounds + '\" could not find an element.');\n    }\n    const boundNodeEl: HTMLElement = boundNode; // for Flow, can't seem to refine correctly\n    const nodeStyle = ownerWindow.getComputedStyle(node);\n    const boundNodeStyle = ownerWindow.getComputedStyle(boundNodeEl);\n    // Compute bounds. This is a pain with padding and offsets but this gets it exactly right.\n    bounds = {\n      left: -node.offsetLeft + int(boundNodeStyle.paddingLeft) + int(nodeStyle.marginLeft),\n      top: -node.offsetTop + int(boundNodeStyle.paddingTop) + int(nodeStyle.marginTop),\n      right: innerWidth(boundNodeEl) - outerWidth(node) - node.offsetLeft +\n        int(boundNodeStyle.paddingRight) - int(nodeStyle.marginRight),\n      bottom: innerHeight(boundNodeEl) - outerHeight(node) - node.offsetTop +\n        int(boundNodeStyle.paddingBottom) - int(nodeStyle.marginBottom)\n    };\n  }\n\n  // Keep x and y below right and bottom limits...\n  if (isNum(bounds.right)) x = Math.min(x, bounds.right);\n  if (isNum(bounds.bottom)) y = Math.min(y, bounds.bottom);\n\n  // But above left and top limits.\n  if (isNum(bounds.left)) x = Math.max(x, bounds.left);\n  if (isNum(bounds.top)) y = Math.max(y, bounds.top);\n\n  return [x, y];\n}\n\nexport function snapToGrid(grid: [number, number], pendingX: number, pendingY: number): [number, number] {\n  const x = Math.round(pendingX / grid[0]) * grid[0];\n  const y = Math.round(pendingY / grid[1]) * grid[1];\n  return [x, y];\n}\n\nexport function canDragX(draggable: Draggable): boolean {\n  return draggable.props.axis === 'both' || draggable.props.axis === 'x';\n}\n\nexport function canDragY(draggable: Draggable): boolean {\n  return draggable.props.axis === 'both' || draggable.props.axis === 'y';\n}\n\n// Get {x, y} positions from event.\nexport function getControlPosition(e: MouseTouchEvent, touchIdentifier: ?number, draggableCore: DraggableCore): ?ControlPosition {\n  const touchObj = typeof touchIdentifier === 'number' ? getTouch(e, touchIdentifier) : null;\n  if (typeof touchIdentifier === 'number' && !touchObj) return null; // not the right touch\n  const node = findDOMNode(draggableCore);\n  // User can provide an offsetParent if desired.\n  const offsetParent = draggableCore.props.offsetParent || node.offsetParent || node.ownerDocument.body;\n  return offsetXYFromParent(touchObj || e, offsetParent, draggableCore.props.scale);\n}\n\n// Create an data object exposed by <DraggableCore>'s events\nexport function createCoreData(draggable: DraggableCore, x: number, y: number): DraggableData {\n  const state = draggable.state;\n  const isStart = !isNum(state.lastX);\n  const node = findDOMNode(draggable);\n\n  if (isStart) {\n    // If this is our first move, use the x and y as last coords.\n    return {\n      node,\n      deltaX: 0, deltaY: 0,\n      lastX: x, lastY: y,\n      x, y,\n    };\n  } else {\n    // Otherwise calculate proper values.\n    return {\n      node,\n      deltaX: x - state.lastX, deltaY: y - state.lastY,\n      lastX: state.lastX, lastY: state.lastY,\n      x, y,\n    };\n  }\n}\n\n// Create an data exposed by <Draggable>'s events\nexport function createDraggableData(draggable: Draggable, coreData: DraggableData): DraggableData {\n  const scale = draggable.props.scale;\n  return {\n    node: coreData.node,\n    x: draggable.state.x + (coreData.deltaX / scale),\n    y: draggable.state.y + (coreData.deltaY / scale),\n    deltaX: (coreData.deltaX / scale),\n    deltaY: (coreData.deltaY / scale),\n    lastX: draggable.state.x,\n    lastY: draggable.state.y\n  };\n}\n\n// A lot faster than stringify/parse\nfunction cloneBounds(bounds: Bounds): Bounds {\n  return {\n    left: bounds.left,\n    top: bounds.top,\n    right: bounds.right,\n    bottom: bounds.bottom\n  };\n}\n\nfunction findDOMNode(draggable: Draggable | DraggableCore): HTMLElement {\n  const node = draggable.findDOMNode();\n  if (!node) {\n    throw new Error('<DraggableCore>: Unmounted during event!');\n  }\n  // $FlowIgnore we can't assert on HTMLElement due to tests... FIXME\n  return node;\n}\n", "// @flow\n/*eslint no-console:0*/\nexport default function log(...args: any) {\n  if (process.env.DRAGGABLE_DEBUG) console.log(...args);\n}\n", "// @flow\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport ReactDOM from 'react-dom';\nimport {matchesSelectorAndParentsTo, addEvent, removeEvent, addUserSelectStyles, getTouchIdentifier,\n        removeUserSelectStyles} from './utils/domFns';\nimport {createCoreData, getControlPosition, snapToGrid} from './utils/positionFns';\nimport {dontSetMe} from './utils/shims';\nimport log from './utils/log';\n\nimport type {EventHandler, MouseTouchEvent} from './utils/types';\nimport type {Element as ReactElement} from 'react';\n\n// Simple abstraction for dragging events names.\nconst eventsFor = {\n  touch: {\n    start: 'touchstart',\n    move: 'touchmove',\n    stop: 'touchend'\n  },\n  mouse: {\n    start: 'mousedown',\n    move: 'mousemove',\n    stop: 'mouseup'\n  }\n};\n\n// Default to mouse events.\nlet dragEventFor = eventsFor.mouse;\n\ntype DraggableCoreState = {\n  dragging: boolean,\n  lastX: number,\n  lastY: number,\n  touchIdentifier: ?number\n};\n\nexport type DraggableData = {\n  node: HTMLElement,\n  x: number, y: number,\n  deltaX: number, deltaY: number,\n  lastX: number, lastY: number,\n};\n\nexport type DraggableEventHandler = (e: MouseEvent, data: DraggableData) => void | false;\n\nexport type ControlPosition = {x: number, y: number};\nexport type PositionOffsetControlPosition = {x: number|string, y: number|string};\n\nexport type DraggableCoreDefaultProps = {\n  allowAnyClick: boolean,\n  disabled: boolean,\n  enableUserSelectHack: boolean,\n  onStart: DraggableEventHandler,\n  onDrag: DraggableEventHandler,\n  onStop: DraggableEventHandler,\n  onMouseDown: (e: MouseEvent) => void,\n  scale: number,\n};\n\nexport type DraggableCoreProps = {\n  ...DraggableCoreDefaultProps,\n  cancel: string,\n  children: ReactElement<any>,\n  offsetParent: HTMLElement,\n  grid: [number, number],\n  handle: string,\n  nodeRef?: ?React.ElementRef<any>,\n};\n\n//\n// Define <DraggableCore>.\n//\n// <DraggableCore> is for advanced usage of <Draggable>. It maintains minimal internal state so it can\n// work well with libraries that require more control over the element.\n//\n\nexport default class DraggableCore extends React.Component<DraggableCoreProps, DraggableCoreState> {\n\n  static displayName: ?string = 'DraggableCore';\n\n  static propTypes = {\n    /**\n     * `allowAnyClick` allows dragging using any mouse button.\n     * By default, we only accept the left button.\n     *\n     * Defaults to `false`.\n     */\n    allowAnyClick: PropTypes.bool,\n\n    /**\n     * `disabled`, if true, stops the <Draggable> from dragging. All handlers,\n     * with the exception of `onMouseDown`, will not fire.\n     */\n    disabled: PropTypes.bool,\n\n    /**\n     * By default, we add 'user-select:none' attributes to the document body\n     * to prevent ugly text selection during drag. If this is causing problems\n     * for your app, set this to `false`.\n     */\n    enableUserSelectHack: PropTypes.bool,\n\n    /**\n     * `offsetParent`, if set, uses the passed DOM node to compute drag offsets\n     * instead of using the parent node.\n     */\n    offsetParent: function(props: DraggableCoreProps, propName: $Keys<DraggableCoreProps>) {\n      if (props[propName] && props[propName].nodeType !== 1) {\n        throw new Error('Draggable\\'s offsetParent must be a DOM Node.');\n      }\n    },\n\n    /**\n     * `grid` specifies the x and y that dragging should snap to.\n     */\n    grid: PropTypes.arrayOf(PropTypes.number),\n\n    /**\n     * `handle` specifies a selector to be used as the handle that initiates drag.\n     *\n     * Example:\n     *\n     * ```jsx\n     *   let App = React.createClass({\n     *       render: function () {\n     *         return (\n     *            <Draggable handle=\".handle\">\n     *              <div>\n     *                  <div className=\"handle\">Click me to drag</div>\n     *                  <div>This is some other content</div>\n     *              </div>\n     *           </Draggable>\n     *         );\n     *       }\n     *   });\n     * ```\n     */\n    handle: PropTypes.string,\n\n    /**\n     * `cancel` specifies a selector to be used to prevent drag initialization.\n     *\n     * Example:\n     *\n     * ```jsx\n     *   let App = React.createClass({\n     *       render: function () {\n     *           return(\n     *               <Draggable cancel=\".cancel\">\n     *                   <div>\n     *                     <div className=\"cancel\">You can't drag from here</div>\n     *                     <div>Dragging here works fine</div>\n     *                   </div>\n     *               </Draggable>\n     *           );\n     *       }\n     *   });\n     * ```\n     */\n    cancel: PropTypes.string,\n\n    /* If running in React Strict mode, ReactDOM.findDOMNode() is deprecated.\n     * Unfortunately, in order for <Draggable> to work properly, we need raw access\n     * to the underlying DOM node. If you want to avoid the warning, pass a `nodeRef`\n     * as in this example:\n     *\n     * function MyComponent() {\n     *   const nodeRef = React.useRef(null);\n     *   return (\n     *     <Draggable nodeRef={nodeRef}>\n     *       <div ref={nodeRef}>Example Target</div>\n     *     </Draggable>\n     *   );\n     * }\n     *\n     * This can be used for arbitrarily nested components, so long as the ref ends up\n     * pointing to the actual child DOM node and not a custom component.\n     */\n    nodeRef: PropTypes.object,\n\n    /**\n     * Called when dragging starts.\n     * If this function returns the boolean false, dragging will be canceled.\n     */\n    onStart: PropTypes.func,\n\n    /**\n     * Called while dragging.\n     * If this function returns the boolean false, dragging will be canceled.\n     */\n    onDrag: PropTypes.func,\n\n    /**\n     * Called when dragging stops.\n     * If this function returns the boolean false, the drag will remain active.\n     */\n    onStop: PropTypes.func,\n\n    /**\n     * A workaround option which can be passed if onMouseDown needs to be accessed,\n     * since it'll always be blocked (as there is internal use of onMouseDown)\n     */\n    onMouseDown: PropTypes.func,\n\n    /**\n     * `scale`, if set, applies scaling while dragging an element\n     */\n    scale: PropTypes.number,\n\n    /**\n     * These properties should be defined on the child, not here.\n     */\n    className: dontSetMe,\n    style: dontSetMe,\n    transform: dontSetMe\n  };\n\n  static defaultProps: DraggableCoreDefaultProps = {\n    allowAnyClick: false, // by default only accept left click\n    disabled: false,\n    enableUserSelectHack: true,\n    onStart: function(){},\n    onDrag: function(){},\n    onStop: function(){},\n    onMouseDown: function(){},\n    scale: 1,\n  };\n\n  state: DraggableCoreState = {\n    dragging: false,\n    // Used while dragging to determine deltas.\n    lastX: NaN, lastY: NaN,\n    touchIdentifier: null\n  };\n\n  mounted: boolean = false;\n\n  componentDidMount() {\n    this.mounted = true;\n    // Touch handlers must be added with {passive: false} to be cancelable.\n    // https://developers.google.com/web/updates/2017/01/scrolling-intervention\n    const thisNode = this.findDOMNode();\n    if (thisNode) {\n      addEvent(thisNode, eventsFor.touch.start, this.onTouchStart, {passive: false});\n    }\n  }\n\n  componentWillUnmount() {\n    this.mounted = false;\n    // Remove any leftover event handlers. Remove both touch and mouse handlers in case\n    // some browser quirk caused a touch event to fire during a mouse move, or vice versa.\n    const thisNode = this.findDOMNode();\n    if (thisNode) {\n      const {ownerDocument} = thisNode;\n      removeEvent(ownerDocument, eventsFor.mouse.move, this.handleDrag);\n      removeEvent(ownerDocument, eventsFor.touch.move, this.handleDrag);\n      removeEvent(ownerDocument, eventsFor.mouse.stop, this.handleDragStop);\n      removeEvent(ownerDocument, eventsFor.touch.stop, this.handleDragStop);\n      removeEvent(thisNode, eventsFor.touch.start, this.onTouchStart, {passive: false});\n      if (this.props.enableUserSelectHack) removeUserSelectStyles(ownerDocument);\n    }\n  }\n\n  // React Strict Mode compatibility: if `nodeRef` is passed, we will use it instead of trying to find\n  // the underlying DOM node ourselves. See the README for more information.\n  findDOMNode(): ?HTMLElement {\n    return this.props?.nodeRef ? this.props?.nodeRef?.current : ReactDOM.findDOMNode(this);\n  }\n\n  handleDragStart: EventHandler<MouseTouchEvent> = (e) => {\n    // Make it possible to attach event handlers on top of this one.\n    this.props.onMouseDown(e);\n\n    // Only accept left-clicks.\n    if (!this.props.allowAnyClick && typeof e.button === 'number' && e.button !== 0) return false;\n\n    // Get nodes. Be sure to grab relative document (could be iframed)\n    const thisNode = this.findDOMNode();\n    if (!thisNode || !thisNode.ownerDocument || !thisNode.ownerDocument.body) {\n      throw new Error('<DraggableCore> not mounted on DragStart!');\n    }\n    const {ownerDocument} = thisNode;\n\n    // Short circuit if handle or cancel prop was provided and selector doesn't match.\n    if (this.props.disabled ||\n      (!(e.target instanceof ownerDocument.defaultView.Node)) ||\n      (this.props.handle && !matchesSelectorAndParentsTo(e.target, this.props.handle, thisNode)) ||\n      (this.props.cancel && matchesSelectorAndParentsTo(e.target, this.props.cancel, thisNode))) {\n      return;\n    }\n\n    // Prevent scrolling on mobile devices, like ipad/iphone.\n    // Important that this is after handle/cancel.\n    if (e.type === 'touchstart') e.preventDefault();\n\n    // Set touch identifier in component state if this is a touch event. This allows us to\n    // distinguish between individual touches on multitouch screens by identifying which\n    // touchpoint was set to this element.\n    const touchIdentifier = getTouchIdentifier(e);\n    this.setState({touchIdentifier});\n\n    // Get the current drag point from the event. This is used as the offset.\n    const position = getControlPosition(e, touchIdentifier, this);\n    if (position == null) return; // not possible but satisfies flow\n    const {x, y} = position;\n\n    // Create an event object with all the data parents need to make a decision here.\n    const coreEvent = createCoreData(this, x, y);\n\n    log('DraggableCore: handleDragStart: %j', coreEvent);\n\n    // Call event handler. If it returns explicit false, cancel.\n    log('calling', this.props.onStart);\n    const shouldUpdate = this.props.onStart(e, coreEvent);\n    if (shouldUpdate === false || this.mounted === false) return;\n\n    // Add a style to the body to disable user-select. This prevents text from\n    // being selected all over the page.\n    if (this.props.enableUserSelectHack) addUserSelectStyles(ownerDocument);\n\n    // Initiate dragging. Set the current x and y as offsets\n    // so we know how much we've moved during the drag. This allows us\n    // to drag elements around even if they have been moved, without issue.\n    this.setState({\n      dragging: true,\n\n      lastX: x,\n      lastY: y\n    });\n\n    // Add events to the document directly so we catch when the user's mouse/touch moves outside of\n    // this element. We use different events depending on whether or not we have detected that this\n    // is a touch-capable device.\n    addEvent(ownerDocument, dragEventFor.move, this.handleDrag);\n    addEvent(ownerDocument, dragEventFor.stop, this.handleDragStop);\n  };\n\n  handleDrag: EventHandler<MouseTouchEvent> = (e) => {\n\n    // Get the current drag point from the event. This is used as the offset.\n    const position = getControlPosition(e, this.state.touchIdentifier, this);\n    if (position == null) return;\n    let {x, y} = position;\n\n    // Snap to grid if prop has been provided\n    if (Array.isArray(this.props.grid)) {\n      let deltaX = x - this.state.lastX, deltaY = y - this.state.lastY;\n      [deltaX, deltaY] = snapToGrid(this.props.grid, deltaX, deltaY);\n      if (!deltaX && !deltaY) return; // skip useless drag\n      x = this.state.lastX + deltaX, y = this.state.lastY + deltaY;\n    }\n\n    const coreEvent = createCoreData(this, x, y);\n\n    log('DraggableCore: handleDrag: %j', coreEvent);\n\n    // Call event handler. If it returns explicit false, trigger end.\n    const shouldUpdate = this.props.onDrag(e, coreEvent);\n    if (shouldUpdate === false || this.mounted === false) {\n      try {\n        // $FlowIgnore\n        this.handleDragStop(new MouseEvent('mouseup'));\n      } catch (err) {\n        // Old browsers\n        const event = ((document.createEvent('MouseEvents'): any): MouseTouchEvent);\n        // I see why this insanity was deprecated\n        // $FlowIgnore\n        event.initMouseEvent('mouseup', true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);\n        this.handleDragStop(event);\n      }\n      return;\n    }\n\n    this.setState({\n      lastX: x,\n      lastY: y\n    });\n  };\n\n  handleDragStop: EventHandler<MouseTouchEvent> = (e) => {\n    if (!this.state.dragging) return;\n\n    const position = getControlPosition(e, this.state.touchIdentifier, this);\n    if (position == null) return;\n    let {x, y} = position;\n\n    // Snap to grid if prop has been provided\n    if (Array.isArray(this.props.grid)) {\n      let deltaX = x - this.state.lastX || 0;\n      let deltaY = y - this.state.lastY || 0;\n      [deltaX, deltaY] = snapToGrid(this.props.grid, deltaX, deltaY);\n      x = this.state.lastX + deltaX, y = this.state.lastY + deltaY;\n    }\n\n    const coreEvent = createCoreData(this, x, y);\n\n    // Call event handler\n    const shouldContinue = this.props.onStop(e, coreEvent);\n    if (shouldContinue === false || this.mounted === false) return false;\n\n    const thisNode = this.findDOMNode();\n    if (thisNode) {\n      // Remove user-select hack\n      if (this.props.enableUserSelectHack) removeUserSelectStyles(thisNode.ownerDocument);\n    }\n\n    log('DraggableCore: handleDragStop: %j', coreEvent);\n\n    // Reset the el.\n    this.setState({\n      dragging: false,\n      lastX: NaN,\n      lastY: NaN\n    });\n\n    if (thisNode) {\n      // Remove event handlers\n      log('DraggableCore: Removing handlers');\n      removeEvent(thisNode.ownerDocument, dragEventFor.move, this.handleDrag);\n      removeEvent(thisNode.ownerDocument, dragEventFor.stop, this.handleDragStop);\n    }\n  };\n\n  onMouseDown: EventHandler<MouseTouchEvent> = (e) => {\n    dragEventFor = eventsFor.mouse; // on touchscreen laptops we could switch back to mouse\n\n    return this.handleDragStart(e);\n  };\n\n  onMouseUp: EventHandler<MouseTouchEvent> = (e) => {\n    dragEventFor = eventsFor.mouse;\n\n    return this.handleDragStop(e);\n  };\n\n  // Same as onMouseDown (start drag), but now consider this a touch device.\n  onTouchStart: EventHandler<MouseTouchEvent> = (e) => {\n    // We're on a touch device now, so change the event handlers\n    dragEventFor = eventsFor.touch;\n\n    return this.handleDragStart(e);\n  };\n\n  onTouchEnd: EventHandler<MouseTouchEvent> = (e) => {\n    // We're on a touch device now, so change the event handlers\n    dragEventFor = eventsFor.touch;\n\n    return this.handleDragStop(e);\n  };\n\n  render(): React.Element<any> {\n    // Reuse the child provided\n    // This makes it flexible to use whatever element is wanted (div, ul, etc)\n    return React.cloneElement(React.Children.only(this.props.children), {\n      // Note: mouseMove handler is attached to document so it will still function\n      // when the user drags quickly and leaves the bounds of the element.\n      onMouseDown: this.onMouseDown,\n      onMouseUp: this.onMouseUp,\n      // onTouchStart is added on `componentDidMount` so they can be added with\n      // {passive: false}, which allows it to cancel. See\n      // https://developers.google.com/web/updates/2017/01/scrolling-intervention\n      onTouchEnd: this.onTouchEnd\n    });\n  }\n}\n", "// @flow\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport ReactDOM from 'react-dom';\nimport clsx from 'clsx';\nimport {createCSSTransform, createSVGTransform} from './utils/domFns';\nimport {canDragX, canDragY, createDraggableData, getBoundPosition} from './utils/positionFns';\nimport {dontSetMe} from './utils/shims';\nimport DraggableCore from './DraggableCore';\nimport type {ControlPosition, PositionOffsetControlPosition, DraggableCoreProps, DraggableCoreDefaultProps} from './DraggableCore';\nimport log from './utils/log';\nimport type {Bounds, DraggableEventHandler} from './utils/types';\nimport type {Element as ReactElement} from 'react';\n\ntype DraggableState = {\n  dragging: boolean,\n  dragged: boolean,\n  x: number, y: number,\n  slackX: number, slackY: number,\n  isElementSVG: boolean,\n  prevPropsPosition: ?ControlPosition,\n};\n\nexport type DraggableDefaultProps = {\n  ...DraggableCoreDefaultProps,\n  axis: 'both' | 'x' | 'y' | 'none',\n  bounds: Bounds | string | false,\n  defaultClassName: string,\n  defaultClassNameDragging: string,\n  defaultClassNameDragged: string,\n  defaultPosition: ControlPosition,\n  scale: number,\n};\n\nexport type DraggableProps = {\n  ...DraggableCoreProps,\n  ...DraggableDefaultProps,\n  positionOffset: PositionOffsetControlPosition,\n  position: ControlPosition,\n};\n\n//\n// Define <Draggable>\n//\n\nclass Draggable extends React.Component<DraggableProps, DraggableState> {\n\n  static displayName: ?string = 'Draggable';\n\n  static propTypes = {\n    // Accepts all props <DraggableCore> accepts.\n    ...DraggableCore.propTypes,\n\n    /**\n     * `axis` determines which axis the draggable can move.\n     *\n     *  Note that all callbacks will still return data as normal. This only\n     *  controls flushing to the DOM.\n     *\n     * 'both' allows movement horizontally and vertically.\n     * 'x' limits movement to horizontal axis.\n     * 'y' limits movement to vertical axis.\n     * 'none' limits all movement.\n     *\n     * Defaults to 'both'.\n     */\n    axis: PropTypes.oneOf(['both', 'x', 'y', 'none']),\n\n    /**\n     * `bounds` determines the range of movement available to the element.\n     * Available values are:\n     *\n     * 'parent' restricts movement within the Draggable's parent node.\n     *\n     * Alternatively, pass an object with the following properties, all of which are optional:\n     *\n     * {left: LEFT_BOUND, right: RIGHT_BOUND, bottom: BOTTOM_BOUND, top: TOP_BOUND}\n     *\n     * All values are in px.\n     *\n     * Example:\n     *\n     * ```jsx\n     *   let App = React.createClass({\n     *       render: function () {\n     *         return (\n     *            <Draggable bounds={{right: 300, bottom: 300}}>\n     *              <div>Content</div>\n     *           </Draggable>\n     *         );\n     *       }\n     *   });\n     * ```\n     */\n    bounds: PropTypes.oneOfType([\n      PropTypes.shape({\n        left: PropTypes.number,\n        right: PropTypes.number,\n        top: PropTypes.number,\n        bottom: PropTypes.number\n      }),\n      PropTypes.string,\n      PropTypes.oneOf([false])\n    ]),\n\n    defaultClassName: PropTypes.string,\n    defaultClassNameDragging: PropTypes.string,\n    defaultClassNameDragged: PropTypes.string,\n\n    /**\n     * `defaultPosition` specifies the x and y that the dragged item should start at\n     *\n     * Example:\n     *\n     * ```jsx\n     *      let App = React.createClass({\n     *          render: function () {\n     *              return (\n     *                  <Draggable defaultPosition={{x: 25, y: 25}}>\n     *                      <div>I start with transformX: 25px and transformY: 25px;</div>\n     *                  </Draggable>\n     *              );\n     *          }\n     *      });\n     * ```\n     */\n    defaultPosition: PropTypes.shape({\n      x: PropTypes.number,\n      y: PropTypes.number\n    }),\n    positionOffset: PropTypes.shape({\n      x: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n      y: PropTypes.oneOfType([PropTypes.number, PropTypes.string])\n    }),\n\n    /**\n     * `position`, if present, defines the current position of the element.\n     *\n     *  This is similar to how form elements in React work - if no `position` is supplied, the component\n     *  is uncontrolled.\n     *\n     * Example:\n     *\n     * ```jsx\n     *      let App = React.createClass({\n     *          render: function () {\n     *              return (\n     *                  <Draggable position={{x: 25, y: 25}}>\n     *                      <div>I start with transformX: 25px and transformY: 25px;</div>\n     *                  </Draggable>\n     *              );\n     *          }\n     *      });\n     * ```\n     */\n    position: PropTypes.shape({\n      x: PropTypes.number,\n      y: PropTypes.number\n    }),\n\n    /**\n     * These properties should be defined on the child, not here.\n     */\n    className: dontSetMe,\n    style: dontSetMe,\n    transform: dontSetMe\n  };\n\n  static defaultProps: DraggableDefaultProps = {\n    ...DraggableCore.defaultProps,\n    axis: 'both',\n    bounds: false,\n    defaultClassName: 'react-draggable',\n    defaultClassNameDragging: 'react-draggable-dragging',\n    defaultClassNameDragged: 'react-draggable-dragged',\n    defaultPosition: {x: 0, y: 0},\n    scale: 1\n  };\n\n  // React 16.3+\n  // Arity (props, state)\n  static getDerivedStateFromProps({position}: DraggableProps, {prevPropsPosition}: DraggableState): ?$Shape<DraggableState> {\n    // Set x/y if a new position is provided in props that is different than the previous.\n    if (\n      position &&\n      (!prevPropsPosition ||\n        position.x !== prevPropsPosition.x || position.y !== prevPropsPosition.y\n      )\n    ) {\n      log('Draggable: getDerivedStateFromProps %j', {position, prevPropsPosition});\n      return {\n        x: position.x,\n        y: position.y,\n        prevPropsPosition: {...position}\n      };\n    }\n    return null;\n  }\n\n  constructor(props: DraggableProps) {\n    super(props);\n\n    this.state = {\n      // Whether or not we are currently dragging.\n      dragging: false,\n\n      // Whether or not we have been dragged before.\n      dragged: false,\n\n      // Current transform x and y.\n      x: props.position ? props.position.x : props.defaultPosition.x,\n      y: props.position ? props.position.y : props.defaultPosition.y,\n\n      prevPropsPosition: {...props.position},\n\n      // Used for compensating for out-of-bounds drags\n      slackX: 0, slackY: 0,\n\n      // Can only determine if SVG after mounting\n      isElementSVG: false\n    };\n\n    if (props.position && !(props.onDrag || props.onStop)) {\n      // eslint-disable-next-line no-console\n      console.warn('A `position` was applied to this <Draggable>, without drag handlers. This will make this ' +\n        'component effectively undraggable. Please attach `onDrag` or `onStop` handlers so you can adjust the ' +\n        '`position` of this element.');\n    }\n  }\n\n  componentDidMount() {\n    // Check to see if the element passed is an instanceof SVGElement\n    if(typeof window.SVGElement !== 'undefined' && this.findDOMNode() instanceof window.SVGElement) {\n      this.setState({isElementSVG: true});\n    }\n  }\n\n  componentWillUnmount() {\n    this.setState({dragging: false}); // prevents invariant if unmounted while dragging\n  }\n\n  // React Strict Mode compatibility: if `nodeRef` is passed, we will use it instead of trying to find\n  // the underlying DOM node ourselves. See the README for more information.\n  findDOMNode(): ?HTMLElement {\n    return this.props?.nodeRef?.current ?? ReactDOM.findDOMNode(this);\n  }\n\n  onDragStart: DraggableEventHandler = (e, coreData) => {\n    log('Draggable: onDragStart: %j', coreData);\n\n    // Short-circuit if user's callback killed it.\n    const shouldStart = this.props.onStart(e, createDraggableData(this, coreData));\n    // Kills start event on core as well, so move handlers are never bound.\n    if (shouldStart === false) return false;\n\n    this.setState({dragging: true, dragged: true});\n  };\n\n  onDrag: DraggableEventHandler = (e, coreData) => {\n    if (!this.state.dragging) return false;\n    log('Draggable: onDrag: %j', coreData);\n\n    const uiData = createDraggableData(this, coreData);\n\n    const newState: $Shape<DraggableState> = {\n      x: uiData.x,\n      y: uiData.y\n    };\n\n    // Keep within bounds.\n    if (this.props.bounds) {\n      // Save original x and y.\n      const {x, y} = newState;\n\n      // Add slack to the values used to calculate bound position. This will ensure that if\n      // we start removing slack, the element won't react to it right away until it's been\n      // completely removed.\n      newState.x += this.state.slackX;\n      newState.y += this.state.slackY;\n\n      // Get bound position. This will ceil/floor the x and y within the boundaries.\n      const [newStateX, newStateY] = getBoundPosition(this, newState.x, newState.y);\n      newState.x = newStateX;\n      newState.y = newStateY;\n\n      // Recalculate slack by noting how much was shaved by the boundPosition handler.\n      newState.slackX = this.state.slackX + (x - newState.x);\n      newState.slackY = this.state.slackY + (y - newState.y);\n\n      // Update the event we fire to reflect what really happened after bounds took effect.\n      uiData.x = newState.x;\n      uiData.y = newState.y;\n      uiData.deltaX = newState.x - this.state.x;\n      uiData.deltaY = newState.y - this.state.y;\n    }\n\n    // Short-circuit if user's callback killed it.\n    const shouldUpdate = this.props.onDrag(e, uiData);\n    if (shouldUpdate === false) return false;\n\n    this.setState(newState);\n  };\n\n  onDragStop: DraggableEventHandler = (e, coreData) => {\n    if (!this.state.dragging) return false;\n\n    // Short-circuit if user's callback killed it.\n    const shouldContinue = this.props.onStop(e, createDraggableData(this, coreData));\n    if (shouldContinue === false) return false;\n\n    log('Draggable: onDragStop: %j', coreData);\n\n    const newState: $Shape<DraggableState> = {\n      dragging: false,\n      slackX: 0,\n      slackY: 0\n    };\n\n    // If this is a controlled component, the result of this operation will be to\n    // revert back to the old position. We expect a handler on `onDragStop`, at the least.\n    const controlled = Boolean(this.props.position);\n    if (controlled) {\n      const {x, y} = this.props.position;\n      newState.x = x;\n      newState.y = y;\n    }\n\n    this.setState(newState);\n  };\n\n  render(): ReactElement<any> {\n    const {\n      axis,\n      bounds,\n      children,\n      defaultPosition,\n      defaultClassName,\n      defaultClassNameDragging,\n      defaultClassNameDragged,\n      position,\n      positionOffset,\n      scale,\n      ...draggableCoreProps\n    } = this.props;\n\n    let style = {};\n    let svgTransform = null;\n\n    // If this is controlled, we don't want to move it - unless it's dragging.\n    const controlled = Boolean(position);\n    const draggable = !controlled || this.state.dragging;\n\n    const validPosition = position || defaultPosition;\n    const transformOpts = {\n      // Set left if horizontal drag is enabled\n      x: canDragX(this) && draggable ?\n        this.state.x :\n        validPosition.x,\n\n      // Set top if vertical drag is enabled\n      y: canDragY(this) && draggable ?\n        this.state.y :\n        validPosition.y\n    };\n\n    // If this element was SVG, we use the `transform` attribute.\n    if (this.state.isElementSVG) {\n      svgTransform = createSVGTransform(transformOpts, positionOffset);\n    } else {\n      // Add a CSS transform to move the element around. This allows us to move the element around\n      // without worrying about whether or not it is relatively or absolutely positioned.\n      // If the item you are dragging already has a transform set, wrap it in a <span> so <Draggable>\n      // has a clean slate.\n      style = createCSSTransform(transformOpts, positionOffset);\n    }\n\n    // Mark with class while dragging\n    const className = clsx((children.props.className || ''), defaultClassName, {\n      [defaultClassNameDragging]: this.state.dragging,\n      [defaultClassNameDragged]: this.state.dragged\n    });\n\n    // Reuse the child provided\n    // This makes it flexible to use whatever element is wanted (div, ul, etc)\n    return (\n      <DraggableCore {...draggableCoreProps} onStart={this.onDragStart} onDrag={this.onDrag} onStop={this.onDragStop}>\n        {React.cloneElement(React.Children.only(children), {\n          className: className,\n          style: {...children.props.style, ...style},\n          transform: svgTransform\n        })}\n      </DraggableCore>\n    );\n  }\n}\n\nexport {Draggable as default, DraggableCore};\n", "const {default: Draggable, DraggableCore} = require('./Draggable');\n\n// Previous versions of this lib exported <Draggable> as the root export. As to no-// them, or TypeScript, we export *both* as the root and as 'default'.\n// See https://github.com/mzabriskie/react-draggable/pull/254\n// and https://github.com/mzabriskie/react-draggable/issues/266\nmodule.exports = Draggable;\nmodule.exports.default = Draggable;\nmodule.exports.DraggableCore = DraggableCore;\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n\nfunction emptyFunction() {}\nfunction emptyFunctionWithReset() {}\nemptyFunctionWithReset.resetWarningCache = emptyFunction;\n\nmodule.exports = function() {\n  function shim(props, propName, componentName, location, propFullName, secret) {\n    if (secret === ReactPropTypesSecret) {\n      // It is still safe when called from React.\n      return;\n    }\n    var err = new Error(\n      'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n      'Use PropTypes.checkPropTypes() to call them. ' +\n      'Read more at http://fb.me/use-check-prop-types'\n    );\n    err.name = 'Invariant Violation';\n    throw err;\n  };\n  shim.isRequired = shim;\n  function getShim() {\n    return shim;\n  };\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.\n  var ReactPropTypes = {\n    array: shim,\n    bigint: shim,\n    bool: shim,\n    func: shim,\n    number: shim,\n    object: shim,\n    string: shim,\n    symbol: shim,\n\n    any: shim,\n    arrayOf: getShim,\n    element: shim,\n    elementType: shim,\n    instanceOf: getShim,\n    node: shim,\n    objectOf: getShim,\n    oneOf: getShim,\n    oneOfType: getShim,\n    shape: getShim,\n    exact: getShim,\n\n    checkPropTypes: emptyFunctionWithReset,\n    resetWarningCache: emptyFunction\n  };\n\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactIs = require('react-is');\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  module.exports = require('./factoryWithTypeCheckers')(ReactIs.isElement, throwOnDirectAccess);\n} else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = require('./factoryWithThrowingShims')();\n}\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nmodule.exports = ReactPropTypesSecret;\n", "module.exports = __WEBPACK_EXTERNAL_MODULE__359__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__318__;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(16);\n", ""], "names": ["findInArray", "array", "callback", "i", "length", "apply", "isFunction", "func", "Object", "prototype", "toString", "call", "isNum", "num", "isNaN", "int", "a", "parseInt", "dontSetMe", "props", "propName", "componentName", "Error", "prefixes", "getPrefix", "prop", "window", "style", "document", "documentElement", "browserPrefixToKey", "prefix", "kebabToTitleCase", "browserPrefixToStyle", "toLowerCase", "str", "out", "shouldCapitalize", "toUpperCase", "browserPrefix", "matchesSelectorFunc", "matchesSelector", "el", "selector", "method", "matchesSelectorAndParentsTo", "baseNode", "node", "parentNode", "addEvent", "event", "handler", "inputOptions", "options", "capture", "addEventListener", "attachEvent", "removeEvent", "removeEventListener", "detachEvent", "outerHeight", "height", "clientHeight", "computedStyle", "ownerDocument", "defaultView", "getComputedStyle", "borderTopWidth", "borderBottomWidth", "outerWidth", "width", "clientWidth", "borderLeftWidth", "borderRightWidth", "innerHeight", "paddingTop", "paddingBottom", "innerWidth", "paddingLeft", "paddingRight", "offsetXYFromParent", "evt", "offsetParent", "scale", "isBody", "body", "offsetParentRect", "left", "top", "getBoundingClientRect", "x", "clientX", "scrollLeft", "y", "clientY", "scrollTop", "createCSSTransform", "controlPos", "positionOffset", "translation", "getTranslation", "createSVGTransform", "unitSuffix", "defaultX", "defaultY", "getTouch", "e", "identifier", "targetTouches", "t", "changedTouches", "getTouchIdentifier", "addUserSelectStyles", "doc", "styleEl", "getElementById", "createElement", "type", "id", "innerHTML", "getElementsByTagName", "append<PERSON><PERSON><PERSON>", "addClassName", "removeUserSelectStyles", "removeClassName", "selection", "empty", "getSelection", "removeAllRanges", "className", "classList", "add", "match", "RegExp", "remove", "replace", "getBoundPosition", "draggable", "bounds", "cloneBounds", "findDOMNode", "ownerWindow", "boundNode", "querySelector", "HTMLElement", "boundNodeEl", "nodeStyle", "boundNodeStyle", "offsetLeft", "marginLeft", "offsetTop", "marginTop", "right", "marginRight", "bottom", "marginBottom", "Math", "min", "max", "snapToGrid", "grid", "pendingX", "pendingY", "round", "canDragX", "axis", "canDragY", "getControlPosition", "touchIdentifier", "draggableCore", "touchObj", "createCoreData", "state", "isStart", "lastX", "deltaX", "deltaY", "lastY", "createDraggableData", "coreData", "log", "console", "React", "PropTypes", "ReactDOM", "eventsFor", "touch", "start", "move", "stop", "mouse", "dragEventFor", "DraggableCore", "dragging", "NaN", "onMouseDown", "allowAnyClick", "button", "thisNode", "disabled", "target", "Node", "handle", "cancel", "preventDefault", "setState", "position", "coreEvent", "onStart", "shouldUpdate", "mounted", "enableUserSelectHack", "handleDrag", "handleDragStop", "Array", "isArray", "onDrag", "MouseEvent", "err", "createEvent", "initMouseEvent", "shouldC<PERSON><PERSON>ue", "onStop", "handleDragStart", "onTouchStart", "passive", "nodeRef", "current", "cloneElement", "Children", "only", "children", "onMouseUp", "onTouchEnd", "Component", "bool", "nodeType", "arrayOf", "number", "string", "object", "transform", "clsx", "Draggable", "shouldStart", "dragged", "uiData", "newState", "slackX", "slackY", "newStateX", "newStateY", "controlled", "Boolean", "defaultPosition", "prevPropsPosition", "isElementSVG", "warn", "SVGElement", "defaultClassName", "defaultClassNameDragging", "defaultClassNameDragged", "draggableCoreProps", "svgTransform", "validPosition", "transformOpts", "onDragStart", "onDragStop", "propTypes", "oneOf", "oneOfType", "shape", "defaultProps", "default", "require", "module", "exports"], "sourceRoot": ""}