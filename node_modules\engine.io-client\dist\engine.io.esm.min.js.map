{"version": 3, "file": "engine.io.esm.min.js", "sources": ["../node_modules/engine.io-parser/build/esm/commons.js", "../node_modules/engine.io-parser/build/esm/encodePacket.browser.js", "../node_modules/@socket.io/base64-arraybuffer/dist/base64-arraybuffer.es5.js", "../node_modules/engine.io-parser/build/esm/decodePacket.browser.js", "../node_modules/engine.io-parser/build/esm/index.js", "../node_modules/@socket.io/component-emitter/index.mjs", "../build/esm/globalThis.browser.js", "../build/esm/util.js", "../build/esm/transport.js", "../build/esm/contrib/yeast.js", "../build/esm/contrib/parseqs.js", "../build/esm/contrib/has-cors.js", "../build/esm/transports/xmlhttprequest.browser.js", "../build/esm/transports/polling.js", "../build/esm/transports/websocket-constructor.browser.js", "../build/esm/transports/websocket.js", "../build/esm/transports/index.js", "../build/esm/contrib/parseuri.js", "../build/esm/socket.js", "../build/esm/index.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + content);\n    };\n    return fileReader.readAsDataURL(data);\n};\nexport default encodePacket;\n", "/*\n * base64-arraybuffer 1.0.1 <https://github.com/niklasvh/base64-arraybuffer>\n * Copyright (c) 2022 <PERSON><PERSON> <https://hertzen.com>\n * Released under MIT License\n */\nvar chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nvar lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (var i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nvar encode = function (arraybuffer) {\n    var bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nvar decode = function (base64) {\n    var bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    var arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n\nexport { decode, encode };\n//# sourceMappingURL=base64-arraybuffer.es5.js.map\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE } from \"./commons.js\";\nimport { decode } from \"@socket.io/base64-arraybuffer\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType)\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1)\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type]\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            return data instanceof ArrayBuffer ? new Blob([data]) : data;\n        case \"arraybuffer\":\n        default:\n            return data; // assuming the data is already an ArrayBuffer\n    }\n};\nexport default decodePacket;\n", "import encodePacket from \"./encodePacket.js\";\nimport decodePacket from \"./decodePacket.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, encodedPacket => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload };\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\n", "import { globalThisShim as globalThis } from \"./globalThis.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nclass TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n}\n", "// imported from https://github.com/unshiftio/yeast\n'use strict';\nconst alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split(''), length = 64, map = {};\nlet seed = 0, i = 0, prev;\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nexport function encode(num) {\n    let encoded = '';\n    do {\n        encoded = alphabet[num % length] + encoded;\n        num = Math.floor(num / length);\n    } while (num > 0);\n    return encoded;\n}\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nexport function decode(str) {\n    let decoded = 0;\n    for (i = 0; i < str.length; i++) {\n        decoded = decoded * length + map[str.charAt(i)];\n    }\n    return decoded;\n}\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nexport function yeast() {\n    const now = encode(+new Date());\n    if (now !== prev)\n        return seed = 0, prev = now;\n    return now + '.' + encode(seed++);\n}\n//\n// Map each character to its index.\n//\nfor (; i < length; i++)\n    map[alphabet[i]] = i;\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "// browser shim for xmlhttprequest module\nimport { hasCORS } from \"../contrib/has-cors.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nexport function XHR(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { encode } from \"../contrib/parseqs.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nimport { XHR as XMLHttpRequest } from \"./xmlhttprequest.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nfunction empty() { }\nconst hasXHR2 = (function () {\n    const xhr = new XMLHttpRequest({\n        xdomain: false,\n    });\n    return null != xhr.responseType;\n})();\nexport class Polling extends Transport {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        this.polling = false;\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n            this.xs = opts.secure !== isSSL;\n        }\n        /**\n         * XHR supports binary\n         */\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this.poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this.polling || !this.writable) {\n            let total = 0;\n            if (this.polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    poll() {\n        this.polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this.polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this.poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        let query = this.query || {};\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        let port = \"\";\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        // avoid port if default for schema\n        if (this.opts.port &&\n            ((\"https\" === schema && Number(this.opts.port) !== 443) ||\n                (\"http\" === schema && Number(this.opts.port) !== 80))) {\n            port = \":\" + this.opts.port;\n        }\n        const encodedQuery = encode(query);\n        const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n        return (schema +\n            \"://\" +\n            (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n            port +\n            this.opts.path +\n            (encodedQuery.length ? \"?\" + encodedQuery : \"\"));\n    }\n    /**\n     * Creates a request.\n     *\n     * @param {String} method\n     * @private\n     */\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd, xs: this.xs }, this.opts);\n        return new Request(this.uri(), opts);\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(uri, opts) {\n        super();\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.method = opts.method || \"GET\";\n        this.uri = uri;\n        this.async = false !== opts.async;\n        this.data = undefined !== opts.data ? opts.data : null;\n        this.create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    create() {\n        const opts = pick(this.opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this.opts.xd;\n        opts.xscheme = !!this.opts.xs;\n        const xhr = (this.xhr = new XMLHttpRequest(opts));\n        try {\n            xhr.open(this.method, this.uri, this.async);\n            try {\n                if (this.opts.extraHeaders) {\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this.opts.extraHeaders) {\n                        if (this.opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this.method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this.opts.withCredentials;\n            }\n            if (this.opts.requestTimeout) {\n                xhr.timeout = this.opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this.onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this.data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this.onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this.index = Request.requestsCount++;\n            Request.requests[this.index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    onError(err) {\n        this.emitReserved(\"error\", err, this.xhr);\n        this.cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    cleanup(fromError) {\n        if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n            return;\n        }\n        this.xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this.xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this.index];\n        }\n        this.xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    onLoad() {\n        const data = this.xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this.cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this.cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\n", "import { globalThisShim as globalThis } from \"../globalThis.js\";\nexport const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const WebSocket = globalThis.WebSocket || globalThis.MozWebSocket;\nexport const usingBrowserWebSocket = true;\nexport const defaultBinaryType = \"arraybuffer\";\n", "import { Transport } from \"../transport.js\";\nimport { encode } from \"../contrib/parseqs.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { pick } from \"../util.js\";\nimport { defaultBinaryType, nextTick, usingBrowserWebSocket, WebSocket, } from \"./websocket-constructor.js\";\nimport { encodePacket } from \"engine.io-parser\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class WS extends Transport {\n    /**\n     * WebSocket transport constructor.\n     *\n     * @param {Object} opts - connection options\n     * @protected\n     */\n    constructor(opts) {\n        super(opts);\n        this.supportsBinary = !opts.forceBase64;\n    }\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        if (!this.check()) {\n            // let probe timeout\n            return;\n        }\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws =\n                usingBrowserWebSocket && !isReactNative\n                    ? protocols\n                        ? new WebSocket(uri, protocols)\n                        : new WebSocket(uri)\n                    : new WebSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType || defaultBinaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // always create a new object (GH-437)\n                const opts = {};\n                if (!usingBrowserWebSocket) {\n                    if (packet.options) {\n                        opts.compress = packet.options.compress;\n                    }\n                    if (this.opts.perMessageDeflate) {\n                        const len = \n                        // @ts-ignore\n                        \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n                        if (len < this.opts.perMessageDeflate.threshold) {\n                            opts.compress = false;\n                        }\n                    }\n                }\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    if (usingBrowserWebSocket) {\n                        // TypeError is thrown when passing the second argument on Safari\n                        this.ws.send(data);\n                    }\n                    else {\n                        this.ws.send(data, opts);\n                    }\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        let query = this.query || {};\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        let port = \"\";\n        // avoid port if default for schema\n        if (this.opts.port &&\n            ((\"wss\" === schema && Number(this.opts.port) !== 443) ||\n                (\"ws\" === schema && Number(this.opts.port) !== 80))) {\n            port = \":\" + this.opts.port;\n        }\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        const encodedQuery = encode(query);\n        const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n        return (schema +\n            \"://\" +\n            (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n            port +\n            this.opts.path +\n            (encodedQuery.length ? \"?\" + encodedQuery : \"\"));\n    }\n    /**\n     * Feature detection for WebSocket.\n     *\n     * @return {Boolean} whether this transport is available.\n     * @private\n     */\n    check() {\n        return !!WebSocket;\n    }\n}\n", "import { Polling } from \"./polling.js\";\nimport { WS } from \"./websocket.js\";\nexport const transports = {\n    websocket: WS,\n    polling: Polling,\n};\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nexport class Socket extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts = {}) {\n        super();\n        this.writeBuffer = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            uri = parse(uri);\n            opts.hostname = uri.host;\n            opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n            opts.port = uri.port;\n            if (uri.query)\n                opts.query = uri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = opts.transports || [\"polling\", \"websocket\"];\n        this.writeBuffer = [];\n        this.prevBufferLen = 0;\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: true,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        // set on handshake\n        this.id = null;\n        this.upgrades = null;\n        this.pingInterval = null;\n        this.pingTimeout = null;\n        // set on heartbeat\n        this.pingTimeoutTimer = null;\n        if (typeof addEventListener === \"function\") {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this.beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this.offlineEventListener = () => {\n                    this.onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                addEventListener(\"offline\", this.offlineEventListener, false);\n            }\n        }\n        this.open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts.transportOptions[name], this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        });\n        return new transports[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    open() {\n        let transport;\n        if (this.opts.rememberUpgrade &&\n            Socket.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1) {\n            transport = \"websocket\";\n        }\n        else if (0 === this.transports.length) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        else {\n            transport = this.transports[0];\n        }\n        this.readyState = \"opening\";\n        // Retry with the next transport if the transport is disabled (jsonp: false)\n        try {\n            transport = this.createTransport(transport);\n        }\n        catch (e) {\n            this.transports.shift();\n            this.open();\n            return;\n        }\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this.onDrain.bind(this))\n            .on(\"packet\", this.onPacket.bind(this))\n            .on(\"error\", this.onError.bind(this))\n            .on(\"close\", (reason) => this.onClose(\"transport close\", reason));\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        Socket.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        transport.open();\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n        // we check for `readyState` in case an `open`\n        // listener already closed the socket\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            let i = 0;\n            const l = this.upgrades.length;\n            for (; i < l; i++) {\n                this.probe(this.upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this.resetPingTimeout();\n                    this.sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this.onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this.upgrades = this.filterUpgrades(data.upgrades);\n        this.pingInterval = data.pingInterval;\n        this.pingTimeout = data.pingTimeout;\n        this.maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this.resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    resetPingTimeout() {\n        this.clearTimeoutFn(this.pingTimeoutTimer);\n        this.pingTimeoutTimer = this.setTimeoutFn(() => {\n            this.onClose(\"ping timeout\");\n        }, this.pingInterval + this.pingTimeout);\n        if (this.opts.autoUnref) {\n            this.pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    onDrain() {\n        this.writeBuffer.splice(0, this.prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this.prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this.getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this.prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    getWritablePackets() {\n        const shouldCheckPayloadSize = this.maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this.maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    send(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this.onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    onError(err) {\n        Socket.priorWebsocketSuccess = false;\n        this.emitReserved(\"error\", err);\n        this.onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this.pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (typeof removeEventListener === \"function\") {\n                removeEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n                removeEventListener(\"offline\", this.offlineEventListener, false);\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this.prevBufferLen = 0;\n        }\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        let i = 0;\n        const j = upgrades.length;\n        for (; i < j; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\nSocket.protocol = protocol;\n", "import { Socket } from \"./socket.js\";\nexport { Socket };\nexport const protocol = Socket.protocol;\nexport { Transport } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\nexport { nextTick } from \"./transports/websocket-constructor.js\";\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "encodePacket", "supportsBinary", "callback", "encodeBlobAsBase64", "obj", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "chars", "lookup", "Uint8Array", "i", "length", "charCodeAt", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "decoded", "base64", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "len", "p", "arraybuffer", "bytes", "decode", "SEPARATOR", "String", "fromCharCode", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "this", "_callbacks", "push", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "cb", "callbacks", "splice", "emit", "args", "Array", "slice", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "globalThisShim", "self", "window", "Function", "pick", "attr", "reduce", "acc", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "globalThis", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "setTimeoutFn", "bind", "clearTimeoutFn", "TransportError", "Error", "constructor", "reason", "description", "context", "super", "Transport", "writable", "query", "socket", "onError", "open", "readyState", "doOpen", "close", "doClose", "onClose", "send", "packets", "write", "onOpen", "onData", "packet", "onPacket", "details", "pause", "onPause", "alphabet", "map", "prev", "seed", "encode", "num", "encoded", "Math", "floor", "yeast", "now", "Date", "str", "encodeURIComponent", "value", "XMLHttpRequest", "err", "hasCORS", "XHR", "xdomain", "e", "concat", "join", "empty", "hasXHR2", "responseType", "Request", "uri", "method", "async", "undefined", "xd", "xscheme", "xs", "xhr", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "withCredentials", "requestTimeout", "timeout", "onreadystatechange", "status", "onLoad", "document", "index", "requestsCount", "requests", "cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "nextTick", "Promise", "resolve", "then", "WebSocket", "MozWebSocket", "isReactNative", "navigator", "product", "toLowerCase", "transports", "websocket", "forceBase64", "name", "check", "protocols", "headers", "ws", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "lastPacket", "schema", "secure", "port", "Number", "timestampRequests", "timestampParam", "b64", "<PERSON><PERSON><PERSON><PERSON>", "hostname", "indexOf", "path", "polling", "location", "isSSL", "protocol", "poll", "total", "doPoll", "encodedPayload", "encodedPackets", "decodedPacket", "decodePayload", "count", "encodePayload", "doWrite", "sid", "request", "assign", "req", "xhrStatus", "pollXhr", "re", "parts", "parse", "src", "b", "replace", "m", "exec", "source", "host", "authority", "ipv6uri", "pathNames", "regx", "names", "query<PERSON><PERSON>", "$0", "$1", "$2", "Socket", "writeBuffer", "prevBufferLen", "agent", "upgrade", "rememberUpgrade", "addTrailingSlash", "rejectUnauthorized", "perMessageDeflate", "threshold", "transportOptions", "closeOnBeforeunload", "qs", "qry", "pairs", "l", "pair", "decodeURIComponent", "id", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "beforeunloadEventListener", "transport", "offlineEventListener", "createTransport", "EIO", "priorWebsocketSuccess", "shift", "setTransport", "onDrain", "probe", "failed", "onTransportOpen", "msg", "upgrading", "flush", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "onHandshake", "JSON", "resetPingTimeout", "sendPacket", "code", "filterUpgrades", "maxPayload", "getWritablePackets", "payloadSize", "c", "utf8Length", "ceil", "byteLength", "size", "options", "compress", "cleanupAndClose", "waitForUpgrade", "filteredUpgrades", "j"], "mappings": ";;;;;AAAA,MAAMA,EAAeC,OAAOC,OAAO,MACnCF,EAAmB,KAAI,IACvBA,EAAoB,MAAI,IACxBA,EAAmB,KAAI,IACvBA,EAAmB,KAAI,IACvBA,EAAsB,QAAI,IAC1BA,EAAsB,QAAI,IAC1BA,EAAmB,KAAI,IACvB,MAAMG,EAAuBF,OAAOC,OAAO,MAC3CD,OAAOG,KAAKJ,GAAcK,SAAQC,IAC9BH,EAAqBH,EAAaM,IAAQA,CAAG,IAEjD,MAAMC,EAAe,CAAEC,KAAM,QAASC,KAAM,gBCXtCC,EAAiC,mBAATC,MACT,oBAATA,MACqC,6BAAzCV,OAAOW,UAAUC,SAASC,KAAKH,MACjCI,EAA+C,mBAAhBC,YAO/BC,EAAe,EAAGT,OAAMC,QAAQS,EAAgBC,KAClD,OAAIT,GAAkBD,aAAgBE,KAC9BO,EACOC,EAASV,GAGTW,EAAmBX,EAAMU,GAG/BJ,IACJN,aAAgBO,cAfVK,EAegCZ,EAdN,mBAAvBO,YAAYM,OACpBN,YAAYM,OAAOD,GACnBA,GAAOA,EAAIE,kBAAkBP,cAa3BE,EACOC,EAASV,GAGTW,EAAmB,IAAIT,KAAK,CAACF,IAAQU,GAI7CA,EAASnB,EAAaQ,IAASC,GAAQ,KAxBnCY,KAwBuC,EAEhDD,EAAqB,CAACX,EAAMU,KAC9B,MAAMK,EAAa,IAAIC,WAKvB,OAJAD,EAAWE,OAAS,WAChB,MAAMC,EAAUH,EAAWI,OAAOC,MAAM,KAAK,GAC7CV,EAAS,IAAMQ,IAEZH,EAAWM,cAAcrB,EAAK,EC9BzC,IAHA,IAAIsB,EAAQ,mEAERC,EAA+B,oBAAfC,WAA6B,GAAK,IAAIA,WAAW,KAC5DC,EAAI,EAAGA,EAAIH,EAAMI,OAAQD,IAC9BF,EAAOD,EAAMK,WAAWF,IAAMA,ECPlC,MAAMnB,EAA+C,mBAAhBC,YAC/BqB,EAAe,CAACC,EAAeC,KACjC,GAA6B,iBAAlBD,EACP,MAAO,CACH9B,KAAM,UACNC,KAAM+B,EAAUF,EAAeC,IAGvC,MAAM/B,EAAO8B,EAAcG,OAAO,GAClC,GAAa,MAATjC,EACA,MAAO,CACHA,KAAM,UACNC,KAAMiC,EAAmBJ,EAAcK,UAAU,GAAIJ,IAI7D,OADmBpC,EAAqBK,GAIjC8B,EAAcH,OAAS,EACxB,CACE3B,KAAML,EAAqBK,GAC3BC,KAAM6B,EAAcK,UAAU,IAEhC,CACEnC,KAAML,EAAqBK,IARxBD,CASN,EAEHmC,EAAqB,CAACjC,EAAM8B,KAC9B,GAAIxB,EAAuB,CACvB,MAAM6B,EDLD,SAAUC,GACnB,IAA8DX,EAAUY,EAAUC,EAAUC,EAAUC,EAAlGC,EAA+B,IAAhBL,EAAOV,OAAegB,EAAMN,EAAOV,OAAWiB,EAAI,EACnC,MAA9BP,EAAOA,EAAOV,OAAS,KACvBe,IACkC,MAA9BL,EAAOA,EAAOV,OAAS,IACvBe,KAGR,IAAIG,EAAc,IAAIrC,YAAYkC,GAAeI,EAAQ,IAAIrB,WAAWoB,GACxE,IAAKnB,EAAI,EAAGA,EAAIiB,EAAKjB,GAAK,EACtBY,EAAWd,EAAOa,EAAOT,WAAWF,IACpCa,EAAWf,EAAOa,EAAOT,WAAWF,EAAI,IACxCc,EAAWhB,EAAOa,EAAOT,WAAWF,EAAI,IACxCe,EAAWjB,EAAOa,EAAOT,WAAWF,EAAI,IACxCoB,EAAMF,KAAQN,GAAY,EAAMC,GAAY,EAC5CO,EAAMF,MAAoB,GAAXL,IAAkB,EAAMC,GAAY,EACnDM,EAAMF,MAAoB,EAAXJ,IAAiB,EAAiB,GAAXC,EAE1C,OAAOI,CACX,CCdwBE,CAAO9C,GACvB,OAAO+B,EAAUI,EAASL,GAG1B,MAAO,CAAEM,QAAQ,EAAMpC,SAGzB+B,EAAY,CAAC/B,EAAM8B,IAEZ,SADDA,GAEO9B,aAAgBO,YAAc,IAAIL,KAAK,CAACF,IAGxCA,EC3Cb+C,EAAYC,OAAOC,aAAa,ICI/B,SAASC,EAAQtC,GACtB,GAAIA,EAAK,OAWX,SAAeA,GACb,IAAK,IAAIf,KAAOqD,EAAQ/C,UACtBS,EAAIf,GAAOqD,EAAQ/C,UAAUN,GAE/B,OAAOe,CACT,CAhBkBuC,CAAMvC,EACxB,CA0BAsC,EAAQ/C,UAAUiD,GAClBF,EAAQ/C,UAAUkD,iBAAmB,SAASC,EAAOC,GAInD,OAHAC,KAAKC,WAAaD,KAAKC,YAAc,IACpCD,KAAKC,WAAW,IAAMH,GAASE,KAAKC,WAAW,IAAMH,IAAU,IAC7DI,KAAKH,GACDC,IACT,EAYAN,EAAQ/C,UAAUwD,KAAO,SAASL,EAAOC,GACvC,SAASH,IACPI,KAAKI,IAAIN,EAAOF,GAChBG,EAAGM,MAAML,KAAMM,WAKjB,OAFAV,EAAGG,GAAKA,EACRC,KAAKJ,GAAGE,EAAOF,GACRI,IACT,EAYAN,EAAQ/C,UAAUyD,IAClBV,EAAQ/C,UAAU4D,eAClBb,EAAQ/C,UAAU6D,mBAClBd,EAAQ/C,UAAU8D,oBAAsB,SAASX,EAAOC,GAItD,GAHAC,KAAKC,WAAaD,KAAKC,YAAc,GAGjC,GAAKK,UAAUpC,OAEjB,OADA8B,KAAKC,WAAa,GACXD,KAIT,IAUIU,EAVAC,EAAYX,KAAKC,WAAW,IAAMH,GACtC,IAAKa,EAAW,OAAOX,KAGvB,GAAI,GAAKM,UAAUpC,OAEjB,cADO8B,KAAKC,WAAW,IAAMH,GACtBE,KAKT,IAAK,IAAI/B,EAAI,EAAGA,EAAI0C,EAAUzC,OAAQD,IAEpC,IADAyC,EAAKC,EAAU1C,MACJ8B,GAAMW,EAAGX,KAAOA,EAAI,CAC7BY,EAAUC,OAAO3C,EAAG,GACpB,MAUJ,OAJyB,IAArB0C,EAAUzC,eACL8B,KAAKC,WAAW,IAAMH,GAGxBE,IACT,EAUAN,EAAQ/C,UAAUkE,KAAO,SAASf,GAChCE,KAAKC,WAAaD,KAAKC,YAAc,GAKrC,IAHA,IAAIa,EAAO,IAAIC,MAAMT,UAAUpC,OAAS,GACpCyC,EAAYX,KAAKC,WAAW,IAAMH,GAE7B7B,EAAI,EAAGA,EAAIqC,UAAUpC,OAAQD,IACpC6C,EAAK7C,EAAI,GAAKqC,UAAUrC,GAG1B,GAAI0C,EAEG,CAAI1C,EAAI,EAAb,IAAK,IAAWiB,GADhByB,EAAYA,EAAUK,MAAM,IACI9C,OAAQD,EAAIiB,IAAOjB,EACjD0C,EAAU1C,GAAGoC,MAAML,KAAMc,EADK5C,CAKlC,OAAO8B,IACT,EAGAN,EAAQ/C,UAAUsE,aAAevB,EAAQ/C,UAAUkE,KAUnDnB,EAAQ/C,UAAUuE,UAAY,SAASpB,GAErC,OADAE,KAAKC,WAAaD,KAAKC,YAAc,GAC9BD,KAAKC,WAAW,IAAMH,IAAU,EACzC,EAUAJ,EAAQ/C,UAAUwE,aAAe,SAASrB,GACxC,QAAUE,KAAKkB,UAAUpB,GAAO5B,MAClC,ECxKO,MAAMkD,EACW,oBAATC,KACAA,KAEgB,oBAAXC,OACLA,OAGAC,SAAS,cAATA,GCPR,SAASC,EAAKpE,KAAQqE,GACzB,OAAOA,EAAKC,QAAO,CAACC,EAAKC,KACjBxE,EAAIyE,eAAeD,KACnBD,EAAIC,GAAKxE,EAAIwE,IAEVD,IACR,GACP,CAEA,MAAMG,EAAqBC,EAAWC,WAChCC,EAAuBF,EAAWG,aACjC,SAASC,EAAsB/E,EAAKgF,GACnCA,EAAKC,iBACLjF,EAAIkF,aAAeR,EAAmBS,KAAKR,GAC3C3E,EAAIoF,eAAiBP,EAAqBM,KAAKR,KAG/C3E,EAAIkF,aAAeP,EAAWC,WAAWO,KAAKR,GAC9C3E,EAAIoF,eAAiBT,EAAWG,aAAaK,KAAKR,GAE1D,CClBA,MAAMU,UAAuBC,MACzBC,YAAYC,EAAQC,EAAaC,GAC7BC,MAAMH,GACN5C,KAAK6C,YAAcA,EACnB7C,KAAK8C,QAAUA,EACf9C,KAAKzD,KAAO,kBAGb,MAAMyG,UAAkBtD,EAO3BiD,YAAYP,GACRW,QACA/C,KAAKiD,UAAW,EAChBd,EAAsBnC,KAAMoC,GAC5BpC,KAAKoC,KAAOA,EACZpC,KAAKkD,MAAQd,EAAKc,MAClBlD,KAAKmD,OAASf,EAAKe,OAWvBC,QAAQR,EAAQC,EAAaC,GAEzB,OADAC,MAAM9B,aAAa,QAAS,IAAIwB,EAAeG,EAAQC,EAAaC,IAC7D9C,KAKXqD,OAGI,OAFArD,KAAKsD,WAAa,UAClBtD,KAAKuD,SACEvD,KAKXwD,QAKI,MAJwB,YAApBxD,KAAKsD,YAAgD,SAApBtD,KAAKsD,aACtCtD,KAAKyD,UACLzD,KAAK0D,WAEF1D,KAOX2D,KAAKC,GACuB,SAApB5D,KAAKsD,YACLtD,KAAK6D,MAAMD,GAWnBE,SACI9D,KAAKsD,WAAa,OAClBtD,KAAKiD,UAAW,EAChBF,MAAM9B,aAAa,QAQvB8C,OAAOvH,GACH,MAAMwH,EAAS5F,EAAa5B,EAAMwD,KAAKmD,OAAO7E,YAC9C0B,KAAKiE,SAASD,GAOlBC,SAASD,GACLjB,MAAM9B,aAAa,SAAU+C,GAOjCN,QAAQQ,GACJlE,KAAKsD,WAAa,SAClBP,MAAM9B,aAAa,QAASiD,GAOhCC,MAAMC,KC9GV,MAAMC,EAAW,mEAAmEzG,MAAM,IAAkB0G,EAAM,GAClH,IAAqBC,EAAjBC,EAAO,EAAGvG,EAAI,EAQX,SAASwG,EAAOC,GACnB,IAAIC,EAAU,GACd,GACIA,EAAUN,EAASK,EAZ6E,IAY7DC,EACnCD,EAAME,KAAKC,MAAMH,EAb+E,UAc3FA,EAAM,GACf,OAAOC,CACX,CAqBO,SAASG,IACZ,MAAMC,EAAMN,GAAQ,IAAIO,MACxB,OAAID,IAAQR,GACDC,EAAO,EAAGD,EAAOQ,GACrBA,EAAM,IAAMN,EAAOD,IAC9B,CAIA,KAAOvG,EA9CiG,GA8CrFA,IACfqG,EAAID,EAASpG,IAAMA,ECzChB,SAASwG,EAAOrH,GACnB,IAAI6H,EAAM,GACV,IAAK,IAAIhH,KAAKb,EACNA,EAAIyE,eAAe5D,KACfgH,EAAI/G,SACJ+G,GAAO,KACXA,GAAOC,mBAAmBjH,GAAK,IAAMiH,mBAAmB9H,EAAIa,KAGpE,OAAOgH,CACX,CCjBA,IAAIE,GAAQ,EACZ,IACIA,EAAkC,oBAAnBC,gBACX,oBAAqB,IAAIA,cAKjC,CAHA,MAAOC,GAGP,CACO,MAAMC,EAAUH,ECPhB,SAASI,EAAInD,GAChB,MAAMoD,EAAUpD,EAAKoD,QAErB,IACI,GAAI,oBAAuBJ,kBAAoBI,GAAWF,GACtD,OAAO,IAAIF,eAGnB,MAAOK,IACP,IAAKD,EACD,IACI,OAAO,IAAIzD,EAAW,CAAC,UAAU2D,OAAO,UAAUC,KAAK,OAAM,qBAEjE,MAAOF,IAEf,CCVA,SAASG,KACT,MAAMC,EAIK,MAHK,IAAIT,EAAe,CAC3BI,SAAS,IAEMM,aA8NhB,MAAMC,UAAgBrG,EAOzBiD,YAAYqD,EAAK5D,GACbW,QACAZ,EAAsBnC,KAAMoC,GAC5BpC,KAAKoC,KAAOA,EACZpC,KAAKiG,OAAS7D,EAAK6D,QAAU,MAC7BjG,KAAKgG,IAAMA,EACXhG,KAAKkG,OAAQ,IAAU9D,EAAK8D,MAC5BlG,KAAKxD,UAAO2J,IAAc/D,EAAK5F,KAAO4F,EAAK5F,KAAO,KAClDwD,KAAK/D,SAOTA,SACI,MAAMmG,EAAOZ,EAAKxB,KAAKoC,KAAM,QAAS,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,aACjHA,EAAKoD,UAAYxF,KAAKoC,KAAKgE,GAC3BhE,EAAKiE,UAAYrG,KAAKoC,KAAKkE,GAC3B,MAAMC,EAAOvG,KAAKuG,IAAM,IAAInB,EAAehD,GAC3C,IACImE,EAAIlD,KAAKrD,KAAKiG,OAAQjG,KAAKgG,IAAKhG,KAAKkG,OACrC,IACI,GAAIlG,KAAKoC,KAAKoE,aAAc,CACxBD,EAAIE,uBAAyBF,EAAIE,uBAAsB,GACvD,IAAK,IAAIxI,KAAK+B,KAAKoC,KAAKoE,aAChBxG,KAAKoC,KAAKoE,aAAa3E,eAAe5D,IACtCsI,EAAIG,iBAAiBzI,EAAG+B,KAAKoC,KAAKoE,aAAavI,KAK/D,MAAOwH,IACP,GAAI,SAAWzF,KAAKiG,OAChB,IACIM,EAAIG,iBAAiB,eAAgB,4BAEzC,MAAOjB,IAEX,IACIc,EAAIG,iBAAiB,SAAU,OAEnC,MAAOjB,IAEH,oBAAqBc,IACrBA,EAAII,gBAAkB3G,KAAKoC,KAAKuE,iBAEhC3G,KAAKoC,KAAKwE,iBACVL,EAAIM,QAAU7G,KAAKoC,KAAKwE,gBAE5BL,EAAIO,mBAAqB,KACjB,IAAMP,EAAIjD,aAEV,MAAQiD,EAAIQ,QAAU,OAASR,EAAIQ,OACnC/G,KAAKgH,SAKLhH,KAAKsC,cAAa,KACdtC,KAAKoD,QAA8B,iBAAfmD,EAAIQ,OAAsBR,EAAIQ,OAAS,EAAE,GAC9D,KAGXR,EAAI5C,KAAK3D,KAAKxD,MAElB,MAAOiJ,GAOH,YAHAzF,KAAKsC,cAAa,KACdtC,KAAKoD,QAAQqC,EAAE,GAChB,GAGiB,oBAAbwB,WACPjH,KAAKkH,MAAQnB,EAAQoB,gBACrBpB,EAAQqB,SAASpH,KAAKkH,OAASlH,MAQvCoD,QAAQiC,GACJrF,KAAKiB,aAAa,QAASoE,EAAKrF,KAAKuG,KACrCvG,KAAKqH,SAAQ,GAOjBA,QAAQC,GACJ,QAAI,IAAuBtH,KAAKuG,KAAO,OAASvG,KAAKuG,IAArD,CAIA,GADAvG,KAAKuG,IAAIO,mBAAqBlB,EAC1B0B,EACA,IACItH,KAAKuG,IAAIgB,QAEb,MAAO9B,IAEa,oBAAbwB,iBACAlB,EAAQqB,SAASpH,KAAKkH,OAEjClH,KAAKuG,IAAM,MAOfS,SACI,MAAMxK,EAAOwD,KAAKuG,IAAIiB,aACT,OAAThL,IACAwD,KAAKiB,aAAa,OAAQzE,GAC1BwD,KAAKiB,aAAa,WAClBjB,KAAKqH,WAQbE,QACIvH,KAAKqH,WAUb,GAPAtB,EAAQoB,cAAgB,EACxBpB,EAAQqB,SAAW,GAMK,oBAAbH,SAEP,GAA2B,mBAAhBQ,YAEPA,YAAY,WAAYC,QAEvB,GAAgC,mBAArB7H,iBAAiC,CAE7CA,iBADyB,eAAgBkC,EAAa,WAAa,SAChC2F,GAAe,GAG1D,SAASA,IACL,IAAK,IAAIzJ,KAAK8H,EAAQqB,SACdrB,EAAQqB,SAASvF,eAAe5D,IAChC8H,EAAQqB,SAASnJ,GAAGsJ,OAGhC,CC7YY,MAACI,EACqC,mBAAZC,SAAqD,mBAApBA,QAAQC,QAE/DnH,GAAOkH,QAAQC,UAAUC,KAAKpH,GAG/B,CAACA,EAAI4B,IAAiBA,EAAa5B,EAAI,GAGzCqH,EAAYhG,EAAWgG,WAAahG,EAAWiG,aCHtDC,EAAqC,oBAAdC,WACI,iBAAtBA,UAAUC,SACmB,gBAApCD,UAAUC,QAAQC,cCPV,MAACC,EAAa,CACtBC,UDOG,cAAiBtF,EAOpBL,YAAYP,GACRW,MAAMX,GACNpC,KAAK/C,gBAAkBmF,EAAKmG,YAE5BC,WACA,MAAO,YAEXjF,SACI,IAAKvD,KAAKyI,QAEN,OAEJ,MAAMzC,EAAMhG,KAAKgG,MACX0C,EAAY1I,KAAKoC,KAAKsG,UAEtBtG,EAAO6F,EACP,GACAzG,EAAKxB,KAAKoC,KAAM,QAAS,oBAAqB,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,eAAgB,kBAAmB,SAAU,aAAc,SAAU,uBAChMpC,KAAKoC,KAAKoE,eACVpE,EAAKuG,QAAU3I,KAAKoC,KAAKoE,cAE7B,IACIxG,KAAK4I,GACyBX,EAIpB,IAAIF,EAAU/B,EAAK0C,EAAWtG,GAH9BsG,EACI,IAAIX,EAAU/B,EAAK0C,GACnB,IAAIX,EAAU/B,GAGhC,MAAOX,GACH,OAAOrF,KAAKiB,aAAa,QAASoE,GAEtCrF,KAAK4I,GAAGtK,WAAa0B,KAAKmD,OAAO7E,YDrCR,cCsCzB0B,KAAK6I,oBAOTA,oBACI7I,KAAK4I,GAAGE,OAAS,KACT9I,KAAKoC,KAAK2G,WACV/I,KAAK4I,GAAGI,QAAQC,QAEpBjJ,KAAK8D,QAAQ,EAEjB9D,KAAK4I,GAAGM,QAAWC,GAAenJ,KAAK0D,QAAQ,CAC3Cb,YAAa,8BACbC,QAASqG,IAEbnJ,KAAK4I,GAAGQ,UAAaC,GAAOrJ,KAAK+D,OAAOsF,EAAG7M,MAC3CwD,KAAK4I,GAAGU,QAAW7D,GAAMzF,KAAKoD,QAAQ,kBAAmBqC,GAE7D5B,MAAMD,GACF5D,KAAKiD,UAAW,EAGhB,IAAK,IAAIhF,EAAI,EAAGA,EAAI2F,EAAQ1F,OAAQD,IAAK,CACrC,MAAM+F,EAASJ,EAAQ3F,GACjBsL,EAAatL,IAAM2F,EAAQ1F,OAAS,EAC1ClB,EAAagH,EAAQhE,KAAK/C,gBAAiBT,IAmBvC,IAGQwD,KAAK4I,GAAGjF,KAAKnH,GAMrB,MAAOiJ,IAEH8D,GAGA5B,GAAS,KACL3H,KAAKiD,UAAW,EAChBjD,KAAKiB,aAAa,QAAQ,GAC3BjB,KAAKsC,kBAKxBmB,eAC2B,IAAZzD,KAAK4I,KACZ5I,KAAK4I,GAAGpF,QACRxD,KAAK4I,GAAK,MAQlB5C,MACI,IAAI9C,EAAQlD,KAAKkD,OAAS,GAC1B,MAAMsG,EAASxJ,KAAKoC,KAAKqH,OAAS,MAAQ,KAC1C,IAAIC,EAAO,GAEP1J,KAAKoC,KAAKsH,OACR,QAAUF,GAAqC,MAA3BG,OAAO3J,KAAKoC,KAAKsH,OAClC,OAASF,GAAqC,KAA3BG,OAAO3J,KAAKoC,KAAKsH,SACzCA,EAAO,IAAM1J,KAAKoC,KAAKsH,MAGvB1J,KAAKoC,KAAKwH,oBACV1G,EAAMlD,KAAKoC,KAAKyH,gBAAkB/E,KAGjC9E,KAAK/C,iBACNiG,EAAM4G,IAAM,GAEhB,MAAMC,EAAetF,EAAOvB,GAE5B,OAAQsG,EACJ,QAF8C,IAArCxJ,KAAKoC,KAAK4H,SAASC,QAAQ,KAG5B,IAAMjK,KAAKoC,KAAK4H,SAAW,IAAMhK,KAAKoC,KAAK4H,UACnDN,EACA1J,KAAKoC,KAAK8H,MACTH,EAAa7L,OAAS,IAAM6L,EAAe,IAQpDtB,QACI,QAASV,IChKboC,QHWG,cAAsBnH,EAOzBL,YAAYP,GAGR,GAFAW,MAAMX,GACNpC,KAAKmK,SAAU,EACS,oBAAbC,SAA0B,CACjC,MAAMC,EAAQ,WAAaD,SAASE,SACpC,IAAIZ,EAAOU,SAASV,KAEfA,IACDA,EAAOW,EAAQ,MAAQ,MAE3BrK,KAAKoG,GACoB,oBAAbgE,UACJhI,EAAK4H,WAAaI,SAASJ,UAC3BN,IAAStH,EAAKsH,KACtB1J,KAAKsG,GAAKlE,EAAKqH,SAAWY,EAK9B,MAAM9B,EAAcnG,GAAQA,EAAKmG,YACjCvI,KAAK/C,eAAiB4I,IAAY0C,EAElCC,WACA,MAAO,UAQXjF,SACIvD,KAAKuK,OAQTpG,MAAMC,GACFpE,KAAKsD,WAAa,UAClB,MAAMa,EAAQ,KACVnE,KAAKsD,WAAa,SAClBc,GAAS,EAEb,GAAIpE,KAAKmK,UAAYnK,KAAKiD,SAAU,CAChC,IAAIuH,EAAQ,EACRxK,KAAKmK,UACLK,IACAxK,KAAKG,KAAK,gBAAgB,aACpBqK,GAASrG,QAGdnE,KAAKiD,WACNuH,IACAxK,KAAKG,KAAK,SAAS,aACbqK,GAASrG,aAKnBA,IAQRoG,OACIvK,KAAKmK,SAAU,EACfnK,KAAKyK,SACLzK,KAAKiB,aAAa,QAOtB8C,OAAOvH,GTpFW,EAACkO,EAAgBpM,KACnC,MAAMqM,EAAiBD,EAAe9M,MAAM2B,GACtCqE,EAAU,GAChB,IAAK,IAAI3F,EAAI,EAAGA,EAAI0M,EAAezM,OAAQD,IAAK,CAC5C,MAAM2M,EAAgBxM,EAAauM,EAAe1M,GAAIK,GAEtD,GADAsF,EAAQ1D,KAAK0K,GACc,UAAvBA,EAAcrO,KACd,MAGR,OAAOqH,CAAO,ESyFViH,CAAcrO,EAAMwD,KAAKmD,OAAO7E,YAAYlC,SAd1B4H,IAMd,GAJI,YAAchE,KAAKsD,YAA8B,SAAhBU,EAAOzH,MACxCyD,KAAK8D,SAGL,UAAYE,EAAOzH,KAEnB,OADAyD,KAAK0D,QAAQ,CAAEb,YAAa,oCACrB,EAGX7C,KAAKiE,SAASD,EAAO,IAKrB,WAAahE,KAAKsD,aAElBtD,KAAKmK,SAAU,EACfnK,KAAKiB,aAAa,gBACd,SAAWjB,KAAKsD,YAChBtD,KAAKuK,QAWjB9G,UACI,MAAMD,EAAQ,KACVxD,KAAK6D,MAAM,CAAC,CAAEtH,KAAM,UAAW,EAE/B,SAAWyD,KAAKsD,WAChBE,IAKAxD,KAAKG,KAAK,OAAQqD,GAS1BK,MAAMD,GACF5D,KAAKiD,UAAW,ETxJF,EAACW,EAAS1G,KAE5B,MAAMgB,EAAS0F,EAAQ1F,OACjByM,EAAiB,IAAI5J,MAAM7C,GACjC,IAAI4M,EAAQ,EACZlH,EAAQxH,SAAQ,CAAC4H,EAAQ/F,KAErBjB,EAAagH,GAAQ,GAAO3F,IACxBsM,EAAe1M,GAAKI,IACdyM,IAAU5M,GACZhB,EAASyN,EAAehF,KAAKpG,MAEnC,GACJ,ES4IEwL,CAAcnH,GAAUpH,IACpBwD,KAAKgL,QAAQxO,GAAM,KACfwD,KAAKiD,UAAW,EAChBjD,KAAKiB,aAAa,QAAQ,GAC5B,IAQV+E,MACI,IAAI9C,EAAQlD,KAAKkD,OAAS,GAC1B,MAAMsG,EAASxJ,KAAKoC,KAAKqH,OAAS,QAAU,OAC5C,IAAIC,EAAO,IAEP,IAAU1J,KAAKoC,KAAKwH,oBACpB1G,EAAMlD,KAAKoC,KAAKyH,gBAAkB/E,KAEjC9E,KAAK/C,gBAAmBiG,EAAM+H,MAC/B/H,EAAM4G,IAAM,GAGZ9J,KAAKoC,KAAKsH,OACR,UAAYF,GAAqC,MAA3BG,OAAO3J,KAAKoC,KAAKsH,OACpC,SAAWF,GAAqC,KAA3BG,OAAO3J,KAAKoC,KAAKsH,SAC3CA,EAAO,IAAM1J,KAAKoC,KAAKsH,MAE3B,MAAMK,EAAetF,EAAOvB,GAE5B,OAAQsG,EACJ,QAF8C,IAArCxJ,KAAKoC,KAAK4H,SAASC,QAAQ,KAG5B,IAAMjK,KAAKoC,KAAK4H,SAAW,IAAMhK,KAAKoC,KAAK4H,UACnDN,EACA1J,KAAKoC,KAAK8H,MACTH,EAAa7L,OAAS,IAAM6L,EAAe,IAQpDmB,QAAQ9I,EAAO,IAEX,OADApG,OAAOmP,OAAO/I,EAAM,CAAEgE,GAAIpG,KAAKoG,GAAIE,GAAItG,KAAKsG,IAAMtG,KAAKoC,MAChD,IAAI2D,EAAQ/F,KAAKgG,MAAO5D,GASnC4I,QAAQxO,EAAMuD,GACV,MAAMqL,EAAMpL,KAAKkL,QAAQ,CACrBjF,OAAQ,OACRzJ,KAAMA,IAEV4O,EAAIxL,GAAG,UAAWG,GAClBqL,EAAIxL,GAAG,SAAS,CAACyL,EAAWvI,KACxB9C,KAAKoD,QAAQ,iBAAkBiI,EAAWvI,EAAQ,IAQ1D2H,SACI,MAAMW,EAAMpL,KAAKkL,UACjBE,EAAIxL,GAAG,OAAQI,KAAK+D,OAAOxB,KAAKvC,OAChCoL,EAAIxL,GAAG,SAAS,CAACyL,EAAWvI,KACxB9C,KAAKoD,QAAQ,iBAAkBiI,EAAWvI,EAAQ,IAEtD9C,KAAKsL,QAAUF,KIrNjBG,EAAK,sPACLC,EAAQ,CACV,SAAU,WAAY,YAAa,WAAY,OAAQ,WAAY,OAAQ,OAAQ,WAAY,OAAQ,YAAa,OAAQ,QAAS,UAElI,SAASC,EAAMxG,GAClB,MAAMyG,EAAMzG,EAAK0G,EAAI1G,EAAIgF,QAAQ,KAAMxE,EAAIR,EAAIgF,QAAQ,MAC7C,GAAN0B,IAAiB,GAANlG,IACXR,EAAMA,EAAIvG,UAAU,EAAGiN,GAAK1G,EAAIvG,UAAUiN,EAAGlG,GAAGmG,QAAQ,KAAM,KAAO3G,EAAIvG,UAAU+G,EAAGR,EAAI/G,SAE9F,IAAI2N,EAAIN,EAAGO,KAAK7G,GAAO,IAAKe,EAAM,GAAI/H,EAAI,GAC1C,KAAOA,KACH+H,EAAIwF,EAAMvN,IAAM4N,EAAE5N,IAAM,GAU5B,OARU,GAAN0N,IAAiB,GAANlG,IACXO,EAAI+F,OAASL,EACb1F,EAAIgG,KAAOhG,EAAIgG,KAAKtN,UAAU,EAAGsH,EAAIgG,KAAK9N,OAAS,GAAG0N,QAAQ,KAAM,KACpE5F,EAAIiG,UAAYjG,EAAIiG,UAAUL,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAAIA,QAAQ,KAAM,KAC9E5F,EAAIkG,SAAU,GAElBlG,EAAImG,UAIR,SAAmB/O,EAAK8M,GACpB,MAAMkC,EAAO,WAAYC,EAAQnC,EAAK0B,QAAQQ,EAAM,KAAKxO,MAAM,KACvC,KAApBsM,EAAKlJ,MAAM,EAAG,IAA6B,IAAhBkJ,EAAKhM,QAChCmO,EAAMzL,OAAO,EAAG,GAEE,KAAlBsJ,EAAKlJ,OAAO,IACZqL,EAAMzL,OAAOyL,EAAMnO,OAAS,EAAG,GAEnC,OAAOmO,CACX,CAboBF,CAAUnG,EAAKA,EAAU,MACzCA,EAAIsG,SAaR,SAAkBtG,EAAK9C,GACnB,MAAM1G,EAAO,GAMb,OALA0G,EAAM0I,QAAQ,6BAA6B,SAAUW,EAAIC,EAAIC,GACrDD,IACAhQ,EAAKgQ,GAAMC,MAGZjQ,CACX,CArBmB8P,CAAStG,EAAKA,EAAW,OACjCA,CACX,CCnCO,MAAM0G,UAAehN,EAOxBiD,YAAYqD,EAAK5D,EAAO,IACpBW,QACA/C,KAAK2M,YAAc,GACf3G,GAAO,iBAAoBA,IAC3B5D,EAAO4D,EACPA,EAAM,MAENA,GACAA,EAAMyF,EAAMzF,GACZ5D,EAAK4H,SAAWhE,EAAIgG,KACpB5J,EAAKqH,OAA0B,UAAjBzD,EAAIsE,UAAyC,QAAjBtE,EAAIsE,SAC9ClI,EAAKsH,KAAO1D,EAAI0D,KACZ1D,EAAI9C,QACJd,EAAKc,MAAQ8C,EAAI9C,QAEhBd,EAAK4J,OACV5J,EAAK4H,SAAWyB,EAAMrJ,EAAK4J,MAAMA,MAErC7J,EAAsBnC,KAAMoC,GAC5BpC,KAAKyJ,OACD,MAAQrH,EAAKqH,OACPrH,EAAKqH,OACe,oBAAbW,UAA4B,WAAaA,SAASE,SAC/DlI,EAAK4H,WAAa5H,EAAKsH,OAEvBtH,EAAKsH,KAAO1J,KAAKyJ,OAAS,MAAQ,MAEtCzJ,KAAKgK,SACD5H,EAAK4H,WACoB,oBAAbI,SAA2BA,SAASJ,SAAW,aAC/DhK,KAAK0J,KACDtH,EAAKsH,OACoB,oBAAbU,UAA4BA,SAASV,KACvCU,SAASV,KACT1J,KAAKyJ,OACD,MACA,MAClBzJ,KAAKqI,WAAajG,EAAKiG,YAAc,CAAC,UAAW,aACjDrI,KAAK2M,YAAc,GACnB3M,KAAK4M,cAAgB,EACrB5M,KAAKoC,KAAOpG,OAAOmP,OAAO,CACtBjB,KAAM,aACN2C,OAAO,EACPlG,iBAAiB,EACjBmG,SAAS,EACTjD,eAAgB,IAChBkD,iBAAiB,EACjBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAmB,CACfC,UAAW,MAEfC,iBAAkB,GAClBC,qBAAqB,GACtBjL,GACHpC,KAAKoC,KAAK8H,KACNlK,KAAKoC,KAAK8H,KAAK0B,QAAQ,MAAO,KACzB5L,KAAKoC,KAAK4K,iBAAmB,IAAM,IACb,iBAApBhN,KAAKoC,KAAKc,QACjBlD,KAAKoC,KAAKc,MR/Cf,SAAgBoK,GACnB,IAAIC,EAAM,GACNC,EAAQF,EAAG1P,MAAM,KACrB,IAAK,IAAIK,EAAI,EAAGwP,EAAID,EAAMtP,OAAQD,EAAIwP,EAAGxP,IAAK,CAC1C,IAAIyP,EAAOF,EAAMvP,GAAGL,MAAM,KAC1B2P,EAAII,mBAAmBD,EAAK,KAAOC,mBAAmBD,EAAK,IAE/D,OAAOH,CACX,CQuC8BjO,CAAOU,KAAKoC,KAAKc,QAGvClD,KAAK4N,GAAK,KACV5N,KAAK6N,SAAW,KAChB7N,KAAK8N,aAAe,KACpB9N,KAAK+N,YAAc,KAEnB/N,KAAKgO,iBAAmB,KACQ,mBAArBnO,mBACHG,KAAKoC,KAAKiL,sBAIVrN,KAAKiO,0BAA4B,KACzBjO,KAAKkO,YAELlO,KAAKkO,UAAU1N,qBACfR,KAAKkO,UAAU1K,UAGvB3D,iBAAiB,eAAgBG,KAAKiO,2BAA2B,IAE/C,cAAlBjO,KAAKgK,WACLhK,KAAKmO,qBAAuB,KACxBnO,KAAK0D,QAAQ,kBAAmB,CAC5Bb,YAAa,2BACf,EAENhD,iBAAiB,UAAWG,KAAKmO,sBAAsB,KAG/DnO,KAAKqD,OAST+K,gBAAgB5F,GACZ,MAAMtF,EAAQlH,OAAOmP,OAAO,GAAInL,KAAKoC,KAAKc,OAE1CA,EAAMmL,IdtFU,EcwFhBnL,EAAMgL,UAAY1F,EAEdxI,KAAK4N,KACL1K,EAAM+H,IAAMjL,KAAK4N,IACrB,MAAMxL,EAAOpG,OAAOmP,OAAO,GAAInL,KAAKoC,KAAKgL,iBAAiB5E,GAAOxI,KAAKoC,KAAM,CACxEc,QACAC,OAAQnD,KACRgK,SAAUhK,KAAKgK,SACfP,OAAQzJ,KAAKyJ,OACbC,KAAM1J,KAAK0J,OAEf,OAAO,IAAIrB,EAAWG,GAAMpG,GAOhCiB,OACI,IAAI6K,EACJ,GAAIlO,KAAKoC,KAAK2K,iBACVL,EAAO4B,wBACmC,IAA1CtO,KAAKqI,WAAW4B,QAAQ,aACxBiE,EAAY,gBAEX,IAAI,IAAMlO,KAAKqI,WAAWnK,OAK3B,YAHA8B,KAAKsC,cAAa,KACdtC,KAAKiB,aAAa,QAAS,0BAA0B,GACtD,GAIHiN,EAAYlO,KAAKqI,WAAW,GAEhCrI,KAAKsD,WAAa,UAElB,IACI4K,EAAYlO,KAAKoO,gBAAgBF,GAErC,MAAOzI,GAGH,OAFAzF,KAAKqI,WAAWkG,aAChBvO,KAAKqD,OAGT6K,EAAU7K,OACVrD,KAAKwO,aAAaN,GAOtBM,aAAaN,GACLlO,KAAKkO,WACLlO,KAAKkO,UAAU1N,qBAGnBR,KAAKkO,UAAYA,EAEjBA,EACKtO,GAAG,QAASI,KAAKyO,QAAQlM,KAAKvC,OAC9BJ,GAAG,SAAUI,KAAKiE,SAAS1B,KAAKvC,OAChCJ,GAAG,QAASI,KAAKoD,QAAQb,KAAKvC,OAC9BJ,GAAG,SAAUgD,GAAW5C,KAAK0D,QAAQ,kBAAmBd,KAQjE8L,MAAMlG,GACF,IAAI0F,EAAYlO,KAAKoO,gBAAgB5F,GACjCmG,GAAS,EACbjC,EAAO4B,uBAAwB,EAC/B,MAAMM,EAAkB,KAChBD,IAEJT,EAAUvK,KAAK,CAAC,CAAEpH,KAAM,OAAQC,KAAM,WACtC0R,EAAU/N,KAAK,UAAW0O,IACtB,IAAIF,EAEJ,GAAI,SAAWE,EAAItS,MAAQ,UAAYsS,EAAIrS,KAAM,CAG7C,GAFAwD,KAAK8O,WAAY,EACjB9O,KAAKiB,aAAa,YAAaiN,IAC1BA,EACD,OACJxB,EAAO4B,sBAAwB,cAAgBJ,EAAU1F,KACzDxI,KAAKkO,UAAU/J,OAAM,KACbwK,GAEA,WAAa3O,KAAKsD,aAEtB+D,IACArH,KAAKwO,aAAaN,GAClBA,EAAUvK,KAAK,CAAC,CAAEpH,KAAM,aACxByD,KAAKiB,aAAa,UAAWiN,GAC7BA,EAAY,KACZlO,KAAK8O,WAAY,EACjB9O,KAAK+O,QAAO,QAGf,CACD,MAAM1J,EAAM,IAAI3C,MAAM,eAEtB2C,EAAI6I,UAAYA,EAAU1F,KAC1BxI,KAAKiB,aAAa,eAAgBoE,OAExC,EAEN,SAAS2J,IACDL,IAGJA,GAAS,EACTtH,IACA6G,EAAU1K,QACV0K,EAAY,MAGhB,MAAM5E,EAAWjE,IACb,MAAM4J,EAAQ,IAAIvM,MAAM,gBAAkB2C,GAE1C4J,EAAMf,UAAYA,EAAU1F,KAC5BwG,IACAhP,KAAKiB,aAAa,eAAgBgO,EAAM,EAE5C,SAASC,IACL5F,EAAQ,oBAGZ,SAASJ,IACLI,EAAQ,iBAGZ,SAAS6F,EAAUC,GACXlB,GAAakB,EAAG5G,OAAS0F,EAAU1F,MACnCwG,IAIR,MAAM3H,EAAU,KACZ6G,EAAU3N,eAAe,OAAQqO,GACjCV,EAAU3N,eAAe,QAAS+I,GAClC4E,EAAU3N,eAAe,QAAS2O,GAClClP,KAAKI,IAAI,QAAS8I,GAClBlJ,KAAKI,IAAI,YAAa+O,EAAU,EAEpCjB,EAAU/N,KAAK,OAAQyO,GACvBV,EAAU/N,KAAK,QAASmJ,GACxB4E,EAAU/N,KAAK,QAAS+O,GACxBlP,KAAKG,KAAK,QAAS+I,GACnBlJ,KAAKG,KAAK,YAAagP,GACvBjB,EAAU7K,OAOdS,SAOI,GANA9D,KAAKsD,WAAa,OAClBoJ,EAAO4B,sBAAwB,cAAgBtO,KAAKkO,UAAU1F,KAC9DxI,KAAKiB,aAAa,QAClBjB,KAAK+O,QAGD,SAAW/O,KAAKsD,YAActD,KAAKoC,KAAK0K,QAAS,CACjD,IAAI7O,EAAI,EACR,MAAMwP,EAAIzN,KAAK6N,SAAS3P,OACxB,KAAOD,EAAIwP,EAAGxP,IACV+B,KAAK0O,MAAM1O,KAAK6N,SAAS5P,KASrCgG,SAASD,GACL,GAAI,YAAchE,KAAKsD,YACnB,SAAWtD,KAAKsD,YAChB,YAActD,KAAKsD,WAInB,OAHAtD,KAAKiB,aAAa,SAAU+C,GAE5BhE,KAAKiB,aAAa,aACV+C,EAAOzH,MACX,IAAK,OACDyD,KAAKqP,YAAYC,KAAK7D,MAAMzH,EAAOxH,OACnC,MACJ,IAAK,OACDwD,KAAKuP,mBACLvP,KAAKwP,WAAW,QAChBxP,KAAKiB,aAAa,QAClBjB,KAAKiB,aAAa,QAClB,MACJ,IAAK,QACD,MAAMoE,EAAM,IAAI3C,MAAM,gBAEtB2C,EAAIoK,KAAOzL,EAAOxH,KAClBwD,KAAKoD,QAAQiC,GACb,MACJ,IAAK,UACDrF,KAAKiB,aAAa,OAAQ+C,EAAOxH,MACjCwD,KAAKiB,aAAa,UAAW+C,EAAOxH,OAapD6S,YAAY7S,GACRwD,KAAKiB,aAAa,YAAazE,GAC/BwD,KAAK4N,GAAKpR,EAAKyO,IACfjL,KAAKkO,UAAUhL,MAAM+H,IAAMzO,EAAKyO,IAChCjL,KAAK6N,SAAW7N,KAAK0P,eAAelT,EAAKqR,UACzC7N,KAAK8N,aAAetR,EAAKsR,aACzB9N,KAAK+N,YAAcvR,EAAKuR,YACxB/N,KAAK2P,WAAanT,EAAKmT,WACvB3P,KAAK8D,SAED,WAAa9D,KAAKsD,YAEtBtD,KAAKuP,mBAOTA,mBACIvP,KAAKwC,eAAexC,KAAKgO,kBACzBhO,KAAKgO,iBAAmBhO,KAAKsC,cAAa,KACtCtC,KAAK0D,QAAQ,eAAe,GAC7B1D,KAAK8N,aAAe9N,KAAK+N,aACxB/N,KAAKoC,KAAK2G,WACV/I,KAAKgO,iBAAiB/E,QAQ9BwF,UACIzO,KAAK2M,YAAY/L,OAAO,EAAGZ,KAAK4M,eAIhC5M,KAAK4M,cAAgB,EACjB,IAAM5M,KAAK2M,YAAYzO,OACvB8B,KAAKiB,aAAa,SAGlBjB,KAAK+O,QAQbA,QACI,GAAI,WAAa/O,KAAKsD,YAClBtD,KAAKkO,UAAUjL,WACdjD,KAAK8O,WACN9O,KAAK2M,YAAYzO,OAAQ,CACzB,MAAM0F,EAAU5D,KAAK4P,qBACrB5P,KAAKkO,UAAUvK,KAAKC,GAGpB5D,KAAK4M,cAAgBhJ,EAAQ1F,OAC7B8B,KAAKiB,aAAa,UAS1B2O,qBAII,KAH+B5P,KAAK2P,YACR,YAAxB3P,KAAKkO,UAAU1F,MACfxI,KAAK2M,YAAYzO,OAAS,GAE1B,OAAO8B,KAAK2M,YAEhB,IAAIkD,EAAc,EAClB,IAAK,IAAI5R,EAAI,EAAGA,EAAI+B,KAAK2M,YAAYzO,OAAQD,IAAK,CAC9C,MAAMzB,EAAOwD,KAAK2M,YAAY1O,GAAGzB,KAIjC,GAHIA,IACAqT,GXxYO,iBADIzS,EWyYeZ,GXlY1C,SAAoByI,GAChB,IAAI6K,EAAI,EAAG5R,EAAS,EACpB,IAAK,IAAID,EAAI,EAAGwP,EAAIxI,EAAI/G,OAAQD,EAAIwP,EAAGxP,IACnC6R,EAAI7K,EAAI9G,WAAWF,GACf6R,EAAI,IACJ5R,GAAU,EAEL4R,EAAI,KACT5R,GAAU,EAEL4R,EAAI,OAAUA,GAAK,MACxB5R,GAAU,GAGVD,IACAC,GAAU,GAGlB,OAAOA,CACX,CAxBe6R,CAAW3S,GAGfwH,KAAKoL,KAPQ,MAOF5S,EAAI6S,YAAc7S,EAAI8S,QWsY5BjS,EAAI,GAAK4R,EAAc7P,KAAK2P,WAC5B,OAAO3P,KAAK2M,YAAY3L,MAAM,EAAG/C,GAErC4R,GAAe,EX9YpB,IAAoBzS,EWgZnB,OAAO4C,KAAK2M,YAUhB9I,MAAMgL,EAAKsB,EAASpQ,GAEhB,OADAC,KAAKwP,WAAW,UAAWX,EAAKsB,EAASpQ,GAClCC,KAEX2D,KAAKkL,EAAKsB,EAASpQ,GAEf,OADAC,KAAKwP,WAAW,UAAWX,EAAKsB,EAASpQ,GAClCC,KAWXwP,WAAWjT,EAAMC,EAAM2T,EAASpQ,GAS5B,GARI,mBAAsBvD,IACtBuD,EAAKvD,EACLA,OAAO2J,GAEP,mBAAsBgK,IACtBpQ,EAAKoQ,EACLA,EAAU,MAEV,YAAcnQ,KAAKsD,YAAc,WAAatD,KAAKsD,WACnD,QAEJ6M,EAAUA,GAAW,IACbC,UAAW,IAAUD,EAAQC,SACrC,MAAMpM,EAAS,CACXzH,KAAMA,EACNC,KAAMA,EACN2T,QAASA,GAEbnQ,KAAKiB,aAAa,eAAgB+C,GAClChE,KAAK2M,YAAYzM,KAAK8D,GAClBjE,GACAC,KAAKG,KAAK,QAASJ,GACvBC,KAAK+O,QAKTvL,QACI,MAAMA,EAAQ,KACVxD,KAAK0D,QAAQ,gBACb1D,KAAKkO,UAAU1K,OAAO,EAEpB6M,EAAkB,KACpBrQ,KAAKI,IAAI,UAAWiQ,GACpBrQ,KAAKI,IAAI,eAAgBiQ,GACzB7M,GAAO,EAEL8M,EAAiB,KAEnBtQ,KAAKG,KAAK,UAAWkQ,GACrBrQ,KAAKG,KAAK,eAAgBkQ,EAAgB,EAqB9C,MAnBI,YAAcrQ,KAAKsD,YAAc,SAAWtD,KAAKsD,aACjDtD,KAAKsD,WAAa,UACdtD,KAAK2M,YAAYzO,OACjB8B,KAAKG,KAAK,SAAS,KACXH,KAAK8O,UACLwB,IAGA9M,OAIHxD,KAAK8O,UACVwB,IAGA9M,KAGDxD,KAOXoD,QAAQiC,GACJqH,EAAO4B,uBAAwB,EAC/BtO,KAAKiB,aAAa,QAASoE,GAC3BrF,KAAK0D,QAAQ,kBAAmB2B,GAOpC3B,QAAQd,EAAQC,GACR,YAAc7C,KAAKsD,YACnB,SAAWtD,KAAKsD,YAChB,YAActD,KAAKsD,aAEnBtD,KAAKwC,eAAexC,KAAKgO,kBAEzBhO,KAAKkO,UAAU1N,mBAAmB,SAElCR,KAAKkO,UAAU1K,QAEfxD,KAAKkO,UAAU1N,qBACoB,mBAAxBC,sBACPA,oBAAoB,eAAgBT,KAAKiO,2BAA2B,GACpExN,oBAAoB,UAAWT,KAAKmO,sBAAsB,IAG9DnO,KAAKsD,WAAa,SAElBtD,KAAK4N,GAAK,KAEV5N,KAAKiB,aAAa,QAAS2B,EAAQC,GAGnC7C,KAAK2M,YAAc,GACnB3M,KAAK4M,cAAgB,GAS7B8C,eAAe7B,GACX,MAAM0C,EAAmB,GACzB,IAAItS,EAAI,EACR,MAAMuS,EAAI3C,EAAS3P,OACnB,KAAOD,EAAIuS,EAAGvS,KACL+B,KAAKqI,WAAW4B,QAAQ4D,EAAS5P,KAClCsS,EAAiBrQ,KAAK2N,EAAS5P,IAEvC,OAAOsS,GAGf7D,EAAOpC,SdliBiB,Ee5BZ,MAACA,EAAWoC,EAAOpC"}