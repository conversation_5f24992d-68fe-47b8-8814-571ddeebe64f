!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-dom"),require("@popperjs/core")):"function"==typeof define&&define.amd?define(["exports","react","react-dom","@popperjs/core"],t):t((e=e||self).ReactPopper={},e.<PERSON>,e.<PERSON>actDOM,e.Pop<PERSON>)}(this,(function(e,t,r,n){"use strict";var o=t.createContext(),u=t.createContext();var i=function(e){return Array.isArray(e)?e[0]:e},a=function(e){if("function"==typeof e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return e.apply(void 0,r)}},f=function(e,t){if("function"==typeof e)return a(e,t);null!=e&&(e.current=t)},c=function(e){return e.reduce((function(e,t){var r=t[0],n=t[1];return e[r]=n,e}),{})},s="undefined"!=typeof window&&window.document&&window.document.createElement?t.useLayoutEffect:t.useEffect,l="undefined"!=typeof Element,p="function"==typeof Map,d="function"==typeof Set,y="function"==typeof ArrayBuffer;var m=function(e,t){try{return function e(t,r){if(t===r)return!0;if(t&&r&&"object"==typeof t&&"object"==typeof r){if(t.constructor!==r.constructor)return!1;var n,o,u,i;if(Array.isArray(t)){if((n=t.length)!=r.length)return!1;for(o=n;0!=o--;)if(!e(t[o],r[o]))return!1;return!0}if(p&&t instanceof Map&&r instanceof Map){if(t.size!==r.size)return!1;for(i=t.entries();!(o=i.next()).done;)if(!r.has(o.value[0]))return!1;for(i=t.entries();!(o=i.next()).done;)if(!e(o.value[1],r.get(o.value[0])))return!1;return!0}if(d&&t instanceof Set&&r instanceof Set){if(t.size!==r.size)return!1;for(i=t.entries();!(o=i.next()).done;)if(!r.has(o.value[0]))return!1;return!0}if(y&&ArrayBuffer.isView(t)&&ArrayBuffer.isView(r)){if((n=t.length)!=r.length)return!1;for(o=n;0!=o--;)if(t[o]!==r[o])return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if((n=(u=Object.keys(t)).length)!==Object.keys(r).length)return!1;for(o=n;0!=o--;)if(!Object.prototype.hasOwnProperty.call(r,u[o]))return!1;if(l&&t instanceof Element)return!1;for(o=n;0!=o--;)if(!("_owner"===u[o]&&t.$$typeof||e(t[u[o]],r[u[o]])))return!1;return!0}return t!=t&&r!=r}(e,t)}catch(e){if((e.message||"").match(/stack|recursion/i))return console.warn("react-fast-compare cannot handle circular refs"),!1;throw e}},v=[],h=function(e,o,u){void 0===u&&(u={});var i=t.useRef(null),a={onFirstUpdate:u.onFirstUpdate,placement:u.placement||"bottom",strategy:u.strategy||"absolute",modifiers:u.modifiers||v},f=t.useState({styles:{popper:{position:a.strategy,left:"0",top:"0"},arrow:{position:"absolute"}},attributes:{}}),l=f[0],p=f[1],d=t.useMemo((function(){return{name:"updateState",enabled:!0,phase:"write",fn:function(e){var t=e.state,n=Object.keys(t.elements);r.flushSync((function(){p({styles:c(n.map((function(e){return[e,t.styles[e]||{}]}))),attributes:c(n.map((function(e){return[e,t.attributes[e]]})))})}))},requires:["computeStyles"]}}),[]),y=t.useMemo((function(){var e={onFirstUpdate:a.onFirstUpdate,placement:a.placement,strategy:a.strategy,modifiers:[].concat(a.modifiers,[d,{name:"applyStyles",enabled:!1}])};return m(i.current,e)?i.current||e:(i.current=e,e)}),[a.onFirstUpdate,a.placement,a.strategy,a.modifiers,d]),h=t.useRef();return s((function(){h.current&&h.current.setOptions(y)}),[y]),s((function(){if(null!=e&&null!=o){var t=(u.createPopper||n.createPopper)(e,o,y);return h.current=t,function(){t.destroy(),h.current=null}}}),[e,o,u.createPopper]),{state:h.current?h.current.state:null,styles:l.styles,attributes:l.attributes,update:h.current?h.current.update:null,forceUpdate:h.current?h.current.forceUpdate:null}},b=function(){},g=function(){return Promise.resolve(null)},w=[];e.Manager=function(e){var r=e.children,n=t.useState(null),i=n[0],a=n[1],f=t.useRef(!1);t.useEffect((function(){return function(){f.current=!0}}),[]);var c=t.useCallback((function(e){f.current||a(e)}),[]);return t.createElement(o.Provider,{value:i},t.createElement(u.Provider,{value:c},r))},e.Popper=function(e){var r=e.placement,n=void 0===r?"bottom":r,u=e.strategy,a=void 0===u?"absolute":u,c=e.modifiers,s=void 0===c?w:c,l=e.referenceElement,p=e.onFirstUpdate,d=e.innerRef,y=e.children,m=t.useContext(o),v=t.useState(null),E=v[0],P=v[1],S=t.useState(null),O=S[0],j=S[1];t.useEffect((function(){f(d,E)}),[d,E]);var R=t.useMemo((function(){return{placement:n,strategy:a,onFirstUpdate:p,modifiers:[].concat(s,[{name:"arrow",enabled:null!=O,options:{element:O}}])}}),[n,a,p,s,O]),x=h(l||m,E,R),U=x.state,M=x.styles,A=x.forceUpdate,F=x.update,k=t.useMemo((function(){return{ref:P,style:M.popper,placement:U?U.placement:n,hasPopperEscaped:U&&U.modifiersData.hide?U.modifiersData.hide.hasPopperEscaped:null,isReferenceHidden:U&&U.modifiersData.hide?U.modifiersData.hide.isReferenceHidden:null,arrowProps:{style:M.arrow,ref:j},forceUpdate:A||b,update:F||g}}),[P,j,n,U,M,F,A]);return i(y)(k)},e.Reference=function(e){var r=e.children,n=e.innerRef,o=t.useContext(u),c=t.useCallback((function(e){f(n,e),a(o,e)}),[n,o]);return t.useEffect((function(){return function(){return f(n,null)}}),[]),t.useEffect((function(){}),[o]),i(r)({ref:c})},e.usePopper=h,Object.defineProperty(e,"__esModule",{value:!0})}));
