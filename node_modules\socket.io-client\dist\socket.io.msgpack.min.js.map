{"version": 3, "file": "socket.io.msgpack.min.js", "sources": ["../node_modules/engine.io-parser/build/esm/commons.js", "../node_modules/engine.io-parser/build/esm/contrib/base64-arraybuffer.js", "../node_modules/engine.io-parser/build/esm/encodePacket.browser.js", "../node_modules/engine.io-parser/build/esm/decodePacket.browser.js", "../node_modules/engine.io-parser/build/esm/index.js", "../node_modules/@socket.io/component-emitter/index.mjs", "../node_modules/engine.io-client/build/esm/globalThis.browser.js", "../node_modules/engine.io-client/build/esm/util.js", "../node_modules/engine.io-client/build/esm/contrib/yeast.js", "../node_modules/engine.io-client/build/esm/transport.js", "../node_modules/engine.io-client/build/esm/contrib/parseqs.js", "../node_modules/engine.io-client/build/esm/contrib/has-cors.js", "../node_modules/engine.io-client/build/esm/transports/xmlhttprequest.browser.js", "../node_modules/engine.io-client/build/esm/transports/polling.js", "../node_modules/engine.io-client/build/esm/transports/websocket-constructor.browser.js", "../node_modules/engine.io-client/build/esm/transports/websocket.js", "../node_modules/engine.io-client/build/esm/transports/index.js", "../node_modules/engine.io-client/build/esm/contrib/parseuri.js", "../node_modules/engine.io-client/build/esm/socket.js", "../node_modules/engine.io-client/build/esm/index.js", "../node_modules/notepack.io/browser/encode.js", "../node_modules/notepack.io/browser/decode.js", "../node_modules/notepack.io/lib/index.js", "../node_modules/component-emitter/index.js", "../node_modules/socket.io-msgpack-parser/index.js", "../build/esm/on.js", "../build/esm/socket.js", "../build/esm/contrib/backo2.js", "../build/esm/manager.js", "../build/esm/index.js", "../build/esm/url.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = (arraybuffer) => {\n    let bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nexport const decode = (base64) => {\n    let bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    const arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + content);\n    };\n    return fileReader.readAsDataURL(data);\n};\nexport default encodePacket;\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType)\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1)\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type]\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            return data instanceof ArrayBuffer ? new Blob([data]) : data;\n        case \"arraybuffer\":\n        default:\n            return data; // assuming the data is already an ArrayBuffer\n    }\n};\nexport default decodePacket;\n", "import encodePacket from \"./encodePacket.js\";\nimport decodePacket from \"./decodePacket.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, encodedPacket => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload };\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\n", "import { globalThisShim as globalThis } from \"./globalThis.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n", "// imported from https://github.com/unshiftio/yeast\n'use strict';\nconst alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split(''), length = 64, map = {};\nlet seed = 0, i = 0, prev;\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nexport function encode(num) {\n    let encoded = '';\n    do {\n        encoded = alphabet[num % length] + encoded;\n        num = Math.floor(num / length);\n    } while (num > 0);\n    return encoded;\n}\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nexport function decode(str) {\n    let decoded = 0;\n    for (i = 0; i < str.length; i++) {\n        decoded = decoded * length + map[str.charAt(i)];\n    }\n    return decoded;\n}\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nexport function yeast() {\n    const now = encode(+new Date());\n    if (now !== prev)\n        return seed = 0, prev = now;\n    return now + '.' + encode(seed++);\n}\n//\n// Map each character to its index.\n//\nfor (; i < length; i++)\n    map[alphabet[i]] = i;\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nclass TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n}\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "// browser shim for xmlhttprequest module\nimport { hasCORS } from \"../contrib/has-cors.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nexport function XHR(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { encode } from \"../contrib/parseqs.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nimport { XHR as XMLHttpRequest } from \"./xmlhttprequest.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nfunction empty() { }\nconst hasXHR2 = (function () {\n    const xhr = new XMLHttpRequest({\n        xdomain: false,\n    });\n    return null != xhr.responseType;\n})();\nexport class Polling extends Transport {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        this.polling = false;\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n            this.xs = opts.secure !== isSSL;\n        }\n        /**\n         * XHR supports binary\n         */\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this.poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this.polling || !this.writable) {\n            let total = 0;\n            if (this.polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    poll() {\n        this.polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this.polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this.poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        let query = this.query || {};\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        let port = \"\";\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        // avoid port if default for schema\n        if (this.opts.port &&\n            ((\"https\" === schema && Number(this.opts.port) !== 443) ||\n                (\"http\" === schema && Number(this.opts.port) !== 80))) {\n            port = \":\" + this.opts.port;\n        }\n        const encodedQuery = encode(query);\n        const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n        return (schema +\n            \"://\" +\n            (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n            port +\n            this.opts.path +\n            (encodedQuery.length ? \"?\" + encodedQuery : \"\"));\n    }\n    /**\n     * Creates a request.\n     *\n     * @param {String} method\n     * @private\n     */\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd, xs: this.xs }, this.opts);\n        return new Request(this.uri(), opts);\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(uri, opts) {\n        super();\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.method = opts.method || \"GET\";\n        this.uri = uri;\n        this.async = false !== opts.async;\n        this.data = undefined !== opts.data ? opts.data : null;\n        this.create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    create() {\n        const opts = pick(this.opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this.opts.xd;\n        opts.xscheme = !!this.opts.xs;\n        const xhr = (this.xhr = new XMLHttpRequest(opts));\n        try {\n            xhr.open(this.method, this.uri, this.async);\n            try {\n                if (this.opts.extraHeaders) {\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this.opts.extraHeaders) {\n                        if (this.opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this.method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this.opts.withCredentials;\n            }\n            if (this.opts.requestTimeout) {\n                xhr.timeout = this.opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this.onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this.data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this.onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this.index = Request.requestsCount++;\n            Request.requests[this.index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    onError(err) {\n        this.emitReserved(\"error\", err, this.xhr);\n        this.cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    cleanup(fromError) {\n        if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n            return;\n        }\n        this.xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this.xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this.index];\n        }\n        this.xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    onLoad() {\n        const data = this.xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this.cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this.cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\n", "import { globalThisShim as globalThis } from \"../globalThis.js\";\nexport const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const WebSocket = globalThis.WebSocket || globalThis.MozWebSocket;\nexport const usingBrowserWebSocket = true;\nexport const defaultBinaryType = \"arraybuffer\";\n", "import { Transport } from \"../transport.js\";\nimport { encode } from \"../contrib/parseqs.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { pick } from \"../util.js\";\nimport { defaultBinaryType, nextTick, usingBrowserWebSocket, WebSocket, } from \"./websocket-constructor.js\";\nimport { encodePacket } from \"engine.io-parser\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class WS extends Transport {\n    /**\n     * WebSocket transport constructor.\n     *\n     * @param {Object} opts - connection options\n     * @protected\n     */\n    constructor(opts) {\n        super(opts);\n        this.supportsBinary = !opts.forceBase64;\n    }\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        if (!this.check()) {\n            // let probe timeout\n            return;\n        }\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws =\n                usingBrowserWebSocket && !isReactNative\n                    ? protocols\n                        ? new WebSocket(uri, protocols)\n                        : new WebSocket(uri)\n                    : new WebSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType || defaultBinaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // always create a new object (GH-437)\n                const opts = {};\n                if (!usingBrowserWebSocket) {\n                    if (packet.options) {\n                        opts.compress = packet.options.compress;\n                    }\n                    if (this.opts.perMessageDeflate) {\n                        const len = \n                        // @ts-ignore\n                        \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n                        if (len < this.opts.perMessageDeflate.threshold) {\n                            opts.compress = false;\n                        }\n                    }\n                }\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    if (usingBrowserWebSocket) {\n                        // TypeError is thrown when passing the second argument on Safari\n                        this.ws.send(data);\n                    }\n                    else {\n                        this.ws.send(data, opts);\n                    }\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        let query = this.query || {};\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        let port = \"\";\n        // avoid port if default for schema\n        if (this.opts.port &&\n            ((\"wss\" === schema && Number(this.opts.port) !== 443) ||\n                (\"ws\" === schema && Number(this.opts.port) !== 80))) {\n            port = \":\" + this.opts.port;\n        }\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        const encodedQuery = encode(query);\n        const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n        return (schema +\n            \"://\" +\n            (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n            port +\n            this.opts.path +\n            (encodedQuery.length ? \"?\" + encodedQuery : \"\"));\n    }\n    /**\n     * Feature detection for WebSocket.\n     *\n     * @return {Boolean} whether this transport is available.\n     * @private\n     */\n    check() {\n        return !!WebSocket;\n    }\n}\n", "import { Polling } from \"./polling.js\";\nimport { WS } from \"./websocket.js\";\nexport const transports = {\n    websocket: WS,\n    polling: Polling,\n};\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nexport class Socket extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts = {}) {\n        super();\n        this.writeBuffer = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            uri = parse(uri);\n            opts.hostname = uri.host;\n            opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n            opts.port = uri.port;\n            if (uri.query)\n                opts.query = uri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = opts.transports || [\"polling\", \"websocket\"];\n        this.writeBuffer = [];\n        this.prevBufferLen = 0;\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: true,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        // set on handshake\n        this.id = null;\n        this.upgrades = null;\n        this.pingInterval = null;\n        this.pingTimeout = null;\n        // set on heartbeat\n        this.pingTimeoutTimer = null;\n        if (typeof addEventListener === \"function\") {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this.beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this.offlineEventListener = () => {\n                    this.onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                addEventListener(\"offline\", this.offlineEventListener, false);\n            }\n        }\n        this.open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts.transportOptions[name], this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        });\n        return new transports[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    open() {\n        let transport;\n        if (this.opts.rememberUpgrade &&\n            Socket.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1) {\n            transport = \"websocket\";\n        }\n        else if (0 === this.transports.length) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        else {\n            transport = this.transports[0];\n        }\n        this.readyState = \"opening\";\n        // Retry with the next transport if the transport is disabled (jsonp: false)\n        try {\n            transport = this.createTransport(transport);\n        }\n        catch (e) {\n            this.transports.shift();\n            this.open();\n            return;\n        }\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this.onDrain.bind(this))\n            .on(\"packet\", this.onPacket.bind(this))\n            .on(\"error\", this.onError.bind(this))\n            .on(\"close\", (reason) => this.onClose(\"transport close\", reason));\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        Socket.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        transport.open();\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n        // we check for `readyState` in case an `open`\n        // listener already closed the socket\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            let i = 0;\n            const l = this.upgrades.length;\n            for (; i < l; i++) {\n                this.probe(this.upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this.resetPingTimeout();\n                    this.sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this.onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this.upgrades = this.filterUpgrades(data.upgrades);\n        this.pingInterval = data.pingInterval;\n        this.pingTimeout = data.pingTimeout;\n        this.maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this.resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    resetPingTimeout() {\n        this.clearTimeoutFn(this.pingTimeoutTimer);\n        this.pingTimeoutTimer = this.setTimeoutFn(() => {\n            this.onClose(\"ping timeout\");\n        }, this.pingInterval + this.pingTimeout);\n        if (this.opts.autoUnref) {\n            this.pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    onDrain() {\n        this.writeBuffer.splice(0, this.prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this.prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this.getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this.prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    getWritablePackets() {\n        const shouldCheckPayloadSize = this.maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this.maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    send(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this.onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    onError(err) {\n        Socket.priorWebsocketSuccess = false;\n        this.emitReserved(\"error\", err);\n        this.onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this.pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (typeof removeEventListener === \"function\") {\n                removeEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n                removeEventListener(\"offline\", this.offlineEventListener, false);\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this.prevBufferLen = 0;\n        }\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        let i = 0;\n        const j = upgrades.length;\n        for (; i < j; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\nSocket.protocol = protocol;\n", "import { Socket } from \"./socket.js\";\nexport { Socket };\nexport const protocol = Socket.protocol;\nexport { Transport } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\nexport { nextTick } from \"./transports/websocket-constructor.js\";\n", "'use strict';\n\nfunction utf8Write(view, offset, str) {\n  var c = 0;\n  for (var i = 0, l = str.length; i < l; i++) {\n    c = str.charCodeAt(i);\n    if (c < 0x80) {\n      view.setUint8(offset++, c);\n    }\n    else if (c < 0x800) {\n      view.setUint8(offset++, 0xc0 | (c >> 6));\n      view.setUint8(offset++, 0x80 | (c & 0x3f));\n    }\n    else if (c < 0xd800 || c >= 0xe000) {\n      view.setUint8(offset++, 0xe0 | (c >> 12));\n      view.setUint8(offset++, 0x80 | (c >> 6) & 0x3f);\n      view.setUint8(offset++, 0x80 | (c & 0x3f));\n    }\n    else {\n      i++;\n      c = 0x10000 + (((c & 0x3ff) << 10) | (str.charCodeAt(i) & 0x3ff));\n      view.setUint8(offset++, 0xf0 | (c >> 18));\n      view.setUint8(offset++, 0x80 | (c >> 12) & 0x3f);\n      view.setUint8(offset++, 0x80 | (c >> 6) & 0x3f);\n      view.setUint8(offset++, 0x80 | (c & 0x3f));\n    }\n  }\n}\n\nfunction utf8Length(str) {\n  var c = 0, length = 0;\n  for (var i = 0, l = str.length; i < l; i++) {\n    c = str.charCodeAt(i);\n    if (c < 0x80) {\n      length += 1;\n    }\n    else if (c < 0x800) {\n      length += 2;\n    }\n    else if (c < 0xd800 || c >= 0xe000) {\n      length += 3;\n    }\n    else {\n      i++;\n      length += 4;\n    }\n  }\n  return length;\n}\n\nfunction _encode(bytes, defers, value) {\n  var type = typeof value, i = 0, l = 0, hi = 0, lo = 0, length = 0, size = 0;\n\n  if (type === 'string') {\n    length = utf8Length(value);\n\n    // fixstr\n    if (length < 0x20) {\n      bytes.push(length | 0xa0);\n      size = 1;\n    }\n    // str 8\n    else if (length < 0x100) {\n      bytes.push(0xd9, length);\n      size = 2;\n    }\n    // str 16\n    else if (length < 0x10000) {\n      bytes.push(0xda, length >> 8, length);\n      size = 3;\n    }\n    // str 32\n    else if (length < 0x100000000) {\n      bytes.push(0xdb, length >> 24, length >> 16, length >> 8, length);\n      size = 5;\n    } else {\n      throw new Error('String too long');\n    }\n    defers.push({ _str: value, _length: length, _offset: bytes.length });\n    return size + length;\n  }\n  if (type === 'number') {\n    // TODO: encode to float 32?\n\n    // float 64\n    if (Math.floor(value) !== value || !isFinite(value)) {\n      bytes.push(0xcb);\n      defers.push({ _float: value, _length: 8, _offset: bytes.length });\n      return 9;\n    }\n\n    if (value >= 0) {\n      // positive fixnum\n      if (value < 0x80) {\n        bytes.push(value);\n        return 1;\n      }\n      // uint 8\n      if (value < 0x100) {\n        bytes.push(0xcc, value);\n        return 2;\n      }\n      // uint 16\n      if (value < 0x10000) {\n        bytes.push(0xcd, value >> 8, value);\n        return 3;\n      }\n      // uint 32\n      if (value < 0x100000000) {\n        bytes.push(0xce, value >> 24, value >> 16, value >> 8, value);\n        return 5;\n      }\n      // uint 64\n      hi = (value / Math.pow(2, 32)) >> 0;\n      lo = value >>> 0;\n      bytes.push(0xcf, hi >> 24, hi >> 16, hi >> 8, hi, lo >> 24, lo >> 16, lo >> 8, lo);\n      return 9;\n    } else {\n      // negative fixnum\n      if (value >= -0x20) {\n        bytes.push(value);\n        return 1;\n      }\n      // int 8\n      if (value >= -0x80) {\n        bytes.push(0xd0, value);\n        return 2;\n      }\n      // int 16\n      if (value >= -0x8000) {\n        bytes.push(0xd1, value >> 8, value);\n        return 3;\n      }\n      // int 32\n      if (value >= -0x80000000) {\n        bytes.push(0xd2, value >> 24, value >> 16, value >> 8, value);\n        return 5;\n      }\n      // int 64\n      hi = Math.floor(value / Math.pow(2, 32));\n      lo = value >>> 0;\n      bytes.push(0xd3, hi >> 24, hi >> 16, hi >> 8, hi, lo >> 24, lo >> 16, lo >> 8, lo);\n      return 9;\n    }\n  }\n  if (type === 'object') {\n    // nil\n    if (value === null) {\n      bytes.push(0xc0);\n      return 1;\n    }\n\n    if (Array.isArray(value)) {\n      length = value.length;\n\n      // fixarray\n      if (length < 0x10) {\n        bytes.push(length | 0x90);\n        size = 1;\n      }\n      // array 16\n      else if (length < 0x10000) {\n        bytes.push(0xdc, length >> 8, length);\n        size = 3;\n      }\n      // array 32\n      else if (length < 0x100000000) {\n        bytes.push(0xdd, length >> 24, length >> 16, length >> 8, length);\n        size = 5;\n      } else {\n        throw new Error('Array too large');\n      }\n      for (i = 0; i < length; i++) {\n        size += _encode(bytes, defers, value[i]);\n      }\n      return size;\n    }\n\n    // fixext 8 / Date\n    if (value instanceof Date) {\n      var time = value.getTime();\n      hi = Math.floor(time / Math.pow(2, 32));\n      lo = time >>> 0;\n      bytes.push(0xd7, 0, hi >> 24, hi >> 16, hi >> 8, hi, lo >> 24, lo >> 16, lo >> 8, lo);\n      return 10;\n    }\n\n    if (value instanceof ArrayBuffer) {\n      length = value.byteLength;\n\n      // bin 8\n      if (length < 0x100) {\n        bytes.push(0xc4, length);\n        size = 2;\n      } else\n      // bin 16\n      if (length < 0x10000) {\n        bytes.push(0xc5, length >> 8, length);\n        size = 3;\n      } else\n      // bin 32\n      if (length < 0x100000000) {\n        bytes.push(0xc6, length >> 24, length >> 16, length >> 8, length);\n        size = 5;\n      } else {\n        throw new Error('Buffer too large');\n      }\n      defers.push({ _bin: value, _length: length, _offset: bytes.length });\n      return size + length;\n    }\n\n    if (typeof value.toJSON === 'function') {\n      return _encode(bytes, defers, value.toJSON());\n    }\n\n    var keys = [], key = '';\n\n    var allKeys = Object.keys(value);\n    for (i = 0, l = allKeys.length; i < l; i++) {\n      key = allKeys[i];\n      if (typeof value[key] !== 'function') {\n        keys.push(key);\n      }\n    }\n    length = keys.length;\n\n    // fixmap\n    if (length < 0x10) {\n      bytes.push(length | 0x80);\n      size = 1;\n    }\n    // map 16\n    else if (length < 0x10000) {\n      bytes.push(0xde, length >> 8, length);\n      size = 3;\n    }\n    // map 32\n    else if (length < 0x100000000) {\n      bytes.push(0xdf, length >> 24, length >> 16, length >> 8, length);\n      size = 5;\n    } else {\n      throw new Error('Object too large');\n    }\n\n    for (i = 0; i < length; i++) {\n      key = keys[i];\n      size += _encode(bytes, defers, key);\n      size += _encode(bytes, defers, value[key]);\n    }\n    return size;\n  }\n  // false/true\n  if (type === 'boolean') {\n    bytes.push(value ? 0xc3 : 0xc2);\n    return 1;\n  }\n  // fixext 1 / undefined\n  if (type === 'undefined') {\n    bytes.push(0xd4, 0, 0);\n    return 3;\n  }\n  throw new Error('Could not encode');\n}\n\nfunction encode(value) {\n  var bytes = [];\n  var defers = [];\n  var size = _encode(bytes, defers, value);\n  var buf = new ArrayBuffer(size);\n  var view = new DataView(buf);\n\n  var deferIndex = 0;\n  var deferWritten = 0;\n  var nextOffset = -1;\n  if (defers.length > 0) {\n    nextOffset = defers[0]._offset;\n  }\n\n  var defer, deferLength = 0, offset = 0;\n  for (var i = 0, l = bytes.length; i < l; i++) {\n    view.setUint8(deferWritten + i, bytes[i]);\n    if (i + 1 !== nextOffset) { continue; }\n    defer = defers[deferIndex];\n    deferLength = defer._length;\n    offset = deferWritten + nextOffset;\n    if (defer._bin) {\n      var bin = new Uint8Array(defer._bin);\n      for (var j = 0; j < deferLength; j++) {\n        view.setUint8(offset + j, bin[j]);\n      }\n    } else if (defer._str) {\n      utf8Write(view, offset, defer._str);\n    } else if (defer._float !== undefined) {\n      view.setFloat64(offset, defer._float);\n    }\n    deferIndex++;\n    deferWritten += deferLength;\n    if (defers[deferIndex]) {\n      nextOffset = defers[deferIndex]._offset;\n    }\n  }\n  return buf;\n}\n\nmodule.exports = encode;\n", "'use strict';\n\nfunction Decoder(buffer) {\n  this._offset = 0;\n  if (buffer instanceof ArrayBuffer) {\n    this._buffer = buffer;\n    this._view = new DataView(this._buffer);\n  } else if (ArrayBuffer.isView(buffer)) {\n    this._buffer = buffer.buffer;\n    this._view = new DataView(this._buffer, buffer.byteOffset, buffer.byteLength);\n  } else {\n    throw new Error('Invalid argument');\n  }\n}\n\nfunction utf8Read(view, offset, length) {\n  var string = '', chr = 0;\n  for (var i = offset, end = offset + length; i < end; i++) {\n    var byte = view.getUint8(i);\n    if ((byte & 0x80) === 0x00) {\n      string += String.fromCharCode(byte);\n      continue;\n    }\n    if ((byte & 0xe0) === 0xc0) {\n      string += String.fromCharCode(\n        ((byte & 0x1f) << 6) |\n        (view.getUint8(++i) & 0x3f)\n      );\n      continue;\n    }\n    if ((byte & 0xf0) === 0xe0) {\n      string += String.fromCharCode(\n        ((byte & 0x0f) << 12) |\n        ((view.getUint8(++i) & 0x3f) << 6) |\n        ((view.getUint8(++i) & 0x3f) << 0)\n      );\n      continue;\n    }\n    if ((byte & 0xf8) === 0xf0) {\n      chr = ((byte & 0x07) << 18) |\n        ((view.getUint8(++i) & 0x3f) << 12) |\n        ((view.getUint8(++i) & 0x3f) << 6) |\n        ((view.getUint8(++i) & 0x3f) << 0);\n      if (chr >= 0x010000) { // surrogate pair\n        chr -= 0x010000;\n        string += String.fromCharCode((chr >>> 10) + 0xD800, (chr & 0x3FF) + 0xDC00);\n      } else {\n        string += String.fromCharCode(chr);\n      }\n      continue;\n    }\n    throw new Error('Invalid byte ' + byte.toString(16));\n  }\n  return string;\n}\n\nDecoder.prototype._array = function (length) {\n  var value = new Array(length);\n  for (var i = 0; i < length; i++) {\n    value[i] = this._parse();\n  }\n  return value;\n};\n\nDecoder.prototype._map = function (length) {\n  var key = '', value = {};\n  for (var i = 0; i < length; i++) {\n    key = this._parse();\n    value[key] = this._parse();\n  }\n  return value;\n};\n\nDecoder.prototype._str = function (length) {\n  var value = utf8Read(this._view, this._offset, length);\n  this._offset += length;\n  return value;\n};\n\nDecoder.prototype._bin = function (length) {\n  var value = this._buffer.slice(this._offset, this._offset + length);\n  this._offset += length;\n  return value;\n};\n\nDecoder.prototype._parse = function () {\n  var prefix = this._view.getUint8(this._offset++);\n  var value, length = 0, type = 0, hi = 0, lo = 0;\n\n  if (prefix < 0xc0) {\n    // positive fixint\n    if (prefix < 0x80) {\n      return prefix;\n    }\n    // fixmap\n    if (prefix < 0x90) {\n      return this._map(prefix & 0x0f);\n    }\n    // fixarray\n    if (prefix < 0xa0) {\n      return this._array(prefix & 0x0f);\n    }\n    // fixstr\n    return this._str(prefix & 0x1f);\n  }\n\n  // negative fixint\n  if (prefix > 0xdf) {\n    return (0xff - prefix + 1) * -1;\n  }\n\n  switch (prefix) {\n    // nil\n    case 0xc0:\n      return null;\n    // false\n    case 0xc2:\n      return false;\n    // true\n    case 0xc3:\n      return true;\n\n    // bin\n    case 0xc4:\n      length = this._view.getUint8(this._offset);\n      this._offset += 1;\n      return this._bin(length);\n    case 0xc5:\n      length = this._view.getUint16(this._offset);\n      this._offset += 2;\n      return this._bin(length);\n    case 0xc6:\n      length = this._view.getUint32(this._offset);\n      this._offset += 4;\n      return this._bin(length);\n\n    // ext\n    case 0xc7:\n      length = this._view.getUint8(this._offset);\n      type = this._view.getInt8(this._offset + 1);\n      this._offset += 2;\n      return [type, this._bin(length)];\n    case 0xc8:\n      length = this._view.getUint16(this._offset);\n      type = this._view.getInt8(this._offset + 2);\n      this._offset += 3;\n      return [type, this._bin(length)];\n    case 0xc9:\n      length = this._view.getUint32(this._offset);\n      type = this._view.getInt8(this._offset + 4);\n      this._offset += 5;\n      return [type, this._bin(length)];\n\n    // float\n    case 0xca:\n      value = this._view.getFloat32(this._offset);\n      this._offset += 4;\n      return value;\n    case 0xcb:\n      value = this._view.getFloat64(this._offset);\n      this._offset += 8;\n      return value;\n\n    // uint\n    case 0xcc:\n      value = this._view.getUint8(this._offset);\n      this._offset += 1;\n      return value;\n    case 0xcd:\n      value = this._view.getUint16(this._offset);\n      this._offset += 2;\n      return value;\n    case 0xce:\n      value = this._view.getUint32(this._offset);\n      this._offset += 4;\n      return value;\n    case 0xcf:\n      hi = this._view.getUint32(this._offset) * Math.pow(2, 32);\n      lo = this._view.getUint32(this._offset + 4);\n      this._offset += 8;\n      return hi + lo;\n\n    // int\n    case 0xd0:\n      value = this._view.getInt8(this._offset);\n      this._offset += 1;\n      return value;\n    case 0xd1:\n      value = this._view.getInt16(this._offset);\n      this._offset += 2;\n      return value;\n    case 0xd2:\n      value = this._view.getInt32(this._offset);\n      this._offset += 4;\n      return value;\n    case 0xd3:\n      hi = this._view.getInt32(this._offset) * Math.pow(2, 32);\n      lo = this._view.getUint32(this._offset + 4);\n      this._offset += 8;\n      return hi + lo;\n\n    // fixext\n    case 0xd4:\n      type = this._view.getInt8(this._offset);\n      this._offset += 1;\n      if (type === 0x00) {\n        this._offset += 1;\n        return void 0;\n      }\n      return [type, this._bin(1)];\n    case 0xd5:\n      type = this._view.getInt8(this._offset);\n      this._offset += 1;\n      return [type, this._bin(2)];\n    case 0xd6:\n      type = this._view.getInt8(this._offset);\n      this._offset += 1;\n      return [type, this._bin(4)];\n    case 0xd7:\n      type = this._view.getInt8(this._offset);\n      this._offset += 1;\n      if (type === 0x00) {\n        hi = this._view.getInt32(this._offset) * Math.pow(2, 32);\n        lo = this._view.getUint32(this._offset + 4);\n        this._offset += 8;\n        return new Date(hi + lo);\n      }\n      return [type, this._bin(8)];\n    case 0xd8:\n      type = this._view.getInt8(this._offset);\n      this._offset += 1;\n      return [type, this._bin(16)];\n\n    // str\n    case 0xd9:\n      length = this._view.getUint8(this._offset);\n      this._offset += 1;\n      return this._str(length);\n    case 0xda:\n      length = this._view.getUint16(this._offset);\n      this._offset += 2;\n      return this._str(length);\n    case 0xdb:\n      length = this._view.getUint32(this._offset);\n      this._offset += 4;\n      return this._str(length);\n\n    // array\n    case 0xdc:\n      length = this._view.getUint16(this._offset);\n      this._offset += 2;\n      return this._array(length);\n    case 0xdd:\n      length = this._view.getUint32(this._offset);\n      this._offset += 4;\n      return this._array(length);\n\n    // map\n    case 0xde:\n      length = this._view.getUint16(this._offset);\n      this._offset += 2;\n      return this._map(length);\n    case 0xdf:\n      length = this._view.getUint32(this._offset);\n      this._offset += 4;\n      return this._map(length);\n  }\n\n  throw new Error('Could not parse');\n};\n\nfunction decode(buffer) {\n  var decoder = new Decoder(buffer);\n  var value = decoder._parse();\n  if (decoder._offset !== buffer.byteLength) {\n    throw new Error((buffer.byteLength - decoder._offset) + ' trailing bytes');\n  }\n  return value;\n}\n\nmodule.exports = decode;\n", "exports.encode = require('./encode');\nexports.decode = require('./decode');\n", "\r\n/**\r\n * Expose `Emitter`.\r\n */\r\n\r\nif (typeof module !== 'undefined') {\r\n  module.exports = Emitter;\r\n}\r\n\r\n/**\r\n * Initialize a new `Emitter`.\r\n *\r\n * @api public\r\n */\r\n\r\nfunction Emitter(obj) {\r\n  if (obj) return mixin(obj);\r\n};\r\n\r\n/**\r\n * Mixin the emitter properties.\r\n *\r\n * @param {Object} obj\r\n * @return {Object}\r\n * @api private\r\n */\r\n\r\nfunction mixin(obj) {\r\n  for (var key in Emitter.prototype) {\r\n    obj[key] = Emitter.prototype[key];\r\n  }\r\n  return obj;\r\n}\r\n\r\n/**\r\n * Listen on the given `event` with `fn`.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.on =\r\nEmitter.prototype.addEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\r\n    .push(fn);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Adds an `event` listener that will be invoked a single\r\n * time then automatically removed.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.once = function(event, fn){\r\n  function on() {\r\n    this.off(event, on);\r\n    fn.apply(this, arguments);\r\n  }\r\n\r\n  on.fn = fn;\r\n  this.on(event, on);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Remove the given callback for `event` or all\r\n * registered callbacks.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.off =\r\nEmitter.prototype.removeListener =\r\nEmitter.prototype.removeAllListeners =\r\nEmitter.prototype.removeEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n\r\n  // all\r\n  if (0 == arguments.length) {\r\n    this._callbacks = {};\r\n    return this;\r\n  }\r\n\r\n  // specific event\r\n  var callbacks = this._callbacks['$' + event];\r\n  if (!callbacks) return this;\r\n\r\n  // remove all handlers\r\n  if (1 == arguments.length) {\r\n    delete this._callbacks['$' + event];\r\n    return this;\r\n  }\r\n\r\n  // remove specific handler\r\n  var cb;\r\n  for (var i = 0; i < callbacks.length; i++) {\r\n    cb = callbacks[i];\r\n    if (cb === fn || cb.fn === fn) {\r\n      callbacks.splice(i, 1);\r\n      break;\r\n    }\r\n  }\r\n\r\n  // Remove event specific arrays for event types that no\r\n  // one is subscribed for to avoid memory leak.\r\n  if (callbacks.length === 0) {\r\n    delete this._callbacks['$' + event];\r\n  }\r\n\r\n  return this;\r\n};\r\n\r\n/**\r\n * Emit `event` with the given args.\r\n *\r\n * @param {String} event\r\n * @param {Mixed} ...\r\n * @return {Emitter}\r\n */\r\n\r\nEmitter.prototype.emit = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n\r\n  var args = new Array(arguments.length - 1)\r\n    , callbacks = this._callbacks['$' + event];\r\n\r\n  for (var i = 1; i < arguments.length; i++) {\r\n    args[i - 1] = arguments[i];\r\n  }\r\n\r\n  if (callbacks) {\r\n    callbacks = callbacks.slice(0);\r\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\r\n      callbacks[i].apply(this, args);\r\n    }\r\n  }\r\n\r\n  return this;\r\n};\r\n\r\n/**\r\n * Return array of callbacks for `event`.\r\n *\r\n * @param {String} event\r\n * @return {Array}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.listeners = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n  return this._callbacks['$' + event] || [];\r\n};\r\n\r\n/**\r\n * Check if this emitter has `event` handlers.\r\n *\r\n * @param {String} event\r\n * @return {Boolean}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.hasListeners = function(event){\r\n  return !! this.listeners(event).length;\r\n};\r\n", "var msgpack = require(\"notepack.io\");\nvar Emitter = require(\"component-emitter\");\n\nexports.protocol = 5;\n\n/**\n * Packet types (see https://github.com/socketio/socket.io-protocol)\n */\n\nvar PacketType = (exports.PacketType = {\n  CONNECT: 0,\n  DISCONNECT: 1,\n  EVENT: 2,\n  ACK: 3,\n  CONNECT_ERROR: 4,\n});\n\nvar isInteger =\n  Number.isInteger ||\n  function (value) {\n    return (\n      typeof value === \"number\" &&\n      isFinite(value) &&\n      Math.floor(value) === value\n    );\n  };\n\nvar isString = function (value) {\n  return typeof value === \"string\";\n};\n\nvar isObject = function (value) {\n  return Object.prototype.toString.call(value) === \"[object Object]\";\n};\n\nfunction Encoder() {}\n\nEncoder.prototype.encode = function (packet) {\n  return [msgpack.encode(packet)];\n};\n\nfunction Decoder() {}\n\nEmitter(Decoder.prototype);\n\nDecoder.prototype.add = function (obj) {\n  var decoded = msgpack.decode(obj);\n  this.checkPacket(decoded);\n  this.emit(\"decoded\", decoded);\n};\n\nfunction isDataValid(decoded) {\n  switch (decoded.type) {\n    case PacketType.CONNECT:\n      return decoded.data === undefined || isObject(decoded.data);\n    case PacketType.DISCONNECT:\n      return decoded.data === undefined;\n    case PacketType.CONNECT_ERROR:\n      return isString(decoded.data) || isObject(decoded.data);\n    default:\n      return Array.isArray(decoded.data);\n  }\n}\n\nDecoder.prototype.checkPacket = function (decoded) {\n  var isTypeValid =\n    isInteger(decoded.type) &&\n    decoded.type >= PacketType.CONNECT &&\n    decoded.type <= PacketType.CONNECT_ERROR;\n  if (!isTypeValid) {\n    throw new Error(\"invalid packet type\");\n  }\n\n  if (!isString(decoded.nsp)) {\n    throw new Error(\"invalid namespace\");\n  }\n\n  if (!isDataValid(decoded)) {\n    throw new Error(\"invalid payload\");\n  }\n\n  var isAckValid = decoded.id === undefined || isInteger(decoded.id);\n  if (!isAckValid) {\n    throw new Error(\"invalid packet id\");\n  }\n};\n\nDecoder.prototype.destroy = function () {};\n\nexports.Encoder = Encoder;\nexports.Decoder = Decoder;\n", "export function on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n", "import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */\nexport class Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     */\n    constructor(io, nsp, opts) {\n        super();\n        /**\n         * Whether the socket is currently connected to the server.\n         *\n         * @example\n         * const socket = io();\n         *\n         * socket.on(\"connect\", () => {\n         *   console.log(socket.connected); // true\n         * });\n         *\n         * socket.on(\"disconnect\", () => {\n         *   console.log(socket.connected); // false\n         * });\n         */\n        this.connected = false;\n        /**\n         * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n         * be transmitted by the server.\n         */\n        this.recovered = false;\n        /**\n         * Buffer for packets received before the CONNECT packet\n         */\n        this.receiveBuffer = [];\n        /**\n         * Buffer for packets that will be sent once the socket is connected\n         */\n        this.sendBuffer = [];\n        /**\n         * The queue of packets to be sent with retry in case of failure.\n         *\n         * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n         * @private\n         */\n        this._queue = [];\n        /**\n         * A sequence to generate the ID of the {@link QueuedPacket}.\n         * @private\n         */\n        this._queueSeq = 0;\n        this.ids = 0;\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        this._opts = Object.assign({}, opts);\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.disconnected); // false\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.disconnected); // true\n     * });\n     */\n    get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on(io, \"open\", this.onopen.bind(this)),\n            on(io, \"packet\", this.onpacket.bind(this)),\n            on(io, \"error\", this.onerror.bind(this)),\n            on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n     *\n     * @example\n     * const socket = io();\n     *\n     * console.log(socket.active); // true\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   if (reason === \"io server disconnect\") {\n     *     // the disconnection was initiated by the server, you need to manually reconnect\n     *     console.log(socket.active); // false\n     *   }\n     *   // else the socket will automatically try to reconnect\n     *   console.log(socket.active); // true\n     * });\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @example\n     * const socket = io({\n     *   autoConnect: false\n     * });\n     *\n     * socket.connect();\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for {@link connect()}.\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * This method mimics the WebSocket.send() method.\n     *\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n     *\n     * @example\n     * socket.send(\"hello\");\n     *\n     * // this is equivalent to\n     * socket.emit(\"message\", \"hello\");\n     *\n     * @return self\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @example\n     * socket.emit(\"hello\", \"world\");\n     *\n     * // all serializable datastructures are supported (no need to call JSON.stringify)\n     * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n     *\n     * // with an acknowledgement from the server\n     * socket.emit(\"hello\", \"world\", (val) => {\n     *   // ...\n     * });\n     *\n     * @return self\n     */\n    emit(ev, ...args) {\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n            this._addToQueue(args);\n            return this;\n        }\n        const packet = {\n            type: PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = this.io.engine &&\n            this.io.engine.transport &&\n            this.io.engine.transport.writable;\n        const discardPacket = this.flags.volatile && (!isTransportWritable || !this.connected);\n        if (discardPacket) {\n        }\n        else if (this.connected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */\n    _registerAckCallback(id, ack) {\n        var _a;\n        const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(() => {\n            delete this.acks[id];\n            for (let i = 0; i < this.sendBuffer.length; i++) {\n                if (this.sendBuffer[i].id === id) {\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        this.acks[id] = (...args) => {\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, [null, ...args]);\n        };\n    }\n    /**\n     * Emits an event and waits for an acknowledgement\n     *\n     * @example\n     * // without timeout\n     * const response = await socket.emitWithAck(\"hello\", \"world\");\n     *\n     * // with a specific timeout\n     * try {\n     *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n     * } catch (err) {\n     *   // the server did not acknowledge the event in the given delay\n     * }\n     *\n     * @return a Promise that will be fulfilled when the server acknowledges the event\n     */\n    emitWithAck(ev, ...args) {\n        // the timeout flag is optional\n        const withErr = this.flags.timeout !== undefined || this._opts.ackTimeout !== undefined;\n        return new Promise((resolve, reject) => {\n            args.push((arg1, arg2) => {\n                if (withErr) {\n                    return arg1 ? reject(arg1) : resolve(arg2);\n                }\n                else {\n                    return resolve(arg1);\n                }\n            });\n            this.emit(ev, ...args);\n        });\n    }\n    /**\n     * Add the packet to the queue.\n     * @param args\n     * @private\n     */\n    _addToQueue(args) {\n        let ack;\n        if (typeof args[args.length - 1] === \"function\") {\n            ack = args.pop();\n        }\n        const packet = {\n            id: this._queueSeq++,\n            tryCount: 0,\n            pending: false,\n            args,\n            flags: Object.assign({ fromQueue: true }, this.flags),\n        };\n        args.push((err, ...responseArgs) => {\n            if (packet !== this._queue[0]) {\n                // the packet has already been acknowledged\n                return;\n            }\n            const hasError = err !== null;\n            if (hasError) {\n                if (packet.tryCount > this._opts.retries) {\n                    this._queue.shift();\n                    if (ack) {\n                        ack(err);\n                    }\n                }\n            }\n            else {\n                this._queue.shift();\n                if (ack) {\n                    ack(null, ...responseArgs);\n                }\n            }\n            packet.pending = false;\n            return this._drainQueue();\n        });\n        this._queue.push(packet);\n        this._drainQueue();\n    }\n    /**\n     * Send the first packet of the queue, and wait for an acknowledgement from the server.\n     * @param force - whether to resend a packet that has not been acknowledged yet\n     *\n     * @private\n     */\n    _drainQueue(force = false) {\n        if (!this.connected || this._queue.length === 0) {\n            return;\n        }\n        const packet = this._queue[0];\n        if (packet.pending && !force) {\n            return;\n        }\n        packet.pending = true;\n        packet.tryCount++;\n        this.flags = packet.flags;\n        this.emit.apply(this, packet.args);\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this._sendConnectPacket(data);\n            });\n        }\n        else {\n            this._sendConnectPacket(this.auth);\n        }\n    }\n    /**\n     * Sends a CONNECT packet to initiate the Socket.IO session.\n     *\n     * @param data\n     * @private\n     */\n    _sendConnectPacket(data) {\n        this.packet({\n            type: PacketType.CONNECT,\n            data: this._pid\n                ? Object.assign({ pid: this._pid, offset: this._lastOffset }, data)\n                : data,\n        });\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */\n    onclose(reason, description) {\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    this.onconnect(packet.data.sid, packet.data.pid);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        if (null != packet.id) {\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n        if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n            this._lastOffset = args[args.length - 1];\n        }\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            self.packet({\n                type: PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowlegement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (\"function\" === typeof ack) {\n            ack.apply(this, packet.data);\n            delete this.acks[packet.id];\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id, pid) {\n        this.id = id;\n        this.recovered = pid && this._pid === pid;\n        this._pid = pid; // defined only if connection state recovery is enabled\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n        this._drainQueue(true);\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n     *\n     * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   // console.log(reason); prints \"io client disconnect\"\n     * });\n     *\n     * socket.disconnect();\n     *\n     * @return self\n     */\n    disconnect() {\n        if (this.connected) {\n            this.packet({ type: PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for {@link disconnect()}.\n     *\n     * @return self\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @example\n     * socket.compress(false).emit(\"hello\");\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @example\n     * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n     *\n     * @returns self\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * @example\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     *\n     * @returns self\n     */\n    timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @example\n     * socket.onAny((event, ...args) => {\n     *   console.log(`got ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @example\n     * socket.prependAny((event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * }\n     *\n     * socket.onAny(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAny(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAny();\n     *\n     * @param listener\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * }\n     *\n     * socket.onAnyOutgoing(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAnyOutgoing(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAnyOutgoing();\n     *\n     * @param [listener] - the catch-all listener (optional)\n     */\n    offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */\n    notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n", "/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\nexport function Backoff(opts) {\n    opts = opts || {};\n    this.ms = opts.min || 100;\n    this.max = opts.max || 10000;\n    this.factor = opts.factor || 2;\n    this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n    this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\nBackoff.prototype.duration = function () {\n    var ms = this.ms * Math.pow(this.factor, this.attempts++);\n    if (this.jitter) {\n        var rand = Math.random();\n        var deviation = Math.floor(rand * this.jitter * ms);\n        ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n    }\n    return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\nBackoff.prototype.reset = function () {\n    this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */\nBackoff.prototype.setMin = function (min) {\n    this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */\nBackoff.prototype.setMax = function (max) {\n    this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */\nBackoff.prototype.setJitter = function (jitter) {\n    this.jitter = jitter;\n};\n", "import { Socket as Engine, installTimerFunctions, nextTick, } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\nexport class Manager extends Emitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        installTimerFunctions(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n        this.engine = new Engine(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        // emit `error`\n        const errorSub = on(socket, \"error\", (err) => {\n            self.cleanup();\n            self._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                self.maybeReconnectOnOpen();\n            }\n        });\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            if (timeout === 0) {\n                openSubDestroy(); // prevents a race condition with the 'open' event\n            }\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n                openSubDestroy();\n                socket.close();\n                // @ts-ignore\n                socket.emit(\"error\", new Error(\"timeout\"));\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        try {\n            this.decoder.add(data);\n        }\n        catch (e) {\n            this.onclose(\"parse error\", e);\n        }\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n        nextTick(() => {\n            this.emitReserved(\"packet\", packet);\n        }, this.setTimeoutFn);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        else if (this._autoConnect && !socket.active) {\n            socket.connect();\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n        if (this.engine)\n            this.engine.close();\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called upon engine close.\n     *\n     * @private\n     */\n    onclose(reason, description) {\n        this.cleanup();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n", "import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\n/**\n * Managers cache.\n */\nconst cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n        io = new Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n            cache[id] = new Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager,\n    Socket,\n    io: lookup,\n    connect: lookup,\n});\n/**\n * Protocol version.\n *\n * @public\n */\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default, };\n", "import { parse } from \"engine.io-client\";\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nexport function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        obj = parse(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "encodePacket", "supportsBinary", "callback", "obj", "encodeBlobAsBase64", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "chars", "lookup", "Uint8Array", "i", "length", "charCodeAt", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "decoded", "base64", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "len", "p", "arraybuffer", "bytes", "decode", "SEPARATOR", "String", "fromCharCode", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "this", "_callbacks", "push", "Emitter$1", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "cb", "callbacks", "splice", "emit", "args", "Array", "slice", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "globalThisShim", "self", "window", "Function", "pick", "_len", "attr", "_key", "reduce", "acc", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "globalThis", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "setTimeoutFn", "bind", "clearTimeoutFn", "prev", "TransportError", "reason", "description", "context", "_this", "_classCallCheck", "_super", "Error", "Transport", "_Emitter", "_inherits", "_super2", "_createSuper", "_this2", "writable", "_assertThisInitialized", "query", "socket", "_createClass", "value", "_get", "_getPrototypeOf", "readyState", "doOpen", "doClose", "onClose", "packets", "write", "packet", "onPacket", "details", "onPause", "alphabet", "map", "seed", "encode", "num", "encoded", "Math", "floor", "yeast", "now", "Date", "str", "encodeURIComponent", "qs", "qry", "pairs", "l", "pair", "decodeURIComponent", "XMLHttpRequest", "err", "hasCORS", "XHR", "xdomain", "e", "concat", "join", "empty", "hasXHR2", "responseType", "Polling", "_Transport", "polling", "location", "isSSL", "protocol", "port", "xd", "hostname", "xs", "secure", "forceBase64", "get", "poll", "pause", "total", "doPoll", "_this3", "encodedPayload", "encodedPackets", "decodedPacket", "decodePayload", "onOpen", "_this4", "close", "_this5", "count", "encodePayload", "doWrite", "schema", "timestampRequests", "timestampParam", "sid", "b64", "Number", "<PERSON><PERSON><PERSON><PERSON>", "indexOf", "path", "_extends", "Request", "uri", "_this6", "req", "request", "method", "xhrStatus", "onError", "_this7", "onData", "pollXhr", "_this8", "async", "undefined", "_this9", "xscheme", "xhr", "open", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "withCredentials", "requestTimeout", "timeout", "onreadystatechange", "status", "onLoad", "send", "document", "index", "requestsCount", "requests", "cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "nextTick", "Promise", "resolve", "then", "WebSocket", "MozWebSocket", "isReactNative", "navigator", "product", "toLowerCase", "WS", "check", "protocols", "headers", "ws", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "_loop", "lastPacket", "transports", "websocket", "re", "parts", "parse", "src", "b", "replace", "m", "exec", "source", "host", "authority", "ipv6uri", "pathNames", "regx", "names", "query<PERSON><PERSON>", "$0", "$1", "$2", "Socket", "writeBuffer", "prevBufferLen", "agent", "upgrade", "rememberUpgrade", "addTrailingSlash", "rejectUnauthorized", "perMessageDeflate", "threshold", "transportOptions", "closeOnBeforeunload", "id", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "beforeunloadEventListener", "transport", "offlineEventListener", "name", "EIO", "priorWebsocketSuccess", "createTransport", "shift", "setTransport", "onDrain", "failed", "onTransportOpen", "msg", "upgrading", "flush", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "probe", "onHandshake", "JSON", "resetPingTimeout", "sendPacket", "code", "filterUpgrades", "maxPayload", "getWritablePackets", "payloadSize", "c", "utf8Length", "ceil", "byteLength", "size", "options", "compress", "cleanupAndClose", "waitForUpgrade", "filteredUpgrades", "j", "Socket$1", "utf8Write", "view", "offset", "setUint8", "_encode", "defers", "hi", "lo", "_str", "_length", "_offset", "isFinite", "pow", "_float", "isArray", "time", "getTime", "_bin", "toJSON", "allKeys", "encode_1", "buf", "DataView", "deferIndex", "defer<PERSON><PERSON>ten", "nextOffset", "defer", "deferL<PERSON>th", "bin", "setFloat64", "Decoder", "_buffer", "_view", "byteOffset", "_array", "_parse", "_map", "string", "chr", "end", "byte", "getUint8", "utf8Read", "prefix", "getUint16", "getUint32", "getInt8", "getFloat32", "getFloat64", "getInt16", "getInt32", "decode_1", "decoder", "lib", "require$$0", "require$$1", "module", "exports", "msgpack", "socket_ioMsgpackParser", "PacketType", "PacketType_1", "CONNECT", "DISCONNECT", "EVENT", "ACK", "CONNECT_ERROR", "isInteger", "isString", "isObject", "Encoder", "add", "checkPacket", "nsp", "isDataValid", "destroy", "Encoder_1", "Decoder_1", "RESERVED_EVENTS", "freeze", "connect", "connect_error", "disconnect", "disconnecting", "newListener", "io", "connected", "recovered", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "_queue", "_queueSeq", "ids", "acks", "flags", "auth", "_opts", "_autoConnect", "subs", "onpacket", "subEvents", "_readyState", "unshift", "_len2", "_key2", "retries", "fromQueue", "_addToQueue", "ack", "pop", "_registerAckCallback", "isTransportWritable", "engine", "discardPacket", "notifyOutgoingListeners", "_a", "ackTimeout", "timer", "_len3", "_key3", "_len4", "_key4", "withErr", "reject", "arg1", "arg2", "tryCount", "pending", "<PERSON><PERSON><PERSON><PERSON>", "_len5", "responseArgs", "_key5", "_drainQueue", "force", "_packet", "_sendConnectPacket", "_pid", "pid", "_lastOffset", "onconnect", "BINARY_EVENT", "onevent", "BINARY_ACK", "onack", "ondisconnect", "message", "emitEvent", "_anyListeners", "_step", "_iterator", "_createForOfIteratorHelper", "s", "n", "done", "f", "sent", "_len6", "_key6", "emitBuffered", "subDestroy", "listener", "_anyOutgoingListeners", "_step2", "_iterator2", "Backoff", "ms", "min", "max", "factor", "jitter", "attempts", "duration", "rand", "random", "deviation", "reset", "setMin", "setMax", "setJitter", "Manager", "nsps", "reconnection", "reconnectionAttempts", "Infinity", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "_parser", "parser", "encoder", "autoConnect", "v", "_reconnection", "_reconnectionAttempts", "_reconnectionDelay", "_randomizationFactor", "_reconnectionDelayMax", "_timeout", "_reconnecting", "reconnect", "Engine", "skipReconnect", "openSubDestroy", "errorSub", "maybeReconnectOnOpen", "onping", "ondata", "ondecoded", "active", "_i", "_nsps", "_close", "delay", "onreconnect", "attempt", "cache", "_typeof", "parsed", "loc", "test", "href", "url", "sameNamespace", "forceNew", "multiplex"], "mappings": ";;;;;qkJAAA,IAAMA,EAAeC,OAAOC,OAAO,MACnCF,EAAY,KAAW,IACvBA,EAAY,MAAY,IACxBA,EAAY,KAAW,IACvBA,EAAY,KAAW,IACvBA,EAAY,QAAc,IAC1BA,EAAY,QAAc,IAC1BA,EAAY,KAAW,IACvB,IAAMG,EAAuBF,OAAOC,OAAO,MAC3CD,OAAOG,KAAKJ,GAAcK,SAAQ,SAAAC,GAC9BH,EAAqBH,EAAaM,IAAQA,CAC7C,ICRD,IDSA,IAAMC,EAAe,CAAEC,KAAM,QAASC,KAAM,gBEXtCC,EAAiC,mBAATC,MACT,oBAATA,MACqC,6BAAzCV,OAAOW,UAAUC,SAASC,KAAKH,MACjCI,EAA+C,mBAAhBC,YAO/BC,EAAe,WAAiBC,EAAgBC,GAAa,IALpDC,EAKSZ,IAAAA,KAAMC,IAAAA,KAC1B,OAAIC,GAAkBD,aAAgBE,KAC9BO,EACOC,EAASV,GAGTY,EAAmBZ,EAAMU,GAG/BJ,IACJN,aAAgBO,cAfVI,EAegCX,EAdN,mBAAvBO,YAAYM,OACpBN,YAAYM,OAAOF,GACnBA,GAAOA,EAAIG,kBAAkBP,cAa3BE,EACOC,EAASV,GAGTY,EAAmB,IAAIV,KAAK,CAACF,IAAQU,GAI7CA,EAASnB,EAAaQ,IAASC,GAAQ,IACjD,EACKY,EAAqB,SAACZ,EAAMU,GAC9B,IAAMK,EAAa,IAAIC,WAKvB,OAJAD,EAAWE,OAAS,WAChB,IAAMC,EAAUH,EAAWI,OAAOC,MAAM,KAAK,GAC7CV,EAAS,IAAMQ,IAEZH,EAAWM,cAAcrB,EACnC,EDvCKsB,EAAQ,mEAERC,EAA+B,oBAAfC,WAA6B,GAAK,IAAIA,WAAW,KAC9DC,EAAI,EAAGA,EAAIH,EAAMI,OAAQD,IAC9BF,EAAOD,EAAMK,WAAWF,IAAMA,EAkB3B,IEpBDnB,EAA+C,mBAAhBC,YAC/BqB,EAAe,SAACC,EAAeC,GACjC,GAA6B,iBAAlBD,EACP,MAAO,CACH9B,KAAM,UACNC,KAAM+B,EAAUF,EAAeC,IAGvC,IAAM/B,EAAO8B,EAAcG,OAAO,GAClC,MAAa,MAATjC,EACO,CACHA,KAAM,UACNC,KAAMiC,EAAmBJ,EAAcK,UAAU,GAAIJ,IAG1CpC,EAAqBK,GAIjC8B,EAAcH,OAAS,EACxB,CACE3B,KAAML,EAAqBK,GAC3BC,KAAM6B,EAAcK,UAAU,IAEhC,CACEnC,KAAML,EAAqBK,IARxBD,CAUd,EACKmC,EAAqB,SAACjC,EAAM8B,GAC9B,GAAIxB,EAAuB,CACvB,IAAM6B,EFVQ,SAACC,GACnB,IAA8DX,EAAUY,EAAUC,EAAUC,EAAUC,EAAlGC,EAA+B,IAAhBL,EAAOV,OAAegB,EAAMN,EAAOV,OAAWiB,EAAI,EACnC,MAA9BP,EAAOA,EAAOV,OAAS,KACvBe,IACkC,MAA9BL,EAAOA,EAAOV,OAAS,IACvBe,KAGR,IAAMG,EAAc,IAAIrC,YAAYkC,GAAeI,EAAQ,IAAIrB,WAAWoB,GAC1E,IAAKnB,EAAI,EAAGA,EAAIiB,EAAKjB,GAAK,EACtBY,EAAWd,EAAOa,EAAOT,WAAWF,IACpCa,EAAWf,EAAOa,EAAOT,WAAWF,EAAI,IACxCc,EAAWhB,EAAOa,EAAOT,WAAWF,EAAI,IACxCe,EAAWjB,EAAOa,EAAOT,WAAWF,EAAI,IACxCoB,EAAMF,KAAQN,GAAY,EAAMC,GAAY,EAC5CO,EAAMF,MAAoB,GAAXL,IAAkB,EAAMC,GAAY,EACnDM,EAAMF,MAAoB,EAAXJ,IAAiB,EAAiB,GAAXC,EAE1C,OAAOI,CACV,CETuBE,CAAO9C,GACvB,OAAO+B,EAAUI,EAASL,EAC7B,CAEG,MAAO,CAAEM,QAAQ,EAAMpC,KAAAA,EAE9B,EACK+B,EAAY,SAAC/B,EAAM8B,GACrB,MACS,SADDA,GAEO9B,aAAgBO,YAAc,IAAIL,KAAK,CAACF,IAGxCA,CAElB,EC7CK+C,EAAYC,OAAOC,aAAa,ICI/B,SAASC,EAAQvC,GACtB,GAAIA,EAAK,OAWX,SAAeA,GACb,IAAK,IAAId,KAAOqD,EAAQ/C,UACtBQ,EAAId,GAAOqD,EAAQ/C,UAAUN,GAE/B,OAAOc,CACR,CAhBiBwC,CAAMxC,EACvB,CA0BDuC,EAAQ/C,UAAUiD,GAClBF,EAAQ/C,UAAUkD,iBAAmB,SAASC,EAAOC,GAInD,OAHAC,KAAKC,WAAaD,KAAKC,YAAc,CAAA,GACpCD,KAAKC,WAAW,IAAMH,GAASE,KAAKC,WAAW,IAAMH,IAAU,IAC7DI,KAAKH,GACDC,IACR,EAYMG,EAACxD,UAAUyD,KAAO,SAASN,EAAOC,GACvC,SAASH,IACPI,KAAKK,IAAIP,EAAOF,GAChBG,EAAGO,MAAMN,KAAMO,UAChB,CAID,OAFAX,EAAGG,GAAKA,EACRC,KAAKJ,GAAGE,EAAOF,GACRI,IACR,EAYMG,EAACxD,UAAU0D,IAClBX,EAAQ/C,UAAU6D,eAClBd,EAAQ/C,UAAU8D,mBAClBf,EAAQ/C,UAAU+D,oBAAsB,SAASZ,EAAOC,GAItD,GAHAC,KAAKC,WAAaD,KAAKC,YAAc,CAAA,EAGjC,GAAKM,UAAUrC,OAEjB,OADA8B,KAAKC,WAAa,GACXD,KAIT,IAUIW,EAVAC,EAAYZ,KAAKC,WAAW,IAAMH,GACtC,IAAKc,EAAW,OAAOZ,KAGvB,GAAI,GAAKO,UAAUrC,OAEjB,cADO8B,KAAKC,WAAW,IAAMH,GACtBE,KAKT,IAAK,IAAI/B,EAAI,EAAGA,EAAI2C,EAAU1C,OAAQD,IAEpC,IADA0C,EAAKC,EAAU3C,MACJ8B,GAAMY,EAAGZ,KAAOA,EAAI,CAC7Ba,EAAUC,OAAO5C,EAAG,GACpB,KACD,CASH,OAJyB,IAArB2C,EAAU1C,eACL8B,KAAKC,WAAW,IAAMH,GAGxBE,IACR,EAUDN,EAAQ/C,UAAUmE,KAAO,SAAShB,GAChCE,KAAKC,WAAaD,KAAKC,YAAc,CAAA,EAKrC,IAHA,IAAIc,EAAO,IAAIC,MAAMT,UAAUrC,OAAS,GACpC0C,EAAYZ,KAAKC,WAAW,IAAMH,GAE7B7B,EAAI,EAAGA,EAAIsC,UAAUrC,OAAQD,IACpC8C,EAAK9C,EAAI,GAAKsC,UAAUtC,GAG1B,GAAI2C,EAEG,CAAI3C,EAAI,EAAb,IAAK,IAAWiB,GADhB0B,EAAYA,EAAUK,MAAM,IACI/C,OAAQD,EAAIiB,IAAOjB,EACjD2C,EAAU3C,GAAGqC,MAAMN,KAAMe,EADK7C,CAKlC,OAAO8B,IACR,EAGMG,EAACxD,UAAUuE,aAAexB,EAAQ/C,UAAUmE,KAUnDpB,EAAQ/C,UAAUwE,UAAY,SAASrB,GAErC,OADAE,KAAKC,WAAaD,KAAKC,YAAc,CAAA,EAC9BD,KAAKC,WAAW,IAAMH,IAAU,EACxC,EAUDJ,EAAQ/C,UAAUyE,aAAe,SAAStB,GACxC,QAAUE,KAAKmB,UAAUrB,GAAO5B,MACjC,ECxKM,IAAMmD,EACW,oBAATC,KACAA,KAEgB,oBAAXC,OACLA,OAGAC,SAAS,cAATA,GCPR,SAASC,EAAKtE,GAAc,IAAA,IAAAuE,EAAAnB,UAAArC,OAANyD,EAAM,IAAAX,MAAAU,EAAA,EAAAA,EAAA,EAAA,GAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAND,EAAMC,EAAA,GAAArB,UAAAqB,GAC/B,OAAOD,EAAKE,QAAO,SAACC,EAAKC,GAIrB,OAHI5E,EAAI6E,eAAeD,KACnBD,EAAIC,GAAK5E,EAAI4E,IAEVD,CAJJ,GAKJ,CALI,EAMV,CAED,IAAMG,EAAqBC,EAAWC,WAChCC,EAAuBF,EAAWG,aACjC,SAASC,EAAsBnF,EAAKoF,GACnCA,EAAKC,iBACLrF,EAAIsF,aAAeR,EAAmBS,KAAKR,GAC3C/E,EAAIwF,eAAiBP,EAAqBM,KAAKR,KAG/C/E,EAAIsF,aAAeP,EAAWC,WAAWO,KAAKR,GAC9C/E,EAAIwF,eAAiBT,EAAWG,aAAaK,KAAKR,GAEzD,KClBoBU,ECAfC,gCACF,SAAAA,EAAYC,EAAQC,EAAaC,GAAS,IAAAC,EAAA,OAAAC,EAAAlD,KAAA6C,IACtCI,EAAAE,EAAAtG,KAAAmD,KAAM8C,IACDC,YAAcA,EACnBE,EAAKD,QAAUA,EACfC,EAAK1G,KAAO,iBAJ0B0G,CAKzC,gBANwBG,QAQhBC,EAAb,SAAAC,GAAAC,EAAAF,EAAAC,GAAA,IAAAE,EAAAC,EAAAJ,GAOI,SAAAA,EAAYd,GAAM,IAAAmB,EAAA,OAAAR,EAAAlD,KAAAqD,IACdK,EAAAF,EAAA3G,KAAAmD,OACK2D,UAAW,EAChBrB,EAAqBsB,EAAAF,GAAOnB,GAC5BmB,EAAKnB,KAAOA,EACZmB,EAAKG,MAAQtB,EAAKsB,MAClBH,EAAKI,OAASvB,EAAKuB,OANLJ,CAOjB,CAdL,OAAAK,EAAAV,EAAA,CAAA,CAAAhH,IAAA,UAAA2H,MAwBI,SAAQlB,EAAQC,EAAaC,GAEzB,OADAiB,EAAmBC,EAAAb,EAAA1G,WAAA,eAAAqD,MAAAnD,KAAAmD,KAAA,QAAS,IAAI6C,EAAeC,EAAQC,EAAaC,IAC7DhD,IACV,GA3BL,CAAA3D,IAAA,OAAA2H,MA+BI,WAGI,OAFAhE,KAAKmE,WAAa,UAClBnE,KAAKoE,SACEpE,IACV,GAnCL,CAAA3D,IAAA,QAAA2H,MAuCI,WAKI,MAJwB,YAApBhE,KAAKmE,YAAgD,SAApBnE,KAAKmE,aACtCnE,KAAKqE,UACLrE,KAAKsE,WAEFtE,IACV,GA7CL,CAAA3D,IAAA,OAAA2H,MAmDI,SAAKO,GACuB,SAApBvE,KAAKmE,YACLnE,KAAKwE,MAAMD,EAKlB,GA1DL,CAAAlI,IAAA,SAAA2H,MAgEI,WACIhE,KAAKmE,WAAa,OAClBnE,KAAK2D,UAAW,EAChBM,EAAAC,EAAAb,EAAA1G,WAAA,eAAAqD,MAAAnD,KAAAmD,KAAmB,OACtB,GApEL,CAAA3D,IAAA,SAAA2H,MA2EI,SAAOxH,GACH,IAAMiI,EAASrG,EAAa5B,EAAMwD,KAAK8D,OAAOxF,YAC9C0B,KAAK0E,SAASD,EACjB,GA9EL,CAAApI,IAAA,WAAA2H,MAoFI,SAASS,GACLR,EAAmBC,EAAAb,EAAA1G,WAAA,eAAAqD,MAAAnD,KAAAmD,KAAA,SAAUyE,EAChC,GAtFL,CAAApI,IAAA,UAAA2H,MA4FI,SAAQW,GACJ3E,KAAKmE,WAAa,SAClBF,EAAmBC,EAAAb,EAAA1G,WAAA,eAAAqD,MAAAnD,KAAAmD,KAAA,QAAS2E,EAC/B,GA/FL,CAAAtI,IAAA,QAAA2H,MAqGI,SAAMY,GAAY,KArGtBvB,CAAA,CAAA,CAA+B3D,GDTzBmF,EAAW,mEAAmEjH,MAAM,IAAkBkH,EAAM,CAAA,EAC9GC,EAAO,EAAG9G,EAAI,EAQX,SAAS+G,EAAOC,GACnB,IAAIC,EAAU,GACd,GACIA,EAAUL,EAASI,EAZ6E,IAY7DC,EACnCD,EAAME,KAAKC,MAAMH,EAb+E,UAc3FA,EAAM,GACf,OAAOC,CACV,CAqBM,SAASG,IACZ,IAAMC,EAAMN,GAAQ,IAAIO,MACxB,OAAID,IAAQ1C,GACDmC,EAAO,EAAGnC,EAAO0C,GACrBA,EAAM,IAAMN,EAAOD,IAC7B,CAID,KAAO9G,EA9CiG,GA8CrFA,IACf6G,EAAID,EAAS5G,IAAMA,EEzChB,SAAS+G,EAAO7H,GACnB,IAAIqI,EAAM,GACV,IAAK,IAAIvH,KAAKd,EACNA,EAAI6E,eAAe/D,KACfuH,EAAItH,SACJsH,GAAO,KACXA,GAAOC,mBAAmBxH,GAAK,IAAMwH,mBAAmBtI,EAAIc,KAGpE,OAAOuH,CACV,CAOM,SAASlG,EAAOoG,GAGnB,IAFA,IAAIC,EAAM,CAAA,EACNC,EAAQF,EAAG9H,MAAM,KACZK,EAAI,EAAG4H,EAAID,EAAM1H,OAAQD,EAAI4H,EAAG5H,IAAK,CAC1C,IAAI6H,EAAOF,EAAM3H,GAAGL,MAAM,KAC1B+H,EAAII,mBAAmBD,EAAK,KAAOC,mBAAmBD,EAAK,GAC9D,CACD,OAAOH,CACV,CChCD,IAAI3B,GAAQ,EACZ,IACIA,EAAkC,oBAAnBgC,gBACX,oBAAqB,IAAIA,cAKhC,CAHD,MAAOC,GAGN,CACM,IAAMC,EAAUlC,ECPhB,SAASmC,EAAI5D,GAChB,IAAM6D,EAAU7D,EAAK6D,QAErB,IACI,GAAI,oBAAuBJ,kBAAoBI,GAAWF,GACtD,OAAO,IAAIF,cAGN,CAAb,MAAOK,GAAM,CACb,IAAKD,EACD,IACI,OAAO,IAAIlE,EAAW,CAAC,UAAUoE,OAAO,UAAUC,KAAK,OAAM,oBAEpD,CAAb,MAAOF,GAAM,CAEpB,CCVD,SAASG,KAAW,CACpB,IAAMC,GAIK,MAHK,IAAIT,EAAe,CAC3BI,SAAS,IAEMM,aAEVC,GAAb,SAAAC,GAAArD,EAAAoD,EAAAC,GAAA,IAAAzD,EAAAM,EAAAkD,GAOI,SAAAA,EAAYpE,GAAM,IAAAU,EAGd,GAHcC,EAAAlD,KAAA2G,IACd1D,EAAAE,EAAAtG,KAAAmD,KAAMuC,IACDsE,SAAU,EACS,oBAAbC,SAA0B,CACjC,IAAMC,EAAQ,WAAaD,SAASE,SAChCC,EAAOH,SAASG,KAEfA,IACDA,EAAOF,EAAQ,MAAQ,MAE3B9D,EAAKiE,GACoB,oBAAbJ,UACJvE,EAAK4E,WAAaL,SAASK,UAC3BF,IAAS1E,EAAK0E,KACtBhE,EAAKmE,GAAK7E,EAAK8E,SAAWN,CAC7B,CAID,IAAMO,EAAc/E,GAAQA,EAAK+E,YAnBnB,OAoBdrE,EAAKhG,eAAiBwJ,KAAYa,EApBpBrE,CAqBjB,CA5BL,OAAAc,EAAA4C,EAAA,CAAA,CAAAtK,IAAA,OAAAkL,IA6BI,WACI,MAAO,SACV,GA/BL,CAAAlL,IAAA,SAAA2H,MAsCI,WACIhE,KAAKwH,MACR,GAxCL,CAAAnL,IAAA,QAAA2H,MA+CI,SAAMY,GAAS,IAAAlB,EAAA1D,KACXA,KAAKmE,WAAa,UAClB,IAAMsD,EAAQ,WACV/D,EAAKS,WAAa,SAClBS,KAEJ,GAAI5E,KAAK6G,UAAY7G,KAAK2D,SAAU,CAChC,IAAI+D,EAAQ,EACR1H,KAAK6G,UACLa,IACA1H,KAAKI,KAAK,gBAAgB,aACpBsH,GAASD,QAGdzH,KAAK2D,WACN+D,IACA1H,KAAKI,KAAK,SAAS,aACbsH,GAASD,OAGtB,MAEGA,GAEP,GAvEL,CAAApL,IAAA,OAAA2H,MA6EI,WACIhE,KAAK6G,SAAU,EACf7G,KAAK2H,SACL3H,KAAKkB,aAAa,OACrB,GAjFL,CAAA7E,IAAA,SAAA2H,MAuFI,SAAOxH,GAAM,IAAAoL,EAAA5H,MTpFK,SAAC6H,EAAgBvJ,GAGnC,IAFA,IAAMwJ,EAAiBD,EAAejK,MAAM2B,GACtCgF,EAAU,GACPtG,EAAI,EAAGA,EAAI6J,EAAe5J,OAAQD,IAAK,CAC5C,IAAM8J,EAAgB3J,EAAa0J,EAAe7J,GAAIK,GAEtD,GADAiG,EAAQrE,KAAK6H,GACc,UAAvBA,EAAcxL,KACd,KAEP,CACD,OAAOgI,CACV,ESwFOyD,CAAcxL,EAAMwD,KAAK8D,OAAOxF,YAAYlC,SAd3B,SAACqI,GAMd,GAJI,YAAcmD,EAAKzD,YAA8B,SAAhBM,EAAOlI,MACxCqL,EAAKK,SAGL,UAAYxD,EAAOlI,KAEnB,OADAqL,EAAKtD,QAAQ,CAAEvB,YAAa,oCACrB,EAGX6E,EAAKlD,SAASD,EACjB,IAIG,WAAazE,KAAKmE,aAElBnE,KAAK6G,SAAU,EACf7G,KAAKkB,aAAa,gBACd,SAAWlB,KAAKmE,YAChBnE,KAAKwH,OAKhB,GAlHL,CAAAnL,IAAA,UAAA2H,MAwHI,WAAU,IAAAkE,EAAAlI,KACAmI,EAAQ,WACVD,EAAK1D,MAAM,CAAC,CAAEjI,KAAM,YAEpB,SAAWyD,KAAKmE,WAChBgE,IAKAnI,KAAKI,KAAK,OAAQ+H,EAEzB,GApIL,CAAA9L,IAAA,QAAA2H,MA2II,SAAMO,GAAS,IAAA6D,EAAApI,KACXA,KAAK2D,UAAW,ETxJF,SAACY,EAASrH,GAE5B,IAAMgB,EAASqG,EAAQrG,OACjB4J,EAAiB,IAAI9G,MAAM9C,GAC7BmK,EAAQ,EACZ9D,EAAQnI,SAAQ,SAACqI,EAAQxG,GAErBjB,EAAayH,GAAQ,GAAO,SAAApG,GACxByJ,EAAe7J,GAAKI,IACdgK,IAAUnK,GACZhB,EAAS4K,EAAevB,KAAKhH,GAEpC,MAER,CS2IO+I,CAAc/D,GAAS,SAAC/H,GACpB4L,EAAKG,QAAQ/L,GAAM,WACf4L,EAAKzE,UAAW,EAChByE,EAAKlH,aAAa,WAEzB,GACJ,GAnJL,CAAA7E,IAAA,MAAA2H,MAyJI,WACI,IAAIH,EAAQ7D,KAAK6D,OAAS,GACpB2E,EAASxI,KAAKuC,KAAK8E,OAAS,QAAU,OACxCJ,EAAO,IAEP,IAAUjH,KAAKuC,KAAKkG,oBACpB5E,EAAM7D,KAAKuC,KAAKmG,gBAAkBrD,KAEjCrF,KAAK/C,gBAAmB4G,EAAM8E,MAC/B9E,EAAM+E,IAAM,GAGZ5I,KAAKuC,KAAK0E,OACR,UAAYuB,GAAqC,MAA3BK,OAAO7I,KAAKuC,KAAK0E,OACpC,SAAWuB,GAAqC,KAA3BK,OAAO7I,KAAKuC,KAAK0E,SAC3CA,EAAO,IAAMjH,KAAKuC,KAAK0E,MAE3B,IAAM6B,EAAe9D,EAAOnB,GAE5B,OAAQ2E,EACJ,QAF8C,IAArCxI,KAAKuC,KAAK4E,SAAS4B,QAAQ,KAG5B,IAAM/I,KAAKuC,KAAK4E,SAAW,IAAMnH,KAAKuC,KAAK4E,UACnDF,EACAjH,KAAKuC,KAAKyG,MACTF,EAAa5K,OAAS,IAAM4K,EAAe,GACnD,GAlLL,CAAAzM,IAAA,UAAA2H,MAyLI,WAAmB,IAAXzB,yDAAO,CAAA,EAEX,OADA0G,EAAc1G,EAAM,CAAE2E,GAAIlH,KAAKkH,GAAIE,GAAIpH,KAAKoH,IAAMpH,KAAKuC,MAChD,IAAI2G,GAAQlJ,KAAKmJ,MAAO5G,EAClC,GA5LL,CAAAlG,IAAA,UAAA2H,MAoMI,SAAQxH,EAAMuD,GAAI,IAAAqJ,EAAApJ,KACRqJ,EAAMrJ,KAAKsJ,QAAQ,CACrBC,OAAQ,OACR/M,KAAMA,IAEV6M,EAAIzJ,GAAG,UAAWG,GAClBsJ,EAAIzJ,GAAG,SAAS,SAAC4J,EAAWxG,GACxBoG,EAAKK,QAAQ,iBAAkBD,EAAWxG,KAEjD,GA7ML,CAAA3G,IAAA,SAAA2H,MAmNI,WAAS,IAAA0F,EAAA1J,KACCqJ,EAAMrJ,KAAKsJ,UACjBD,EAAIzJ,GAAG,OAAQI,KAAK2J,OAAOjH,KAAK1C,OAChCqJ,EAAIzJ,GAAG,SAAS,SAAC4J,EAAWxG,GACxB0G,EAAKD,QAAQ,iBAAkBD,EAAWxG,MAE9ChD,KAAK4J,QAAUP,CAClB,KA1NL1C,CAAA,CAAA,CAA6BtD,GA4NhB6F,GAAb,SAAA5F,GAAAC,EAAA2F,EAAA5F,GAAA,IAAAE,EAAAC,EAAAyF,GAOI,SAAYC,EAAAA,EAAK5G,GAAM,IAAAsH,EAAA,OAAA3G,EAAAlD,KAAAkJ,GAEnB5G,EAAqBsB,EADrBiG,EAAArG,EAAA3G,KAAAmD,OAC4BuC,GAC5BsH,EAAKtH,KAAOA,EACZsH,EAAKN,OAAShH,EAAKgH,QAAU,MAC7BM,EAAKV,IAAMA,EACXU,EAAKC,OAAQ,IAAUvH,EAAKuH,MAC5BD,EAAKrN,UAAOuN,IAAcxH,EAAK/F,KAAO+F,EAAK/F,KAAO,KAClDqN,EAAK5N,SARc4N,CAStB,CAhBL,OAAA9F,EAAAmF,EAAA,CAAA,CAAA7M,IAAA,SAAA2H,MAsBI,WAAS,IAAAgG,EAAAhK,KACCuC,EAAOd,EAAKzB,KAAKuC,KAAM,QAAS,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,aACjHA,EAAK6D,UAAYpG,KAAKuC,KAAK2E,GAC3B3E,EAAK0H,UAAYjK,KAAKuC,KAAK6E,GAC3B,IAAM8C,EAAOlK,KAAKkK,IAAM,IAAIlE,EAAezD,GAC3C,IACI2H,EAAIC,KAAKnK,KAAKuJ,OAAQvJ,KAAKmJ,IAAKnJ,KAAK8J,OACrC,IACI,GAAI9J,KAAKuC,KAAK6H,aAEV,IAAK,IAAInM,KADTiM,EAAIG,uBAAyBH,EAAIG,uBAAsB,GACzCrK,KAAKuC,KAAK6H,aAChBpK,KAAKuC,KAAK6H,aAAapI,eAAe/D,IACtCiM,EAAII,iBAAiBrM,EAAG+B,KAAKuC,KAAK6H,aAAanM,GAKlD,CAAb,MAAOoI,GAAM,CACb,GAAI,SAAWrG,KAAKuJ,OAChB,IACIW,EAAII,iBAAiB,eAAgB,2BAE5B,CAAb,MAAOjE,GAAM,CAEjB,IACI6D,EAAII,iBAAiB,SAAU,MApBnC,CAsBA,MAAOjE,GAtBP,CAwBI,oBAAqB6D,IACrBA,EAAIK,gBAAkBvK,KAAKuC,KAAKgI,iBAEhCvK,KAAKuC,KAAKiI,iBACVN,EAAIO,QAAUzK,KAAKuC,KAAKiI,gBAE5BN,EAAIQ,mBAAqB,WACjB,IAAMR,EAAI/F,aAEV,MAAQ+F,EAAIS,QAAU,OAAST,EAAIS,OACnCX,EAAKY,SAKLZ,EAAKvH,cAAa,WACduH,EAAKP,QAA8B,iBAAfS,EAAIS,OAAsBT,EAAIS,OAAS,EAD/D,GAEG,KAGXT,EAAIW,KAAK7K,KAAKxD,KAUjB,CARD,MAAO6J,GAOH,YAHArG,KAAKyC,cAAa,WACduH,EAAKP,QAAQpD,EADjB,GAEG,EAEN,CACuB,oBAAbyE,WACP9K,KAAK+K,MAAQ7B,EAAQ8B,gBACrB9B,EAAQ+B,SAASjL,KAAK+K,OAAS/K,KAEtC,GAtFL,CAAA3D,IAAA,UAAA2H,MA4FI,SAAQiC,GACJjG,KAAKkB,aAAa,QAAS+E,EAAKjG,KAAKkK,KACrClK,KAAKkL,SAAQ,EAChB,GA/FL,CAAA7O,IAAA,UAAA2H,MAqGI,SAAQmH,GACJ,QAAI,IAAuBnL,KAAKkK,KAAO,OAASlK,KAAKkK,IAArD,CAIA,GADAlK,KAAKkK,IAAIQ,mBAAqBlE,GAC1B2E,EACA,IACInL,KAAKkK,IAAIkB,OAEA,CAAb,MAAO/E,GAAM,CAEO,oBAAbyE,iBACA5B,EAAQ+B,SAASjL,KAAK+K,OAEjC/K,KAAKkK,IAAM,IAXV,CAYJ,GApHL,CAAA7N,IAAA,SAAA2H,MA0HI,WACI,IAAMxH,EAAOwD,KAAKkK,IAAImB,aACT,OAAT7O,IACAwD,KAAKkB,aAAa,OAAQ1E,GAC1BwD,KAAKkB,aAAa,WAClBlB,KAAKkL,UAEZ,GAjIL,CAAA7O,IAAA,QAAA2H,MAuII,WACIhE,KAAKkL,SACR,KAzILhC,CAAA,CAAA,CAA6BxJ,GAkJ7B,GAPAwJ,GAAQ8B,cAAgB,EACxB9B,GAAQ+B,SAAW,CAAA,EAMK,oBAAbH,SAEP,GAA2B,mBAAhBQ,YAEPA,YAAY,WAAYC,SAEvB,GAAgC,mBAArB1L,iBAAiC,CAE7CA,iBADyB,eAAgBqC,EAAa,WAAa,SAChCqJ,IAAe,EACrD,CAEL,SAASA,KACL,IAAK,IAAItN,KAAKiL,GAAQ+B,SACd/B,GAAQ+B,SAASjJ,eAAe/D,IAChCiL,GAAQ+B,SAAShN,GAAGmN,OAG/B,CC7YM,IAAMI,GACqC,mBAAZC,SAAqD,mBAApBA,QAAQC,QAEhE,SAAC/K,GAAD,OAAQ8K,QAAQC,UAAUC,KAAKhL,IAG/B,SAACA,EAAI8B,GAAL,OAAsBA,EAAa9B,EAAI,IAGzCiL,GAAY1J,EAAW0J,WAAa1J,EAAW2J,aCHtDC,GAAqC,oBAAdC,WACI,iBAAtBA,UAAUC,SACmB,gBAApCD,UAAUC,QAAQC,cACTC,GAAb,SAAAtF,GAAArD,EAAA2I,EAAAtF,GAAA,IAAAzD,EAAAM,EAAAyI,GAOI,SAAAA,EAAY3J,GAAM,IAAAU,EAAA,OAAAC,EAAAlD,KAAAkM,IACdjJ,EAAAE,EAAAtG,KAAAmD,KAAMuC,IACDtF,gBAAkBsF,EAAK+E,YAFdrE,CAGjB,CAVL,OAAAc,EAAAmI,EAAA,CAAA,CAAA7P,IAAA,OAAAkL,IAWI,WACI,MAAO,WACV,GAbL,CAAAlL,IAAA,SAAA2H,MAcI,WACI,GAAKhE,KAAKmM,QAAV,CAIA,IAAMhD,EAAMnJ,KAAKmJ,MACXiD,EAAYpM,KAAKuC,KAAK6J,UAEtB7J,EAAOuJ,GACP,CAAA,EACArK,EAAKzB,KAAKuC,KAAM,QAAS,oBAAqB,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,eAAgB,kBAAmB,SAAU,aAAc,SAAU,uBAChMvC,KAAKuC,KAAK6H,eACV7H,EAAK8J,QAAUrM,KAAKuC,KAAK6H,cAE7B,IACIpK,KAAKsM,GACyBR,GAIpB,IAAIF,GAAUzC,EAAKiD,EAAW7J,GAH9B6J,EACI,IAAIR,GAAUzC,EAAKiD,GACnB,IAAIR,GAAUzC,EAK/B,CAFD,MAAOlD,GACH,OAAOjG,KAAKkB,aAAa,QAAS+E,EACrC,CACDjG,KAAKsM,GAAGhO,WAAa0B,KAAK8D,OAAOxF,YDrCR,cCsCzB0B,KAAKuM,mBAtBJ,CAuBJ,GAzCL,CAAAlQ,IAAA,oBAAA2H,MA+CI,WAAoB,IAAAN,EAAA1D,KAChBA,KAAKsM,GAAGE,OAAS,WACT9I,EAAKnB,KAAKkK,WACV/I,EAAK4I,GAAGI,QAAQC,QAEpBjJ,EAAKuE,UAETjI,KAAKsM,GAAGM,QAAU,SAACC,GAAD,OAAgBnJ,EAAKY,QAAQ,CAC3CvB,YAAa,8BACbC,QAAS6J,KAEb7M,KAAKsM,GAAGQ,UAAY,SAACC,GAAD,OAAQrJ,EAAKiG,OAAOoD,EAAGvQ,OAC3CwD,KAAKsM,GAAGU,QAAU,SAAC3G,GAAD,OAAO3C,EAAK+F,QAAQ,kBAAmBpD,GAC5D,GA5DL,CAAAhK,IAAA,QAAA2H,MA6DI,SAAMO,GAAS,IAAAqD,EAAA5H,KACXA,KAAK2D,UAAW,EAGhB,IAJW,IAAAsJ,EAAA,SAIFhP,GACL,IAAMwG,EAASF,EAAQtG,GACjBiP,EAAajP,IAAMsG,EAAQrG,OAAS,EAC1ClB,EAAayH,EAAQmD,EAAK3K,gBAAgB,SAACT,GAmBvC,IAGQoL,EAAK0E,GAAGzB,KAAKrO,EAOpB,CADD,MAAO6J,GACN,CACG6G,GAGA1B,IAAS,WACL5D,EAAKjE,UAAW,EAChBiE,EAAK1G,aAAa,QACrB,GAAE0G,EAAKnF,aAEf,GA7CM,EAIFxE,EAAI,EAAGA,EAAIsG,EAAQrG,OAAQD,IAAKgP,EAAhChP,EA2CZ,GA5GL,CAAA5B,IAAA,UAAA2H,MA6GI,gBAC2B,IAAZhE,KAAKsM,KACZtM,KAAKsM,GAAGnE,QACRnI,KAAKsM,GAAK,KAEjB,GAlHL,CAAAjQ,IAAA,MAAA2H,MAwHI,WACI,IAAIH,EAAQ7D,KAAK6D,OAAS,GACpB2E,EAASxI,KAAKuC,KAAK8E,OAAS,MAAQ,KACtCJ,EAAO,GAEPjH,KAAKuC,KAAK0E,OACR,QAAUuB,GAAqC,MAA3BK,OAAO7I,KAAKuC,KAAK0E,OAClC,OAASuB,GAAqC,KAA3BK,OAAO7I,KAAKuC,KAAK0E,SACzCA,EAAO,IAAMjH,KAAKuC,KAAK0E,MAGvBjH,KAAKuC,KAAKkG,oBACV5E,EAAM7D,KAAKuC,KAAKmG,gBAAkBrD,KAGjCrF,KAAK/C,iBACN4G,EAAM+E,IAAM,GAEhB,IAAME,EAAe9D,EAAOnB,GAE5B,OAAQ2E,EACJ,QAF8C,IAArCxI,KAAKuC,KAAK4E,SAAS4B,QAAQ,KAG5B,IAAM/I,KAAKuC,KAAK4E,SAAW,IAAMnH,KAAKuC,KAAK4E,UACnDF,EACAjH,KAAKuC,KAAKyG,MACTF,EAAa5K,OAAS,IAAM4K,EAAe,GACnD,GAlJL,CAAAzM,IAAA,QAAA2H,MAyJI,WACI,QAAS4H,EACZ,KA3JLM,CAAA,CAAA,CAAwB7I,GCRX8J,GAAa,CACtBC,UAAWlB,GACXrF,QAASF,ICeP0G,GAAK,sPACLC,GAAQ,CACV,SAAU,WAAY,YAAa,WAAY,OAAQ,WAAY,OAAQ,OAAQ,WAAY,OAAQ,YAAa,OAAQ,QAAS,UAElI,SAASC,GAAM/H,GAClB,IAAMgI,EAAMhI,EAAKiI,EAAIjI,EAAIuD,QAAQ,KAAM1C,EAAIb,EAAIuD,QAAQ,MAC7C,GAAN0E,IAAiB,GAANpH,IACXb,EAAMA,EAAI9G,UAAU,EAAG+O,GAAKjI,EAAI9G,UAAU+O,EAAGpH,GAAGqH,QAAQ,KAAM,KAAOlI,EAAI9G,UAAU2H,EAAGb,EAAItH,SAG9F,IADA,IAwBmB2F,EACbrH,EAzBFmR,EAAIN,GAAGO,KAAKpI,GAAO,IAAK2D,EAAM,CAAlC,EAAsClL,EAAI,GACnCA,KACHkL,EAAImE,GAAMrP,IAAM0P,EAAE1P,IAAM,GAU5B,OARU,GAANwP,IAAiB,GAANpH,IACX8C,EAAI0E,OAASL,EACbrE,EAAI2E,KAAO3E,EAAI2E,KAAKpP,UAAU,EAAGyK,EAAI2E,KAAK5P,OAAS,GAAGwP,QAAQ,KAAM,KACpEvE,EAAI4E,UAAY5E,EAAI4E,UAAUL,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAAIA,QAAQ,KAAM,KAC9EvE,EAAI6E,SAAU,GAElB7E,EAAI8E,UAIR,SAAmB9Q,EAAK6L,GACpB,IAAMkF,EAAO,WAAYC,EAAQnF,EAAK0E,QAAQQ,EAAM,KAAKtQ,MAAM,KACvC,KAApBoL,EAAK/H,MAAM,EAAG,IAA6B,IAAhB+H,EAAK9K,QAChCiQ,EAAMtN,OAAO,EAAG,GAEE,KAAlBmI,EAAK/H,OAAO,IACZkN,EAAMtN,OAAOsN,EAAMjQ,OAAS,EAAG,GAEnC,OAAOiQ,CACV,CAbmBF,CAAU9E,EAAKA,EAAG,MAClCA,EAAIiF,UAaevK,EAbUsF,EAAG,MAc1B3M,EAAO,CAAA,EACbqH,EAAM6J,QAAQ,6BAA6B,SAAUW,EAAIC,EAAIC,GACrDD,IACA9R,EAAK8R,GAAMC,MAGZ/R,GAnBA2M,CACV,CCnCD,IAAaqF,GAAb,SAAAlL,GAAAC,EAAAiL,EAAAlL,GAAA,IAAAH,EAAAM,EAAA+K,GAOI,SAAAA,EAAYrF,GAAgB,IAAAlG,EAAXV,yDAAO,CAAA,EAAI,OAAAW,EAAAlD,KAAAwO,IACxBvL,EAAAE,EAAAtG,KAAAmD,OACKyO,YAAc,GACftF,GAAO,WAAoBA,EAAAA,KAC3B5G,EAAO4G,EACPA,EAAM,MAENA,GACAA,EAAMoE,GAAMpE,GACZ5G,EAAK4E,SAAWgC,EAAI2E,KACpBvL,EAAK8E,OAA0B,UAAjB8B,EAAInC,UAAyC,QAAjBmC,EAAInC,SAC9CzE,EAAK0E,KAAOkC,EAAIlC,KACZkC,EAAItF,QACJtB,EAAKsB,MAAQsF,EAAItF,QAEhBtB,EAAKuL,OACVvL,EAAK4E,SAAWoG,GAAMhL,EAAKuL,MAAMA,MAErCxL,EAAqBsB,EAAAX,GAAOV,GAC5BU,EAAKoE,OACD,MAAQ9E,EAAK8E,OACP9E,EAAK8E,OACe,oBAAbP,UAA4B,WAAaA,SAASE,SAC/DzE,EAAK4E,WAAa5E,EAAK0E,OAEvB1E,EAAK0E,KAAOhE,EAAKoE,OAAS,MAAQ,MAEtCpE,EAAKkE,SACD5E,EAAK4E,WACoB,oBAAbL,SAA2BA,SAASK,SAAW,aAC/DlE,EAAKgE,KACD1E,EAAK0E,OACoB,oBAAbH,UAA4BA,SAASG,KACvCH,SAASG,KACThE,EAAKoE,OACD,MACA,MAClBpE,EAAKkK,WAAa5K,EAAK4K,YAAc,CAAC,UAAW,aACjDlK,EAAKwL,YAAc,GACnBxL,EAAKyL,cAAgB,EACrBzL,EAAKV,KAAO0G,EAAc,CACtBD,KAAM,aACN2F,OAAO,EACPpE,iBAAiB,EACjBqE,SAAS,EACTlG,eAAgB,IAChBmG,iBAAiB,EACjBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAmB,CACfC,UAAW,MAEfC,iBAAkB,CAZI,EAatBC,qBAAqB,GACtB5M,GACHU,EAAKV,KAAKyG,KACN/F,EAAKV,KAAKyG,KAAK0E,QAAQ,MAAO,KACzBzK,EAAKV,KAAKuM,iBAAmB,IAAM,IACb,iBAApB7L,EAAKV,KAAKsB,QACjBZ,EAAKV,KAAKsB,MAAQvE,EAAO2D,EAAKV,KAAKsB,QAGvCZ,EAAKmM,GAAK,KACVnM,EAAKoM,SAAW,KAChBpM,EAAKqM,aAAe,KACpBrM,EAAKsM,YAAc,KAEnBtM,EAAKuM,iBAAmB,KACQ,mBAArB3P,mBACHoD,EAAKV,KAAK4M,sBAIVlM,EAAKwM,0BAA4B,WACzBxM,EAAKyM,YAELzM,EAAKyM,UAAUjP,qBACfwC,EAAKyM,UAAUvH,UAGvBtI,iBAAiB,eAAgBoD,EAAKwM,2BAA2B,IAE/C,cAAlBxM,EAAKkE,WACLlE,EAAK0M,qBAAuB,WACxB1M,EAAKqB,QAAQ,kBAAmB,CAC5BvB,YAAa,6BAGrBlD,iBAAiB,UAAWoD,EAAK0M,sBAAsB,KAG/D1M,EAAKkH,OA3FmBlH,CA4F3B,CAnGL,OAAAc,EAAAyK,EAAA,CAAA,CAAAnS,IAAA,kBAAA2H,MA2GI,SAAgB4L,GACZ,IAAM/L,EAAQoF,EAAc,CAAA,EAAIjJ,KAAKuC,KAAKsB,OAE1CA,EAAMgM,IdtFU,EcwFhBhM,EAAM6L,UAAYE,EAEd5P,KAAKoP,KACLvL,EAAM8E,IAAM3I,KAAKoP,IACrB,IAAM7M,EAAO0G,EAAc,CAAA,EAAIjJ,KAAKuC,KAAK2M,iBAAiBU,GAAO5P,KAAKuC,KAAM,CACxEsB,MAAAA,EACAC,OAAQ9D,KACRmH,SAAUnH,KAAKmH,SACfE,OAAQrH,KAAKqH,OACbJ,KAAMjH,KAAKiH,OAEf,OAAO,IAAIkG,GAAWyC,GAAMrN,EAC/B,GA5HL,CAAAlG,IAAA,OAAA2H,MAkII,WAAO,IACC0L,EADDhM,EAAA1D,KAEH,GAAIA,KAAKuC,KAAKsM,iBACVL,EAAOsB,wBACmC,IAA1C9P,KAAKmN,WAAWpE,QAAQ,aACxB2G,EAAY,gBAEX,IAAI,IAAM1P,KAAKmN,WAAWjP,OAK3B,YAHA8B,KAAKyC,cAAa,WACdiB,EAAKxC,aAAa,QAAS,0BAD/B,GAEG,GAIHwO,EAAY1P,KAAKmN,WAAW,EAC/B,CACDnN,KAAKmE,WAAa,UAElB,IACIuL,EAAY1P,KAAK+P,gBAAgBL,EAMpC,CAJD,MAAOrJ,GAGH,OAFArG,KAAKmN,WAAW6C,aAChBhQ,KAAKmK,MAER,CACDuF,EAAUvF,OACVnK,KAAKiQ,aAAaP,EACrB,GA/JL,CAAArT,IAAA,eAAA2H,MAqKI,SAAa0L,GAAW,IAAA9H,EAAA5H,KAChBA,KAAK0P,WACL1P,KAAK0P,UAAUjP,qBAGnBT,KAAK0P,UAAYA,EAEjBA,EACK9P,GAAG,QAASI,KAAKkQ,QAAQxN,KAAK1C,OAC9BJ,GAAG,SAAUI,KAAK0E,SAAShC,KAAK1C,OAChCJ,GAAG,QAASI,KAAKyJ,QAAQ/G,KAAK1C,OAC9BJ,GAAG,SAAS,SAACkD,GAAD,OAAY8E,EAAKtD,QAAQ,kBAAmBxB,KAChE,GAjLL,CAAAzG,IAAA,QAAA2H,MAwLI,SAAM4L,GAAM,IAAA1H,EAAAlI,KACJ0P,EAAY1P,KAAK+P,gBAAgBH,GACjCO,GAAS,EACb3B,EAAOsB,uBAAwB,EAC/B,IAAMM,EAAkB,WAChBD,IAEJT,EAAU7E,KAAK,CAAC,CAAEtO,KAAM,OAAQC,KAAM,WACtCkT,EAAUtP,KAAK,UAAU,SAACiQ,GACtB,IAAIF,EAEJ,GAAI,SAAWE,EAAI9T,MAAQ,UAAY8T,EAAI7T,KAAM,CAG7C,GAFA0L,EAAKoI,WAAY,EACjBpI,EAAKhH,aAAa,YAAawO,IAC1BA,EACD,OACJlB,EAAOsB,sBAAwB,cAAgBJ,EAAUE,KACzD1H,EAAKwH,UAAUjI,OAAM,WACb0I,GAEA,WAAajI,EAAK/D,aAEtB+G,IACAhD,EAAK+H,aAAaP,GAClBA,EAAU7E,KAAK,CAAC,CAAEtO,KAAM,aACxB2L,EAAKhH,aAAa,UAAWwO,GAC7BA,EAAY,KACZxH,EAAKoI,WAAY,EACjBpI,EAAKqI,WAEZ,KACI,CACD,IAAMtK,EAAM,IAAI7C,MAAM,eAEtB6C,EAAIyJ,UAAYA,EAAUE,KAC1B1H,EAAKhH,aAAa,eAAgB+E,EACrC,OAGT,SAASuK,IACDL,IAGJA,GAAS,EACTjF,IACAwE,EAAUvH,QACVuH,EAAY,KA9CR,CAiDR,IAAM1C,EAAU,SAAC/G,GACb,IAAMwK,EAAQ,IAAIrN,MAAM,gBAAkB6C,GAE1CwK,EAAMf,UAAYA,EAAUE,KAC5BY,IACAtI,EAAKhH,aAAa,eAAgBuP,IAEtC,SAASC,IACL1D,EAAQ,mBAzDJ,CA4DR,SAASJ,IACLI,EAAQ,gBA7DJ,CAgER,SAAS2D,EAAUC,GACXlB,GAAakB,EAAGhB,OAASF,EAAUE,MACnCY,GAlEA,CAsER,IAAMtF,EAAU,WACZwE,EAAUlP,eAAe,OAAQ4P,GACjCV,EAAUlP,eAAe,QAASwM,GAClC0C,EAAUlP,eAAe,QAASkQ,GAClCxI,EAAK7H,IAAI,QAASuM,GAClB1E,EAAK7H,IAAI,YAAasQ,IAE1BjB,EAAUtP,KAAK,OAAQgQ,GACvBV,EAAUtP,KAAK,QAAS4M,GACxB0C,EAAUtP,KAAK,QAASsQ,GACxB1Q,KAAKI,KAAK,QAASwM,GACnB5M,KAAKI,KAAK,YAAauQ,GACvBjB,EAAUvF,MACb,GA3QL,CAAA9N,IAAA,SAAA2H,MAiRI,WAOI,GANAhE,KAAKmE,WAAa,OAClBqK,EAAOsB,sBAAwB,cAAgB9P,KAAK0P,UAAUE,KAC9D5P,KAAKkB,aAAa,QAClBlB,KAAKuQ,QAGD,SAAWvQ,KAAKmE,YAAcnE,KAAKuC,KAAKqM,QAGxC,IAFA,IAAI3Q,EAAI,EACF4H,EAAI7F,KAAKqP,SAASnR,OACjBD,EAAI4H,EAAG5H,IACV+B,KAAK6Q,MAAM7Q,KAAKqP,SAASpR,GAGpC,GA/RL,CAAA5B,IAAA,WAAA2H,MAqSI,SAASS,GACL,GAAI,YAAczE,KAAKmE,YACnB,SAAWnE,KAAKmE,YAChB,YAAcnE,KAAKmE,WAInB,OAHAnE,KAAKkB,aAAa,SAAUuD,GAE5BzE,KAAKkB,aAAa,aACVuD,EAAOlI,MACX,IAAK,OACDyD,KAAK8Q,YAAYC,KAAKxD,MAAM9I,EAAOjI,OACnC,MACJ,IAAK,OACDwD,KAAKgR,mBACLhR,KAAKiR,WAAW,QAChBjR,KAAKkB,aAAa,QAClBlB,KAAKkB,aAAa,QAClB,MACJ,IAAK,QACD,IAAM+E,EAAM,IAAI7C,MAAM,gBAEtB6C,EAAIiL,KAAOzM,EAAOjI,KAClBwD,KAAKyJ,QAAQxD,GACb,MACJ,IAAK,UACDjG,KAAKkB,aAAa,OAAQuD,EAAOjI,MACjCwD,KAAKkB,aAAa,UAAWuD,EAAOjI,MAMnD,GApUL,CAAAH,IAAA,cAAA2H,MA2UI,SAAYxH,GACRwD,KAAKkB,aAAa,YAAa1E,GAC/BwD,KAAKoP,GAAK5S,EAAKmM,IACf3I,KAAK0P,UAAU7L,MAAM8E,IAAMnM,EAAKmM,IAChC3I,KAAKqP,SAAWrP,KAAKmR,eAAe3U,EAAK6S,UACzCrP,KAAKsP,aAAe9S,EAAK8S,aACzBtP,KAAKuP,YAAc/S,EAAK+S,YACxBvP,KAAKoR,WAAa5U,EAAK4U,WACvBpR,KAAKiI,SAED,WAAajI,KAAKmE,YAEtBnE,KAAKgR,kBACR,GAxVL,CAAA3U,IAAA,mBAAA2H,MA8VI,WAAmB,IAAAoE,EAAApI,KACfA,KAAK2C,eAAe3C,KAAKwP,kBACzBxP,KAAKwP,iBAAmBxP,KAAKyC,cAAa,WACtC2F,EAAK9D,QAAQ,eADO,GAErBtE,KAAKsP,aAAetP,KAAKuP,aACxBvP,KAAKuC,KAAKkK,WACVzM,KAAKwP,iBAAiB7C,OAE7B,GAtWL,CAAAtQ,IAAA,UAAA2H,MA4WI,WACIhE,KAAKyO,YAAY5N,OAAO,EAAGb,KAAK0O,eAIhC1O,KAAK0O,cAAgB,EACjB,IAAM1O,KAAKyO,YAAYvQ,OACvB8B,KAAKkB,aAAa,SAGlBlB,KAAKuQ,OAEZ,GAxXL,CAAAlU,IAAA,QAAA2H,MA8XI,WACI,GAAI,WAAahE,KAAKmE,YAClBnE,KAAK0P,UAAU/L,WACd3D,KAAKsQ,WACNtQ,KAAKyO,YAAYvQ,OAAQ,CACzB,IAAMqG,EAAUvE,KAAKqR,qBACrBrR,KAAK0P,UAAU7E,KAAKtG,GAGpBvE,KAAK0O,cAAgBnK,EAAQrG,OAC7B8B,KAAKkB,aAAa,QACrB,CACJ,GA1YL,CAAA7E,IAAA,qBAAA2H,MAiZI,WAII,KAH+BhE,KAAKoR,YACR,YAAxBpR,KAAK0P,UAAUE,MACf5P,KAAKyO,YAAYvQ,OAAS,GAE1B,OAAO8B,KAAKyO,YAGhB,IADA,IXrYmBtR,EWqYfmU,EAAc,EACTrT,EAAI,EAAGA,EAAI+B,KAAKyO,YAAYvQ,OAAQD,IAAK,CAC9C,IAAMzB,EAAOwD,KAAKyO,YAAYxQ,GAAGzB,KAIjC,GAHIA,IACA8U,GXxYO,iBADInU,EWyYeX,GXlY1C,SAAoBgJ,GAEhB,IADA,IAAI+L,EAAI,EAAGrT,EAAS,EACXD,EAAI,EAAG4H,EAAIL,EAAItH,OAAQD,EAAI4H,EAAG5H,KACnCsT,EAAI/L,EAAIrH,WAAWF,IACX,IACJC,GAAU,EAELqT,EAAI,KACTrT,GAAU,EAELqT,EAAI,OAAUA,GAAK,MACxBrT,GAAU,GAGVD,IACAC,GAAU,GAGlB,OAAOA,CACV,CAxBcsT,CAAWrU,GAGfgI,KAAKsM,KAPQ,MAOFtU,EAAIuU,YAAcvU,EAAIwU,QWsY5B1T,EAAI,GAAKqT,EAActR,KAAKoR,WAC5B,OAAOpR,KAAKyO,YAAYxN,MAAM,EAAGhD,GAErCqT,GAAe,CAClB,CACD,OAAOtR,KAAKyO,WACf,GApaL,CAAApS,IAAA,QAAA2H,MA6aI,SAAMqM,EAAKuB,EAAS7R,GAEhB,OADAC,KAAKiR,WAAW,UAAWZ,EAAKuB,EAAS7R,GAClCC,IACV,GAhbL,CAAA3D,IAAA,OAAA2H,MAibI,SAAKqM,EAAKuB,EAAS7R,GAEf,OADAC,KAAKiR,WAAW,UAAWZ,EAAKuB,EAAS7R,GAClCC,IACV,GApbL,CAAA3D,IAAA,aAAA2H,MA8bI,SAAWzH,EAAMC,EAAMoV,EAAS7R,GAS5B,GARI,mBAAsBvD,IACtBuD,EAAKvD,EACLA,OAAOuN,GAEP,mBAAsB6H,IACtB7R,EAAK6R,EACLA,EAAU,MAEV,YAAc5R,KAAKmE,YAAc,WAAanE,KAAKmE,WAAvD,EAGAyN,EAAUA,GAAW,IACbC,UAAW,IAAUD,EAAQC,SACrC,IAAMpN,EAAS,CACXlI,KAAMA,EACNC,KAAMA,EACNoV,QAASA,GAEb5R,KAAKkB,aAAa,eAAgBuD,GAClCzE,KAAKyO,YAAYvO,KAAKuE,GAClB1E,GACAC,KAAKI,KAAK,QAASL,GACvBC,KAAKuQ,OAZJ,CAaJ,GAtdL,CAAAlU,IAAA,QAAA2H,MA0dI,WAAQ,IAAAoF,EAAApJ,KACEmI,EAAQ,WACViB,EAAK9E,QAAQ,gBACb8E,EAAKsG,UAAUvH,SAEb2J,EAAkB,SAAlBA,IACF1I,EAAK/I,IAAI,UAAWyR,GACpB1I,EAAK/I,IAAI,eAAgByR,GACzB3J,KAEE4J,EAAiB,WAEnB3I,EAAKhJ,KAAK,UAAW0R,GACrB1I,EAAKhJ,KAAK,eAAgB0R,IAqB9B,MAnBI,YAAc9R,KAAKmE,YAAc,SAAWnE,KAAKmE,aACjDnE,KAAKmE,WAAa,UACdnE,KAAKyO,YAAYvQ,OACjB8B,KAAKI,KAAK,SAAS,WACXgJ,EAAKkH,UACLyB,IAGA5J,OAIHnI,KAAKsQ,UACVyB,IAGA5J,KAGDnI,IACV,GA7fL,CAAA3D,IAAA,UAAA2H,MAmgBI,SAAQiC,GACJuI,EAAOsB,uBAAwB,EAC/B9P,KAAKkB,aAAa,QAAS+E,GAC3BjG,KAAKsE,QAAQ,kBAAmB2B,EACnC,GAvgBL,CAAA5J,IAAA,UAAA2H,MA6gBI,SAAQlB,EAAQC,GACR,YAAc/C,KAAKmE,YACnB,SAAWnE,KAAKmE,YAChB,YAAcnE,KAAKmE,aAEnBnE,KAAK2C,eAAe3C,KAAKwP,kBAEzBxP,KAAK0P,UAAUjP,mBAAmB,SAElCT,KAAK0P,UAAUvH,QAEfnI,KAAK0P,UAAUjP,qBACoB,mBAAxBC,sBACPA,oBAAoB,eAAgBV,KAAKyP,2BAA2B,GACpE/O,oBAAoB,UAAWV,KAAK2P,sBAAsB,IAG9D3P,KAAKmE,WAAa,SAElBnE,KAAKoP,GAAK,KAEVpP,KAAKkB,aAAa,QAAS4B,EAAQC,GAGnC/C,KAAKyO,YAAc,GACnBzO,KAAK0O,cAAgB,EAE5B,GAxiBL,CAAArS,IAAA,iBAAA2H,MA+iBI,SAAeqL,GAIX,IAHA,IAAM2C,EAAmB,GACrB/T,EAAI,EACFgU,EAAI5C,EAASnR,OACZD,EAAIgU,EAAGhU,KACL+B,KAAKmN,WAAWpE,QAAQsG,EAASpR,KAClC+T,EAAiB9R,KAAKmP,EAASpR,IAEvC,OAAO+T,CACV,KAxjBLxD,CAAA,CAAA,CAA4B9O,GA0jBtBwS,GAAClL,SdliBiB,Ee5BAwH,GAAOxH,yBCA/B,SAASmL,GAAUC,EAAMC,EAAQ7M,GAE/B,IADA,IAAI+L,EAAI,EACCtT,EAAI,EAAG4H,EAAIL,EAAItH,OAAQD,EAAI4H,EAAG5H,KACrCsT,EAAI/L,EAAIrH,WAAWF,IACX,IACNmU,EAAKE,SAASD,IAAUd,GAEjBA,EAAI,MACXa,EAAKE,SAASD,IAAU,IAAQd,GAAK,GACrCa,EAAKE,SAASD,IAAU,IAAY,GAAJd,IAEzBA,EAAI,OAAUA,GAAK,OAC1Ba,EAAKE,SAASD,IAAU,IAAQd,GAAK,IACrCa,EAAKE,SAASD,IAAU,IAAQd,GAAK,EAAK,IAC1Ca,EAAKE,SAASD,IAAU,IAAY,GAAJd,KAGhCtT,IACAsT,EAAI,QAAiB,KAAJA,IAAc,GAA2B,KAApB/L,EAAIrH,WAAWF,IACrDmU,EAAKE,SAASD,IAAU,IAAQd,GAAK,IACrCa,EAAKE,SAASD,IAAU,IAAQd,GAAK,GAAM,IAC3Ca,EAAKE,SAASD,IAAU,IAAQd,GAAK,EAAK,IAC1Ca,EAAKE,SAASD,IAAU,IAAY,GAAJd,GAGrC,CAuBD,SAASgB,GAAQlT,EAAOmT,EAAQxO,GAC9B,IAAIzH,EAAcyH,EAAAA,GAAO/F,EAAI,EAAG4H,EAAI,EAAG4M,EAAK,EAAGC,EAAK,EAAGxU,EAAS,EAAGyT,EAAO,EAE1E,GAAa,WAATpV,EAAmB,CAIrB,GAHA2B,EAzBJ,SAAoBsH,GAElB,IADA,IAAI+L,EAAI,EAAGrT,EAAS,EACXD,EAAI,EAAG4H,EAAIL,EAAItH,OAAQD,EAAI4H,EAAG5H,KACrCsT,EAAI/L,EAAIrH,WAAWF,IACX,IACNC,GAAU,EAEHqT,EAAI,KACXrT,GAAU,EAEHqT,EAAI,OAAUA,GAAK,MAC1BrT,GAAU,GAGVD,IACAC,GAAU,GAGd,OAAOA,CACR,CAMYsT,CAAWxN,GAGhB9F,EAAS,GACXmB,EAAMa,KAAc,IAAThC,GACXyT,EAAO,OAGJ,GAAIzT,EAAS,IAChBmB,EAAMa,KAAK,IAAMhC,GACjByT,EAAO,OAGJ,GAAIzT,EAAS,MAChBmB,EAAMa,KAAK,IAAMhC,GAAU,EAAGA,GAC9ByT,EAAO,MAGJ,MAAIzT,EAAS,YAIhB,MAAM,IAAIkF,MAAM,mBAHhB/D,EAAMa,KAAK,IAAMhC,GAAU,GAAIA,GAAU,GAAIA,GAAU,EAAGA,GAC1DyT,EAAO,CAGR,CAED,OADAa,EAAOtS,KAAK,CAAEyS,KAAM3O,EAAO4O,QAAS1U,EAAQ2U,QAASxT,EAAMnB,SACpDyT,EAAOzT,CACf,CACD,GAAa,WAAT3B,EAIF,OAAI4I,KAAKC,MAAMpB,KAAWA,GAAU8O,SAAS9O,GAMzCA,GAAS,EAEPA,EAAQ,KACV3E,EAAMa,KAAK8D,GACJ,GAGLA,EAAQ,KACV3E,EAAMa,KAAK,IAAM8D,GACV,GAGLA,EAAQ,OACV3E,EAAMa,KAAK,IAAM8D,GAAS,EAAGA,GACtB,GAGLA,EAAQ,YACV3E,EAAMa,KAAK,IAAM8D,GAAS,GAAIA,GAAS,GAAIA,GAAS,EAAGA,GAChD,IAGTyO,EAAMzO,EAAQmB,KAAK4N,IAAI,EAAG,KAAQ,EAClCL,EAAK1O,IAAU,EACf3E,EAAMa,KAAK,IAAMuS,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,EAAIC,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,GACxE,GAGH1O,IAAU,IACZ3E,EAAMa,KAAK8D,GACJ,GAGLA,IAAU,KACZ3E,EAAMa,KAAK,IAAM8D,GACV,GAGLA,IAAU,OACZ3E,EAAMa,KAAK,IAAM8D,GAAS,EAAGA,GACtB,GAGLA,IAAU,YACZ3E,EAAMa,KAAK,IAAM8D,GAAS,GAAIA,GAAS,GAAIA,GAAS,EAAGA,GAChD,IAGTyO,EAAKtN,KAAKC,MAAMpB,EAAQmB,KAAK4N,IAAI,EAAG,KACpCL,EAAK1O,IAAU,EACf3E,EAAMa,KAAK,IAAMuS,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,EAAIC,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,GACxE,IAxDPrT,EAAMa,KAAK,KACXsS,EAAOtS,KAAK,CAAE8S,OAAQhP,EAAO4O,QAAS,EAAGC,QAASxT,EAAMnB,SACjD,GAyDX,GAAa,WAAT3B,EAAmB,CAErB,GAAc,OAAVyH,EAEF,OADA3E,EAAMa,KAAK,KACJ,EAGT,GAAIc,MAAMiS,QAAQjP,GAAQ,CAIxB,IAHA9F,EAAS8F,EAAM9F,QAGF,GACXmB,EAAMa,KAAc,IAAThC,GACXyT,EAAO,OAGJ,GAAIzT,EAAS,MAChBmB,EAAMa,KAAK,IAAMhC,GAAU,EAAGA,GAC9ByT,EAAO,MAGJ,MAAIzT,EAAS,YAIhB,MAAM,IAAIkF,MAAM,mBAHhB/D,EAAMa,KAAK,IAAMhC,GAAU,GAAIA,GAAU,GAAIA,GAAU,EAAGA,GAC1DyT,EAAO,CAGR,CACD,IAAK1T,EAAI,EAAGA,EAAIC,EAAQD,IACtB0T,GAAQY,GAAQlT,EAAOmT,EAAQxO,EAAM/F,IAEvC,OAAO0T,CA9BY,CAkCrB,GAAI3N,aAAiBuB,KAAM,CACzB,IAAI2N,EAAOlP,EAAMmP,UAIjB,OAHAV,EAAKtN,KAAKC,MAAM8N,EAAO/N,KAAK4N,IAAI,EAAG,KACnCL,EAAKQ,IAAS,EACd7T,EAAMa,KAAK,IAAM,EAAGuS,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,EAAIC,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,GAC3E,EACR,CAED,GAAI1O,aAAiBjH,YAAa,CAIhC,IAHAmB,EAAS8F,EAAM0N,YAGF,IACXrS,EAAMa,KAAK,IAAMhC,GACjByT,EAAO,OAGT,GAAIzT,EAAS,MACXmB,EAAMa,KAAK,IAAMhC,GAAU,EAAGA,GAC9ByT,EAAO,MAGT,MAAIzT,EAAS,YAIX,MAAM,IAAIkF,MAAM,oBAHhB/D,EAAMa,KAAK,IAAMhC,GAAU,GAAIA,GAAU,GAAIA,GAAU,EAAGA,GAC1DyT,EAAO,CAGR,CAED,OADAa,EAAOtS,KAAK,CAAEkT,KAAMpP,EAAO4O,QAAS1U,EAAQ2U,QAASxT,EAAMnB,SACpDyT,EAAOzT,CACf,CAED,GAA4B,mBAAjB8F,EAAMqP,OACf,OAAOd,GAAQlT,EAAOmT,EAAQxO,EAAMqP,UAGtC,IAAIlX,EAAO,GAAIE,EAAM,GAEjBiX,EAAUtX,OAAOG,KAAK6H,GAC1B,IAAK/F,EAAI,EAAG4H,EAAIyN,EAAQpV,OAAQD,EAAI4H,EAAG5H,IAEX,mBAAf+F,EADX3H,EAAMiX,EAAQrV,KAEZ9B,EAAK+D,KAAK7D,GAMd,IAHA6B,EAAS/B,EAAK+B,QAGD,GACXmB,EAAMa,KAAc,IAAThC,GACXyT,EAAO,OAGJ,GAAIzT,EAAS,MAChBmB,EAAMa,KAAK,IAAMhC,GAAU,EAAGA,GAC9ByT,EAAO,MAGJ,MAAIzT,EAAS,YAIhB,MAAM,IAAIkF,MAAM,oBAHhB/D,EAAMa,KAAK,IAAMhC,GAAU,GAAIA,GAAU,GAAIA,GAAU,EAAGA,GAC1DyT,EAAO,CAGR,CAED,IAAK1T,EAAI,EAAGA,EAAIC,EAAQD,IAEtB0T,GAAQY,GAAQlT,EAAOmT,EADvBnW,EAAMF,EAAK8B,IAEX0T,GAAQY,GAAQlT,EAAOmT,EAAQxO,EAAM3H,IAEvC,OAAOsV,CAvM4B,CA0MrC,GAAa,YAATpV,EAEF,OADA8C,EAAMa,KAAK8D,EAAQ,IAAO,KACnB,EAGT,GAAa,cAATzH,EAEF,OADA8C,EAAMa,KAAK,IAAM,EAAG,GACb,EAET,MAAM,IAAIkD,MAAM,mBACjB,CA0CD,IAAAmQ,GAxCA,SAAgBvP,GACd,IAAI3E,EAAQ,GACRmT,EAAS,GACTb,EAAOY,GAAQlT,EAAOmT,EAAQxO,GAC9BwP,EAAM,IAAIzW,YAAY4U,GACtBS,EAAO,IAAIqB,SAASD,GAEpBE,EAAa,EACbC,EAAe,EACfC,GAAc,EACdpB,EAAOtU,OAAS,IAClB0V,EAAapB,EAAO,GAAGK,SAIzB,IADA,IAAIgB,EAAOC,EAAc,EAAGzB,EAAS,EAC5BpU,EAAI,EAAG4H,EAAIxG,EAAMnB,OAAQD,EAAI4H,EAAG5H,IAEvC,GADAmU,EAAKE,SAASqB,EAAe1V,EAAGoB,EAAMpB,IAClCA,EAAI,IAAM2V,EAAd,CAIA,GAFAE,GADAD,EAAQrB,EAAOkB,IACKd,QACpBP,EAASsB,EAAeC,EACpBC,EAAMT,KAER,IADA,IAAIW,EAAM,IAAI/V,WAAW6V,EAAMT,MACtBnB,EAAI,EAAGA,EAAI6B,EAAa7B,IAC/BG,EAAKE,SAASD,EAASJ,EAAG8B,EAAI9B,SAEvB4B,EAAMlB,KACfR,GAAUC,EAAMC,EAAQwB,EAAMlB,WACJ5I,IAAjB8J,EAAMb,QACfZ,EAAK4B,WAAW3B,EAAQwB,EAAMb,QAGhCW,GAAgBG,EACZtB,IAFJkB,KAGEE,EAAapB,EAAOkB,GAAYb,QAjBK,CAoBzC,OAAOW,CACR,EC5SD,SAASS,GAAQ3W,GAEf,GADA0C,KAAK6S,QAAU,EACXvV,aAAkBP,YACpBiD,KAAKkU,QAAU5W,EACf0C,KAAKmU,MAAQ,IAAIV,SAASzT,KAAKkU,aAC1B,KAAInX,YAAYM,OAAOC,GAI5B,MAAM,IAAI8F,MAAM,oBAHhBpD,KAAKkU,QAAU5W,EAAOA,OACtB0C,KAAKmU,MAAQ,IAAIV,SAASzT,KAAKkU,QAAS5W,EAAO8W,WAAY9W,EAAOoU,WAGnE,CACF,CA2CDuC,GAAQtX,UAAU0X,OAAS,SAAUnW,GAEnC,IADA,IAAI8F,EAAQ,IAAIhD,MAAM9C,GACbD,EAAI,EAAGA,EAAIC,EAAQD,IAC1B+F,EAAM/F,GAAK+B,KAAKsU,SAElB,OAAOtQ,CACR,EAEDiQ,GAAQtX,UAAU4X,KAAO,SAAUrW,GAEjC,IADA,IAAc8F,EAAQ,CAAA,EACb/F,EAAI,EAAGA,EAAIC,EAAQD,IAE1B+F,EADMhE,KAAKsU,UACEtU,KAAKsU,SAEpB,OAAOtQ,CACR,EAEDiQ,GAAQtX,UAAUgW,KAAO,SAAUzU,GACjC,IAAI8F,EA3DN,SAAkBoO,EAAMC,EAAQnU,GAE9B,IADA,IAAIsW,EAAS,GAAIC,EAAM,EACdxW,EAAIoU,EAAQqC,EAAMrC,EAASnU,EAAQD,EAAIyW,EAAKzW,IAAK,CACxD,IAAI0W,EAAOvC,EAAKwC,SAAS3W,GACzB,GAAsB,IAAV,IAAP0W,GAIL,GAAsB,MAAV,IAAPA,GAOL,GAAsB,MAAV,IAAPA,GAAL,CAQA,GAAsB,MAAV,IAAPA,GAaL,MAAM,IAAIvR,MAAM,gBAAkBuR,EAAK/X,SAAS,MAZ9C6X,GAAe,EAAPE,IAAgB,IACC,GAArBvC,EAAKwC,WAAW3W,KAAc,IACT,GAArBmU,EAAKwC,WAAW3W,KAAc,GACT,GAArBmU,EAAKwC,WAAW3W,KAAc,IACvB,OACTwW,GAAO,MACPD,GAAUhV,OAAOC,aAA4B,OAAdgV,IAAQ,IAA8B,OAAT,KAANA,KAEtDD,GAAUhV,OAAOC,aAAagV,EAVjC,MANCD,GAAUhV,OAAOC,cACN,GAAPkV,IAAgB,IACK,GAArBvC,EAAKwC,WAAW3W,KAAc,GACT,GAArBmU,EAAKwC,WAAW3W,KAAc,QAVlCuW,GAAUhV,OAAOC,cACN,GAAPkV,IAAgB,EACI,GAArBvC,EAAKwC,WAAW3W,SANnBuW,GAAUhV,OAAOC,aAAakV,EAgCjC,CACD,OAAOH,CACR,CAoBaK,CAAS7U,KAAKmU,MAAOnU,KAAK6S,QAAS3U,GAE/C,OADA8B,KAAK6S,SAAW3U,EACT8F,CACR,EAEDiQ,GAAQtX,UAAUyW,KAAO,SAAUlV,GACjC,IAAI8F,EAAQhE,KAAKkU,QAAQjT,MAAMjB,KAAK6S,QAAS7S,KAAK6S,QAAU3U,GAE5D,OADA8B,KAAK6S,SAAW3U,EACT8F,CACR,EAEDiQ,GAAQtX,UAAU2X,OAAS,WACzB,IACItQ,EADA8Q,EAAS9U,KAAKmU,MAAMS,SAAS5U,KAAK6S,WAC3B3U,EAAS,EAAG3B,EAAO,EAAGkW,EAAK,EAAGC,EAAK,EAE9C,GAAIoC,EAAS,IAEX,OAAIA,EAAS,IACJA,EAGLA,EAAS,IACJ9U,KAAKuU,KAAc,GAATO,GAGfA,EAAS,IACJ9U,KAAKqU,OAAgB,GAATS,GAGd9U,KAAK2S,KAAc,GAATmC,GAInB,GAAIA,EAAS,IACX,OAA8B,GAAtB,IAAOA,EAAS,GAG1B,OAAQA,GAEN,KAAK,IACH,OAAO,KAET,KAAK,IACH,OAAO,EAET,KAAK,IACH,OAAO,EAGT,KAAK,IAGH,OAFA5W,EAAS8B,KAAKmU,MAAMS,SAAS5U,KAAK6S,SAClC7S,KAAK6S,SAAW,EACT7S,KAAKoT,KAAKlV,GACnB,KAAK,IAGH,OAFAA,EAAS8B,KAAKmU,MAAMY,UAAU/U,KAAK6S,SACnC7S,KAAK6S,SAAW,EACT7S,KAAKoT,KAAKlV,GACnB,KAAK,IAGH,OAFAA,EAAS8B,KAAKmU,MAAMa,UAAUhV,KAAK6S,SACnC7S,KAAK6S,SAAW,EACT7S,KAAKoT,KAAKlV,GAGnB,KAAK,IAIH,OAHAA,EAAS8B,KAAKmU,MAAMS,SAAS5U,KAAK6S,SAClCtW,EAAOyD,KAAKmU,MAAMc,QAAQjV,KAAK6S,QAAU,GACzC7S,KAAK6S,SAAW,EACT,CAACtW,EAAMyD,KAAKoT,KAAKlV,IAC1B,KAAK,IAIH,OAHAA,EAAS8B,KAAKmU,MAAMY,UAAU/U,KAAK6S,SACnCtW,EAAOyD,KAAKmU,MAAMc,QAAQjV,KAAK6S,QAAU,GACzC7S,KAAK6S,SAAW,EACT,CAACtW,EAAMyD,KAAKoT,KAAKlV,IAC1B,KAAK,IAIH,OAHAA,EAAS8B,KAAKmU,MAAMa,UAAUhV,KAAK6S,SACnCtW,EAAOyD,KAAKmU,MAAMc,QAAQjV,KAAK6S,QAAU,GACzC7S,KAAK6S,SAAW,EACT,CAACtW,EAAMyD,KAAKoT,KAAKlV,IAG1B,KAAK,IAGH,OAFA8F,EAAQhE,KAAKmU,MAAMe,WAAWlV,KAAK6S,SACnC7S,KAAK6S,SAAW,EACT7O,EACT,KAAK,IAGH,OAFAA,EAAQhE,KAAKmU,MAAMgB,WAAWnV,KAAK6S,SACnC7S,KAAK6S,SAAW,EACT7O,EAGT,KAAK,IAGH,OAFAA,EAAQhE,KAAKmU,MAAMS,SAAS5U,KAAK6S,SACjC7S,KAAK6S,SAAW,EACT7O,EACT,KAAK,IAGH,OAFAA,EAAQhE,KAAKmU,MAAMY,UAAU/U,KAAK6S,SAClC7S,KAAK6S,SAAW,EACT7O,EACT,KAAK,IAGH,OAFAA,EAAQhE,KAAKmU,MAAMa,UAAUhV,KAAK6S,SAClC7S,KAAK6S,SAAW,EACT7O,EACT,KAAK,IAIH,OAHAyO,EAAKzS,KAAKmU,MAAMa,UAAUhV,KAAK6S,SAAW1N,KAAK4N,IAAI,EAAG,IACtDL,EAAK1S,KAAKmU,MAAMa,UAAUhV,KAAK6S,QAAU,GACzC7S,KAAK6S,SAAW,EACTJ,EAAKC,EAGd,KAAK,IAGH,OAFA1O,EAAQhE,KAAKmU,MAAMc,QAAQjV,KAAK6S,SAChC7S,KAAK6S,SAAW,EACT7O,EACT,KAAK,IAGH,OAFAA,EAAQhE,KAAKmU,MAAMiB,SAASpV,KAAK6S,SACjC7S,KAAK6S,SAAW,EACT7O,EACT,KAAK,IAGH,OAFAA,EAAQhE,KAAKmU,MAAMkB,SAASrV,KAAK6S,SACjC7S,KAAK6S,SAAW,EACT7O,EACT,KAAK,IAIH,OAHAyO,EAAKzS,KAAKmU,MAAMkB,SAASrV,KAAK6S,SAAW1N,KAAK4N,IAAI,EAAG,IACrDL,EAAK1S,KAAKmU,MAAMa,UAAUhV,KAAK6S,QAAU,GACzC7S,KAAK6S,SAAW,EACTJ,EAAKC,EAGd,KAAK,IAGH,OAFAnW,EAAOyD,KAAKmU,MAAMc,QAAQjV,KAAK6S,SAC/B7S,KAAK6S,SAAW,EACH,IAATtW,OACFyD,KAAK6S,SAAW,GAGX,CAACtW,EAAMyD,KAAKoT,KAAK,IAC1B,KAAK,IAGH,OAFA7W,EAAOyD,KAAKmU,MAAMc,QAAQjV,KAAK6S,SAC/B7S,KAAK6S,SAAW,EACT,CAACtW,EAAMyD,KAAKoT,KAAK,IAC1B,KAAK,IAGH,OAFA7W,EAAOyD,KAAKmU,MAAMc,QAAQjV,KAAK6S,SAC/B7S,KAAK6S,SAAW,EACT,CAACtW,EAAMyD,KAAKoT,KAAK,IAC1B,KAAK,IAGH,OAFA7W,EAAOyD,KAAKmU,MAAMc,QAAQjV,KAAK6S,SAC/B7S,KAAK6S,SAAW,EACH,IAATtW,GACFkW,EAAKzS,KAAKmU,MAAMkB,SAASrV,KAAK6S,SAAW1N,KAAK4N,IAAI,EAAG,IACrDL,EAAK1S,KAAKmU,MAAMa,UAAUhV,KAAK6S,QAAU,GACzC7S,KAAK6S,SAAW,EACT,IAAItN,KAAKkN,EAAKC,IAEhB,CAACnW,EAAMyD,KAAKoT,KAAK,IAC1B,KAAK,IAGH,OAFA7W,EAAOyD,KAAKmU,MAAMc,QAAQjV,KAAK6S,SAC/B7S,KAAK6S,SAAW,EACT,CAACtW,EAAMyD,KAAKoT,KAAK,KAG1B,KAAK,IAGH,OAFAlV,EAAS8B,KAAKmU,MAAMS,SAAS5U,KAAK6S,SAClC7S,KAAK6S,SAAW,EACT7S,KAAK2S,KAAKzU,GACnB,KAAK,IAGH,OAFAA,EAAS8B,KAAKmU,MAAMY,UAAU/U,KAAK6S,SACnC7S,KAAK6S,SAAW,EACT7S,KAAK2S,KAAKzU,GACnB,KAAK,IAGH,OAFAA,EAAS8B,KAAKmU,MAAMa,UAAUhV,KAAK6S,SACnC7S,KAAK6S,SAAW,EACT7S,KAAK2S,KAAKzU,GAGnB,KAAK,IAGH,OAFAA,EAAS8B,KAAKmU,MAAMY,UAAU/U,KAAK6S,SACnC7S,KAAK6S,SAAW,EACT7S,KAAKqU,OAAOnW,GACrB,KAAK,IAGH,OAFAA,EAAS8B,KAAKmU,MAAMa,UAAUhV,KAAK6S,SACnC7S,KAAK6S,SAAW,EACT7S,KAAKqU,OAAOnW,GAGrB,KAAK,IAGH,OAFAA,EAAS8B,KAAKmU,MAAMY,UAAU/U,KAAK6S,SACnC7S,KAAK6S,SAAW,EACT7S,KAAKuU,KAAKrW,GACnB,KAAK,IAGH,OAFAA,EAAS8B,KAAKmU,MAAMa,UAAUhV,KAAK6S,SACnC7S,KAAK6S,SAAW,EACT7S,KAAKuU,KAAKrW,GAGrB,MAAM,IAAIkF,MAAM,kBACjB,EAWD,IAAAkS,GATA,SAAgBhY,GACd,IAAIiY,EAAU,IAAItB,GAAQ3W,GACtB0G,EAAQuR,EAAQjB,SACpB,GAAIiB,EAAQ1C,UAAYvV,EAAOoU,WAC7B,MAAM,IAAItO,MAAO9F,EAAOoU,WAAa6D,EAAQ1C,QAAW,mBAE1D,OAAO7O,CACR,ECtRawR,GAAAxQ,OAAGyQ,GACjBD,GAAAlW,OAAiBoW,uCCcjB,SAAShW,EAAQvC,GACf,GAAIA,EAAK,OAWX,SAAeA,GACb,IAAK,IAAId,KAAOqD,EAAQ/C,UACtBQ,EAAId,GAAOqD,EAAQ/C,UAAUN,GAE/B,OAAOc,CACR,CAhBiBwC,CAAMxC,EACvB,CAXCwY,EAAAC,QAAiBlW,EAqCnBA,EAAQ/C,UAAUiD,GAClBF,EAAQ/C,UAAUkD,iBAAmB,SAASC,EAAOC,GAInD,OAHAC,KAAKC,WAAaD,KAAKC,YAAc,CAAA,GACpCD,KAAKC,WAAW,IAAMH,GAASE,KAAKC,WAAW,IAAMH,IAAU,IAC7DI,KAAKH,GACDC,MAaTN,EAAQ/C,UAAUyD,KAAO,SAASN,EAAOC,GACvC,SAASH,IACPI,KAAKK,IAAIP,EAAOF,GAChBG,EAAGO,MAAMN,KAAMO,UAChB,CAID,OAFAX,EAAGG,GAAKA,EACRC,KAAKJ,GAAGE,EAAOF,GACRI,MAaTN,EAAQ/C,UAAU0D,IAClBX,EAAQ/C,UAAU6D,eAClBd,EAAQ/C,UAAU8D,mBAClBf,EAAQ/C,UAAU+D,oBAAsB,SAASZ,EAAOC,GAItD,GAHAC,KAAKC,WAAaD,KAAKC,YAAc,CAAA,EAGjC,GAAKM,UAAUrC,OAEjB,OADA8B,KAAKC,WAAa,GACXD,KAIT,IAUIW,EAVAC,EAAYZ,KAAKC,WAAW,IAAMH,GACtC,IAAKc,EAAW,OAAOZ,KAGvB,GAAI,GAAKO,UAAUrC,OAEjB,cADO8B,KAAKC,WAAW,IAAMH,GACtBE,KAKT,IAAK,IAAI/B,EAAI,EAAGA,EAAI2C,EAAU1C,OAAQD,IAEpC,IADA0C,EAAKC,EAAU3C,MACJ8B,GAAMY,EAAGZ,KAAOA,EAAI,CAC7Ba,EAAUC,OAAO5C,EAAG,GACpB,KACD,CASH,OAJyB,IAArB2C,EAAU1C,eACL8B,KAAKC,WAAW,IAAMH,GAGxBE,MAWTN,EAAQ/C,UAAUmE,KAAO,SAAShB,GAChCE,KAAKC,WAAaD,KAAKC,YAAc,CAAA,EAKrC,IAHA,IAAIc,EAAO,IAAIC,MAAMT,UAAUrC,OAAS,GACpC0C,EAAYZ,KAAKC,WAAW,IAAMH,GAE7B7B,EAAI,EAAGA,EAAIsC,UAAUrC,OAAQD,IACpC8C,EAAK9C,EAAI,GAAKsC,UAAUtC,GAG1B,GAAI2C,EAEG,CAAI3C,EAAI,EAAb,IAAK,IAAWiB,GADhB0B,EAAYA,EAAUK,MAAM,IACI/C,OAAQD,EAAIiB,IAAOjB,EACjD2C,EAAU3C,GAAGqC,MAAMN,KAAMe,EADK7C,CAKlC,OAAO8B,MAWTN,EAAQ/C,UAAUwE,UAAY,SAASrB,GAErC,OADAE,KAAKC,WAAaD,KAAKC,YAAc,CAAA,EAC9BD,KAAKC,WAAW,IAAMH,IAAU,IAWzCJ,EAAQ/C,UAAUyE,aAAe,SAAStB,GACxC,QAAUE,KAAKmB,UAAUrB,GAAO5B,aC7KlC,IAAI2X,GAAUJ,GACV/V,GAAUgW,GAAAA,QAEE1O,GAAA8O,GAAA9O,SAAG,EAMf+O,GAAcC,GAAAF,GAAAC,WAAqB,CACrCE,QAAS,EACTC,WAAY,EACZC,MAAO,EACPC,IAAK,EACLC,cAAe,GAGbC,GACFzN,OAAOyN,WACP,SAAUtS,GACR,MACmB,iBAAVA,GACP8O,SAAS9O,IACTmB,KAAKC,MAAMpB,KAAWA,CAEzB,EAECuS,GAAW,SAAUvS,GACvB,MAAwB,iBAAVA,CACf,EAEGwS,GAAW,SAAUxS,GACvB,MAAiD,oBAA1ChI,OAAOW,UAAUC,SAASC,KAAKmH,EACvC,EAED,SAASyS,KAAY,CAMrB,SAASxC,KAAY,CAJrBwC,GAAQ9Z,UAAUqI,OAAS,SAAUP,GACnC,MAAO,CAACoR,GAAQ7Q,OAAOP,GACxB,EAID/E,GAAQuU,GAAQtX,WAEhBsX,GAAQtX,UAAU+Z,IAAM,SAAUvZ,GAChC,IAAIwB,EAAUkX,GAAQvW,OAAOnC,GAC7B6C,KAAK2W,YAAYhY,GACjBqB,KAAKc,KAAK,UAAWnC,EACtB,EAeDsV,GAAQtX,UAAUga,YAAc,SAAUhY,GAKxC,KAHE2X,GAAU3X,EAAQpC,OAClBoC,EAAQpC,MAAQwZ,GAAWE,SAC3BtX,EAAQpC,MAAQwZ,GAAWM,eAE3B,MAAM,IAAIjT,MAAM,uBAGlB,IAAKmT,GAAS5X,EAAQiY,KACpB,MAAM,IAAIxT,MAAM,qBAGlB,IA1BF,SAAqBzE,GACnB,OAAQA,EAAQpC,MACd,KAAKwZ,GAAWE,QACd,YAAwBlM,IAAjBpL,EAAQnC,MAAsBga,GAAS7X,EAAQnC,MACxD,KAAKuZ,GAAWG,WACd,YAAwBnM,IAAjBpL,EAAQnC,KACjB,KAAKuZ,GAAWM,cACd,OAAOE,GAAS5X,EAAQnC,OAASga,GAAS7X,EAAQnC,MACpD,QACE,OAAOwE,MAAMiS,QAAQtU,EAAQnC,MAElC,CAeMqa,CAAYlY,GACf,MAAM,IAAIyE,MAAM,mBAIlB,UADgC2G,IAAfpL,EAAQyQ,IAAoBkH,GAAU3X,EAAQyQ,KAE7D,MAAM,IAAIhM,MAAM,oBAEnB,EAED6Q,GAAQtX,UAAUma,QAAU,aAE5B,IAAeC,GAAAjB,GAAAW,QAAGA,GAClBO,GAAAlB,GAAA7B,QAAkBA,wGC1FX,SAASrU,GAAGzC,EAAK4P,EAAIhN,GAExB,OADA5C,EAAIyC,GAAGmN,EAAIhN,GACJ,WACH5C,EAAIkD,IAAI0M,EAAIhN,GAEnB,CCED,IAAMkX,GAAkBjb,OAAOkb,OAAO,CAClCC,QAAS,EACTC,cAAe,EACfC,WAAY,EACZC,cAAe,EAEfC,YAAa,EACb/W,eAAgB,IA0BPgO,GAAb,SAAAlL,GAAAC,EAAAiL,EAAAlL,GAAA,IAAAH,EAAAM,EAAA+K,GAII,SAAAA,EAAYgJ,EAAIZ,EAAKrU,GAAM,IAAAU,EAAA,OAAAC,EAAAlD,KAAAwO,IACvBvL,EAAAE,EAAAtG,KAAAmD,OAeKyX,WAAY,EAKjBxU,EAAKyU,WAAY,EAIjBzU,EAAK0U,cAAgB,GAIrB1U,EAAK2U,WAAa,GAOlB3U,EAAK4U,OAAS,GAKd5U,EAAK6U,UAAY,EACjB7U,EAAK8U,IAAM,EACX9U,EAAK+U,KAAO,GACZ/U,EAAKgV,MAAQ,GACbhV,EAAKuU,GAAKA,EACVvU,EAAK2T,IAAMA,EACPrU,GAAQA,EAAK2V,OACbjV,EAAKiV,KAAO3V,EAAK2V,MAErBjV,EAAKkV,MAAQlP,EAAc,CAAd,EAAkB1G,GAC3BU,EAAKuU,GAAGY,cACRnV,EAAKkH,OApDclH,CAqD1B,CAzDL,OAAAc,EAAAyK,EAAA,CAAA,CAAAnS,IAAA,eAAAkL,IAwEI,WACI,OAAQvH,KAAKyX,SAChB,GA1EL,CAAApb,IAAA,YAAA2H,MAgFI,WACI,IAAIhE,KAAKqY,KAAT,CAEA,IAAMb,EAAKxX,KAAKwX,GAChBxX,KAAKqY,KAAO,CACRzY,GAAG4X,EAAI,OAAQxX,KAAKwM,OAAO9J,KAAK1C,OAChCJ,GAAG4X,EAAI,SAAUxX,KAAKsY,SAAS5V,KAAK1C,OACpCJ,GAAG4X,EAAI,QAASxX,KAAKgN,QAAQtK,KAAK1C,OAClCJ,GAAG4X,EAAI,QAASxX,KAAK4M,QAAQlK,KAAK1C,OANlC,CAQP,GA1FL,CAAA3D,IAAA,SAAAkL,IA4GI,WACI,QAASvH,KAAKqY,IACjB,GA9GL,CAAAhc,IAAA,UAAA2H,MAyHI,WACI,OAAIhE,KAAKyX,YAETzX,KAAKuY,YACAvY,KAAKwX,GAAL,eACDxX,KAAKwX,GAAGrN,OACR,SAAWnK,KAAKwX,GAAGgB,aACnBxY,KAAKwM,UALExM,IAOd,GAlIL,CAAA3D,IAAA,OAAA2H,MAsII,WACI,OAAOhE,KAAKmX,SACf,GAxIL,CAAA9a,IAAA,OAAA2H,MAwJI,WAAc,IAAA,IAAAtC,EAAAnB,UAAArC,OAAN6C,EAAM,IAAAC,MAAAU,GAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAANb,EAAMa,GAAArB,UAAAqB,GAGV,OAFAb,EAAK0X,QAAQ,WACbzY,KAAKc,KAAKR,MAAMN,KAAMe,GACff,IACV,GA5JL,CAAA3D,IAAA,OAAA2H,MA8KI,SAAK+I,GACD,GAAIkK,GAAgBjV,eAAe+K,GAC/B,MAAM,IAAI3J,MAAM,IAAM2J,EAAGnQ,WAAa,8BAF5B,IAAA,IAAA8b,EAAAnY,UAAArC,OAAN6C,EAAM,IAAAC,MAAA0X,EAAA,EAAAA,EAAA,EAAA,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAN5X,EAAM4X,EAAA,GAAApY,UAAAoY,GAKd,GADA5X,EAAK0X,QAAQ1L,GACT/M,KAAKmY,MAAMS,UAAY5Y,KAAKiY,MAAMY,YAAc7Y,KAAKiY,eAErD,OADAjY,KAAK8Y,YAAY/X,GACVf,KAEX,IAAMyE,EAAS,CACXlI,KAAMwZ,GAAWI,MACjB3Z,KAAMuE,EAEV0D,QAAiB,IAGjB,GAFAA,EAAOmN,QAAQC,UAAmC,IAAxB7R,KAAKiY,MAAMpG,SAEjC,mBAAsB9Q,EAAKA,EAAK7C,OAAS,GAAI,CAC7C,IAAMkR,EAAKpP,KAAK+X,MACVgB,EAAMhY,EAAKiY,MACjBhZ,KAAKiZ,qBAAqB7J,EAAI2J,GAC9BtU,EAAO2K,GAAKA,CACf,CACD,IAAM8J,EAAsBlZ,KAAKwX,GAAG2B,QAChCnZ,KAAKwX,GAAG2B,OAAOzJ,WACf1P,KAAKwX,GAAG2B,OAAOzJ,UAAU/L,SACvByV,EAAgBpZ,KAAKiY,MAAL,YAAyBiB,IAAwBlZ,KAAKyX,WAW5E,OAVI2B,IAEKpZ,KAAKyX,WACVzX,KAAKqZ,wBAAwB5U,GAC7BzE,KAAKyE,OAAOA,IAGZzE,KAAK4X,WAAW1X,KAAKuE,IAEzBzE,KAAKiY,MAAQ,GACNjY,IACV,GAnNL,CAAA3D,IAAA,uBAAA2H,MAuNI,SAAqBoL,EAAI2J,GAAK,IACtBO,EADsB5V,EAAA1D,KAEpByK,EAAwC,QAA7B6O,EAAKtZ,KAAKiY,MAAMxN,eAA4B,IAAP6O,EAAgBA,EAAKtZ,KAAKmY,MAAMoB,WACtF,QAAgBxP,IAAZU,EAAJ,CAKA,IAAM+O,EAAQxZ,KAAKwX,GAAG/U,cAAa,kBACxBiB,EAAKsU,KAAK5I,GACjB,IAAK,IAAInR,EAAI,EAAGA,EAAIyF,EAAKkU,WAAW1Z,OAAQD,IACpCyF,EAAKkU,WAAW3Z,GAAGmR,KAAOA,GAC1B1L,EAAKkU,WAAW/W,OAAO5C,EAAG,GAGlC8a,EAAIlc,KAAK6G,EAAM,IAAIN,MAAM,2BAPf,GAQXqH,GACHzK,KAAKgY,KAAK5I,GAAM,WAEZ1L,EAAK8T,GAAG7U,eAAe6W,GAFE,IAAA,IAAAC,EAAAlZ,UAAArC,OAAT6C,EAAS,IAAAC,MAAAyY,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAT3Y,EAAS2Y,GAAAnZ,UAAAmZ,GAGzBX,EAAIzY,MAAMoD,EAAO,CAAA,aAAS3C,IApBJ,MAItBf,KAAKgY,KAAK5I,GAAM2J,CAkBvB,GA7OL,CAAA1c,IAAA,cAAA2H,MA8PI,SAAY+I,GAAa,IAAA,IAAAnF,EAAA5H,KAAA2Z,EAAApZ,UAAArC,OAAN6C,EAAM,IAAAC,MAAA2Y,EAAA,EAAAA,EAAA,EAAA,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAN7Y,EAAM6Y,EAAA,GAAArZ,UAAAqZ,GAErB,IAAMC,OAAiC9P,IAAvB/J,KAAKiY,MAAMxN,cAAmDV,IAA1B/J,KAAKmY,MAAMoB,WAC/D,OAAO,IAAI9N,SAAQ,SAACC,EAASoO,GACzB/Y,EAAKb,MAAK,SAAC6Z,EAAMC,GACb,OAAIH,EACOE,EAAOD,EAAOC,GAAQrO,EAAQsO,GAG9BtO,EAAQqO,MAGvBnS,EAAK9G,KAALR,MAAAsH,GAAUmF,GAANzG,OAAavF,GACpB,GACJ,GA5QL,CAAA1E,IAAA,cAAA2H,MAkRI,SAAYjD,GAAM,IACVgY,EADU7Q,EAAAlI,KAEuB,mBAA1Be,EAAKA,EAAK7C,OAAS,KAC1B6a,EAAMhY,EAAKiY,OAEf,IAAMvU,EAAS,CACX2K,GAAIpP,KAAK8X,YACTmC,SAAU,EACVC,SAAS,EACTnZ,KAAAA,EACAkX,MAAOhP,EAAc,CAAE4P,WAAW,GAAQ7Y,KAAKiY,QAEnDlX,EAAKb,MAAK,SAAC+F,GACP,GAAIxB,IAAWyD,EAAK2P,OAAO,GAA3B,CAIA,IAAMsC,EAAmB,OAARlU,EACjB,GAAIkU,EACI1V,EAAOwV,SAAW/R,EAAKiQ,MAAMS,UAC7B1Q,EAAK2P,OAAO7H,QACR+I,GACAA,EAAI9S,SAMZ,GADAiC,EAAK2P,OAAO7H,QACR+I,EAAK,CAAA,IAAA,IAAAqB,EAAA7Z,UAAArC,OAhBEmc,EAgBF,IAAArZ,MAAAoZ,EAAA,EAAAA,EAAA,EAAA,GAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAhBED,EAgBFC,EAAA,GAAA/Z,UAAA+Z,GACLvB,EAAAzY,WAAA,EAAA,CAAI,MAAJgG,OAAa+T,GAChB,CAGL,OADA5V,EAAOyV,SAAU,EACVhS,EAAKqS,aAjBX,KAmBLva,KAAK6X,OAAO3X,KAAKuE,GACjBzE,KAAKua,aACR,GAvTL,CAAAle,IAAA,cAAA2H,MA8TI,WAA2B,IAAfwW,0DACR,GAAKxa,KAAKyX,WAAoC,IAAvBzX,KAAK6X,OAAO3Z,OAAnC,CAGA,IAAMuG,EAASzE,KAAK6X,OAAO,GACvBpT,EAAOyV,UAAYM,IAGvB/V,EAAOyV,SAAU,EACjBzV,EAAOwV,WACPja,KAAKiY,MAAQxT,EAAOwT,MACpBjY,KAAKc,KAAKR,MAAMN,KAAMyE,EAAO1D,MAR5B,CASJ,GA1UL,CAAA1E,IAAA,SAAA2H,MAiVI,SAAOS,GACHA,EAAOmS,IAAM5W,KAAK4W,IAClB5W,KAAKwX,GAAGiD,QAAQhW,EACnB,GApVL,CAAApI,IAAA,SAAA2H,MA0VI,WAAS,IAAAoE,EAAApI,KACmB,mBAAbA,KAAKkY,KACZlY,KAAKkY,MAAK,SAAC1b,GACP4L,EAAKsS,mBAAmBle,MAI5BwD,KAAK0a,mBAAmB1a,KAAKkY,KAEpC,GAnWL,CAAA7b,IAAA,qBAAA2H,MA0WI,SAAmBxH,GACfwD,KAAKyE,OAAO,CACRlI,KAAMwZ,GAAWE,QACjBzZ,KAAMwD,KAAK2a,KACL1R,EAAc,CAAE2R,IAAK5a,KAAK2a,KAAMtI,OAAQrS,KAAK6a,aAAere,GAC5DA,GAEb,GAjXL,CAAAH,IAAA,UAAA2H,MAwXI,SAAQiC,GACCjG,KAAKyX,WACNzX,KAAKkB,aAAa,gBAAiB+E,EAE1C,GA5XL,CAAA5J,IAAA,UAAA2H,MAoYI,SAAQlB,EAAQC,GACZ/C,KAAKyX,WAAY,SACVzX,KAAKoP,GACZpP,KAAKkB,aAAa,aAAc4B,EAAQC,EAC3C,GAxYL,CAAA1G,IAAA,WAAA2H,MA+YI,SAASS,GAEL,GADsBA,EAAOmS,MAAQ5W,KAAK4W,IAG1C,OAAQnS,EAAOlI,MACX,KAAKwZ,GAAWE,QACRxR,EAAOjI,MAAQiI,EAAOjI,KAAKmM,IAC3B3I,KAAK8a,UAAUrW,EAAOjI,KAAKmM,IAAKlE,EAAOjI,KAAKoe,KAG5C5a,KAAKkB,aAAa,gBAAiB,IAAIkC,MAAM,8LAEjD,MACJ,KAAK2S,GAAWI,MAChB,KAAKJ,GAAWgF,aACZ/a,KAAKgb,QAAQvW,GACb,MACJ,KAAKsR,GAAWK,IAChB,KAAKL,GAAWkF,WACZjb,KAAKkb,MAAMzW,GACX,MACJ,KAAKsR,GAAWG,WACZlW,KAAKmb,eACL,MACJ,KAAKpF,GAAWM,cACZrW,KAAK8W,UACL,IAAM7Q,EAAM,IAAI7C,MAAMqB,EAAOjI,KAAK4e,SAElCnV,EAAIzJ,KAAOiI,EAAOjI,KAAKA,KACvBwD,KAAKkB,aAAa,gBAAiB+E,GAG9C,GA/aL,CAAA5J,IAAA,UAAA2H,MAsbI,SAAQS,GACJ,IAAM1D,EAAO0D,EAAOjI,MAAQ,GACxB,MAAQiI,EAAO2K,IACfrO,EAAKb,KAAKF,KAAK+Y,IAAItU,EAAO2K,KAE1BpP,KAAKyX,UACLzX,KAAKqb,UAAUta,GAGff,KAAK2X,cAAczX,KAAKlE,OAAOkb,OAAOnW,GAE7C,GAjcL,CAAA1E,IAAA,YAAA2H,MAkcI,SAAUjD,GACN,GAAIf,KAAKsb,eAAiBtb,KAAKsb,cAAcpd,OAAQ,CACjD,IADiDqd,EAAAC,EAAAC,EAC/Bzb,KAAKsb,cAAcra,SADY,IAEjD,IAAkCua,EAAAE,MAAAH,EAAAC,EAAAG,KAAAC,MAAA,CAAAL,EAAAvX,MACrB1D,MAAMN,KAAMe,EACxB,CAJgD,CAAA,MAAAkF,GAAAuV,EAAAnV,EAAAJ,EAAA,CAAA,QAAAuV,EAAAK,GAAA,CAKpD,CACD5X,EAAAC,EAAAsK,EAAA7R,WAAA,OAAAqD,MAAWM,MAAMN,KAAMe,GACnBf,KAAK2a,MAAQ5Z,EAAK7C,QAA2C,iBAA1B6C,EAAKA,EAAK7C,OAAS,KACtD8B,KAAK6a,YAAc9Z,EAAKA,EAAK7C,OAAS,GAE7C,GA7cL,CAAA7B,IAAA,MAAA2H,MAmdI,SAAIoL,GACA,IAAM9N,EAAOtB,KACT8b,GAAO,EACX,OAAO,WAEH,IAAIA,EAAJ,CAEAA,GAAO,EAJe,IAAA,IAAAC,EAAAxb,UAAArC,OAAN6C,EAAM,IAAAC,MAAA+a,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAANjb,EAAMib,GAAAzb,UAAAyb,GAKtB1a,EAAKmD,OAAO,CACRlI,KAAMwZ,GAAWK,IACjBhH,GAAIA,EACJ5S,KAAMuE,GALN,EAQX,GAjeL,CAAA1E,IAAA,QAAA2H,MAweI,SAAMS,GACF,IAAMsU,EAAM/Y,KAAKgY,KAAKvT,EAAO2K,IACzB,mBAAsB2J,IACtBA,EAAIzY,MAAMN,KAAMyE,EAAOjI,aAChBwD,KAAKgY,KAAKvT,EAAO2K,IAI/B,GAhfL,CAAA/S,IAAA,YAAA2H,MAsfI,SAAUoL,EAAIwL,GACV5a,KAAKoP,GAAKA,EACVpP,KAAK0X,UAAYkD,GAAO5a,KAAK2a,OAASC,EACtC5a,KAAK2a,KAAOC,EACZ5a,KAAKyX,WAAY,EACjBzX,KAAKic,eACLjc,KAAKkB,aAAa,WAClBlB,KAAKua,aAAY,EACpB,GA9fL,CAAAle,IAAA,eAAA2H,MAogBI,WAAe,IAAAoF,EAAApJ,KACXA,KAAK2X,cAAcvb,SAAQ,SAAC2E,GAAD,OAAUqI,EAAKiS,UAAUta,MACpDf,KAAK2X,cAAgB,GACrB3X,KAAK4X,WAAWxb,SAAQ,SAACqI,GACrB2E,EAAKiQ,wBAAwB5U,GAC7B2E,EAAK3E,OAAOA,MAEhBzE,KAAK4X,WAAa,EACrB,GA5gBL,CAAAvb,IAAA,eAAA2H,MAkhBI,WACIhE,KAAK8W,UACL9W,KAAK4M,QAAQ,uBAChB,GArhBL,CAAAvQ,IAAA,UAAA2H,MA6hBI,WACQhE,KAAKqY,OAELrY,KAAKqY,KAAKjc,SAAQ,SAAC8f,GAAD,OAAgBA,OAClClc,KAAKqY,UAAOtO,GAEhB/J,KAAKwX,GAAL,SAAoBxX,KACvB,GApiBL,CAAA3D,IAAA,aAAA2H,MAqjBI,WAUI,OATIhE,KAAKyX,WACLzX,KAAKyE,OAAO,CAAElI,KAAMwZ,GAAWG,aAGnClW,KAAK8W,UACD9W,KAAKyX,WAELzX,KAAK4M,QAAQ,wBAEV5M,IACV,GAhkBL,CAAA3D,IAAA,QAAA2H,MAskBI,WACI,OAAOhE,KAAKqX,YACf,GAxkBL,CAAAhb,IAAA,WAAA2H,MAklBI,SAAS6N,GAEL,OADA7R,KAAKiY,MAAMpG,SAAWA,EACf7R,IACV,GArlBL,CAAA3D,IAAA,WAAAkL,IA+lBI,WAEI,OADAvH,KAAKiY,gBAAiB,EACfjY,IACV,GAlmBL,CAAA3D,IAAA,UAAA2H,MAgnBI,SAAQyG,GAEJ,OADAzK,KAAKiY,MAAMxN,QAAUA,EACdzK,IACV,GAnnBL,CAAA3D,IAAA,QAAA2H,MA+nBI,SAAMmY,GAGF,OAFAnc,KAAKsb,cAAgBtb,KAAKsb,eAAiB,GAC3Ctb,KAAKsb,cAAcpb,KAAKic,GACjBnc,IACV,GAnoBL,CAAA3D,IAAA,aAAA2H,MA+oBI,SAAWmY,GAGP,OAFAnc,KAAKsb,cAAgBtb,KAAKsb,eAAiB,GAC3Ctb,KAAKsb,cAAc7C,QAAQ0D,GACpBnc,IACV,GAnpBL,CAAA3D,IAAA,SAAA2H,MAsqBI,SAAOmY,GACH,IAAKnc,KAAKsb,cACN,OAAOtb,KAEX,GAAImc,GAEA,IADA,IAAMhb,EAAYnB,KAAKsb,cACdrd,EAAI,EAAGA,EAAIkD,EAAUjD,OAAQD,IAClC,GAAIke,IAAahb,EAAUlD,GAEvB,OADAkD,EAAUN,OAAO5C,EAAG,GACb+B,UAKfA,KAAKsb,cAAgB,GAEzB,OAAOtb,IACV,GAvrBL,CAAA3D,IAAA,eAAA2H,MA4rBI,WACI,OAAOhE,KAAKsb,eAAiB,EAChC,GA9rBL,CAAAjf,IAAA,gBAAA2H,MA4sBI,SAAcmY,GAGV,OAFAnc,KAAKoc,sBAAwBpc,KAAKoc,uBAAyB,GAC3Dpc,KAAKoc,sBAAsBlc,KAAKic,GACzBnc,IACV,GAhtBL,CAAA3D,IAAA,qBAAA2H,MA8tBI,SAAmBmY,GAGf,OAFAnc,KAAKoc,sBAAwBpc,KAAKoc,uBAAyB,GAC3Dpc,KAAKoc,sBAAsB3D,QAAQ0D,GAC5Bnc,IACV,GAluBL,CAAA3D,IAAA,iBAAA2H,MAqvBI,SAAemY,GACX,IAAKnc,KAAKoc,sBACN,OAAOpc,KAEX,GAAImc,GAEA,IADA,IAAMhb,EAAYnB,KAAKoc,sBACdne,EAAI,EAAGA,EAAIkD,EAAUjD,OAAQD,IAClC,GAAIke,IAAahb,EAAUlD,GAEvB,OADAkD,EAAUN,OAAO5C,EAAG,GACb+B,UAKfA,KAAKoc,sBAAwB,GAEjC,OAAOpc,IACV,GAtwBL,CAAA3D,IAAA,uBAAA2H,MA2wBI,WACI,OAAOhE,KAAKoc,uBAAyB,EACxC,GA7wBL,CAAA/f,IAAA,0BAAA2H,MAqxBI,SAAwBS,GACpB,GAAIzE,KAAKoc,uBAAyBpc,KAAKoc,sBAAsBle,OAAQ,CACjE,IADiEme,EAAAC,EAAAb,EAC/Czb,KAAKoc,sBAAsBnb,SADoB,IAEjE,IAAkCqb,EAAAZ,MAAAW,EAAAC,EAAAX,KAAAC,MAAA,CAAAS,EAAArY,MACrB1D,MAAMN,KAAMyE,EAAOjI,KAC/B,CAJgE,CAAA,MAAAyJ,GAAAqW,EAAAjW,EAAAJ,EAAA,CAAA,QAAAqW,EAAAT,GAAA,CAKpE,CACJ,KA5xBLrN,CAAA,CAAA,CAA4B9O,GC7BrB,SAAS6c,GAAQha,GACpBA,EAAOA,GAAQ,GACfvC,KAAKwc,GAAKja,EAAKka,KAAO,IACtBzc,KAAK0c,IAAMna,EAAKma,KAAO,IACvB1c,KAAK2c,OAASpa,EAAKoa,QAAU,EAC7B3c,KAAK4c,OAASra,EAAKqa,OAAS,GAAKra,EAAKqa,QAAU,EAAIra,EAAKqa,OAAS,EAClE5c,KAAK6c,SAAW,CACnB,CAODN,GAAQ5f,UAAUmgB,SAAW,WACzB,IAAIN,EAAKxc,KAAKwc,GAAKrX,KAAK4N,IAAI/S,KAAK2c,OAAQ3c,KAAK6c,YAC9C,GAAI7c,KAAK4c,OAAQ,CACb,IAAIG,EAAO5X,KAAK6X,SACZC,EAAY9X,KAAKC,MAAM2X,EAAO/c,KAAK4c,OAASJ,GAChDA,EAAoC,IAAN,EAAxBrX,KAAKC,MAAa,GAAP2X,IAAuBP,EAAKS,EAAYT,EAAKS,CACjE,CACD,OAAgC,EAAzB9X,KAAKsX,IAAID,EAAIxc,KAAK0c,IAC5B,EAMDH,GAAQ5f,UAAUugB,MAAQ,WACtBld,KAAK6c,SAAW,CACnB,EAMDN,GAAQ5f,UAAUwgB,OAAS,SAAUV,GACjCzc,KAAKwc,GAAKC,CACb,EAMDF,GAAQ5f,UAAUygB,OAAS,SAAUV,GACjC1c,KAAK0c,IAAMA,CACd,EAMDH,GAAQ5f,UAAU0gB,UAAY,SAAUT,GACpC5c,KAAK4c,OAASA,CACjB,EC3DD,IAAaU,GAAb,SAAAha,GAAAC,EAAA+Z,EAAAha,GAAA,IAAAH,EAAAM,EAAA6Z,GACI,SAAYnU,EAAAA,EAAK5G,GAAM,IAAAU,EACfqW,EADepW,EAAAlD,KAAAsd,IAEnBra,EAAAE,EAAAtG,KAAAmD,OACKud,KAAO,GACZta,EAAKoV,KAAO,GACRlP,GAAO,WAAoBA,EAAAA,KAC3B5G,EAAO4G,EACPA,OAAMY,IAEVxH,EAAOA,GAAQ,IACVyG,KAAOzG,EAAKyG,MAAQ,aACzB/F,EAAKV,KAAOA,EACZD,EAAqBsB,EAAAX,GAAOV,GAC5BU,EAAKua,cAAmC,IAAtBjb,EAAKib,cACvBva,EAAKwa,qBAAqBlb,EAAKkb,sBAAwBC,KACvDza,EAAK0a,kBAAkBpb,EAAKob,mBAAqB,KACjD1a,EAAK2a,qBAAqBrb,EAAKqb,sBAAwB,KACvD3a,EAAK4a,oBAAwD,QAAnCvE,EAAK/W,EAAKsb,2BAAwC,IAAPvE,EAAgBA,EAAK,IAC1FrW,EAAK6a,QAAU,IAAIvB,GAAQ,CACvBE,IAAKxZ,EAAK0a,oBACVjB,IAAKzZ,EAAK2a,uBACVhB,OAAQ3Z,EAAK4a,wBAEjB5a,EAAKwH,QAAQ,MAAQlI,EAAKkI,QAAU,IAAQlI,EAAKkI,SACjDxH,EAAKuV,YAAc,SACnBvV,EAAKkG,IAAMA,EACX,IAAM4U,EAAUxb,EAAKyb,QAAUA,GA1BZ,OA2BnB/a,EAAKgb,QAAU,IAAIF,EAAQtH,QAC3BxT,EAAKsS,QAAU,IAAIwI,EAAQ9J,QAC3BhR,EAAKmV,cAAoC,IAArB7V,EAAK2b,YACrBjb,EAAKmV,cACLnV,EAAKkH,OA/BUlH,CAgCtB,CAjCL,OAAAc,EAAAuZ,EAAA,CAAA,CAAAjhB,IAAA,eAAA2H,MAkCI,SAAama,GACT,OAAK5d,UAAUrC,QAEf8B,KAAKoe,gBAAkBD,EAChBne,MAFIA,KAAKoe,aAGnB,GAvCL,CAAA/hB,IAAA,uBAAA2H,MAwCI,SAAqBma,GACjB,YAAUpU,IAANoU,EACOne,KAAKqe,uBAChBre,KAAKqe,sBAAwBF,EACtBne,KACV,GA7CL,CAAA3D,IAAA,oBAAA2H,MA8CI,SAAkBma,GACd,IAAI7E,EACJ,YAAUvP,IAANoU,EACOne,KAAKse,oBAChBte,KAAKse,mBAAqBH,EACF,QAAvB7E,EAAKtZ,KAAK8d,eAA4B,IAAPxE,GAAyBA,EAAG6D,OAAOgB,GAC5Dne,KACV,GArDL,CAAA3D,IAAA,sBAAA2H,MAsDI,SAAoBma,GAChB,IAAI7E,EACJ,YAAUvP,IAANoU,EACOne,KAAKue,sBAChBve,KAAKue,qBAAuBJ,EACJ,QAAvB7E,EAAKtZ,KAAK8d,eAA4B,IAAPxE,GAAyBA,EAAG+D,UAAUc,GAC/Dne,KACV,GA7DL,CAAA3D,IAAA,uBAAA2H,MA8DI,SAAqBma,GACjB,IAAI7E,EACJ,YAAUvP,IAANoU,EACOne,KAAKwe,uBAChBxe,KAAKwe,sBAAwBL,EACL,QAAvB7E,EAAKtZ,KAAK8d,eAA4B,IAAPxE,GAAyBA,EAAG8D,OAAOe,GAC5Dne,KACV,GArEL,CAAA3D,IAAA,UAAA2H,MAsEI,SAAQma,GACJ,OAAK5d,UAAUrC,QAEf8B,KAAKye,SAAWN,EACTne,MAFIA,KAAKye,QAGnB,GA3EL,CAAApiB,IAAA,uBAAA2H,MAkFI,YAEShE,KAAK0e,eACN1e,KAAKoe,eACqB,IAA1Bpe,KAAK8d,QAAQjB,UAEb7c,KAAK2e,WAEZ,GA1FL,CAAAtiB,IAAA,OAAA2H,MAkGI,SAAKjE,GAAI,IAAA2D,EAAA1D,KACL,IAAKA,KAAKwY,YAAYzP,QAAQ,QAC1B,OAAO/I,KACXA,KAAKmZ,OAAS,IAAIyF,GAAO5e,KAAKmJ,IAAKnJ,KAAKuC,MACxC,IAAMuB,EAAS9D,KAAKmZ,OACd7X,EAAOtB,KACbA,KAAKwY,YAAc,UACnBxY,KAAK6e,eAAgB,EAErB,IAAMC,EAAiBlf,GAAGkE,EAAQ,QAAQ,WACtCxC,EAAKkL,SACLzM,GAAMA,OAGJgf,EAAWnf,GAAGkE,EAAQ,SAAS,SAACmC,GAClC3E,EAAK4J,UACL5J,EAAKkX,YAAc,SACnB9U,EAAKxC,aAAa,QAAS+E,GACvBlG,EACAA,EAAGkG,GAIH3E,EAAK0d,sBAEZ,IACD,IAAI,IAAUhf,KAAKye,SAAU,CACzB,IAAMhU,EAAUzK,KAAKye,SACL,IAAZhU,GACAqU,IAGJ,IAAMtF,EAAQxZ,KAAKyC,cAAa,WAC5Bqc,IACAhb,EAAOqE,QAEPrE,EAAOhD,KAAK,QAAS,IAAIsC,MAAM,WAJrB,GAKXqH,GACCzK,KAAKuC,KAAKkK,WACV+M,EAAM7M,QAEV3M,KAAKqY,KAAKnY,MAAK,WACXmC,aAAamX,KAEpB,CAGD,OAFAxZ,KAAKqY,KAAKnY,KAAK4e,GACf9e,KAAKqY,KAAKnY,KAAK6e,GACR/e,IACV,GAlJL,CAAA3D,IAAA,UAAA2H,MAyJI,SAAQjE,GACJ,OAAOC,KAAKmK,KAAKpK,EACpB,GA3JL,CAAA1D,IAAA,SAAA2H,MAiKI,WAEIhE,KAAKkL,UAELlL,KAAKwY,YAAc,OACnBxY,KAAKkB,aAAa,QAElB,IAAM4C,EAAS9D,KAAKmZ,OACpBnZ,KAAKqY,KAAKnY,KAAKN,GAAGkE,EAAQ,OAAQ9D,KAAKif,OAAOvc,KAAK1C,OAAQJ,GAAGkE,EAAQ,OAAQ9D,KAAKkf,OAAOxc,KAAK1C,OAAQJ,GAAGkE,EAAQ,QAAS9D,KAAKgN,QAAQtK,KAAK1C,OAAQJ,GAAGkE,EAAQ,QAAS9D,KAAK4M,QAAQlK,KAAK1C,OAAQJ,GAAGI,KAAKuV,QAAS,UAAWvV,KAAKmf,UAAUzc,KAAK1C,OACtP,GA1KL,CAAA3D,IAAA,SAAA2H,MAgLI,WACIhE,KAAKkB,aAAa,OACrB,GAlLL,CAAA7E,IAAA,SAAA2H,MAwLI,SAAOxH,GACH,IACIwD,KAAKuV,QAAQmB,IAAIla,EAIpB,CAFD,MAAO6J,GACHrG,KAAK4M,QAAQ,cAAevG,EAC/B,CACJ,GA/LL,CAAAhK,IAAA,YAAA2H,MAqMI,SAAUS,GAAQ,IAAAmD,EAAA5H,KAEdwL,IAAS,WACL5D,EAAK1G,aAAa,SAAUuD,KAC7BzE,KAAKyC,aACX,GA1ML,CAAApG,IAAA,UAAA2H,MAgNI,SAAQiC,GACJjG,KAAKkB,aAAa,QAAS+E,EAC9B,GAlNL,CAAA5J,IAAA,SAAA2H,MAyNI,SAAO4S,EAAKrU,GACR,IAAIuB,EAAS9D,KAAKud,KAAK3G,GAQvB,OAPK9S,EAII9D,KAAKoY,eAAiBtU,EAAOsb,QAClCtb,EAAOqT,WAJPrT,EAAS,IAAI0K,GAAOxO,KAAM4W,EAAKrU,GAC/BvC,KAAKud,KAAK3G,GAAO9S,GAKdA,CACV,GAnOL,CAAAzH,IAAA,WAAA2H,MA0OI,SAASF,GAEL,IADA,IACAub,EAAA,EAAAC,EADatjB,OAAOG,KAAK6D,KAAKud,MACN8B,EAAAC,EAAAphB,OAAAmhB,IAAA,CAAnB,IAAMzI,EAAN0I,EAAAD,GAED,GADerf,KAAKud,KAAK3G,GACdwI,OACP,MAEP,CACDpf,KAAKuf,QACR,GAnPL,CAAAljB,IAAA,UAAA2H,MA0PI,SAAQS,GAEJ,IADA,IAAMqD,EAAiB9H,KAAKie,QAAQjZ,OAAOP,GAClCxG,EAAI,EAAGA,EAAI6J,EAAe5J,OAAQD,IACvC+B,KAAKmZ,OAAO3U,MAAMsD,EAAe7J,GAAIwG,EAAOmN,QAEnD,GA/PL,CAAAvV,IAAA,UAAA2H,MAqQI,WACIhE,KAAKqY,KAAKjc,SAAQ,SAAC8f,GAAD,OAAgBA,OAClClc,KAAKqY,KAAKna,OAAS,EACnB8B,KAAKuV,QAAQuB,SAChB,GAzQL,CAAAza,IAAA,SAAA2H,MA+QI,WACIhE,KAAK6e,eAAgB,EACrB7e,KAAK0e,eAAgB,EACrB1e,KAAK4M,QAAQ,gBACT5M,KAAKmZ,QACLnZ,KAAKmZ,OAAOhR,OACnB,GArRL,CAAA9L,IAAA,aAAA2H,MA2RI,WACI,OAAOhE,KAAKuf,QACf,GA7RL,CAAAljB,IAAA,UAAA2H,MAmSI,SAAQlB,EAAQC,GACZ/C,KAAKkL,UACLlL,KAAK8d,QAAQZ,QACbld,KAAKwY,YAAc,SACnBxY,KAAKkB,aAAa,QAAS4B,EAAQC,GAC/B/C,KAAKoe,gBAAkBpe,KAAK6e,eAC5B7e,KAAK2e,WAEZ,GA3SL,CAAAtiB,IAAA,YAAA2H,MAiTI,WAAY,IAAAkE,EAAAlI,KACR,GAAIA,KAAK0e,eAAiB1e,KAAK6e,cAC3B,OAAO7e,KACX,IAAMsB,EAAOtB,KACb,GAAIA,KAAK8d,QAAQjB,UAAY7c,KAAKqe,sBAC9Bre,KAAK8d,QAAQZ,QACbld,KAAKkB,aAAa,oBAClBlB,KAAK0e,eAAgB,MAEpB,CACD,IAAMc,EAAQxf,KAAK8d,QAAQhB,WAC3B9c,KAAK0e,eAAgB,EACrB,IAAMlF,EAAQxZ,KAAKyC,cAAa,WACxBnB,EAAKud,gBAET3W,EAAKhH,aAAa,oBAAqBI,EAAKwc,QAAQjB,UAEhDvb,EAAKud,eAETvd,EAAK6I,MAAK,SAAClE,GACHA,GACA3E,EAAKod,eAAgB,EACrBpd,EAAKqd,YACLzW,EAAKhH,aAAa,kBAAmB+E,IAGrC3E,EAAKme,iBAdH,GAiBXD,GACCxf,KAAKuC,KAAKkK,WACV+M,EAAM7M,QAEV3M,KAAKqY,KAAKnY,MAAK,WACXmC,aAAamX,KAEpB,CACJ,GAtVL,CAAAnd,IAAA,cAAA2H,MA4VI,WACI,IAAM0b,EAAU1f,KAAK8d,QAAQjB,SAC7B7c,KAAK0e,eAAgB,EACrB1e,KAAK8d,QAAQZ,QACbld,KAAKkB,aAAa,YAAawe,EAClC,KAjWLpC,CAAA,CAAA,CAA6B5d,GCAvBigB,GAAQ,CAAA,EACd,SAAS5hB,GAAOoL,EAAK5G,GACE,WAAfqd,EAAOzW,KACP5G,EAAO4G,EACPA,OAAMY,GAGV,IASIyN,EATEqI,ECHH,SAAa1W,GAAqB,IAAhBH,yDAAO,GAAI8W,EAAKvf,UAAArC,OAAA,EAAAqC,UAAA,QAAAwJ,EACjC5M,EAAMgM,EAEV2W,EAAMA,GAA4B,oBAAbhZ,UAA4BA,SAC7C,MAAQqC,IACRA,EAAM2W,EAAI9Y,SAAW,KAAO8Y,EAAIhS,MAEjB,iBAAR3E,IACH,MAAQA,EAAI3K,OAAO,KAEf2K,EADA,MAAQA,EAAI3K,OAAO,GACbshB,EAAI9Y,SAAWmC,EAGf2W,EAAIhS,KAAO3E,GAGpB,sBAAsB4W,KAAK5W,KAExBA,OADA,IAAuB2W,EACjBA,EAAI9Y,SAAW,KAAOmC,EAGtB,WAAaA,GAI3BhM,EAAMoQ,GAAMpE,IAGXhM,EAAI8J,OACD,cAAc8Y,KAAK5iB,EAAI6J,UACvB7J,EAAI8J,KAAO,KAEN,eAAe8Y,KAAK5iB,EAAI6J,YAC7B7J,EAAI8J,KAAO,QAGnB9J,EAAI6L,KAAO7L,EAAI6L,MAAQ,IACvB,IACM8E,GADkC,IAA3B3Q,EAAI2Q,KAAK/E,QAAQ,KACV,IAAM5L,EAAI2Q,KAAO,IAAM3Q,EAAI2Q,KAS/C,OAPA3Q,EAAIiS,GAAKjS,EAAI6J,SAAW,MAAQ8G,EAAO,IAAM3Q,EAAI8J,KAAO+B,EAExD7L,EAAI6iB,KACA7iB,EAAI6J,SACA,MACA8G,GACCgS,GAAOA,EAAI7Y,OAAS9J,EAAI8J,KAAO,GAAK,IAAM9J,EAAI8J,MAChD9J,CACV,CD7CkB8iB,CAAI9W,GADnB5G,EAAOA,GAAQ,IACcyG,MAAQ,cAC/B6E,EAASgS,EAAOhS,OAChBuB,EAAKyQ,EAAOzQ,GACZpG,EAAO6W,EAAO7W,KACdkX,EAAgBP,GAAMvQ,IAAOpG,KAAQ2W,GAAMvQ,GAAN,KAkB3C,OAjBsB7M,EAAK4d,UACvB5d,EAAK,0BACL,IAAUA,EAAK6d,WACfF,EAGA1I,EAAK,IAAI8F,GAAQzP,EAAQtL,IAGpBod,GAAMvQ,KACPuQ,GAAMvQ,GAAM,IAAIkO,GAAQzP,EAAQtL,IAEpCiV,EAAKmI,GAAMvQ,IAEXyQ,EAAOhc,QAAUtB,EAAKsB,QACtBtB,EAAKsB,MAAQgc,EAAOzR,UAEjBoJ,EAAG1T,OAAO+b,EAAO7W,KAAMzG,EACjC,QAGD0G,EAAclL,GAAQ,CAClBuf,QAAAA,GACA9O,OAAAA,GACAgJ,GAAIzZ,GACJoZ,QAASpZ"}