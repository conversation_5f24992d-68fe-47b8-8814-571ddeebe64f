{"version": 3, "file": "socket.io.js", "sources": ["../node_modules/engine.io-parser/build/esm/commons.js", "../node_modules/engine.io-parser/build/esm/encodePacket.browser.js", "../node_modules/engine.io-parser/build/esm/contrib/base64-arraybuffer.js", "../node_modules/engine.io-parser/build/esm/decodePacket.browser.js", "../node_modules/engine.io-parser/build/esm/index.js", "../node_modules/@socket.io/component-emitter/index.mjs", "../node_modules/engine.io-client/build/esm/globalThis.browser.js", "../node_modules/engine.io-client/build/esm/util.js", "../node_modules/engine.io-client/build/esm/transport.js", "../node_modules/engine.io-client/build/esm/contrib/yeast.js", "../node_modules/engine.io-client/build/esm/contrib/parseqs.js", "../node_modules/engine.io-client/build/esm/contrib/has-cors.js", "../node_modules/engine.io-client/build/esm/transports/xmlhttprequest.browser.js", "../node_modules/engine.io-client/build/esm/transports/polling.js", "../node_modules/engine.io-client/build/esm/transports/websocket-constructor.browser.js", "../node_modules/engine.io-client/build/esm/transports/websocket.js", "../node_modules/engine.io-client/build/esm/transports/index.js", "../node_modules/engine.io-client/build/esm/contrib/parseuri.js", "../node_modules/engine.io-client/build/esm/socket.js", "../node_modules/engine.io-client/build/esm/index.js", "../build/esm/url.js", "../node_modules/socket.io-parser/build/esm/is-binary.js", "../node_modules/socket.io-parser/build/esm/binary.js", "../node_modules/socket.io-parser/build/esm/index.js", "../build/esm/on.js", "../build/esm/socket.js", "../build/esm/contrib/backo2.js", "../build/esm/manager.js", "../build/esm/index.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + content);\n    };\n    return fileReader.readAsDataURL(data);\n};\nexport default encodePacket;\n", "const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = (arraybuffer) => {\n    let bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nexport const decode = (base64) => {\n    let bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    const arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType)\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1)\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type]\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            return data instanceof ArrayBuffer ? new Blob([data]) : data;\n        case \"arraybuffer\":\n        default:\n            return data; // assuming the data is already an ArrayBuffer\n    }\n};\nexport default decodePacket;\n", "import encodePacket from \"./encodePacket.js\";\nimport decodePacket from \"./decodePacket.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, encodedPacket => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload };\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\n", "import { globalThisShim as globalThis } from \"./globalThis.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nclass TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n}\n", "// imported from https://github.com/unshiftio/yeast\n'use strict';\nconst alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split(''), length = 64, map = {};\nlet seed = 0, i = 0, prev;\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nexport function encode(num) {\n    let encoded = '';\n    do {\n        encoded = alphabet[num % length] + encoded;\n        num = Math.floor(num / length);\n    } while (num > 0);\n    return encoded;\n}\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nexport function decode(str) {\n    let decoded = 0;\n    for (i = 0; i < str.length; i++) {\n        decoded = decoded * length + map[str.charAt(i)];\n    }\n    return decoded;\n}\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nexport function yeast() {\n    const now = encode(+new Date());\n    if (now !== prev)\n        return seed = 0, prev = now;\n    return now + '.' + encode(seed++);\n}\n//\n// Map each character to its index.\n//\nfor (; i < length; i++)\n    map[alphabet[i]] = i;\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "// browser shim for xmlhttprequest module\nimport { hasCORS } from \"../contrib/has-cors.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nexport function XHR(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { encode } from \"../contrib/parseqs.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nimport { XHR as XMLHttpRequest } from \"./xmlhttprequest.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nfunction empty() { }\nconst hasXHR2 = (function () {\n    const xhr = new XMLHttpRequest({\n        xdomain: false,\n    });\n    return null != xhr.responseType;\n})();\nexport class Polling extends Transport {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        this.polling = false;\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n            this.xs = opts.secure !== isSSL;\n        }\n        /**\n         * XHR supports binary\n         */\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this.poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this.polling || !this.writable) {\n            let total = 0;\n            if (this.polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    poll() {\n        this.polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this.polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this.poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        let query = this.query || {};\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        let port = \"\";\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        // avoid port if default for schema\n        if (this.opts.port &&\n            ((\"https\" === schema && Number(this.opts.port) !== 443) ||\n                (\"http\" === schema && Number(this.opts.port) !== 80))) {\n            port = \":\" + this.opts.port;\n        }\n        const encodedQuery = encode(query);\n        const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n        return (schema +\n            \"://\" +\n            (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n            port +\n            this.opts.path +\n            (encodedQuery.length ? \"?\" + encodedQuery : \"\"));\n    }\n    /**\n     * Creates a request.\n     *\n     * @param {String} method\n     * @private\n     */\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd, xs: this.xs }, this.opts);\n        return new Request(this.uri(), opts);\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(uri, opts) {\n        super();\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.method = opts.method || \"GET\";\n        this.uri = uri;\n        this.async = false !== opts.async;\n        this.data = undefined !== opts.data ? opts.data : null;\n        this.create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    create() {\n        const opts = pick(this.opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this.opts.xd;\n        opts.xscheme = !!this.opts.xs;\n        const xhr = (this.xhr = new XMLHttpRequest(opts));\n        try {\n            xhr.open(this.method, this.uri, this.async);\n            try {\n                if (this.opts.extraHeaders) {\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this.opts.extraHeaders) {\n                        if (this.opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this.method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this.opts.withCredentials;\n            }\n            if (this.opts.requestTimeout) {\n                xhr.timeout = this.opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this.onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this.data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this.onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this.index = Request.requestsCount++;\n            Request.requests[this.index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    onError(err) {\n        this.emitReserved(\"error\", err, this.xhr);\n        this.cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    cleanup(fromError) {\n        if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n            return;\n        }\n        this.xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this.xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this.index];\n        }\n        this.xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    onLoad() {\n        const data = this.xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this.cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this.cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\n", "import { globalThisShim as globalThis } from \"../globalThis.js\";\nexport const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const WebSocket = globalThis.WebSocket || globalThis.MozWebSocket;\nexport const usingBrowserWebSocket = true;\nexport const defaultBinaryType = \"arraybuffer\";\n", "import { Transport } from \"../transport.js\";\nimport { encode } from \"../contrib/parseqs.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { pick } from \"../util.js\";\nimport { defaultBinaryType, nextTick, usingBrowserWebSocket, WebSocket, } from \"./websocket-constructor.js\";\nimport { encodePacket } from \"engine.io-parser\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class WS extends Transport {\n    /**\n     * WebSocket transport constructor.\n     *\n     * @param {Object} opts - connection options\n     * @protected\n     */\n    constructor(opts) {\n        super(opts);\n        this.supportsBinary = !opts.forceBase64;\n    }\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        if (!this.check()) {\n            // let probe timeout\n            return;\n        }\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws =\n                usingBrowserWebSocket && !isReactNative\n                    ? protocols\n                        ? new WebSocket(uri, protocols)\n                        : new WebSocket(uri)\n                    : new WebSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType || defaultBinaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // always create a new object (GH-437)\n                const opts = {};\n                if (!usingBrowserWebSocket) {\n                    if (packet.options) {\n                        opts.compress = packet.options.compress;\n                    }\n                    if (this.opts.perMessageDeflate) {\n                        const len = \n                        // @ts-ignore\n                        \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n                        if (len < this.opts.perMessageDeflate.threshold) {\n                            opts.compress = false;\n                        }\n                    }\n                }\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    if (usingBrowserWebSocket) {\n                        // TypeError is thrown when passing the second argument on Safari\n                        this.ws.send(data);\n                    }\n                    else {\n                        this.ws.send(data, opts);\n                    }\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        let query = this.query || {};\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        let port = \"\";\n        // avoid port if default for schema\n        if (this.opts.port &&\n            ((\"wss\" === schema && Number(this.opts.port) !== 443) ||\n                (\"ws\" === schema && Number(this.opts.port) !== 80))) {\n            port = \":\" + this.opts.port;\n        }\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        const encodedQuery = encode(query);\n        const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n        return (schema +\n            \"://\" +\n            (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n            port +\n            this.opts.path +\n            (encodedQuery.length ? \"?\" + encodedQuery : \"\"));\n    }\n    /**\n     * Feature detection for WebSocket.\n     *\n     * @return {Boolean} whether this transport is available.\n     * @private\n     */\n    check() {\n        return !!WebSocket;\n    }\n}\n", "import { Polling } from \"./polling.js\";\nimport { WS } from \"./websocket.js\";\nexport const transports = {\n    websocket: WS,\n    polling: Polling,\n};\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nexport class Socket extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts = {}) {\n        super();\n        this.writeBuffer = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            uri = parse(uri);\n            opts.hostname = uri.host;\n            opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n            opts.port = uri.port;\n            if (uri.query)\n                opts.query = uri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = opts.transports || [\"polling\", \"websocket\"];\n        this.writeBuffer = [];\n        this.prevBufferLen = 0;\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: true,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        // set on handshake\n        this.id = null;\n        this.upgrades = null;\n        this.pingInterval = null;\n        this.pingTimeout = null;\n        // set on heartbeat\n        this.pingTimeoutTimer = null;\n        if (typeof addEventListener === \"function\") {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this.beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this.offlineEventListener = () => {\n                    this.onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                addEventListener(\"offline\", this.offlineEventListener, false);\n            }\n        }\n        this.open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts.transportOptions[name], this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        });\n        return new transports[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    open() {\n        let transport;\n        if (this.opts.rememberUpgrade &&\n            Socket.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1) {\n            transport = \"websocket\";\n        }\n        else if (0 === this.transports.length) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        else {\n            transport = this.transports[0];\n        }\n        this.readyState = \"opening\";\n        // Retry with the next transport if the transport is disabled (jsonp: false)\n        try {\n            transport = this.createTransport(transport);\n        }\n        catch (e) {\n            this.transports.shift();\n            this.open();\n            return;\n        }\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this.onDrain.bind(this))\n            .on(\"packet\", this.onPacket.bind(this))\n            .on(\"error\", this.onError.bind(this))\n            .on(\"close\", (reason) => this.onClose(\"transport close\", reason));\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        Socket.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        transport.open();\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n        // we check for `readyState` in case an `open`\n        // listener already closed the socket\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            let i = 0;\n            const l = this.upgrades.length;\n            for (; i < l; i++) {\n                this.probe(this.upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this.resetPingTimeout();\n                    this.sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this.onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this.upgrades = this.filterUpgrades(data.upgrades);\n        this.pingInterval = data.pingInterval;\n        this.pingTimeout = data.pingTimeout;\n        this.maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this.resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    resetPingTimeout() {\n        this.clearTimeoutFn(this.pingTimeoutTimer);\n        this.pingTimeoutTimer = this.setTimeoutFn(() => {\n            this.onClose(\"ping timeout\");\n        }, this.pingInterval + this.pingTimeout);\n        if (this.opts.autoUnref) {\n            this.pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    onDrain() {\n        this.writeBuffer.splice(0, this.prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this.prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this.getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this.prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    getWritablePackets() {\n        const shouldCheckPayloadSize = this.maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this.maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    send(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this.onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    onError(err) {\n        Socket.priorWebsocketSuccess = false;\n        this.emitReserved(\"error\", err);\n        this.onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this.pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (typeof removeEventListener === \"function\") {\n                removeEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n                removeEventListener(\"offline\", this.offlineEventListener, false);\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this.prevBufferLen = 0;\n        }\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        let i = 0;\n        const j = upgrades.length;\n        for (; i < j; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\nSocket.protocol = protocol;\n", "import { Socket } from \"./socket.js\";\nexport { Socket };\nexport const protocol = Socket.protocol;\nexport { Transport } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\nexport { nextTick } from \"./transports/websocket-constructor.js\";\n", "import { parse } from \"engine.io-client\";\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nexport function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        obj = parse(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n", "const withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeFile = typeof File === \"function\" ||\n    (typeof File !== \"undefined\" &&\n        toString.call(File) === \"[object FileConstructor]\");\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nexport function isBinary(obj) {\n    return ((withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj))) ||\n        (withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File));\n}\nexport function hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for (let i = 0, l = obj.length; i < l; i++) {\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON &&\n        typeof obj.toJSON === \"function\" &&\n        arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\n", "import { isBinary } from \"./is-binary.js\";\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nexport function deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return { packet: pack, buffers: buffers };\n}\nfunction _deconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (isBinary(data)) {\n        const placeholder = { _placeholder: true, num: buffers.length };\n        buffers.push(data);\n        return placeholder;\n    }\n    else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for (let i = 0; i < data.length; i++) {\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    }\n    else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nexport function reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    delete packet.attachments; // no longer useful\n    return packet;\n}\nfunction _reconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (data && data._placeholder === true) {\n        const isIndexValid = typeof data.num === \"number\" &&\n            data.num >= 0 &&\n            data.num < buffers.length;\n        if (isIndexValid) {\n            return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n        }\n        else {\n            throw new Error(\"illegal attachments\");\n        }\n    }\n    else if (Array.isArray(data)) {\n        for (let i = 0; i < data.length; i++) {\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    }\n    else if (typeof data === \"object\") {\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n", "import { Emitter } from \"@socket.io/component-emitter\";\nimport { deconstructPacket, reconstructPacket } from \"./binary.js\";\nimport { isBinary, hasBinary } from \"./is-binary.js\";\n/**\n * Protocol version.\n *\n * @public\n */\nexport const protocol = 5;\nexport var PacketType;\n(function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType || (PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nexport class Encoder {\n    /**\n     * Encoder constructor\n     *\n     * @param {function} replacer - custom replacer to pass down to JSON.parse\n     */\n    constructor(replacer) {\n        this.replacer = replacer;\n    }\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */\n    encode(obj) {\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if (hasBinary(obj)) {\n                return this.encodeAsBinary({\n                    type: obj.type === PacketType.EVENT\n                        ? PacketType.BINARY_EVENT\n                        : PacketType.BINARY_ACK,\n                    nsp: obj.nsp,\n                    data: obj.data,\n                    id: obj.id,\n                });\n            }\n        }\n        return [this.encodeAsString(obj)];\n    }\n    /**\n     * Encode packet as string.\n     */\n    encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT ||\n            obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data, this.replacer);\n        }\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */\n    encodeAsBinary(obj) {\n        const deconstruction = deconstructPacket(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nexport class Decoder extends Emitter {\n    /**\n     * Decoder constructor\n     *\n     * @param {function} reviver - custom reviver to pass down to JSON.stringify\n     */\n    constructor(reviver) {\n        super();\n        this.reviver = reviver;\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */\n    add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            if (this.reconstructor) {\n                throw new Error(\"got plaintext data when reconstructing a packet\");\n            }\n            packet = this.decodeString(obj);\n            const isBinaryEvent = packet.type === PacketType.BINARY_EVENT;\n            if (isBinaryEvent || packet.type === PacketType.BINARY_ACK) {\n                packet.type = isBinaryEvent ? PacketType.EVENT : PacketType.ACK;\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n            else {\n                // non-binary full packet\n                super.emitReserved(\"decoded\", packet);\n            }\n        }\n        else if (isBinary(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            }\n            else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n        }\n        else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */\n    decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0)),\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT ||\n            p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while (str.charAt(++i) !== \"-\" && i != str.length) { }\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (\",\" === c)\n                    break;\n                if (i === str.length)\n                    break;\n            }\n            p.nsp = str.substring(start, i);\n        }\n        else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length)\n                    break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = this.tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            }\n            else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n        return p;\n    }\n    tryParse(str) {\n        try {\n            return JSON.parse(str, this.reviver);\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    static isPayloadValid(type, payload) {\n        switch (type) {\n            case PacketType.CONNECT:\n                return typeof payload === \"object\";\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || typeof payload === \"object\";\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return Array.isArray(payload) && payload.length > 0;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */\n    destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n            this.reconstructor = null;\n        }\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n    constructor(packet) {\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */\n    takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = reconstructPacket(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */\n    finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\n", "export function on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n", "import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */\nexport class Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     */\n    constructor(io, nsp, opts) {\n        super();\n        /**\n         * Whether the socket is currently connected to the server.\n         *\n         * @example\n         * const socket = io();\n         *\n         * socket.on(\"connect\", () => {\n         *   console.log(socket.connected); // true\n         * });\n         *\n         * socket.on(\"disconnect\", () => {\n         *   console.log(socket.connected); // false\n         * });\n         */\n        this.connected = false;\n        /**\n         * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n         * be transmitted by the server.\n         */\n        this.recovered = false;\n        /**\n         * Buffer for packets received before the CONNECT packet\n         */\n        this.receiveBuffer = [];\n        /**\n         * Buffer for packets that will be sent once the socket is connected\n         */\n        this.sendBuffer = [];\n        /**\n         * The queue of packets to be sent with retry in case of failure.\n         *\n         * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n         * @private\n         */\n        this._queue = [];\n        /**\n         * A sequence to generate the ID of the {@link QueuedPacket}.\n         * @private\n         */\n        this._queueSeq = 0;\n        this.ids = 0;\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        this._opts = Object.assign({}, opts);\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.disconnected); // false\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.disconnected); // true\n     * });\n     */\n    get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on(io, \"open\", this.onopen.bind(this)),\n            on(io, \"packet\", this.onpacket.bind(this)),\n            on(io, \"error\", this.onerror.bind(this)),\n            on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n     *\n     * @example\n     * const socket = io();\n     *\n     * console.log(socket.active); // true\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   if (reason === \"io server disconnect\") {\n     *     // the disconnection was initiated by the server, you need to manually reconnect\n     *     console.log(socket.active); // false\n     *   }\n     *   // else the socket will automatically try to reconnect\n     *   console.log(socket.active); // true\n     * });\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @example\n     * const socket = io({\n     *   autoConnect: false\n     * });\n     *\n     * socket.connect();\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for {@link connect()}.\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * This method mimics the WebSocket.send() method.\n     *\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n     *\n     * @example\n     * socket.send(\"hello\");\n     *\n     * // this is equivalent to\n     * socket.emit(\"message\", \"hello\");\n     *\n     * @return self\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @example\n     * socket.emit(\"hello\", \"world\");\n     *\n     * // all serializable datastructures are supported (no need to call JSON.stringify)\n     * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n     *\n     * // with an acknowledgement from the server\n     * socket.emit(\"hello\", \"world\", (val) => {\n     *   // ...\n     * });\n     *\n     * @return self\n     */\n    emit(ev, ...args) {\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n            this._addToQueue(args);\n            return this;\n        }\n        const packet = {\n            type: PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = this.io.engine &&\n            this.io.engine.transport &&\n            this.io.engine.transport.writable;\n        const discardPacket = this.flags.volatile && (!isTransportWritable || !this.connected);\n        if (discardPacket) {\n        }\n        else if (this.connected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */\n    _registerAckCallback(id, ack) {\n        var _a;\n        const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(() => {\n            delete this.acks[id];\n            for (let i = 0; i < this.sendBuffer.length; i++) {\n                if (this.sendBuffer[i].id === id) {\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        this.acks[id] = (...args) => {\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, [null, ...args]);\n        };\n    }\n    /**\n     * Emits an event and waits for an acknowledgement\n     *\n     * @example\n     * // without timeout\n     * const response = await socket.emitWithAck(\"hello\", \"world\");\n     *\n     * // with a specific timeout\n     * try {\n     *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n     * } catch (err) {\n     *   // the server did not acknowledge the event in the given delay\n     * }\n     *\n     * @return a Promise that will be fulfilled when the server acknowledges the event\n     */\n    emitWithAck(ev, ...args) {\n        // the timeout flag is optional\n        const withErr = this.flags.timeout !== undefined || this._opts.ackTimeout !== undefined;\n        return new Promise((resolve, reject) => {\n            args.push((arg1, arg2) => {\n                if (withErr) {\n                    return arg1 ? reject(arg1) : resolve(arg2);\n                }\n                else {\n                    return resolve(arg1);\n                }\n            });\n            this.emit(ev, ...args);\n        });\n    }\n    /**\n     * Add the packet to the queue.\n     * @param args\n     * @private\n     */\n    _addToQueue(args) {\n        let ack;\n        if (typeof args[args.length - 1] === \"function\") {\n            ack = args.pop();\n        }\n        const packet = {\n            id: this._queueSeq++,\n            tryCount: 0,\n            pending: false,\n            args,\n            flags: Object.assign({ fromQueue: true }, this.flags),\n        };\n        args.push((err, ...responseArgs) => {\n            if (packet !== this._queue[0]) {\n                // the packet has already been acknowledged\n                return;\n            }\n            const hasError = err !== null;\n            if (hasError) {\n                if (packet.tryCount > this._opts.retries) {\n                    this._queue.shift();\n                    if (ack) {\n                        ack(err);\n                    }\n                }\n            }\n            else {\n                this._queue.shift();\n                if (ack) {\n                    ack(null, ...responseArgs);\n                }\n            }\n            packet.pending = false;\n            return this._drainQueue();\n        });\n        this._queue.push(packet);\n        this._drainQueue();\n    }\n    /**\n     * Send the first packet of the queue, and wait for an acknowledgement from the server.\n     * @param force - whether to resend a packet that has not been acknowledged yet\n     *\n     * @private\n     */\n    _drainQueue(force = false) {\n        if (!this.connected || this._queue.length === 0) {\n            return;\n        }\n        const packet = this._queue[0];\n        if (packet.pending && !force) {\n            return;\n        }\n        packet.pending = true;\n        packet.tryCount++;\n        this.flags = packet.flags;\n        this.emit.apply(this, packet.args);\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this._sendConnectPacket(data);\n            });\n        }\n        else {\n            this._sendConnectPacket(this.auth);\n        }\n    }\n    /**\n     * Sends a CONNECT packet to initiate the Socket.IO session.\n     *\n     * @param data\n     * @private\n     */\n    _sendConnectPacket(data) {\n        this.packet({\n            type: PacketType.CONNECT,\n            data: this._pid\n                ? Object.assign({ pid: this._pid, offset: this._lastOffset }, data)\n                : data,\n        });\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */\n    onclose(reason, description) {\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    this.onconnect(packet.data.sid, packet.data.pid);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        if (null != packet.id) {\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n        if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n            this._lastOffset = args[args.length - 1];\n        }\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            self.packet({\n                type: PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowlegement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (\"function\" === typeof ack) {\n            ack.apply(this, packet.data);\n            delete this.acks[packet.id];\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id, pid) {\n        this.id = id;\n        this.recovered = pid && this._pid === pid;\n        this._pid = pid; // defined only if connection state recovery is enabled\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n        this._drainQueue(true);\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n     *\n     * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   // console.log(reason); prints \"io client disconnect\"\n     * });\n     *\n     * socket.disconnect();\n     *\n     * @return self\n     */\n    disconnect() {\n        if (this.connected) {\n            this.packet({ type: PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for {@link disconnect()}.\n     *\n     * @return self\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @example\n     * socket.compress(false).emit(\"hello\");\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @example\n     * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n     *\n     * @returns self\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * @example\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     *\n     * @returns self\n     */\n    timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @example\n     * socket.onAny((event, ...args) => {\n     *   console.log(`got ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @example\n     * socket.prependAny((event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * }\n     *\n     * socket.onAny(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAny(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAny();\n     *\n     * @param listener\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * }\n     *\n     * socket.onAnyOutgoing(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAnyOutgoing(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAnyOutgoing();\n     *\n     * @param [listener] - the catch-all listener (optional)\n     */\n    offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */\n    notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n", "/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\nexport function Backoff(opts) {\n    opts = opts || {};\n    this.ms = opts.min || 100;\n    this.max = opts.max || 10000;\n    this.factor = opts.factor || 2;\n    this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n    this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\nBackoff.prototype.duration = function () {\n    var ms = this.ms * Math.pow(this.factor, this.attempts++);\n    if (this.jitter) {\n        var rand = Math.random();\n        var deviation = Math.floor(rand * this.jitter * ms);\n        ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n    }\n    return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\nBackoff.prototype.reset = function () {\n    this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */\nBackoff.prototype.setMin = function (min) {\n    this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */\nBackoff.prototype.setMax = function (max) {\n    this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */\nBackoff.prototype.setJitter = function (jitter) {\n    this.jitter = jitter;\n};\n", "import { Socket as Engine, installTimerFunctions, nextTick, } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\nexport class Manager extends Emitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        installTimerFunctions(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n        this.engine = new Engine(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        // emit `error`\n        const errorSub = on(socket, \"error\", (err) => {\n            self.cleanup();\n            self._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                self.maybeReconnectOnOpen();\n            }\n        });\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            if (timeout === 0) {\n                openSubDestroy(); // prevents a race condition with the 'open' event\n            }\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n                openSubDestroy();\n                socket.close();\n                // @ts-ignore\n                socket.emit(\"error\", new Error(\"timeout\"));\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        try {\n            this.decoder.add(data);\n        }\n        catch (e) {\n            this.onclose(\"parse error\", e);\n        }\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n        nextTick(() => {\n            this.emitReserved(\"packet\", packet);\n        }, this.setTimeoutFn);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        else if (this._autoConnect && !socket.active) {\n            socket.connect();\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n        if (this.engine)\n            this.engine.close();\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called upon engine close.\n     *\n     * @private\n     */\n    onclose(reason, description) {\n        this.cleanup();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n", "import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\n/**\n * Managers cache.\n */\nconst cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n        io = new Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n            cache[id] = new Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager,\n    Socket,\n    io: lookup,\n    connect: lookup,\n});\n/**\n * Protocol version.\n *\n * @public\n */\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default, };\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "obj", "buffer", "encodePacket", "supportsBinary", "callback", "encodeBlobAsBase64", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "chars", "lookup", "Uint8Array", "i", "length", "charCodeAt", "decode", "base64", "bufferLength", "len", "p", "encoded1", "encoded2", "encoded3", "encoded4", "arraybuffer", "bytes", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "packetType", "decoded", "SEPARATOR", "String", "fromCharCode", "encodePayload", "packets", "encodedPackets", "Array", "count", "packet", "join", "decodePayload", "encodedPayload", "decodedPacket", "push", "protocol", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "_callbacks", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "callbacks", "cb", "splice", "emit", "args", "slice", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "globalThisShim", "self", "window", "Function", "pick", "attr", "reduce", "acc", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "globalThis", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "setTimeoutFn", "bind", "clearTimeoutFn", "BASE64_OVERHEAD", "byteLength", "utf8Length", "Math", "ceil", "size", "str", "c", "l", "TransportError", "reason", "description", "context", "Error", "Transport", "writable", "query", "socket", "readyState", "doOpen", "doClose", "onClose", "write", "onPacket", "details", "onPause", "alphabet", "map", "seed", "prev", "encode", "num", "encoded", "floor", "yeast", "now", "Date", "encodeURIComponent", "qs", "qry", "pairs", "pair", "decodeURIComponent", "value", "XMLHttpRequest", "err", "hasCORS", "XHR", "xdomain", "e", "concat", "empty", "hasXHR2", "xhr", "responseType", "Polling", "polling", "location", "isSSL", "port", "xd", "hostname", "xs", "secure", "forceBase64", "poll", "pause", "total", "doPoll", "onOpen", "close", "doWrite", "schema", "timestampRequests", "timestampParam", "sid", "b64", "Number", "<PERSON><PERSON><PERSON><PERSON>", "ipv6", "indexOf", "path", "Request", "uri", "req", "request", "method", "xhrStatus", "onError", "onData", "pollXhr", "async", "undefined", "xscheme", "open", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "withCredentials", "requestTimeout", "timeout", "onreadystatechange", "status", "onLoad", "send", "document", "index", "requestsCount", "requests", "cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "terminationEvent", "nextTick", "isPromiseAvailable", "Promise", "resolve", "then", "WebSocket", "MozWebSocket", "usingBrowserWebSocket", "defaultBinaryType", "isReactNative", "navigator", "product", "toLowerCase", "WS", "check", "protocols", "headers", "ws", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "lastPacket", "transports", "websocket", "re", "parts", "parse", "src", "b", "replace", "m", "exec", "source", "host", "authority", "ipv6uri", "pathNames", "query<PERSON><PERSON>", "regx", "names", "$0", "$1", "$2", "Socket", "writeBuffer", "prevBufferLen", "agent", "upgrade", "rememberUpgrade", "addTrailingSlash", "rejectUnauthorized", "perMessageDeflate", "threshold", "transportOptions", "closeOnBeforeunload", "id", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "beforeunloadEventListener", "transport", "offlineEventListener", "name", "EIO", "priorWebsocketSuccess", "createTransport", "shift", "setTransport", "onDrain", "failed", "onTransportOpen", "msg", "upgrading", "flush", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "probe", "onHandshake", "JSON", "resetPingTimeout", "sendPacket", "code", "filterUpgrades", "maxPayload", "getWritablePackets", "shouldCheckPayloadSize", "payloadSize", "options", "compress", "cleanupAndClose", "waitForUpgrade", "filteredUpgrades", "j", "url", "loc", "test", "href", "withNativeFile", "File", "isBinary", "hasBinary", "toJSON", "isArray", "deconstructPacket", "buffers", "packetData", "pack", "_deconstructPacket", "attachments", "placeholder", "_placeholder", "newData", "reconstructPacket", "_reconstructPacket", "isIndexValid", "PacketType", "Encoder", "replacer", "EVENT", "ACK", "encodeAsBinary", "BINARY_EVENT", "BINARY_ACK", "nsp", "encodeAsString", "stringify", "deconstruction", "unshift", "Decoder", "reviver", "reconstructor", "decodeString", "isBinaryEvent", "BinaryReconstructor", "takeBinaryData", "start", "buf", "next", "payload", "try<PERSON><PERSON><PERSON>", "substr", "isPayloadValid", "finishedReconstruction", "CONNECT", "DISCONNECT", "CONNECT_ERROR", "reconPack", "binData", "subDestroy", "RESERVED_EVENTS", "freeze", "connect", "connect_error", "disconnect", "disconnecting", "newListener", "io", "connected", "recovered", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "_queue", "_queueSeq", "ids", "acks", "flags", "auth", "_opts", "_autoConnect", "subs", "onpacket", "subEvents", "_readyState", "retries", "fromQueue", "_addToQueue", "ack", "pop", "_registerAckCallback", "isTransportWritable", "engine", "discardPacket", "notifyOutgoingListeners", "_a", "ackTimeout", "timer", "withErr", "reject", "arg1", "arg2", "tryCount", "pending", "<PERSON><PERSON><PERSON><PERSON>", "responseArgs", "_drainQueue", "force", "_packet", "_sendConnectPacket", "_pid", "pid", "offset", "_lastOffset", "sameNamespace", "onconnect", "onevent", "onack", "ondisconnect", "destroy", "message", "emitEvent", "_anyListeners", "listener", "sent", "emitBuffered", "_anyOutgoingListeners", "Backoff", "ms", "min", "max", "factor", "jitter", "attempts", "duration", "pow", "rand", "random", "deviation", "reset", "setMin", "setMax", "setJitter", "Manager", "nsps", "reconnection", "reconnectionAttempts", "Infinity", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "_parser", "parser", "encoder", "decoder", "autoConnect", "v", "_reconnection", "_reconnectionAttempts", "_reconnectionDelay", "_randomizationFactor", "_reconnectionDelayMax", "_timeout", "_reconnecting", "reconnect", "Engine", "skipReconnect", "openSubDestroy", "errorSub", "maybeReconnectOnOpen", "onping", "ondata", "ondecoded", "add", "active", "_close", "delay", "onreconnect", "attempt", "cache", "parsed", "newConnection", "forceNew", "multiplex"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA,IAAMA,YAAY,GAAGC,MAAM,CAACC,MAAP,CAAc,IAAd,CAArB;;EACAF,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB,CAAA;EACAA,YAAY,CAAC,OAAD,CAAZ,GAAwB,GAAxB,CAAA;EACAA,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB,CAAA;EACAA,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB,CAAA;EACAA,YAAY,CAAC,SAAD,CAAZ,GAA0B,GAA1B,CAAA;EACAA,YAAY,CAAC,SAAD,CAAZ,GAA0B,GAA1B,CAAA;EACAA,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB,CAAA;EACA,IAAMG,oBAAoB,GAAGF,MAAM,CAACC,MAAP,CAAc,IAAd,CAA7B,CAAA;EACAD,MAAM,CAACG,IAAP,CAAYJ,YAAZ,EAA0BK,OAA1B,CAAkC,UAAAC,GAAG,EAAI;EACrCH,EAAAA,oBAAoB,CAACH,YAAY,CAACM,GAAD,CAAb,CAApB,GAA0CA,GAA1C,CAAA;EACH,CAFD,CAAA,CAAA;EAGA,IAAMC,YAAY,GAAG;EAAEC,EAAAA,IAAI,EAAE,OAAR;EAAiBC,EAAAA,IAAI,EAAE,cAAA;EAAvB,CAArB;;ECXA,IAAMC,gBAAc,GAAG,OAAOC,IAAP,KAAgB,UAAhB,IAClB,OAAOA,IAAP,KAAgB,WAAhB,IACGV,MAAM,CAACW,SAAP,CAAiBC,QAAjB,CAA0BC,IAA1B,CAA+BH,IAA/B,CAAA,KAAyC,0BAFjD,CAAA;EAGA,IAAMI,uBAAqB,GAAG,OAAOC,WAAP,KAAuB,UAArD;;EAEA,IAAMC,QAAM,GAAG,SAATA,MAAS,CAAAC,GAAG,EAAI;IAClB,OAAO,OAAOF,WAAW,CAACC,MAAnB,KAA8B,UAA9B,GACDD,WAAW,CAACC,MAAZ,CAAmBC,GAAnB,CADC,GAEDA,GAAG,IAAIA,GAAG,CAACC,MAAJ,YAAsBH,WAFnC,CAAA;EAGH,CAJD,CAAA;;EAKA,IAAMI,YAAY,GAAG,SAAfA,YAAe,OAAiBC,cAAjB,EAAiCC,QAAjC,EAA8C;IAAA,IAA3Cd,IAA2C,QAA3CA,IAA2C;QAArCC,IAAqC,QAArCA,IAAqC,CAAA;;EAC/D,EAAA,IAAIC,gBAAc,IAAID,IAAI,YAAYE,IAAtC,EAA4C;EACxC,IAAA,IAAIU,cAAJ,EAAoB;QAChB,OAAOC,QAAQ,CAACb,IAAD,CAAf,CAAA;EACH,KAFD,MAGK;EACD,MAAA,OAAOc,kBAAkB,CAACd,IAAD,EAAOa,QAAP,CAAzB,CAAA;EACH,KAAA;EACJ,GAPD,MAQK,IAAIP,uBAAqB,KACzBN,IAAI,YAAYO,WAAhB,IAA+BC,QAAM,CAACR,IAAD,CADZ,CAAzB,EAC8C;EAC/C,IAAA,IAAIY,cAAJ,EAAoB;QAChB,OAAOC,QAAQ,CAACb,IAAD,CAAf,CAAA;EACH,KAFD,MAGK;QACD,OAAOc,kBAAkB,CAAC,IAAIZ,IAAJ,CAAS,CAACF,IAAD,CAAT,CAAD,EAAmBa,QAAnB,CAAzB,CAAA;EACH,KAAA;EACJ,GAjB8D;;;IAmB/D,OAAOA,QAAQ,CAACtB,YAAY,CAACQ,IAAD,CAAZ,IAAsBC,IAAI,IAAI,EAA9B,CAAD,CAAf,CAAA;EACH,CApBD,CAAA;;EAqBA,IAAMc,kBAAkB,GAAG,SAArBA,kBAAqB,CAACd,IAAD,EAAOa,QAAP,EAAoB;EAC3C,EAAA,IAAME,UAAU,GAAG,IAAIC,UAAJ,EAAnB,CAAA;;IACAD,UAAU,CAACE,MAAX,GAAoB,YAAY;MAC5B,IAAMC,OAAO,GAAGH,UAAU,CAACI,MAAX,CAAkBC,KAAlB,CAAwB,GAAxB,CAA6B,CAAA,CAA7B,CAAhB,CAAA;MACAP,QAAQ,CAAC,GAAMK,GAAAA,OAAP,CAAR,CAAA;KAFJ,CAAA;;EAIA,EAAA,OAAOH,UAAU,CAACM,aAAX,CAAyBrB,IAAzB,CAAP,CAAA;EACH,CAPD;;EChCA,IAAMsB,KAAK,GAAG,kEAAd;;EAEA,IAAMC,QAAM,GAAG,OAAOC,UAAP,KAAsB,WAAtB,GAAoC,EAApC,GAAyC,IAAIA,UAAJ,CAAe,GAAf,CAAxD,CAAA;;EACA,KAAK,IAAIC,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAGH,KAAK,CAACI,MAA1B,EAAkCD,GAAC,EAAnC,EAAuC;IACnCF,QAAM,CAACD,KAAK,CAACK,UAAN,CAAiBF,GAAjB,CAAD,CAAN,GAA8BA,GAA9B,CAAA;EACH,CAAA;EAiBM,IAAMG,QAAM,GAAG,SAATA,MAAS,CAACC,MAAD,EAAY;EAC9B,EAAA,IAAIC,YAAY,GAAGD,MAAM,CAACH,MAAP,GAAgB,IAAnC;EAAA,MAAyCK,GAAG,GAAGF,MAAM,CAACH,MAAtD;EAAA,MAA8DD,CAA9D;QAAiEO,CAAC,GAAG,CAArE;EAAA,MAAwEC,QAAxE;EAAA,MAAkFC,QAAlF;EAAA,MAA4FC,QAA5F;EAAA,MAAsGC,QAAtG,CAAA;;IACA,IAAIP,MAAM,CAACA,MAAM,CAACH,MAAP,GAAgB,CAAjB,CAAN,KAA8B,GAAlC,EAAuC;MACnCI,YAAY,EAAA,CAAA;;MACZ,IAAID,MAAM,CAACA,MAAM,CAACH,MAAP,GAAgB,CAAjB,CAAN,KAA8B,GAAlC,EAAuC;QACnCI,YAAY,EAAA,CAAA;EACf,KAAA;EACJ,GAAA;;EACD,EAAA,IAAMO,WAAW,GAAG,IAAI9B,WAAJ,CAAgBuB,YAAhB,CAApB;EAAA,MAAmDQ,KAAK,GAAG,IAAId,UAAJ,CAAea,WAAf,CAA3D,CAAA;;IACA,KAAKZ,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGM,GAAhB,EAAqBN,CAAC,IAAI,CAA1B,EAA6B;MACzBQ,QAAQ,GAAGV,QAAM,CAACM,MAAM,CAACF,UAAP,CAAkBF,CAAlB,CAAD,CAAjB,CAAA;MACAS,QAAQ,GAAGX,QAAM,CAACM,MAAM,CAACF,UAAP,CAAkBF,CAAC,GAAG,CAAtB,CAAD,CAAjB,CAAA;MACAU,QAAQ,GAAGZ,QAAM,CAACM,MAAM,CAACF,UAAP,CAAkBF,CAAC,GAAG,CAAtB,CAAD,CAAjB,CAAA;MACAW,QAAQ,GAAGb,QAAM,CAACM,MAAM,CAACF,UAAP,CAAkBF,CAAC,GAAG,CAAtB,CAAD,CAAjB,CAAA;MACAa,KAAK,CAACN,CAAC,EAAF,CAAL,GAAcC,QAAQ,IAAI,CAAb,GAAmBC,QAAQ,IAAI,CAA5C,CAAA;EACAI,IAAAA,KAAK,CAACN,CAAC,EAAF,CAAL,GAAc,CAACE,QAAQ,GAAG,EAAZ,KAAmB,CAApB,GAA0BC,QAAQ,IAAI,CAAnD,CAAA;EACAG,IAAAA,KAAK,CAACN,CAAC,EAAF,CAAL,GAAc,CAACG,QAAQ,GAAG,CAAZ,KAAkB,CAAnB,GAAyBC,QAAQ,GAAG,EAAjD,CAAA;EACH,GAAA;;EACD,EAAA,OAAOC,WAAP,CAAA;EACH,CAnBM;;ECpBP,IAAM/B,uBAAqB,GAAG,OAAOC,WAAP,KAAuB,UAArD,CAAA;;EACA,IAAMgC,YAAY,GAAG,SAAfA,YAAe,CAACC,aAAD,EAAgBC,UAAhB,EAA+B;EAChD,EAAA,IAAI,OAAOD,aAAP,KAAyB,QAA7B,EAAuC;MACnC,OAAO;EACHzC,MAAAA,IAAI,EAAE,SADH;EAEHC,MAAAA,IAAI,EAAE0C,SAAS,CAACF,aAAD,EAAgBC,UAAhB,CAAA;OAFnB,CAAA;EAIH,GAAA;;EACD,EAAA,IAAM1C,IAAI,GAAGyC,aAAa,CAACG,MAAd,CAAqB,CAArB,CAAb,CAAA;;IACA,IAAI5C,IAAI,KAAK,GAAb,EAAkB;MACd,OAAO;EACHA,MAAAA,IAAI,EAAE,SADH;QAEHC,IAAI,EAAE4C,kBAAkB,CAACJ,aAAa,CAACK,SAAd,CAAwB,CAAxB,CAAD,EAA6BJ,UAA7B,CAAA;OAF5B,CAAA;EAIH,GAAA;;EACD,EAAA,IAAMK,UAAU,GAAGpD,oBAAoB,CAACK,IAAD,CAAvC,CAAA;;IACA,IAAI,CAAC+C,UAAL,EAAiB;EACb,IAAA,OAAOhD,YAAP,CAAA;EACH,GAAA;;EACD,EAAA,OAAO0C,aAAa,CAACd,MAAd,GAAuB,CAAvB,GACD;EACE3B,IAAAA,IAAI,EAAEL,oBAAoB,CAACK,IAAD,CAD5B;EAEEC,IAAAA,IAAI,EAAEwC,aAAa,CAACK,SAAd,CAAwB,CAAxB,CAAA;EAFR,GADC,GAKD;MACE9C,IAAI,EAAEL,oBAAoB,CAACK,IAAD,CAAA;KANlC,CAAA;EAQH,CA1BD,CAAA;;EA2BA,IAAM6C,kBAAkB,GAAG,SAArBA,kBAAqB,CAAC5C,IAAD,EAAOyC,UAAP,EAAsB;EAC7C,EAAA,IAAInC,uBAAJ,EAA2B;EACvB,IAAA,IAAMyC,OAAO,GAAGnB,QAAM,CAAC5B,IAAD,CAAtB,CAAA;EACA,IAAA,OAAO0C,SAAS,CAACK,OAAD,EAAUN,UAAV,CAAhB,CAAA;EACH,GAHD,MAIK;MACD,OAAO;EAAEZ,MAAAA,MAAM,EAAE,IAAV;EAAgB7B,MAAAA,IAAI,EAAJA,IAAAA;EAAhB,KAAP,CADC;EAEJ,GAAA;EACJ,CARD,CAAA;;EASA,IAAM0C,SAAS,GAAG,SAAZA,SAAY,CAAC1C,IAAD,EAAOyC,UAAP,EAAsB;EACpC,EAAA,QAAQA,UAAR;EACI,IAAA,KAAK,MAAL;EACI,MAAA,OAAOzC,IAAI,YAAYO,WAAhB,GAA8B,IAAIL,IAAJ,CAAS,CAACF,IAAD,CAAT,CAA9B,GAAiDA,IAAxD,CAAA;;EACJ,IAAA,KAAK,aAAL,CAAA;EACA,IAAA;EACI,MAAA,OAAOA,IAAP,CAAA;EAAa;EALrB,GAAA;EAOH,CARD;;ECrCA,IAAMgD,SAAS,GAAGC,MAAM,CAACC,YAAP,CAAoB,EAApB,CAAlB;;EACA,IAAMC,aAAa,GAAG,SAAhBA,aAAgB,CAACC,OAAD,EAAUvC,QAAV,EAAuB;EACzC;EACA,EAAA,IAAMa,MAAM,GAAG0B,OAAO,CAAC1B,MAAvB,CAAA;EACA,EAAA,IAAM2B,cAAc,GAAG,IAAIC,KAAJ,CAAU5B,MAAV,CAAvB,CAAA;IACA,IAAI6B,KAAK,GAAG,CAAZ,CAAA;EACAH,EAAAA,OAAO,CAACxD,OAAR,CAAgB,UAAC4D,MAAD,EAAS/B,CAAT,EAAe;EAC3B;EACAd,IAAAA,YAAY,CAAC6C,MAAD,EAAS,KAAT,EAAgB,UAAAhB,aAAa,EAAI;EACzCa,MAAAA,cAAc,CAAC5B,CAAD,CAAd,GAAoBe,aAApB,CAAA;;EACA,MAAA,IAAI,EAAEe,KAAF,KAAY7B,MAAhB,EAAwB;EACpBb,QAAAA,QAAQ,CAACwC,cAAc,CAACI,IAAf,CAAoBT,SAApB,CAAD,CAAR,CAAA;EACH,OAAA;EACJ,KALW,CAAZ,CAAA;KAFJ,CAAA,CAAA;EASH,CAdD,CAAA;;EAeA,IAAMU,aAAa,GAAG,SAAhBA,aAAgB,CAACC,cAAD,EAAiBlB,UAAjB,EAAgC;EAClD,EAAA,IAAMY,cAAc,GAAGM,cAAc,CAACvC,KAAf,CAAqB4B,SAArB,CAAvB,CAAA;IACA,IAAMI,OAAO,GAAG,EAAhB,CAAA;;EACA,EAAA,KAAK,IAAI3B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4B,cAAc,CAAC3B,MAAnC,EAA2CD,CAAC,EAA5C,EAAgD;MAC5C,IAAMmC,aAAa,GAAGrB,YAAY,CAACc,cAAc,CAAC5B,CAAD,CAAf,EAAoBgB,UAApB,CAAlC,CAAA;MACAW,OAAO,CAACS,IAAR,CAAaD,aAAb,CAAA,CAAA;;EACA,IAAA,IAAIA,aAAa,CAAC7D,IAAd,KAAuB,OAA3B,EAAoC;EAChC,MAAA,MAAA;EACH,KAAA;EACJ,GAAA;;EACD,EAAA,OAAOqD,OAAP,CAAA;EACH,CAXD,CAAA;;EAYO,IAAMU,UAAQ,GAAG,CAAjB;;EC9BP;EACA;EACA;EACA;EACA;EAEO,SAASC,OAAT,CAAiBtD,GAAjB,EAAsB;EAC3B,EAAA,IAAIA,GAAJ,EAAS,OAAOuD,KAAK,CAACvD,GAAD,CAAZ,CAAA;EACV,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,SAASuD,KAAT,CAAevD,GAAf,EAAoB;EAClB,EAAA,KAAK,IAAIZ,GAAT,IAAgBkE,OAAO,CAAC5D,SAAxB,EAAmC;MACjCM,GAAG,CAACZ,GAAD,CAAH,GAAWkE,OAAO,CAAC5D,SAAR,CAAkBN,GAAlB,CAAX,CAAA;EACD,GAAA;;EACD,EAAA,OAAOY,GAAP,CAAA;EACD,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EAEAsD,OAAO,CAAC5D,SAAR,CAAkB8D,EAAlB,GACAF,OAAO,CAAC5D,SAAR,CAAkB+D,gBAAlB,GAAqC,UAASC,KAAT,EAAgBC,EAAhB,EAAmB;EACtD,EAAA,IAAA,CAAKC,UAAL,GAAkB,IAAKA,CAAAA,UAAL,IAAmB,EAArC,CAAA;EACA,EAAA,CAAC,KAAKA,UAAL,CAAgB,GAAMF,GAAAA,KAAtB,IAA+B,IAAKE,CAAAA,UAAL,CAAgB,GAAA,GAAMF,KAAtB,CAAgC,IAAA,EAAhE,EACGN,IADH,CACQO,EADR,CAAA,CAAA;EAEA,EAAA,OAAO,IAAP,CAAA;EACD,CAND,CAAA;EAQA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EAEAL,OAAO,CAAC5D,SAAR,CAAkBmE,IAAlB,GAAyB,UAASH,KAAT,EAAgBC,EAAhB,EAAmB;EAC1C,EAAA,SAASH,EAAT,GAAc;EACZ,IAAA,IAAA,CAAKM,GAAL,CAASJ,KAAT,EAAgBF,EAAhB,CAAA,CAAA;EACAG,IAAAA,EAAE,CAACI,KAAH,CAAS,IAAT,EAAeC,SAAf,CAAA,CAAA;EACD,GAAA;;IAEDR,EAAE,CAACG,EAAH,GAAQA,EAAR,CAAA;EACA,EAAA,IAAA,CAAKH,EAAL,CAAQE,KAAR,EAAeF,EAAf,CAAA,CAAA;EACA,EAAA,OAAO,IAAP,CAAA;EACD,CATD,CAAA;EAWA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EAEAF,OAAO,CAAC5D,SAAR,CAAkBoE,GAAlB,GACAR,OAAO,CAAC5D,SAAR,CAAkBuE,cAAlB,GACAX,OAAO,CAAC5D,SAAR,CAAkBwE,kBAAlB,GACAZ,OAAO,CAAC5D,SAAR,CAAkByE,mBAAlB,GAAwC,UAAST,KAAT,EAAgBC,EAAhB,EAAmB;EACzD,EAAA,IAAA,CAAKC,UAAL,GAAkB,IAAA,CAAKA,UAAL,IAAmB,EAArC,CADyD;;EAIzD,EAAA,IAAI,CAAKI,IAAAA,SAAS,CAAC/C,MAAnB,EAA2B;MACzB,IAAK2C,CAAAA,UAAL,GAAkB,EAAlB,CAAA;EACA,IAAA,OAAO,IAAP,CAAA;EACD,GAPwD;;;EAUzD,EAAA,IAAIQ,SAAS,GAAG,IAAA,CAAKR,UAAL,CAAgB,GAAA,GAAMF,KAAtB,CAAhB,CAAA;EACA,EAAA,IAAI,CAACU,SAAL,EAAgB,OAAO,IAAP,CAXyC;;EAczD,EAAA,IAAI,CAAKJ,IAAAA,SAAS,CAAC/C,MAAnB,EAA2B;EACzB,IAAA,OAAO,IAAK2C,CAAAA,UAAL,CAAgB,GAAA,GAAMF,KAAtB,CAAP,CAAA;EACA,IAAA,OAAO,IAAP,CAAA;EACD,GAjBwD;;;EAoBzD,EAAA,IAAIW,EAAJ,CAAA;;EACA,EAAA,KAAK,IAAIrD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoD,SAAS,CAACnD,MAA9B,EAAsCD,CAAC,EAAvC,EAA2C;EACzCqD,IAAAA,EAAE,GAAGD,SAAS,CAACpD,CAAD,CAAd,CAAA;;MACA,IAAIqD,EAAE,KAAKV,EAAP,IAAaU,EAAE,CAACV,EAAH,KAAUA,EAA3B,EAA+B;EAC7BS,MAAAA,SAAS,CAACE,MAAV,CAAiBtD,CAAjB,EAAoB,CAApB,CAAA,CAAA;EACA,MAAA,MAAA;EACD,KAAA;EACF,GA3BwD;EA8BzD;;;EACA,EAAA,IAAIoD,SAAS,CAACnD,MAAV,KAAqB,CAAzB,EAA4B;EAC1B,IAAA,OAAO,IAAK2C,CAAAA,UAAL,CAAgB,GAAA,GAAMF,KAAtB,CAAP,CAAA;EACD,GAAA;;EAED,EAAA,OAAO,IAAP,CAAA;EACD,CAvCD,CAAA;EAyCA;EACA;EACA;EACA;EACA;EACA;EACA;;;EAEAJ,OAAO,CAAC5D,SAAR,CAAkB6E,IAAlB,GAAyB,UAASb,KAAT,EAAe;EACtC,EAAA,IAAA,CAAKE,UAAL,GAAkB,IAAKA,CAAAA,UAAL,IAAmB,EAArC,CAAA;IAEA,IAAIY,IAAI,GAAG,IAAI3B,KAAJ,CAAUmB,SAAS,CAAC/C,MAAV,GAAmB,CAA7B,CAAX;EAAA,MACImD,SAAS,GAAG,IAAA,CAAKR,UAAL,CAAgB,GAAA,GAAMF,KAAtB,CADhB,CAAA;;EAGA,EAAA,KAAK,IAAI1C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgD,SAAS,CAAC/C,MAA9B,EAAsCD,CAAC,EAAvC,EAA2C;MACzCwD,IAAI,CAACxD,CAAC,GAAG,CAAL,CAAJ,GAAcgD,SAAS,CAAChD,CAAD,CAAvB,CAAA;EACD,GAAA;;EAED,EAAA,IAAIoD,SAAJ,EAAe;EACbA,IAAAA,SAAS,GAAGA,SAAS,CAACK,KAAV,CAAgB,CAAhB,CAAZ,CAAA;;EACA,IAAA,KAAK,IAAIzD,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAG8C,SAAS,CAACnD,MAAhC,EAAwCD,CAAC,GAAGM,GAA5C,EAAiD,EAAEN,CAAnD,EAAsD;QACpDoD,SAAS,CAACpD,CAAD,CAAT,CAAa+C,KAAb,CAAmB,IAAnB,EAAyBS,IAAzB,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAED,EAAA,OAAO,IAAP,CAAA;EACD,CAlBD;;;EAqBAlB,OAAO,CAAC5D,SAAR,CAAkBgF,YAAlB,GAAiCpB,OAAO,CAAC5D,SAAR,CAAkB6E,IAAnD,CAAA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAjB,OAAO,CAAC5D,SAAR,CAAkBiF,SAAlB,GAA8B,UAASjB,KAAT,EAAe;EAC3C,EAAA,IAAA,CAAKE,UAAL,GAAkB,IAAKA,CAAAA,UAAL,IAAmB,EAArC,CAAA;EACA,EAAA,OAAO,KAAKA,UAAL,CAAgB,GAAMF,GAAAA,KAAtB,KAAgC,EAAvC,CAAA;EACD,CAHD,CAAA;EAKA;EACA;EACA;EACA;EACA;EACA;EACA;;;EAEAJ,OAAO,CAAC5D,SAAR,CAAkBkF,YAAlB,GAAiC,UAASlB,KAAT,EAAe;EAC9C,EAAA,OAAO,CAAC,CAAE,IAAA,CAAKiB,SAAL,CAAejB,KAAf,EAAsBzC,MAAhC,CAAA;EACD,CAFD;;ECtKO,IAAM4D,cAAc,GAAI,YAAM;EACjC,EAAA,IAAI,OAAOC,IAAP,KAAgB,WAApB,EAAiC;EAC7B,IAAA,OAAOA,IAAP,CAAA;EACH,GAFD,MAGK,IAAI,OAAOC,MAAP,KAAkB,WAAtB,EAAmC;EACpC,IAAA,OAAOA,MAAP,CAAA;EACH,GAFI,MAGA;EACD,IAAA,OAAOC,QAAQ,CAAC,aAAD,CAAR,EAAP,CAAA;EACH,GAAA;EACJ,CAV6B,EAAvB;;ECCA,SAASC,IAAT,CAAcjF,GAAd,EAA4B;EAAA,EAAA,KAAA,IAAA,IAAA,GAAA,SAAA,CAAA,MAAA,EAANkF,IAAM,GAAA,IAAA,KAAA,CAAA,IAAA,GAAA,CAAA,GAAA,IAAA,GAAA,CAAA,GAAA,CAAA,CAAA,EAAA,IAAA,GAAA,CAAA,EAAA,IAAA,GAAA,IAAA,EAAA,IAAA,EAAA,EAAA;MAANA,IAAM,CAAA,IAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA;EAAA,GAAA;;IAC/B,OAAOA,IAAI,CAACC,MAAL,CAAY,UAACC,GAAD,EAAMC,CAAN,EAAY;EAC3B,IAAA,IAAIrF,GAAG,CAACsF,cAAJ,CAAmBD,CAAnB,CAAJ,EAA2B;EACvBD,MAAAA,GAAG,CAACC,CAAD,CAAH,GAASrF,GAAG,CAACqF,CAAD,CAAZ,CAAA;EACH,KAAA;;EACD,IAAA,OAAOD,GAAP,CAAA;KAJG,EAKJ,EALI,CAAP,CAAA;EAMH;;EAED,IAAMG,kBAAkB,GAAGC,cAAU,CAACC,UAAtC,CAAA;EACA,IAAMC,oBAAoB,GAAGF,cAAU,CAACG,YAAxC,CAAA;EACO,SAASC,qBAAT,CAA+B5F,GAA/B,EAAoC6F,IAApC,EAA0C;IAC7C,IAAIA,IAAI,CAACC,eAAT,EAA0B;MACtB9F,GAAG,CAAC+F,YAAJ,GAAmBR,kBAAkB,CAACS,IAAnB,CAAwBR,cAAxB,CAAnB,CAAA;MACAxF,GAAG,CAACiG,cAAJ,GAAqBP,oBAAoB,CAACM,IAArB,CAA0BR,cAA1B,CAArB,CAAA;EACH,GAHD,MAIK;MACDxF,GAAG,CAAC+F,YAAJ,GAAmBP,cAAU,CAACC,UAAX,CAAsBO,IAAtB,CAA2BR,cAA3B,CAAnB,CAAA;MACAxF,GAAG,CAACiG,cAAJ,GAAqBT,cAAU,CAACG,YAAX,CAAwBK,IAAxB,CAA6BR,cAA7B,CAArB,CAAA;EACH,GAAA;EACJ;;EAED,IAAMU,eAAe,GAAG,IAAxB;;EAEO,SAASC,UAAT,CAAoBnG,GAApB,EAAyB;EAC5B,EAAA,IAAI,OAAOA,GAAP,KAAe,QAAnB,EAA6B;MACzB,OAAOoG,UAAU,CAACpG,GAAD,CAAjB,CAAA;EACH,GAH2B;;;EAK5B,EAAA,OAAOqG,IAAI,CAACC,IAAL,CAAU,CAACtG,GAAG,CAACmG,UAAJ,IAAkBnG,GAAG,CAACuG,IAAvB,IAA+BL,eAAzC,CAAP,CAAA;EACH,CAAA;;EACD,SAASE,UAAT,CAAoBI,GAApB,EAAyB;IACrB,IAAIC,CAAC,GAAG,CAAR;QAAWxF,MAAM,GAAG,CAApB,CAAA;;EACA,EAAA,KAAK,IAAID,CAAC,GAAG,CAAR,EAAW0F,CAAC,GAAGF,GAAG,CAACvF,MAAxB,EAAgCD,CAAC,GAAG0F,CAApC,EAAuC1F,CAAC,EAAxC,EAA4C;EACxCyF,IAAAA,CAAC,GAAGD,GAAG,CAACtF,UAAJ,CAAeF,CAAf,CAAJ,CAAA;;MACA,IAAIyF,CAAC,GAAG,IAAR,EAAc;EACVxF,MAAAA,MAAM,IAAI,CAAV,CAAA;EACH,KAFD,MAGK,IAAIwF,CAAC,GAAG,KAAR,EAAe;EAChBxF,MAAAA,MAAM,IAAI,CAAV,CAAA;OADC,MAGA,IAAIwF,CAAC,GAAG,MAAJ,IAAcA,CAAC,IAAI,MAAvB,EAA+B;EAChCxF,MAAAA,MAAM,IAAI,CAAV,CAAA;EACH,KAFI,MAGA;QACDD,CAAC,EAAA,CAAA;EACDC,MAAAA,MAAM,IAAI,CAAV,CAAA;EACH,KAAA;EACJ,GAAA;;EACD,EAAA,OAAOA,MAAP,CAAA;EACH;;MChDK0F;;;;;EACF,EAAA,SAAA,cAAA,CAAYC,MAAZ,EAAoBC,WAApB,EAAiCC,OAAjC,EAA0C;EAAA,IAAA,IAAA,KAAA,CAAA;;EAAA,IAAA,eAAA,CAAA,IAAA,EAAA,cAAA,CAAA,CAAA;;EACtC,IAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAMF,MAAN,CAAA,CAAA;MACA,KAAKC,CAAAA,WAAL,GAAmBA,WAAnB,CAAA;MACA,KAAKC,CAAAA,OAAL,GAAeA,OAAf,CAAA;MACA,KAAKxH,CAAAA,IAAL,GAAY,gBAAZ,CAAA;EAJsC,IAAA,OAAA,KAAA,CAAA;EAKzC,GAAA;;;mCANwByH;;EAQ7B,IAAaC,SAAb,gBAAA,UAAA,QAAA,EAAA;EAAA,EAAA,SAAA,CAAA,SAAA,EAAA,QAAA,CAAA,CAAA;;EAAA,EAAA,IAAA,OAAA,GAAA,YAAA,CAAA,SAAA,CAAA,CAAA;;EACI;EACJ;EACA;EACA;EACA;EACA;EACI,EAAA,SAAA,SAAA,CAAYnB,IAAZ,EAAkB;EAAA,IAAA,IAAA,MAAA,CAAA;;EAAA,IAAA,eAAA,CAAA,IAAA,EAAA,SAAA,CAAA,CAAA;;EACd,IAAA,MAAA,GAAA,OAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;MACA,MAAKoB,CAAAA,QAAL,GAAgB,KAAhB,CAAA;MACArB,qBAAqB,CAAA,sBAAA,CAAA,MAAA,CAAA,EAAOC,IAAP,CAArB,CAAA;MACA,MAAKA,CAAAA,IAAL,GAAYA,IAAZ,CAAA;EACA,IAAA,MAAA,CAAKqB,KAAL,GAAarB,IAAI,CAACqB,KAAlB,CAAA;EACA,IAAA,MAAA,CAAKC,MAAL,GAActB,IAAI,CAACsB,MAAnB,CAAA;EANc,IAAA,OAAA,MAAA,CAAA;EAOjB,GAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EAvBA,EAAA,YAAA,CAAA,SAAA,EAAA,CAAA;EAAA,IAAA,GAAA,EAAA,SAAA;EAAA,IAAA,KAAA,EAwBI,iBAAQP,MAAR,EAAgBC,WAAhB,EAA6BC,OAA7B,EAAsC;QAClC,IAAmB,CAAA,eAAA,CAAA,SAAA,CAAA,SAAA,CAAA,EAAA,cAAA,EAAA,IAAA,CAAA,CAAA,IAAA,CAAA,IAAA,EAAA,OAAnB,EAA4B,IAAIH,cAAJ,CAAmBC,MAAnB,EAA2BC,WAA3B,EAAwCC,OAAxC,CAA5B,CAAA,CAAA;;EACA,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;;EA9BA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,MAAA;EAAA,IAAA,KAAA,EA+BI,SAAO,IAAA,GAAA;QACH,IAAKM,CAAAA,UAAL,GAAkB,SAAlB,CAAA;EACA,MAAA,IAAA,CAAKC,MAAL,EAAA,CAAA;EACA,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;;EAtCA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,OAAA;EAAA,IAAA,KAAA,EAuCI,SAAQ,KAAA,GAAA;QACJ,IAAI,IAAA,CAAKD,UAAL,KAAoB,SAApB,IAAiC,IAAKA,CAAAA,UAAL,KAAoB,MAAzD,EAAiE;EAC7D,QAAA,IAAA,CAAKE,OAAL,EAAA,CAAA;EACA,QAAA,IAAA,CAAKC,OAAL,EAAA,CAAA;EACH,OAAA;;EACD,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EAlDA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,MAAA;MAAA,KAmDI,EAAA,SAAA,IAAA,CAAK5E,OAAL,EAAc;EACV,MAAA,IAAI,IAAKyE,CAAAA,UAAL,KAAoB,MAAxB,EAAgC;UAC5B,IAAKI,CAAAA,KAAL,CAAW7E,OAAX,CAAA,CAAA;EACH,OAGA;EACJ,KAAA;EACD;EACJ;EACA;EACA;EACA;;EA/DA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,QAAA;EAAA,IAAA,KAAA,EAgEI,SAAS,MAAA,GAAA;QACL,IAAKyE,CAAAA,UAAL,GAAkB,MAAlB,CAAA;QACA,IAAKH,CAAAA,QAAL,GAAgB,IAAhB,CAAA;;EACA,MAAA,IAAA,CAAA,eAAA,CAAA,SAAA,CAAA,SAAA,CAAA,EAAA,cAAA,EAAA,IAAA,CAAA,CAAA,IAAA,CAAA,IAAA,EAAmB,MAAnB,CAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;EA1EA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,QAAA;MAAA,KA2EI,EAAA,SAAA,MAAA,CAAO1H,IAAP,EAAa;QACT,IAAMwD,MAAM,GAAGjB,YAAY,CAACvC,IAAD,EAAO,IAAK4H,CAAAA,MAAL,CAAYnF,UAAnB,CAA3B,CAAA;QACA,IAAKyF,CAAAA,QAAL,CAAc1E,MAAd,CAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EAnFA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,UAAA;MAAA,KAoFI,EAAA,SAAA,QAAA,CAASA,MAAT,EAAiB;QACb,IAAmB,CAAA,eAAA,CAAA,SAAA,CAAA,SAAA,CAAA,EAAA,cAAA,EAAA,IAAA,CAAA,CAAA,IAAA,CAAA,IAAA,EAAA,QAAnB,EAA6BA,MAA7B,CAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EA3FA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,SAAA;MAAA,KA4FI,EAAA,SAAA,OAAA,CAAQ2E,OAAR,EAAiB;QACb,IAAKN,CAAAA,UAAL,GAAkB,QAAlB,CAAA;;QACA,IAAmB,CAAA,eAAA,CAAA,SAAA,CAAA,SAAA,CAAA,EAAA,cAAA,EAAA,IAAA,CAAA,CAAA,IAAA,CAAA,IAAA,EAAA,OAAnB,EAA4BM,OAA5B,CAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EApGA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,OAAA;MAAA,KAqGI,EAAA,SAAA,KAAA,CAAMC,OAAN,EAAe,EAAG;EArGtB,GAAA,CAAA,CAAA,CAAA;;EAAA,EAAA,OAAA,SAAA,CAAA;EAAA,CAAA,CAA+BrE,OAA/B,CAAA;;ECXA;;EAEA,IAAMsE,QAAQ,GAAG,kEAAA,CAAmEjH,KAAnE,CAAyE,EAAzE,CAAjB;EAAA,IAA+FM,MAAM,GAAG,EAAxG;EAAA,IAA4G4G,GAAG,GAAG,EAAlH,CAAA;EACA,IAAIC,IAAI,GAAG,CAAX;EAAA,IAAc9G,CAAC,GAAG,CAAlB;EAAA,IAAqB+G,IAArB,CAAA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASC,QAAT,CAAgBC,GAAhB,EAAqB;IACxB,IAAIC,OAAO,GAAG,EAAd,CAAA;;IACA,GAAG;MACCA,OAAO,GAAGN,QAAQ,CAACK,GAAG,GAAGhH,MAAP,CAAR,GAAyBiH,OAAnC,CAAA;MACAD,GAAG,GAAG5B,IAAI,CAAC8B,KAAL,CAAWF,GAAG,GAAGhH,MAAjB,CAAN,CAAA;KAFJ,QAGSgH,GAAG,GAAG,CAHf,EAAA;;EAIA,EAAA,OAAOC,OAAP,CAAA;EACH,CAAA;EAeD;EACA;EACA;EACA;EACA;EACA;;EACO,SAASE,KAAT,GAAiB;IACpB,IAAMC,GAAG,GAAGL,QAAM,CAAC,CAAC,IAAIM,IAAJ,EAAF,CAAlB,CAAA;IACA,IAAID,GAAG,KAAKN,IAAZ,EACI,OAAOD,IAAI,GAAG,CAAP,EAAUC,IAAI,GAAGM,GAAxB,CAAA;IACJ,OAAOA,GAAG,GAAG,GAAN,GAAYL,QAAM,CAACF,IAAI,EAAL,CAAzB,CAAA;EACH;EAED;EACA;;EACA,OAAO9G,CAAC,GAAGC,MAAX,EAAmBD,CAAC,EAApB,EAAA;EACI6G,EAAAA,GAAG,CAACD,QAAQ,CAAC5G,CAAD,CAAT,CAAH,GAAmBA,CAAnB,CAAA;EADJ;;EChDA;;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASgH,MAAT,CAAgBhI,GAAhB,EAAqB;IACxB,IAAIwG,GAAG,GAAG,EAAV,CAAA;;EACA,EAAA,KAAK,IAAIxF,CAAT,IAAchB,GAAd,EAAmB;EACf,IAAA,IAAIA,GAAG,CAACsF,cAAJ,CAAmBtE,CAAnB,CAAJ,EAA2B;EACvB,MAAA,IAAIwF,GAAG,CAACvF,MAAR,EACIuF,GAAG,IAAI,GAAP,CAAA;EACJA,MAAAA,GAAG,IAAI+B,kBAAkB,CAACvH,CAAD,CAAlB,GAAwB,GAAxB,GAA8BuH,kBAAkB,CAACvI,GAAG,CAACgB,CAAD,CAAJ,CAAvD,CAAA;EACH,KAAA;EACJ,GAAA;;EACD,EAAA,OAAOwF,GAAP,CAAA;EACH,CAAA;EACD;EACA;EACA;EACA;EACA;EACA;;EACO,SAASrF,MAAT,CAAgBqH,EAAhB,EAAoB;IACvB,IAAIC,GAAG,GAAG,EAAV,CAAA;EACA,EAAA,IAAIC,KAAK,GAAGF,EAAE,CAAC7H,KAAH,CAAS,GAAT,CAAZ,CAAA;;EACA,EAAA,KAAK,IAAIK,CAAC,GAAG,CAAR,EAAW0F,CAAC,GAAGgC,KAAK,CAACzH,MAA1B,EAAkCD,CAAC,GAAG0F,CAAtC,EAAyC1F,CAAC,EAA1C,EAA8C;MAC1C,IAAI2H,IAAI,GAAGD,KAAK,CAAC1H,CAAD,CAAL,CAASL,KAAT,CAAe,GAAf,CAAX,CAAA;EACA8H,IAAAA,GAAG,CAACG,kBAAkB,CAACD,IAAI,CAAC,CAAD,CAAL,CAAnB,CAAH,GAAmCC,kBAAkB,CAACD,IAAI,CAAC,CAAD,CAAL,CAArD,CAAA;EACH,GAAA;;EACD,EAAA,OAAOF,GAAP,CAAA;EACH;;ECjCD;EACA,IAAII,KAAK,GAAG,KAAZ,CAAA;;EACA,IAAI;IACAA,KAAK,GAAG,OAAOC,cAAP,KAA0B,WAA1B,IACJ,iBAAA,IAAqB,IAAIA,cAAJ,EADzB,CAAA;EAEH,CAHD,CAIA,OAAOC,GAAP,EAAY;EAER;EACH,CAAA;;EACM,IAAMC,OAAO,GAAGH,KAAhB;;ECVP;EAGO,SAASI,GAAT,CAAapD,IAAb,EAAmB;EACtB,EAAA,IAAMqD,OAAO,GAAGrD,IAAI,CAACqD,OAArB,CADsB;;IAGtB,IAAI;MACA,IAAI,WAAA,KAAgB,OAAOJ,cAAvB,KAA0C,CAACI,OAAD,IAAYF,OAAtD,CAAJ,EAAoE;QAChE,OAAO,IAAIF,cAAJ,EAAP,CAAA;EACH,KAAA;EACJ,GAJD,CAKA,OAAOK,CAAP,EAAU,EAAG;;IACb,IAAI,CAACD,OAAL,EAAc;MACV,IAAI;EACA,MAAA,OAAO,IAAI1D,cAAU,CAAC,CAAC,QAAD,EAAW4D,MAAX,CAAkB,QAAlB,CAAA,CAA4BpG,IAA5B,CAAiC,GAAjC,CAAD,CAAd,CAAsD,mBAAtD,CAAP,CAAA;EACH,KAFD,CAGA,OAAOmG,CAAP,EAAU,EAAG;EAChB,GAAA;EACJ;;ECVD,SAASE,KAAT,GAAiB,EAAG;;EACpB,IAAMC,OAAO,GAAI,YAAY;EACzB,EAAA,IAAMC,GAAG,GAAG,IAAIT,GAAJ,CAAmB;EAC3BI,IAAAA,OAAO,EAAE,KAAA;EADkB,GAAnB,CAAZ,CAAA;IAGA,OAAO,IAAA,IAAQK,GAAG,CAACC,YAAnB,CAAA;EACH,CALe,EAAhB,CAAA;;EAMA,IAAaC,OAAb,gBAAA,UAAA,UAAA,EAAA;EAAA,EAAA,SAAA,CAAA,OAAA,EAAA,UAAA,CAAA,CAAA;;EAAA,EAAA,IAAA,MAAA,GAAA,YAAA,CAAA,OAAA,CAAA,CAAA;;EACI;EACJ;EACA;EACA;EACA;EACA;EACI,EAAA,SAAA,OAAA,CAAY5D,IAAZ,EAAkB;EAAA,IAAA,IAAA,KAAA,CAAA;;EAAA,IAAA,eAAA,CAAA,IAAA,EAAA,OAAA,CAAA,CAAA;;EACd,IAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAMA,IAAN,CAAA,CAAA;MACA,KAAK6D,CAAAA,OAAL,GAAe,KAAf,CAAA;;EACA,IAAA,IAAI,OAAOC,QAAP,KAAoB,WAAxB,EAAqC;EACjC,MAAA,IAAMC,KAAK,GAAG,QAAaD,KAAAA,QAAQ,CAACtG,QAApC,CAAA;EACA,MAAA,IAAIwG,IAAI,GAAGF,QAAQ,CAACE,IAApB,CAFiC;;QAIjC,IAAI,CAACA,IAAL,EAAW;EACPA,QAAAA,IAAI,GAAGD,KAAK,GAAG,KAAH,GAAW,IAAvB,CAAA;EACH,OAAA;;EACD,MAAA,KAAA,CAAKE,EAAL,GACK,OAAOH,QAAP,KAAoB,WAApB,IACG9D,IAAI,CAACkE,QAAL,KAAkBJ,QAAQ,CAACI,QAD/B,IAEIF,IAAI,KAAKhE,IAAI,CAACgE,IAHtB,CAAA;EAIA,MAAA,KAAA,CAAKG,EAAL,GAAUnE,IAAI,CAACoE,MAAL,KAAgBL,KAA1B,CAAA;EACH,KAAA;EACD;EACR;EACA;;;EACQ,IAAA,IAAMM,WAAW,GAAGrE,IAAI,IAAIA,IAAI,CAACqE,WAAjC,CAAA;EACA,IAAA,KAAA,CAAK/J,cAAL,GAAsBmJ,OAAO,IAAI,CAACY,WAAlC,CAAA;EApBc,IAAA,OAAA,KAAA,CAAA;EAqBjB,GAAA;;EA5BL,EAAA,YAAA,CAAA,OAAA,EAAA,CAAA;EAAA,IAAA,GAAA,EAAA,MAAA;EAAA,IAAA,GAAA,EA6BI,SAAW,GAAA,GAAA;EACP,MAAA,OAAO,SAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;EArCA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,QAAA;EAAA,IAAA,KAAA,EAsCI,SAAS,MAAA,GAAA;EACL,MAAA,IAAA,CAAKC,IAAL,EAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;EA9CA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,OAAA;MAAA,KA+CI,EAAA,SAAA,KAAA,CAAMxC,OAAN,EAAe;EAAA,MAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;QACX,IAAKP,CAAAA,UAAL,GAAkB,SAAlB,CAAA;;EACA,MAAA,IAAMgD,KAAK,GAAG,SAARA,KAAQ,GAAM;UAChB,MAAI,CAAChD,UAAL,GAAkB,QAAlB,CAAA;UACAO,OAAO,EAAA,CAAA;SAFX,CAAA;;EAIA,MAAA,IAAI,KAAK+B,OAAL,IAAgB,CAAC,IAAA,CAAKzC,QAA1B,EAAoC;UAChC,IAAIoD,KAAK,GAAG,CAAZ,CAAA;;UACA,IAAI,IAAA,CAAKX,OAAT,EAAkB;YACdW,KAAK,EAAA,CAAA;EACL,UAAA,IAAA,CAAKxG,IAAL,CAAU,cAAV,EAA0B,YAAY;cAClC,EAAEwG,KAAF,IAAWD,KAAK,EAAhB,CAAA;aADJ,CAAA,CAAA;EAGH,SAAA;;UACD,IAAI,CAAC,IAAKnD,CAAAA,QAAV,EAAoB;YAChBoD,KAAK,EAAA,CAAA;EACL,UAAA,IAAA,CAAKxG,IAAL,CAAU,OAAV,EAAmB,YAAY;cAC3B,EAAEwG,KAAF,IAAWD,KAAK,EAAhB,CAAA;aADJ,CAAA,CAAA;EAGH,SAAA;EACJ,OAdD,MAeK;UACDA,KAAK,EAAA,CAAA;EACR,OAAA;EACJ,KAAA;EACD;EACJ;EACA;EACA;EACA;;EA5EA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,MAAA;EAAA,IAAA,KAAA,EA6EI,SAAO,IAAA,GAAA;QACH,IAAKV,CAAAA,OAAL,GAAe,IAAf,CAAA;EACA,MAAA,IAAA,CAAKY,MAAL,EAAA,CAAA;QACA,IAAK5F,CAAAA,YAAL,CAAkB,MAAlB,CAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EAtFA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,QAAA;MAAA,KAuFI,EAAA,SAAA,MAAA,CAAOnF,IAAP,EAAa;EAAA,MAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EACT,MAAA,IAAMa,QAAQ,GAAG,SAAXA,QAAW,CAAC2C,MAAD,EAAY;EACzB;UACA,IAAI,SAAA,KAAc,MAAI,CAACqE,UAAnB,IAAiCrE,MAAM,CAACzD,IAAP,KAAgB,MAArD,EAA6D;EACzD,UAAA,MAAI,CAACiL,MAAL,EAAA,CAAA;EACH,SAJwB;;;EAMzB,QAAA,IAAI,OAAYxH,KAAAA,MAAM,CAACzD,IAAvB,EAA6B;YACzB,MAAI,CAACiI,OAAL,CAAa;EAAEV,YAAAA,WAAW,EAAE,gCAAA;aAA5B,CAAA,CAAA;;EACA,UAAA,OAAO,KAAP,CAAA;EACH,SATwB;;;UAWzB,MAAI,CAACY,QAAL,CAAc1E,MAAd,CAAA,CAAA;EACH,OAZD,CADS;;;EAeTE,MAAAA,aAAa,CAAC1D,IAAD,EAAO,IAAA,CAAK4H,MAAL,CAAYnF,UAAnB,CAAb,CAA4C7C,OAA5C,CAAoDiB,QAApD,EAfS;;QAiBT,IAAI,QAAA,KAAa,IAAKgH,CAAAA,UAAtB,EAAkC;EAC9B;UACA,IAAKsC,CAAAA,OAAL,GAAe,KAAf,CAAA;UACA,IAAKhF,CAAAA,YAAL,CAAkB,cAAlB,CAAA,CAAA;;UACA,IAAI,MAAA,KAAW,IAAK0C,CAAAA,UAApB,EAAgC;EAC5B,UAAA,IAAA,CAAK+C,IAAL,EAAA,CAAA;EACH,SAEA;EACJ,OAAA;EACJ,KAAA;EACD;EACJ;EACA;EACA;EACA;;EAvHA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,SAAA;EAAA,IAAA,KAAA,EAwHI,SAAU,OAAA,GAAA;EAAA,MAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EACN,MAAA,IAAMK,KAAK,GAAG,SAARA,KAAQ,GAAM;UAChB,MAAI,CAAChD,KAAL,CAAW,CAAC;EAAElI,UAAAA,IAAI,EAAE,OAAA;EAAR,SAAD,CAAX,CAAA,CAAA;SADJ,CAAA;;QAGA,IAAI,MAAA,KAAW,IAAK8H,CAAAA,UAApB,EAAgC;UAC5BoD,KAAK,EAAA,CAAA;EACR,OAFD,MAGK;EACD;EACA;EACA,QAAA,IAAA,CAAK3G,IAAL,CAAU,MAAV,EAAkB2G,KAAlB,CAAA,CAAA;EACH,OAAA;EACJ,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;EA1IA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,OAAA;MAAA,KA2II,EAAA,SAAA,KAAA,CAAM7H,OAAN,EAAe;EAAA,MAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;QACX,IAAKsE,CAAAA,QAAL,GAAgB,KAAhB,CAAA;EACAvE,MAAAA,aAAa,CAACC,OAAD,EAAU,UAACpD,IAAD,EAAU;EAC7B,QAAA,MAAI,CAACkL,OAAL,CAAalL,IAAb,EAAmB,YAAM;YACrB,MAAI,CAAC0H,QAAL,GAAgB,IAAhB,CAAA;;YACA,MAAI,CAACvC,YAAL,CAAkB,OAAlB,CAAA,CAAA;WAFJ,CAAA,CAAA;EAIH,OALY,CAAb,CAAA;EAMH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EAxJA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,KAAA;EAAA,IAAA,KAAA,EAyJI,SAAM,GAAA,GAAA;EACF,MAAA,IAAIwC,KAAK,GAAG,IAAKA,CAAAA,KAAL,IAAc,EAA1B,CAAA;QACA,IAAMwD,MAAM,GAAG,IAAK7E,CAAAA,IAAL,CAAUoE,MAAV,GAAmB,OAAnB,GAA6B,MAA5C,CAAA;EACA,MAAA,IAAIJ,IAAI,GAAG,EAAX,CAHE;;EAKF,MAAA,IAAI,KAAU,KAAA,IAAA,CAAKhE,IAAL,CAAU8E,iBAAxB,EAA2C;UACvCzD,KAAK,CAAC,KAAKrB,IAAL,CAAU+E,cAAX,CAAL,GAAkCxC,KAAK,EAAvC,CAAA;EACH,OAAA;;QACD,IAAI,CAAC,KAAKjI,cAAN,IAAwB,CAAC+G,KAAK,CAAC2D,GAAnC,EAAwC;UACpC3D,KAAK,CAAC4D,GAAN,GAAY,CAAZ,CAAA;EACH,OAVC;;;EAYF,MAAA,IAAI,IAAKjF,CAAAA,IAAL,CAAUgE,IAAV,KACE,OAAA,KAAYa,MAAZ,IAAsBK,MAAM,CAAC,IAAKlF,CAAAA,IAAL,CAAUgE,IAAX,CAAN,KAA2B,GAAlD,IACI,MAAA,KAAWa,MAAX,IAAqBK,MAAM,CAAC,IAAA,CAAKlF,IAAL,CAAUgE,IAAX,CAAN,KAA2B,EAFrD,CAAJ,EAE+D;EAC3DA,QAAAA,IAAI,GAAG,GAAA,GAAM,IAAKhE,CAAAA,IAAL,CAAUgE,IAAvB,CAAA;EACH,OAAA;;EACD,MAAA,IAAMmB,YAAY,GAAGhD,MAAM,CAACd,KAAD,CAA3B,CAAA;EACA,MAAA,IAAM+D,IAAI,GAAG,IAAKpF,CAAAA,IAAL,CAAUkE,QAAV,CAAmBmB,OAAnB,CAA2B,GAA3B,CAAoC,KAAA,CAAC,CAAlD,CAAA;EACA,MAAA,OAAQR,MAAM,GACV,KADI,IAEHO,IAAI,GAAG,GAAA,GAAM,IAAKpF,CAAAA,IAAL,CAAUkE,QAAhB,GAA2B,GAA9B,GAAoC,KAAKlE,IAAL,CAAUkE,QAF/C,CAAA,GAGJF,IAHI,GAIJ,IAAKhE,CAAAA,IAAL,CAAUsF,IAJN,IAKHH,YAAY,CAAC/J,MAAb,GAAsB,GAAA,GAAM+J,YAA5B,GAA2C,EALxC,CAAR,CAAA;EAMH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;EAxLA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,SAAA;EAAA,IAAA,KAAA,EAyLI,SAAmB,OAAA,GAAA;QAAA,IAAXnF,IAAW,uEAAJ,EAAI,CAAA;;EACf,MAAA,QAAA,CAAcA,IAAd,EAAoB;UAAEiE,EAAE,EAAE,KAAKA,EAAX;EAAeE,QAAAA,EAAE,EAAE,IAAKA,CAAAA,EAAAA;SAA5C,EAAkD,KAAKnE,IAAvD,CAAA,CAAA;;QACA,OAAO,IAAIuF,OAAJ,CAAY,IAAA,CAAKC,GAAL,EAAZ,EAAwBxF,IAAxB,CAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;EAnMA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,SAAA;EAAA,IAAA,KAAA,EAoMI,SAAQtG,OAAAA,CAAAA,IAAR,EAAcoE,EAAd,EAAkB;EAAA,MAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EACd,MAAA,IAAM2H,GAAG,GAAG,IAAKC,CAAAA,OAAL,CAAa;EACrBC,QAAAA,MAAM,EAAE,MADa;EAErBjM,QAAAA,IAAI,EAAEA,IAAAA;EAFe,OAAb,CAAZ,CAAA;EAIA+L,MAAAA,GAAG,CAAC9H,EAAJ,CAAO,SAAP,EAAkBG,EAAlB,CAAA,CAAA;QACA2H,GAAG,CAAC9H,EAAJ,CAAO,OAAP,EAAgB,UAACiI,SAAD,EAAY3E,OAAZ,EAAwB;EACpC,QAAA,MAAI,CAAC4E,OAAL,CAAa,gBAAb,EAA+BD,SAA/B,EAA0C3E,OAA1C,CAAA,CAAA;SADJ,CAAA,CAAA;EAGH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EAlNA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,QAAA;EAAA,IAAA,KAAA,EAmNI,SAAS,MAAA,GAAA;EAAA,MAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EACL,MAAA,IAAMwE,GAAG,GAAG,IAAKC,CAAAA,OAAL,EAAZ,CAAA;QACAD,GAAG,CAAC9H,EAAJ,CAAO,MAAP,EAAe,IAAKmI,CAAAA,MAAL,CAAY3F,IAAZ,CAAiB,IAAjB,CAAf,CAAA,CAAA;QACAsF,GAAG,CAAC9H,EAAJ,CAAO,OAAP,EAAgB,UAACiI,SAAD,EAAY3E,OAAZ,EAAwB;EACpC,QAAA,MAAI,CAAC4E,OAAL,CAAa,gBAAb,EAA+BD,SAA/B,EAA0C3E,OAA1C,CAAA,CAAA;SADJ,CAAA,CAAA;QAGA,IAAK8E,CAAAA,OAAL,GAAeN,GAAf,CAAA;EACH,KAAA;EA1NL,GAAA,CAAA,CAAA,CAAA;;EAAA,EAAA,OAAA,OAAA,CAAA;EAAA,CAAA,CAA6BtE,SAA7B,CAAA,CAAA;EA4NA,IAAaoE,OAAb,gBAAA,UAAA,QAAA,EAAA;EAAA,EAAA,SAAA,CAAA,OAAA,EAAA,QAAA,CAAA,CAAA;;EAAA,EAAA,IAAA,OAAA,GAAA,YAAA,CAAA,OAAA,CAAA,CAAA;;EACI;EACJ;EACA;EACA;EACA;EACA;IACI,SAAYC,OAAAA,CAAAA,GAAZ,EAAiBxF,IAAjB,EAAuB;EAAA,IAAA,IAAA,MAAA,CAAA;;EAAA,IAAA,eAAA,CAAA,IAAA,EAAA,OAAA,CAAA,CAAA;;EACnB,IAAA,MAAA,GAAA,OAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;MACAD,qBAAqB,CAAA,sBAAA,CAAA,MAAA,CAAA,EAAOC,IAAP,CAArB,CAAA;MACA,MAAKA,CAAAA,IAAL,GAAYA,IAAZ,CAAA;EACA,IAAA,MAAA,CAAK2F,MAAL,GAAc3F,IAAI,CAAC2F,MAAL,IAAe,KAA7B,CAAA;MACA,MAAKH,CAAAA,GAAL,GAAWA,GAAX,CAAA;EACA,IAAA,MAAA,CAAKQ,KAAL,GAAa,KAAUhG,KAAAA,IAAI,CAACgG,KAA5B,CAAA;EACA,IAAA,MAAA,CAAKtM,IAAL,GAAYuM,SAAS,KAAKjG,IAAI,CAACtG,IAAnB,GAA0BsG,IAAI,CAACtG,IAA/B,GAAsC,IAAlD,CAAA;;EACA,IAAA,MAAA,CAAKP,MAAL,EAAA,CAAA;;EARmB,IAAA,OAAA,MAAA,CAAA;EAStB,GAAA;EACD;EACJ;EACA;EACA;EACA;;;EArBA,EAAA,YAAA,CAAA,OAAA,EAAA,CAAA;EAAA,IAAA,GAAA,EAAA,QAAA;EAAA,IAAA,KAAA,EAsBI,SAAS,MAAA,GAAA;EAAA,MAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;QACL,IAAM6G,IAAI,GAAGZ,IAAI,CAAC,IAAA,CAAKY,IAAN,EAAY,OAAZ,EAAqB,KAArB,EAA4B,KAA5B,EAAmC,YAAnC,EAAiD,MAAjD,EAAyD,IAAzD,EAA+D,SAA/D,EAA0E,oBAA1E,EAAgG,WAAhG,CAAjB,CAAA;QACAA,IAAI,CAACqD,OAAL,GAAe,CAAC,CAAC,IAAKrD,CAAAA,IAAL,CAAUiE,EAA3B,CAAA;QACAjE,IAAI,CAACkG,OAAL,GAAe,CAAC,CAAC,IAAKlG,CAAAA,IAAL,CAAUmE,EAA3B,CAAA;QACA,IAAMT,GAAG,GAAI,IAAKA,CAAAA,GAAL,GAAW,IAAIT,GAAJ,CAAmBjD,IAAnB,CAAxB,CAAA;;QACA,IAAI;UACA0D,GAAG,CAACyC,IAAJ,CAAS,IAAKR,CAAAA,MAAd,EAAsB,IAAKH,CAAAA,GAA3B,EAAgC,IAAA,CAAKQ,KAArC,CAAA,CAAA;;UACA,IAAI;EACA,UAAA,IAAI,IAAKhG,CAAAA,IAAL,CAAUoG,YAAd,EAA4B;cACxB1C,GAAG,CAAC2C,qBAAJ,IAA6B3C,GAAG,CAAC2C,qBAAJ,CAA0B,IAA1B,CAA7B,CAAA;;EACA,YAAA,KAAK,IAAIlL,CAAT,IAAc,KAAK6E,IAAL,CAAUoG,YAAxB,EAAsC;gBAClC,IAAI,IAAA,CAAKpG,IAAL,CAAUoG,YAAV,CAAuB3G,cAAvB,CAAsCtE,CAAtC,CAAJ,EAA8C;kBAC1CuI,GAAG,CAAC4C,gBAAJ,CAAqBnL,CAArB,EAAwB,IAAK6E,CAAAA,IAAL,CAAUoG,YAAV,CAAuBjL,CAAvB,CAAxB,CAAA,CAAA;EACH,eAAA;EACJ,aAAA;EACJ,WAAA;EACJ,SATD,CAUA,OAAOmI,CAAP,EAAU,EAAG;;UACb,IAAI,MAAA,KAAW,IAAKqC,CAAAA,MAApB,EAA4B;YACxB,IAAI;EACAjC,YAAAA,GAAG,CAAC4C,gBAAJ,CAAqB,cAArB,EAAqC,0BAArC,CAAA,CAAA;EACH,WAFD,CAGA,OAAOhD,CAAP,EAAU,EAAG;EAChB,SAAA;;UACD,IAAI;EACAI,UAAAA,GAAG,CAAC4C,gBAAJ,CAAqB,QAArB,EAA+B,KAA/B,CAAA,CAAA;EACH,SAFD,CAGA,OAAOhD,CAAP,EAAU,EAtBV;;;UAwBA,IAAI,iBAAA,IAAqBI,GAAzB,EAA8B;EAC1BA,UAAAA,GAAG,CAAC6C,eAAJ,GAAsB,IAAKvG,CAAAA,IAAL,CAAUuG,eAAhC,CAAA;EACH,SAAA;;EACD,QAAA,IAAI,IAAKvG,CAAAA,IAAL,CAAUwG,cAAd,EAA8B;EAC1B9C,UAAAA,GAAG,CAAC+C,OAAJ,GAAc,IAAKzG,CAAAA,IAAL,CAAUwG,cAAxB,CAAA;EACH,SAAA;;UACD9C,GAAG,CAACgD,kBAAJ,GAAyB,YAAM;EAC3B,UAAA,IAAI,CAAMhD,KAAAA,GAAG,CAACnC,UAAd,EACI,OAAA;;YACJ,IAAI,GAAA,KAAQmC,GAAG,CAACiD,MAAZ,IAAsB,IAASjD,KAAAA,GAAG,CAACiD,MAAvC,EAA+C;EAC3C,YAAA,MAAI,CAACC,MAAL,EAAA,CAAA;EACH,WAFD,MAGK;EACD;EACA;cACA,MAAI,CAAC1G,YAAL,CAAkB,YAAM;EACpB,cAAA,MAAI,CAAC2F,OAAL,CAAa,OAAOnC,GAAG,CAACiD,MAAX,KAAsB,QAAtB,GAAiCjD,GAAG,CAACiD,MAArC,GAA8C,CAA3D,CAAA,CAAA;EACH,aAFD,EAEG,CAFH,CAAA,CAAA;EAGH,WAAA;WAZL,CAAA;;EAcAjD,QAAAA,GAAG,CAACmD,IAAJ,CAAS,IAAA,CAAKnN,IAAd,CAAA,CAAA;SA5CJ,CA8CA,OAAO4J,CAAP,EAAU;EACN;EACA;EACA;UACA,IAAKpD,CAAAA,YAAL,CAAkB,YAAM;YACpB,MAAI,CAAC2F,OAAL,CAAavC,CAAb,CAAA,CAAA;EACH,SAFD,EAEG,CAFH,CAAA,CAAA;EAGA,QAAA,OAAA;EACH,OAAA;;EACD,MAAA,IAAI,OAAOwD,QAAP,KAAoB,WAAxB,EAAqC;EACjC,QAAA,IAAA,CAAKC,KAAL,GAAaxB,OAAO,CAACyB,aAAR,EAAb,CAAA;EACAzB,QAAAA,OAAO,CAAC0B,QAAR,CAAiB,IAAKF,CAAAA,KAAtB,IAA+B,IAA/B,CAAA;EACH,OAAA;EACJ,KAAA;EACD;EACJ;EACA;EACA;EACA;;EA3FA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,SAAA;MAAA,KA4FI,EAAA,SAAA,OAAA,CAAQ7D,GAAR,EAAa;EACT,MAAA,IAAA,CAAKrE,YAAL,CAAkB,OAAlB,EAA2BqE,GAA3B,EAAgC,KAAKQ,GAArC,CAAA,CAAA;QACA,IAAKwD,CAAAA,OAAL,CAAa,IAAb,CAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EApGA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,SAAA;MAAA,KAqGI,EAAA,SAAA,OAAA,CAAQC,SAAR,EAAmB;QACf,IAAI,WAAA,KAAgB,OAAO,IAAKzD,CAAAA,GAA5B,IAAmC,IAAS,KAAA,IAAA,CAAKA,GAArD,EAA0D;EACtD,QAAA,OAAA;EACH,OAAA;;EACD,MAAA,IAAA,CAAKA,GAAL,CAASgD,kBAAT,GAA8BlD,KAA9B,CAAA;;EACA,MAAA,IAAI2D,SAAJ,EAAe;UACX,IAAI;YACA,IAAKzD,CAAAA,GAAL,CAAS0D,KAAT,EAAA,CAAA;EACH,SAFD,CAGA,OAAO9D,CAAP,EAAU,EAAG;EAChB,OAAA;;EACD,MAAA,IAAI,OAAOwD,QAAP,KAAoB,WAAxB,EAAqC;EACjC,QAAA,OAAOvB,OAAO,CAAC0B,QAAR,CAAiB,IAAA,CAAKF,KAAtB,CAAP,CAAA;EACH,OAAA;;QACD,IAAKrD,CAAAA,GAAL,GAAW,IAAX,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EAzHA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,QAAA;EAAA,IAAA,KAAA,EA0HI,SAAS,MAAA,GAAA;EACL,MAAA,IAAMhK,IAAI,GAAG,IAAKgK,CAAAA,GAAL,CAAS2D,YAAtB,CAAA;;QACA,IAAI3N,IAAI,KAAK,IAAb,EAAmB;EACf,QAAA,IAAA,CAAKmF,YAAL,CAAkB,MAAlB,EAA0BnF,IAA1B,CAAA,CAAA;UACA,IAAKmF,CAAAA,YAAL,CAAkB,SAAlB,CAAA,CAAA;EACA,QAAA,IAAA,CAAKqI,OAAL,EAAA,CAAA;EACH,OAAA;EACJ,KAAA;EACD;EACJ;EACA;EACA;EACA;;EAtIA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,OAAA;EAAA,IAAA,KAAA,EAuII,SAAQ,KAAA,GAAA;EACJ,MAAA,IAAA,CAAKA,OAAL,EAAA,CAAA;EACH,KAAA;EAzIL,GAAA,CAAA,CAAA,CAAA;;EAAA,EAAA,OAAA,OAAA,CAAA;EAAA,CAAA,CAA6BzJ,OAA7B,CAAA,CAAA;EA2IA8H,OAAO,CAACyB,aAAR,GAAwB,CAAxB,CAAA;EACAzB,OAAO,CAAC0B,QAAR,GAAmB,EAAnB,CAAA;EACA;EACA;EACA;EACA;EACA;;EACA,IAAI,OAAOH,QAAP,KAAoB,WAAxB,EAAqC;EACjC;EACA,EAAA,IAAI,OAAOQ,WAAP,KAAuB,UAA3B,EAAuC;EACnC;EACAA,IAAAA,WAAW,CAAC,UAAD,EAAaC,aAAb,CAAX,CAAA;EACH,GAHD,MAIK,IAAI,OAAO3J,gBAAP,KAA4B,UAAhC,EAA4C;EAC7C,IAAA,IAAM4J,gBAAgB,GAAG,YAAA,IAAgB7H,cAAhB,GAA6B,UAA7B,GAA0C,QAAnE,CAAA;EACA/B,IAAAA,gBAAgB,CAAC4J,gBAAD,EAAmBD,aAAnB,EAAkC,KAAlC,CAAhB,CAAA;EACH,GAAA;EACJ,CAAA;;EACD,SAASA,aAAT,GAAyB;EACrB,EAAA,KAAK,IAAIpM,CAAT,IAAcoK,OAAO,CAAC0B,QAAtB,EAAgC;MAC5B,IAAI1B,OAAO,CAAC0B,QAAR,CAAiBxH,cAAjB,CAAgCtE,CAAhC,CAAJ,EAAwC;EACpCoK,MAAAA,OAAO,CAAC0B,QAAR,CAAiB9L,CAAjB,EAAoBiM,KAApB,EAAA,CAAA;EACH,KAAA;EACJ,GAAA;EACJ;;EC7YM,IAAMK,QAAQ,GAAI,YAAM;EAC3B,EAAA,IAAMC,kBAAkB,GAAG,OAAOC,OAAP,KAAmB,UAAnB,IAAiC,OAAOA,OAAO,CAACC,OAAf,KAA2B,UAAvF,CAAA;;EACA,EAAA,IAAIF,kBAAJ,EAAwB;EACpB,IAAA,OAAO,UAAClJ,EAAD,EAAA;EAAA,MAAA,OAAQmJ,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuBrJ,EAAvB,CAAR,CAAA;OAAP,CAAA;EACH,GAFD,MAGK;MACD,OAAO,UAACA,EAAD,EAAK0B,YAAL,EAAA;EAAA,MAAA,OAAsBA,YAAY,CAAC1B,EAAD,EAAK,CAAL,CAAlC,CAAA;OAAP,CAAA;EACH,GAAA;EACJ,CARuB,EAAjB,CAAA;EASA,IAAMsJ,SAAS,GAAGnI,cAAU,CAACmI,SAAX,IAAwBnI,cAAU,CAACoI,YAArD,CAAA;EACA,IAAMC,qBAAqB,GAAG,IAA9B,CAAA;EACA,IAAMC,iBAAiB,GAAG,aAA1B;;ECLP,IAAMC,aAAa,GAAG,OAAOC,SAAP,KAAqB,WAArB,IAClB,OAAOA,SAAS,CAACC,OAAjB,KAA6B,QADX,IAElBD,SAAS,CAACC,OAAV,CAAkBC,WAAlB,OAAoC,aAFxC,CAAA;EAGA,IAAaC,EAAb,gBAAA,UAAA,UAAA,EAAA;EAAA,EAAA,SAAA,CAAA,EAAA,EAAA,UAAA,CAAA,CAAA;;EAAA,EAAA,IAAA,MAAA,GAAA,YAAA,CAAA,EAAA,CAAA,CAAA;;EACI;EACJ;EACA;EACA;EACA;EACA;EACI,EAAA,SAAA,EAAA,CAAYtI,IAAZ,EAAkB;EAAA,IAAA,IAAA,KAAA,CAAA;;EAAA,IAAA,eAAA,CAAA,IAAA,EAAA,EAAA,CAAA,CAAA;;EACd,IAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAMA,IAAN,CAAA,CAAA;EACA,IAAA,KAAA,CAAK1F,cAAL,GAAsB,CAAC0F,IAAI,CAACqE,WAA5B,CAAA;EAFc,IAAA,OAAA,KAAA,CAAA;EAGjB,GAAA;;EAVL,EAAA,YAAA,CAAA,EAAA,EAAA,CAAA;EAAA,IAAA,GAAA,EAAA,MAAA;EAAA,IAAA,GAAA,EAWI,SAAW,GAAA,GAAA;EACP,MAAA,OAAO,WAAP,CAAA;EACH,KAAA;EAbL,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,QAAA;EAAA,IAAA,KAAA,EAcI,SAAS,MAAA,GAAA;EACL,MAAA,IAAI,CAAC,IAAA,CAAKkE,KAAL,EAAL,EAAmB;EACf;EACA,QAAA,OAAA;EACH,OAAA;;EACD,MAAA,IAAM/C,GAAG,GAAG,IAAKA,CAAAA,GAAL,EAAZ,CAAA;EACA,MAAA,IAAMgD,SAAS,GAAG,IAAA,CAAKxI,IAAL,CAAUwI,SAA5B,CANK;;EAQL,MAAA,IAAMxI,IAAI,GAAGkI,aAAa,GACpB,EADoB,GAEpB9I,IAAI,CAAC,IAAA,CAAKY,IAAN,EAAY,OAAZ,EAAqB,mBAArB,EAA0C,KAA1C,EAAiD,KAAjD,EAAwD,YAAxD,EAAsE,MAAtE,EAA8E,IAA9E,EAAoF,SAApF,EAA+F,oBAA/F,EAAqH,cAArH,EAAqI,iBAArI,EAAwJ,QAAxJ,EAAkK,YAAlK,EAAgL,QAAhL,EAA0L,qBAA1L,CAFV,CAAA;;EAGA,MAAA,IAAI,IAAKA,CAAAA,IAAL,CAAUoG,YAAd,EAA4B;EACxBpG,QAAAA,IAAI,CAACyI,OAAL,GAAe,IAAKzI,CAAAA,IAAL,CAAUoG,YAAzB,CAAA;EACH,OAAA;;QACD,IAAI;EACA,QAAA,IAAA,CAAKsC,EAAL,GACIV,qBAAqB,IAAI,CAACE,aAA1B,GACMM,SAAS,GACL,IAAIV,SAAJ,CAActC,GAAd,EAAmBgD,SAAnB,CADK,GAEL,IAAIV,SAAJ,CAActC,GAAd,CAHV,GAIM,IAAIsC,SAAJ,CAActC,GAAd,EAAmBgD,SAAnB,EAA8BxI,IAA9B,CALV,CAAA;SADJ,CAQA,OAAOkD,GAAP,EAAY;EACR,QAAA,OAAO,KAAKrE,YAAL,CAAkB,OAAlB,EAA2BqE,GAA3B,CAAP,CAAA;EACH,OAAA;;QACD,IAAKwF,CAAAA,EAAL,CAAQvM,UAAR,GAAqB,KAAKmF,MAAL,CAAYnF,UAAZ,IAA0B8L,iBAA/C,CAAA;EACA,MAAA,IAAA,CAAKU,iBAAL,EAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EA9CA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,mBAAA;EAAA,IAAA,KAAA,EA+CI,SAAoB,iBAAA,GAAA;EAAA,MAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EAChB,MAAA,IAAA,CAAKD,EAAL,CAAQE,MAAR,GAAiB,YAAM;EACnB,QAAA,IAAI,MAAI,CAAC5I,IAAL,CAAU6I,SAAd,EAAyB;EACrB,UAAA,MAAI,CAACH,EAAL,CAAQI,OAAR,CAAgBC,KAAhB,EAAA,CAAA;EACH,SAAA;;EACD,QAAA,MAAI,CAACrE,MAAL,EAAA,CAAA;SAJJ,CAAA;;EAMA,MAAA,IAAA,CAAKgE,EAAL,CAAQM,OAAR,GAAkB,UAACC,UAAD,EAAA;UAAA,OAAgB,MAAI,CAACvH,OAAL,CAAa;EAC3CV,UAAAA,WAAW,EAAE,6BAD8B;EAE3CC,UAAAA,OAAO,EAAEgI,UAAAA;EAFkC,SAAb,CAAhB,CAAA;SAAlB,CAAA;;EAIA,MAAA,IAAA,CAAKP,EAAL,CAAQQ,SAAR,GAAoB,UAACC,EAAD,EAAA;EAAA,QAAA,OAAQ,MAAI,CAACrD,MAAL,CAAYqD,EAAE,CAACzP,IAAf,CAAR,CAAA;SAApB,CAAA;;EACA,MAAA,IAAA,CAAKgP,EAAL,CAAQU,OAAR,GAAkB,UAAC9F,CAAD,EAAA;EAAA,QAAA,OAAO,MAAI,CAACuC,OAAL,CAAa,iBAAb,EAAgCvC,CAAhC,CAAP,CAAA;SAAlB,CAAA;EACH,KAAA;EA5DL,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,OAAA;MAAA,KA6DI,EAAA,SAAA,KAAA,CAAMxG,OAAN,EAAe;EAAA,MAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EACX,MAAA,IAAA,CAAKsE,QAAL,GAAgB,KAAhB,CADW;EAGX;;EAHW,MAAA,IAAA,KAAA,GAAA,SAAA,KAAA,CAIFjG,CAJE,EAAA;EAKP,QAAA,IAAM+B,MAAM,GAAGJ,OAAO,CAAC3B,CAAD,CAAtB,CAAA;UACA,IAAMkO,UAAU,GAAGlO,CAAC,KAAK2B,OAAO,CAAC1B,MAAR,GAAiB,CAA1C,CAAA;UACAf,YAAY,CAAC6C,MAAD,EAAS,MAAI,CAAC5C,cAAd,EAA8B,UAACZ,IAAD,EAAU;EAChD;YACA,IAAMsG,IAAI,GAAG,EAAb,CAAA;EAeA;EACA;;;YACA,IAAI;EACA,YAAA,IAAIgI,qBAAJ,EAA2B;EACvB;EACA,cAAA,MAAI,CAACU,EAAL,CAAQ7B,IAAR,CAAanN,IAAb,CAAA,CAAA;EACH,aAGA;EACJ,WARD,CASA,OAAO4J,CAAP,EAAU,EACT;;EACD,UAAA,IAAI+F,UAAJ,EAAgB;EACZ;EACA;EACA5B,YAAAA,QAAQ,CAAC,YAAM;gBACX,MAAI,CAACrG,QAAL,GAAgB,IAAhB,CAAA;;gBACA,MAAI,CAACvC,YAAL,CAAkB,OAAlB,CAAA,CAAA;EACH,aAHO,EAGL,MAAI,CAACqB,YAHA,CAAR,CAAA;EAIH,WAAA;EACJ,SAtCW,CAAZ,CAAA;EAPO,OAAA,CAAA;;EAIX,MAAA,KAAK,IAAI/E,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG2B,OAAO,CAAC1B,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;EAAA,QAAA,KAAA,CAAhCA,CAAgC,CAAA,CAAA;EA0CxC,OAAA;EACJ,KAAA;EA5GL,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,SAAA;EAAA,IAAA,KAAA,EA6GI,SAAU,OAAA,GAAA;EACN,MAAA,IAAI,OAAO,IAAA,CAAKuN,EAAZ,KAAmB,WAAvB,EAAoC;UAChC,IAAKA,CAAAA,EAAL,CAAQ/D,KAAR,EAAA,CAAA;UACA,IAAK+D,CAAAA,EAAL,GAAU,IAAV,CAAA;EACH,OAAA;EACJ,KAAA;EACD;EACJ;EACA;EACA;EACA;;EAvHA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,KAAA;EAAA,IAAA,KAAA,EAwHI,SAAM,GAAA,GAAA;EACF,MAAA,IAAIrH,KAAK,GAAG,IAAKA,CAAAA,KAAL,IAAc,EAA1B,CAAA;QACA,IAAMwD,MAAM,GAAG,IAAK7E,CAAAA,IAAL,CAAUoE,MAAV,GAAmB,KAAnB,GAA2B,IAA1C,CAAA;EACA,MAAA,IAAIJ,IAAI,GAAG,EAAX,CAHE;;EAKF,MAAA,IAAI,IAAKhE,CAAAA,IAAL,CAAUgE,IAAV,KACE,KAAA,KAAUa,MAAV,IAAoBK,MAAM,CAAC,IAAKlF,CAAAA,IAAL,CAAUgE,IAAX,CAAN,KAA2B,GAAhD,IACI,IAAA,KAASa,MAAT,IAAmBK,MAAM,CAAC,IAAA,CAAKlF,IAAL,CAAUgE,IAAX,CAAN,KAA2B,EAFnD,CAAJ,EAE6D;EACzDA,QAAAA,IAAI,GAAG,GAAA,GAAM,IAAKhE,CAAAA,IAAL,CAAUgE,IAAvB,CAAA;EACH,OATC;;;EAWF,MAAA,IAAI,IAAKhE,CAAAA,IAAL,CAAU8E,iBAAd,EAAiC;UAC7BzD,KAAK,CAAC,KAAKrB,IAAL,CAAU+E,cAAX,CAAL,GAAkCxC,KAAK,EAAvC,CAAA;EACH,OAbC;;;QAeF,IAAI,CAAC,IAAKjI,CAAAA,cAAV,EAA0B;UACtB+G,KAAK,CAAC4D,GAAN,GAAY,CAAZ,CAAA;EACH,OAAA;;EACD,MAAA,IAAME,YAAY,GAAGhD,MAAM,CAACd,KAAD,CAA3B,CAAA;EACA,MAAA,IAAM+D,IAAI,GAAG,IAAKpF,CAAAA,IAAL,CAAUkE,QAAV,CAAmBmB,OAAnB,CAA2B,GAA3B,CAAoC,KAAA,CAAC,CAAlD,CAAA;EACA,MAAA,OAAQR,MAAM,GACV,KADI,IAEHO,IAAI,GAAG,GAAA,GAAM,IAAKpF,CAAAA,IAAL,CAAUkE,QAAhB,GAA2B,GAA9B,GAAoC,KAAKlE,IAAL,CAAUkE,QAF/C,CAAA,GAGJF,IAHI,GAIJ,IAAKhE,CAAAA,IAAL,CAAUsF,IAJN,IAKHH,YAAY,CAAC/J,MAAb,GAAsB,GAAA,GAAM+J,YAA5B,GAA2C,EALxC,CAAR,CAAA;EAMH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;EAxJA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,OAAA;EAAA,IAAA,KAAA,EAyJI,SAAQ,KAAA,GAAA;QACJ,OAAO,CAAC,CAAC2C,SAAT,CAAA;EACH,KAAA;EA3JL,GAAA,CAAA,CAAA,CAAA;;EAAA,EAAA,OAAA,EAAA,CAAA;EAAA,CAAA,CAAwB3G,SAAxB,CAAA;;ECRO,IAAMmI,UAAU,GAAG;EACtBC,EAAAA,SAAS,EAAEjB,EADW;EAEtBzE,EAAAA,OAAO,EAAED,OAAAA;EAFa,CAAnB;;ECFP;;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAM4F,EAAE,GAAG,qPAAX,CAAA;EACA,IAAMC,KAAK,GAAG,CACV,QADU,EACA,UADA,EACY,WADZ,EACyB,UADzB,EACqC,MADrC,EAC6C,UAD7C,EACyD,MADzD,EACiE,MADjE,EACyE,UADzE,EACqF,MADrF,EAC6F,WAD7F,EAC0G,MAD1G,EACkH,OADlH,EAC2H,QAD3H,CAAd,CAAA;EAGO,SAASC,KAAT,CAAe/I,GAAf,EAAoB;IACvB,IAAMgJ,GAAG,GAAGhJ,GAAZ;EAAA,MAAiBiJ,CAAC,GAAGjJ,GAAG,CAAC0E,OAAJ,CAAY,GAAZ,CAArB;EAAA,MAAuC/B,CAAC,GAAG3C,GAAG,CAAC0E,OAAJ,CAAY,GAAZ,CAA3C,CAAA;;IACA,IAAIuE,CAAC,IAAI,CAAC,CAAN,IAAWtG,CAAC,IAAI,CAAC,CAArB,EAAwB;EACpB3C,IAAAA,GAAG,GAAGA,GAAG,CAACpE,SAAJ,CAAc,CAAd,EAAiBqN,CAAjB,CAAA,GAAsBjJ,GAAG,CAACpE,SAAJ,CAAcqN,CAAd,EAAiBtG,CAAjB,CAAoBuG,CAAAA,OAApB,CAA4B,IAA5B,EAAkC,GAAlC,CAAtB,GAA+DlJ,GAAG,CAACpE,SAAJ,CAAc+G,CAAd,EAAiB3C,GAAG,CAACvF,MAArB,CAArE,CAAA;EACH,GAAA;;IACD,IAAI0O,CAAC,GAAGN,EAAE,CAACO,IAAH,CAAQpJ,GAAG,IAAI,EAAf,CAAR;QAA4B6E,GAAG,GAAG,EAAlC;QAAsCrK,CAAC,GAAG,EAA1C,CAAA;;IACA,OAAOA,CAAC,EAAR,EAAY;EACRqK,IAAAA,GAAG,CAACiE,KAAK,CAACtO,CAAD,CAAN,CAAH,GAAgB2O,CAAC,CAAC3O,CAAD,CAAD,IAAQ,EAAxB,CAAA;EACH,GAAA;;IACD,IAAIyO,CAAC,IAAI,CAAC,CAAN,IAAWtG,CAAC,IAAI,CAAC,CAArB,EAAwB;MACpBkC,GAAG,CAACwE,MAAJ,GAAaL,GAAb,CAAA;MACAnE,GAAG,CAACyE,IAAJ,GAAWzE,GAAG,CAACyE,IAAJ,CAAS1N,SAAT,CAAmB,CAAnB,EAAsBiJ,GAAG,CAACyE,IAAJ,CAAS7O,MAAT,GAAkB,CAAxC,CAAA,CAA2CyO,OAA3C,CAAmD,IAAnD,EAAyD,GAAzD,CAAX,CAAA;MACArE,GAAG,CAAC0E,SAAJ,GAAgB1E,GAAG,CAAC0E,SAAJ,CAAcL,OAAd,CAAsB,GAAtB,EAA2B,EAA3B,EAA+BA,OAA/B,CAAuC,GAAvC,EAA4C,EAA5C,CAAA,CAAgDA,OAAhD,CAAwD,IAAxD,EAA8D,GAA9D,CAAhB,CAAA;MACArE,GAAG,CAAC2E,OAAJ,GAAc,IAAd,CAAA;EACH,GAAA;;IACD3E,GAAG,CAAC4E,SAAJ,GAAgBA,SAAS,CAAC5E,GAAD,EAAMA,GAAG,CAAC,MAAD,CAAT,CAAzB,CAAA;IACAA,GAAG,CAAC6E,QAAJ,GAAeA,QAAQ,CAAC7E,GAAD,EAAMA,GAAG,CAAC,OAAD,CAAT,CAAvB,CAAA;EACA,EAAA,OAAOA,GAAP,CAAA;EACH,CAAA;;EACD,SAAS4E,SAAT,CAAmBjQ,GAAnB,EAAwBmL,IAAxB,EAA8B;IAC1B,IAAMgF,IAAI,GAAG,UAAb;EAAA,MAAyBC,KAAK,GAAGjF,IAAI,CAACuE,OAAL,CAAaS,IAAb,EAAmB,GAAnB,CAAA,CAAwBxP,KAAxB,CAA8B,GAA9B,CAAjC,CAAA;;EACA,EAAA,IAAIwK,IAAI,CAAC1G,KAAL,CAAW,CAAX,EAAc,CAAd,CAAoB,IAAA,GAApB,IAA2B0G,IAAI,CAAClK,MAAL,KAAgB,CAA/C,EAAkD;EAC9CmP,IAAAA,KAAK,CAAC9L,MAAN,CAAa,CAAb,EAAgB,CAAhB,CAAA,CAAA;EACH,GAAA;;IACD,IAAI6G,IAAI,CAAC1G,KAAL,CAAW,CAAC,CAAZ,CAAA,IAAkB,GAAtB,EAA2B;MACvB2L,KAAK,CAAC9L,MAAN,CAAa8L,KAAK,CAACnP,MAAN,GAAe,CAA5B,EAA+B,CAA/B,CAAA,CAAA;EACH,GAAA;;EACD,EAAA,OAAOmP,KAAP,CAAA;EACH,CAAA;;EACD,SAASF,QAAT,CAAkB7E,GAAlB,EAAuBnE,KAAvB,EAA8B;IAC1B,IAAM3H,IAAI,GAAG,EAAb,CAAA;IACA2H,KAAK,CAACwI,OAAN,CAAc,2BAAd,EAA2C,UAAUW,EAAV,EAAcC,EAAd,EAAkBC,EAAlB,EAAsB;EAC7D,IAAA,IAAID,EAAJ,EAAQ;EACJ/Q,MAAAA,IAAI,CAAC+Q,EAAD,CAAJ,GAAWC,EAAX,CAAA;EACH,KAAA;KAHL,CAAA,CAAA;EAKA,EAAA,OAAOhR,IAAP,CAAA;EACH;;ECtDD,IAAaiR,QAAb,gBAAA,UAAA,QAAA,EAAA;EAAA,EAAA,SAAA,CAAA,MAAA,EAAA,QAAA,CAAA,CAAA;;EAAA,EAAA,IAAA,MAAA,GAAA,YAAA,CAAA,MAAA,CAAA,CAAA;;EACI;EACJ;EACA;EACA;EACA;EACA;EACI,EAAA,SAAA,MAAA,CAAYnF,GAAZ,EAA4B;EAAA,IAAA,IAAA,KAAA,CAAA;;MAAA,IAAXxF,IAAW,uEAAJ,EAAI,CAAA;;EAAA,IAAA,eAAA,CAAA,IAAA,EAAA,MAAA,CAAA,CAAA;;EACxB,IAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;MACA,KAAK4K,CAAAA,WAAL,GAAmB,EAAnB,CAAA;;EACA,IAAA,IAAIpF,GAAG,IAAI,QAAoBA,KAAAA,OAAAA,CAAAA,GAApB,CAAX,EAAoC;EAChCxF,MAAAA,IAAI,GAAGwF,GAAP,CAAA;EACAA,MAAAA,GAAG,GAAG,IAAN,CAAA;EACH,KAAA;;EACD,IAAA,IAAIA,GAAJ,EAAS;EACLA,MAAAA,GAAG,GAAGkE,KAAK,CAAClE,GAAD,CAAX,CAAA;EACAxF,MAAAA,IAAI,CAACkE,QAAL,GAAgBsB,GAAG,CAACyE,IAApB,CAAA;EACAjK,MAAAA,IAAI,CAACoE,MAAL,GAAcoB,GAAG,CAAChI,QAAJ,KAAiB,OAAjB,IAA4BgI,GAAG,CAAChI,QAAJ,KAAiB,KAA3D,CAAA;EACAwC,MAAAA,IAAI,CAACgE,IAAL,GAAYwB,GAAG,CAACxB,IAAhB,CAAA;QACA,IAAIwB,GAAG,CAACnE,KAAR,EACIrB,IAAI,CAACqB,KAAL,GAAamE,GAAG,CAACnE,KAAjB,CAAA;EACP,KAPD,MAQK,IAAIrB,IAAI,CAACiK,IAAT,EAAe;QAChBjK,IAAI,CAACkE,QAAL,GAAgBwF,KAAK,CAAC1J,IAAI,CAACiK,IAAN,CAAL,CAAiBA,IAAjC,CAAA;EACH,KAAA;;MACDlK,qBAAqB,CAAA,sBAAA,CAAA,KAAA,CAAA,EAAOC,IAAP,CAArB,CAAA;EACA,IAAA,KAAA,CAAKoE,MAAL,GACI,IAAA,IAAQpE,IAAI,CAACoE,MAAb,GACMpE,IAAI,CAACoE,MADX,GAEM,OAAON,QAAP,KAAoB,WAApB,IAAmC,QAAaA,KAAAA,QAAQ,CAACtG,QAHnE,CAAA;;MAIA,IAAIwC,IAAI,CAACkE,QAAL,IAAiB,CAAClE,IAAI,CAACgE,IAA3B,EAAiC;EAC7B;QACAhE,IAAI,CAACgE,IAAL,GAAY,KAAA,CAAKI,MAAL,GAAc,KAAd,GAAsB,IAAlC,CAAA;EACH,KAAA;;EACD,IAAA,KAAA,CAAKF,QAAL,GACIlE,IAAI,CAACkE,QAAL,KACK,OAAOJ,QAAP,KAAoB,WAApB,GAAkCA,QAAQ,CAACI,QAA3C,GAAsD,WAD3D,CADJ,CAAA;MAGA,KAAKF,CAAAA,IAAL,GACIhE,IAAI,CAACgE,IAAL,KACK,OAAOF,QAAP,KAAoB,WAApB,IAAmCA,QAAQ,CAACE,IAA5C,GACKF,QAAQ,CAACE,IADd,GAEK,KAAKI,CAAAA,MAAL,GACI,KADJ,GAEI,IALd,CADJ,CAAA;MAOA,KAAKkF,CAAAA,UAAL,GAAkBtJ,IAAI,CAACsJ,UAAL,IAAmB,CAAC,SAAD,EAAY,WAAZ,CAArC,CAAA;MACA,KAAKsB,CAAAA,WAAL,GAAmB,EAAnB,CAAA;MACA,KAAKC,CAAAA,aAAL,GAAqB,CAArB,CAAA;MACA,KAAK7K,CAAAA,IAAL,GAAY,QAAc,CAAA;EACtBsF,MAAAA,IAAI,EAAE,YADgB;EAEtBwF,MAAAA,KAAK,EAAE,KAFe;EAGtBvE,MAAAA,eAAe,EAAE,KAHK;EAItBwE,MAAAA,OAAO,EAAE,IAJa;EAKtBhG,MAAAA,cAAc,EAAE,GALM;EAMtBiG,MAAAA,eAAe,EAAE,KANK;EAOtBC,MAAAA,gBAAgB,EAAE,IAPI;EAQtBC,MAAAA,kBAAkB,EAAE,IARE;EAStBC,MAAAA,iBAAiB,EAAE;EACfC,QAAAA,SAAS,EAAE,IAAA;SAVO;EAYtBC,MAAAA,gBAAgB,EAAE,EAZI;EAatBC,MAAAA,mBAAmB,EAAE,IAAA;OAbb,EAcTtL,IAdS,CAAZ,CAAA;MAeA,KAAKA,CAAAA,IAAL,CAAUsF,IAAV,GACI,MAAKtF,IAAL,CAAUsF,IAAV,CAAeuE,OAAf,CAAuB,KAAvB,EAA8B,EAA9B,CACK,IAAA,KAAA,CAAK7J,IAAL,CAAUiL,gBAAV,GAA6B,GAA7B,GAAmC,EADxC,CADJ,CAAA;;EAGA,IAAA,IAAI,OAAO,KAAKjL,CAAAA,IAAL,CAAUqB,KAAjB,KAA2B,QAA/B,EAAyC;QACrC,KAAKrB,CAAAA,IAAL,CAAUqB,KAAV,GAAkB/F,MAAM,CAAC,KAAK0E,CAAAA,IAAL,CAAUqB,KAAX,CAAxB,CAAA;EACH,KA5DuB;;;MA8DxB,KAAKkK,CAAAA,EAAL,GAAU,IAAV,CAAA;MACA,KAAKC,CAAAA,QAAL,GAAgB,IAAhB,CAAA;MACA,KAAKC,CAAAA,YAAL,GAAoB,IAApB,CAAA;EACA,IAAA,KAAA,CAAKC,WAAL,GAAmB,IAAnB,CAjEwB;;MAmExB,KAAKC,CAAAA,gBAAL,GAAwB,IAAxB,CAAA;;EACA,IAAA,IAAI,OAAO/N,gBAAP,KAA4B,UAAhC,EAA4C;EACxC,MAAA,IAAI,KAAKoC,CAAAA,IAAL,CAAUsL,mBAAd,EAAmC;EAC/B;EACA;EACA;UACA,KAAKM,CAAAA,yBAAL,GAAiC,YAAM;YACnC,IAAI,KAAA,CAAKC,SAAT,EAAoB;EAChB;cACA,KAAKA,CAAAA,SAAL,CAAexN,kBAAf,EAAA,CAAA;;cACA,KAAKwN,CAAAA,SAAL,CAAelH,KAAf,EAAA,CAAA;EACH,WAAA;WALL,CAAA;;EAOA/G,QAAAA,gBAAgB,CAAC,cAAD,EAAiB,MAAKgO,yBAAtB,EAAiD,KAAjD,CAAhB,CAAA;EACH,OAAA;;EACD,MAAA,IAAI,KAAK1H,CAAAA,QAAL,KAAkB,WAAtB,EAAmC;UAC/B,KAAK4H,CAAAA,oBAAL,GAA4B,YAAM;YAC9B,KAAKpK,CAAAA,OAAL,CAAa,iBAAb,EAAgC;EAC5BV,YAAAA,WAAW,EAAE,yBAAA;aADjB,CAAA,CAAA;WADJ,CAAA;;EAKApD,QAAAA,gBAAgB,CAAC,SAAD,EAAY,MAAKkO,oBAAjB,EAAuC,KAAvC,CAAhB,CAAA;EACH,OAAA;EACJ,KAAA;;EACD,IAAA,KAAA,CAAK3F,IAAL,EAAA,CAAA;;EA3FwB,IAAA,OAAA,KAAA,CAAA;EA4F3B,GAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;;EA1GA,EAAA,YAAA,CAAA,MAAA,EAAA,CAAA;EAAA,IAAA,GAAA,EAAA,iBAAA;MAAA,KA2GI,EAAA,SAAA,eAAA,CAAgB4F,IAAhB,EAAsB;QAClB,IAAM1K,KAAK,GAAG,QAAA,CAAc,EAAd,EAAkB,IAAKrB,CAAAA,IAAL,CAAUqB,KAA5B,CAAd,CADkB;;;EAGlBA,MAAAA,KAAK,CAAC2K,GAAN,GAAYxO,UAAZ,CAHkB;;EAKlB6D,MAAAA,KAAK,CAACwK,SAAN,GAAkBE,IAAlB,CALkB;;QAOlB,IAAI,IAAA,CAAKR,EAAT,EACIlK,KAAK,CAAC2D,GAAN,GAAY,KAAKuG,EAAjB,CAAA;;EACJ,MAAA,IAAMvL,IAAI,GAAG,QAAc,CAAA,EAAd,EAAkB,IAAKA,CAAAA,IAAL,CAAUqL,gBAAV,CAA2BU,IAA3B,CAAlB,EAAoD,IAAA,CAAK/L,IAAzD,EAA+D;EACxEqB,QAAAA,KAAK,EAALA,KADwE;EAExEC,QAAAA,MAAM,EAAE,IAFgE;UAGxE4C,QAAQ,EAAE,KAAKA,QAHyD;UAIxEE,MAAM,EAAE,KAAKA,MAJ2D;EAKxEJ,QAAAA,IAAI,EAAE,IAAKA,CAAAA,IAAAA;EAL6D,OAA/D,CAAb,CAAA;;EAOA,MAAA,OAAO,IAAIsF,UAAU,CAACyC,IAAD,CAAd,CAAqB/L,IAArB,CAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EAjIA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,MAAA;EAAA,IAAA,KAAA,EAkII,SAAO,IAAA,GAAA;EAAA,MAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EACH,MAAA,IAAI6L,SAAJ,CAAA;;EACA,MAAA,IAAI,KAAK7L,IAAL,CAAUgL,eAAV,IACAL,MAAM,CAACsB,qBADP,IAEA,IAAK3C,CAAAA,UAAL,CAAgBjE,OAAhB,CAAwB,WAAxB,CAAyC,KAAA,CAAC,CAF9C,EAEiD;EAC7CwG,QAAAA,SAAS,GAAG,WAAZ,CAAA;EACH,OAJD,MAKK,IAAI,CAAA,KAAM,KAAKvC,UAAL,CAAgBlO,MAA1B,EAAkC;EACnC;UACA,IAAK8E,CAAAA,YAAL,CAAkB,YAAM;EACpB,UAAA,MAAI,CAACrB,YAAL,CAAkB,OAAlB,EAA2B,yBAA3B,CAAA,CAAA;EACH,SAFD,EAEG,CAFH,CAAA,CAAA;EAGA,QAAA,OAAA;EACH,OANI,MAOA;EACDgN,QAAAA,SAAS,GAAG,IAAA,CAAKvC,UAAL,CAAgB,CAAhB,CAAZ,CAAA;EACH,OAAA;;EACD,MAAA,IAAA,CAAK/H,UAAL,GAAkB,SAAlB,CAjBG;;QAmBH,IAAI;EACAsK,QAAAA,SAAS,GAAG,IAAA,CAAKK,eAAL,CAAqBL,SAArB,CAAZ,CAAA;SADJ,CAGA,OAAOvI,CAAP,EAAU;UACN,IAAKgG,CAAAA,UAAL,CAAgB6C,KAAhB,EAAA,CAAA;EACA,QAAA,IAAA,CAAKhG,IAAL,EAAA,CAAA;EACA,QAAA,OAAA;EACH,OAAA;;EACD0F,MAAAA,SAAS,CAAC1F,IAAV,EAAA,CAAA;QACA,IAAKiG,CAAAA,YAAL,CAAkBP,SAAlB,CAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EApKA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,cAAA;MAAA,KAqKI,EAAA,SAAA,YAAA,CAAaA,SAAb,EAAwB;EAAA,MAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;QACpB,IAAI,IAAA,CAAKA,SAAT,EAAoB;UAChB,IAAKA,CAAAA,SAAL,CAAexN,kBAAf,EAAA,CAAA;EACH,OAHmB;;;EAKpB,MAAA,IAAA,CAAKwN,SAAL,GAAiBA,SAAjB,CALoB;;EAOpBA,MAAAA,SAAS,CACJlO,EADL,CACQ,OADR,EACiB,IAAA,CAAK0O,OAAL,CAAalM,IAAb,CAAkB,IAAlB,CADjB,EAEKxC,EAFL,CAEQ,QAFR,EAEkB,IAAA,CAAKiE,QAAL,CAAczB,IAAd,CAAmB,IAAnB,CAFlB,CAGKxC,CAAAA,EAHL,CAGQ,OAHR,EAGiB,KAAKkI,OAAL,CAAa1F,IAAb,CAAkB,IAAlB,CAHjB,CAIKxC,CAAAA,EAJL,CAIQ,OAJR,EAIiB,UAACoD,MAAD,EAAA;EAAA,QAAA,OAAY,MAAI,CAACW,OAAL,CAAa,iBAAb,EAAgCX,MAAhC,CAAZ,CAAA;SAJjB,CAAA,CAAA;EAKH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;EAvLA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,OAAA;MAAA,KAwLI,EAAA,SAAA,KAAA,CAAMgL,IAAN,EAAY;EAAA,MAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EACR,MAAA,IAAIF,SAAS,GAAG,IAAA,CAAKK,eAAL,CAAqBH,IAArB,CAAhB,CAAA;QACA,IAAIO,MAAM,GAAG,KAAb,CAAA;QACA3B,MAAM,CAACsB,qBAAP,GAA+B,KAA/B,CAAA;;EACA,MAAA,IAAMM,eAAe,GAAG,SAAlBA,eAAkB,GAAM;EAC1B,QAAA,IAAID,MAAJ,EACI,OAAA;UACJT,SAAS,CAAChF,IAAV,CAAe,CAAC;EAAEpN,UAAAA,IAAI,EAAE,MAAR;EAAgBC,UAAAA,IAAI,EAAE,OAAA;EAAtB,SAAD,CAAf,CAAA,CAAA;EACAmS,QAAAA,SAAS,CAAC7N,IAAV,CAAe,QAAf,EAAyB,UAACwO,GAAD,EAAS;EAC9B,UAAA,IAAIF,MAAJ,EACI,OAAA;;YACJ,IAAI,MAAA,KAAWE,GAAG,CAAC/S,IAAf,IAAuB,OAAY+S,KAAAA,GAAG,CAAC9S,IAA3C,EAAiD;cAC7C,MAAI,CAAC+S,SAAL,GAAiB,IAAjB,CAAA;;EACA,YAAA,MAAI,CAAC5N,YAAL,CAAkB,WAAlB,EAA+BgN,SAA/B,CAAA,CAAA;;cACA,IAAI,CAACA,SAAL,EACI,OAAA;EACJlB,YAAAA,MAAM,CAACsB,qBAAP,GAA+B,WAAgBJ,KAAAA,SAAS,CAACE,IAAzD,CAAA;;EACA,YAAA,MAAI,CAACF,SAAL,CAAetH,KAAf,CAAqB,YAAM;EACvB,cAAA,IAAI+H,MAAJ,EACI,OAAA;EACJ,cAAA,IAAI,QAAa,KAAA,MAAI,CAAC/K,UAAtB,EACI,OAAA;gBACJ2F,OAAO,EAAA,CAAA;;gBACP,MAAI,CAACkF,YAAL,CAAkBP,SAAlB,CAAA,CAAA;;gBACAA,SAAS,CAAChF,IAAV,CAAe,CAAC;EAAEpN,gBAAAA,IAAI,EAAE,SAAA;EAAR,eAAD,CAAf,CAAA,CAAA;;EACA,cAAA,MAAI,CAACoF,YAAL,CAAkB,SAAlB,EAA6BgN,SAA7B,CAAA,CAAA;;EACAA,cAAAA,SAAS,GAAG,IAAZ,CAAA;gBACA,MAAI,CAACY,SAAL,GAAiB,KAAjB,CAAA;;EACA,cAAA,MAAI,CAACC,KAAL,EAAA,CAAA;eAXJ,CAAA,CAAA;EAaH,WAnBD,MAoBK;cACD,IAAMxJ,GAAG,GAAG,IAAIhC,KAAJ,CAAU,aAAV,CAAZ,CADC;;EAGDgC,YAAAA,GAAG,CAAC2I,SAAJ,GAAgBA,SAAS,CAACE,IAA1B,CAAA;;EACA,YAAA,MAAI,CAAClN,YAAL,CAAkB,cAAlB,EAAkCqE,GAAlC,CAAA,CAAA;EACH,WAAA;WA5BL,CAAA,CAAA;SAJJ,CAAA;;EAmCA,MAAA,SAASyJ,eAAT,GAA2B;UACvB,IAAIL,MAAJ,EACI,OAFmB;;EAIvBA,QAAAA,MAAM,GAAG,IAAT,CAAA;UACApF,OAAO,EAAA,CAAA;EACP2E,QAAAA,SAAS,CAAClH,KAAV,EAAA,CAAA;EACAkH,QAAAA,SAAS,GAAG,IAAZ,CAAA;EACH,OA/CO;;;EAiDR,MAAA,IAAMzC,OAAO,GAAG,SAAVA,OAAU,CAAClG,GAAD,EAAS;UACrB,IAAM0J,KAAK,GAAG,IAAI1L,KAAJ,CAAU,eAAkBgC,GAAAA,GAA5B,CAAd,CADqB;;EAGrB0J,QAAAA,KAAK,CAACf,SAAN,GAAkBA,SAAS,CAACE,IAA5B,CAAA;UACAY,eAAe,EAAA,CAAA;;EACf,QAAA,MAAI,CAAC9N,YAAL,CAAkB,cAAlB,EAAkC+N,KAAlC,CAAA,CAAA;SALJ,CAAA;;EAOA,MAAA,SAASC,gBAAT,GAA4B;UACxBzD,OAAO,CAAC,kBAAD,CAAP,CAAA;EACH,OA1DO;;;EA4DR,MAAA,SAASJ,OAAT,GAAmB;UACfI,OAAO,CAAC,eAAD,CAAP,CAAA;EACH,OA9DO;;;QAgER,SAAS0D,SAAT,CAAmBC,EAAnB,EAAuB;UACnB,IAAIlB,SAAS,IAAIkB,EAAE,CAAChB,IAAH,KAAYF,SAAS,CAACE,IAAvC,EAA6C;YACzCY,eAAe,EAAA,CAAA;EAClB,SAAA;EACJ,OApEO;;;EAsER,MAAA,IAAMzF,OAAO,GAAG,SAAVA,OAAU,GAAM;EAClB2E,QAAAA,SAAS,CAACzN,cAAV,CAAyB,MAAzB,EAAiCmO,eAAjC,CAAA,CAAA;EACAV,QAAAA,SAAS,CAACzN,cAAV,CAAyB,OAAzB,EAAkCgL,OAAlC,CAAA,CAAA;EACAyC,QAAAA,SAAS,CAACzN,cAAV,CAAyB,OAAzB,EAAkCyO,gBAAlC,CAAA,CAAA;;EACA,QAAA,MAAI,CAAC5O,GAAL,CAAS,OAAT,EAAkB+K,OAAlB,CAAA,CAAA;;EACA,QAAA,MAAI,CAAC/K,GAAL,CAAS,WAAT,EAAsB6O,SAAtB,CAAA,CAAA;SALJ,CAAA;;EAOAjB,MAAAA,SAAS,CAAC7N,IAAV,CAAe,MAAf,EAAuBuO,eAAvB,CAAA,CAAA;EACAV,MAAAA,SAAS,CAAC7N,IAAV,CAAe,OAAf,EAAwBoL,OAAxB,CAAA,CAAA;EACAyC,MAAAA,SAAS,CAAC7N,IAAV,CAAe,OAAf,EAAwB6O,gBAAxB,CAAA,CAAA;EACA,MAAA,IAAA,CAAK7O,IAAL,CAAU,OAAV,EAAmBgL,OAAnB,CAAA,CAAA;EACA,MAAA,IAAA,CAAKhL,IAAL,CAAU,WAAV,EAAuB8O,SAAvB,CAAA,CAAA;EACAjB,MAAAA,SAAS,CAAC1F,IAAV,EAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EAhRA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,QAAA;EAAA,IAAA,KAAA,EAiRI,SAAS,MAAA,GAAA;QACL,IAAK5E,CAAAA,UAAL,GAAkB,MAAlB,CAAA;EACAoJ,MAAAA,MAAM,CAACsB,qBAAP,GAA+B,gBAAgB,IAAKJ,CAAAA,SAAL,CAAeE,IAA9D,CAAA;QACA,IAAKlN,CAAAA,YAAL,CAAkB,MAAlB,CAAA,CAAA;QACA,IAAK6N,CAAAA,KAAL,GAJK;EAML;;QACA,IAAI,MAAA,KAAW,KAAKnL,UAAhB,IAA8B,KAAKvB,IAAL,CAAU+K,OAA5C,EAAqD;UACjD,IAAI5P,CAAC,GAAG,CAAR,CAAA;EACA,QAAA,IAAM0F,CAAC,GAAG,IAAK2K,CAAAA,QAAL,CAAcpQ,MAAxB,CAAA;;EACA,QAAA,OAAOD,CAAC,GAAG0F,CAAX,EAAc1F,CAAC,EAAf,EAAmB;EACf,UAAA,IAAA,CAAK6R,KAAL,CAAW,IAAA,CAAKxB,QAAL,CAAcrQ,CAAd,CAAX,CAAA,CAAA;EACH,SAAA;EACJ,OAAA;EACJ,KAAA;EACD;EACJ;EACA;EACA;EACA;;EApSA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,UAAA;MAAA,KAqSI,EAAA,SAAA,QAAA,CAAS+B,MAAT,EAAiB;QACb,IAAI,SAAA,KAAc,IAAKqE,CAAAA,UAAnB,IACA,MAAA,KAAW,IAAKA,CAAAA,UADhB,IAEA,SAAA,KAAc,IAAKA,CAAAA,UAFvB,EAEmC;EAC/B,QAAA,IAAA,CAAK1C,YAAL,CAAkB,QAAlB,EAA4B3B,MAA5B,EAD+B;;UAG/B,IAAK2B,CAAAA,YAAL,CAAkB,WAAlB,CAAA,CAAA;;UACA,QAAQ3B,MAAM,CAACzD,IAAf;EACI,UAAA,KAAK,MAAL;cACI,IAAKwT,CAAAA,WAAL,CAAiBC,IAAI,CAACxD,KAAL,CAAWxM,MAAM,CAACxD,IAAlB,CAAjB,CAAA,CAAA;EACA,YAAA,MAAA;;EACJ,UAAA,KAAK,MAAL;EACI,YAAA,IAAA,CAAKyT,gBAAL,EAAA,CAAA;cACA,IAAKC,CAAAA,UAAL,CAAgB,MAAhB,CAAA,CAAA;cACA,IAAKvO,CAAAA,YAAL,CAAkB,MAAlB,CAAA,CAAA;cACA,IAAKA,CAAAA,YAAL,CAAkB,MAAlB,CAAA,CAAA;EACA,YAAA,MAAA;;EACJ,UAAA,KAAK,OAAL;cACI,IAAMqE,GAAG,GAAG,IAAIhC,KAAJ,CAAU,cAAV,CAAZ,CADJ;;EAGIgC,YAAAA,GAAG,CAACmK,IAAJ,GAAWnQ,MAAM,CAACxD,IAAlB,CAAA;cACA,IAAKmM,CAAAA,OAAL,CAAa3C,GAAb,CAAA,CAAA;EACA,YAAA,MAAA;;EACJ,UAAA,KAAK,SAAL;EACI,YAAA,IAAA,CAAKrE,YAAL,CAAkB,MAAlB,EAA0B3B,MAAM,CAACxD,IAAjC,CAAA,CAAA;EACA,YAAA,IAAA,CAAKmF,YAAL,CAAkB,SAAlB,EAA6B3B,MAAM,CAACxD,IAApC,CAAA,CAAA;EACA,YAAA,MAAA;EAnBR,SAAA;EAqBH,OAEA;EACJ,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;EA1UA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,aAAA;MAAA,KA2UI,EAAA,SAAA,WAAA,CAAYA,IAAZ,EAAkB;EACd,MAAA,IAAA,CAAKmF,YAAL,CAAkB,WAAlB,EAA+BnF,IAA/B,CAAA,CAAA;EACA,MAAA,IAAA,CAAK6R,EAAL,GAAU7R,IAAI,CAACsL,GAAf,CAAA;QACA,IAAK6G,CAAAA,SAAL,CAAexK,KAAf,CAAqB2D,GAArB,GAA2BtL,IAAI,CAACsL,GAAhC,CAAA;QACA,IAAKwG,CAAAA,QAAL,GAAgB,IAAK8B,CAAAA,cAAL,CAAoB5T,IAAI,CAAC8R,QAAzB,CAAhB,CAAA;EACA,MAAA,IAAA,CAAKC,YAAL,GAAoB/R,IAAI,CAAC+R,YAAzB,CAAA;EACA,MAAA,IAAA,CAAKC,WAAL,GAAmBhS,IAAI,CAACgS,WAAxB,CAAA;EACA,MAAA,IAAA,CAAK6B,UAAL,GAAkB7T,IAAI,CAAC6T,UAAvB,CAAA;QACA,IAAK7I,CAAAA,MAAL,GARc;;QAUd,IAAI,QAAA,KAAa,IAAKnD,CAAAA,UAAtB,EACI,OAAA;EACJ,MAAA,IAAA,CAAK4L,gBAAL,EAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EA7VA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,kBAAA;EAAA,IAAA,KAAA,EA8VI,SAAmB,gBAAA,GAAA;EAAA,MAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;QACf,IAAK/M,CAAAA,cAAL,CAAoB,IAAA,CAAKuL,gBAAzB,CAAA,CAAA;EACA,MAAA,IAAA,CAAKA,gBAAL,GAAwB,IAAKzL,CAAAA,YAAL,CAAkB,YAAM;UAC5C,MAAI,CAACwB,OAAL,CAAa,cAAb,CAAA,CAAA;EACH,OAFuB,EAErB,IAAK+J,CAAAA,YAAL,GAAoB,IAAA,CAAKC,WAFJ,CAAxB,CAAA;;EAGA,MAAA,IAAI,IAAK1L,CAAAA,IAAL,CAAU6I,SAAd,EAAyB;UACrB,IAAK8C,CAAAA,gBAAL,CAAsB5C,KAAtB,EAAA,CAAA;EACH,OAAA;EACJ,KAAA;EACD;EACJ;EACA;EACA;EACA;;EA3WA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,SAAA;EAAA,IAAA,KAAA,EA4WI,SAAU,OAAA,GAAA;QACN,IAAK6B,CAAAA,WAAL,CAAiBnM,MAAjB,CAAwB,CAAxB,EAA2B,IAAA,CAAKoM,aAAhC,CAAA,CADM;EAGN;EACA;;QACA,IAAKA,CAAAA,aAAL,GAAqB,CAArB,CAAA;;EACA,MAAA,IAAI,CAAM,KAAA,IAAA,CAAKD,WAAL,CAAiBxP,MAA3B,EAAmC;UAC/B,IAAKyD,CAAAA,YAAL,CAAkB,OAAlB,CAAA,CAAA;EACH,OAFD,MAGK;EACD,QAAA,IAAA,CAAK6N,KAAL,EAAA,CAAA;EACH,OAAA;EACJ,KAAA;EACD;EACJ;EACA;EACA;EACA;;EA7XA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,OAAA;EAAA,IAAA,KAAA,EA8XI,SAAQ,KAAA,GAAA;EACJ,MAAA,IAAI,aAAa,IAAKnL,CAAAA,UAAlB,IACA,IAAA,CAAKsK,SAAL,CAAezK,QADf,IAEA,CAAC,KAAKqL,SAFN,IAGA,KAAK7B,WAAL,CAAiBxP,MAHrB,EAG6B;EACzB,QAAA,IAAM0B,OAAO,GAAG,IAAK0Q,CAAAA,kBAAL,EAAhB,CAAA;EACA,QAAA,IAAA,CAAK3B,SAAL,CAAehF,IAAf,CAAoB/J,OAApB,EAFyB;EAIzB;;EACA,QAAA,IAAA,CAAK+N,aAAL,GAAqB/N,OAAO,CAAC1B,MAA7B,CAAA;UACA,IAAKyD,CAAAA,YAAL,CAAkB,OAAlB,CAAA,CAAA;EACH,OAAA;EACJ,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;EAhZA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,oBAAA;EAAA,IAAA,KAAA,EAiZI,SAAqB,kBAAA,GAAA;EACjB,MAAA,IAAM4O,sBAAsB,GAAG,IAAA,CAAKF,UAAL,IAC3B,KAAK1B,SAAL,CAAeE,IAAf,KAAwB,SADG,IAE3B,IAAA,CAAKnB,WAAL,CAAiBxP,MAAjB,GAA0B,CAF9B,CAAA;;QAGA,IAAI,CAACqS,sBAAL,EAA6B;EACzB,QAAA,OAAO,KAAK7C,WAAZ,CAAA;EACH,OAAA;;EACD,MAAA,IAAI8C,WAAW,GAAG,CAAlB,CAPiB;;EAQjB,MAAA,KAAK,IAAIvS,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,IAAKyP,CAAAA,WAAL,CAAiBxP,MAArC,EAA6CD,CAAC,EAA9C,EAAkD;EAC9C,QAAA,IAAMzB,IAAI,GAAG,IAAA,CAAKkR,WAAL,CAAiBzP,CAAjB,EAAoBzB,IAAjC,CAAA;;EACA,QAAA,IAAIA,IAAJ,EAAU;EACNgU,UAAAA,WAAW,IAAIpN,UAAU,CAAC5G,IAAD,CAAzB,CAAA;EACH,SAAA;;UACD,IAAIyB,CAAC,GAAG,CAAJ,IAASuS,WAAW,GAAG,IAAA,CAAKH,UAAhC,EAA4C;YACxC,OAAO,IAAA,CAAK3C,WAAL,CAAiBhM,KAAjB,CAAuB,CAAvB,EAA0BzD,CAA1B,CAAP,CAAA;EACH,SAAA;;UACDuS,WAAW,IAAI,CAAf,CAR8C;EASjD,OAAA;;EACD,MAAA,OAAO,KAAK9C,WAAZ,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;;EA5aA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,OAAA;EAAA,IAAA,KAAA,EA6aI,eAAM4B,GAAN,EAAWmB,OAAX,EAAoB7P,EAApB,EAAwB;QACpB,IAAKsP,CAAAA,UAAL,CAAgB,SAAhB,EAA2BZ,GAA3B,EAAgCmB,OAAhC,EAAyC7P,EAAzC,CAAA,CAAA;EACA,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EAhbL,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,MAAA;EAAA,IAAA,KAAA,EAibI,cAAK0O,GAAL,EAAUmB,OAAV,EAAmB7P,EAAnB,EAAuB;QACnB,IAAKsP,CAAAA,UAAL,CAAgB,SAAhB,EAA2BZ,GAA3B,EAAgCmB,OAAhC,EAAyC7P,EAAzC,CAAA,CAAA;EACA,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EA7bA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,YAAA;MAAA,KA8bI,EAAA,SAAA,UAAA,CAAWrE,IAAX,EAAiBC,IAAjB,EAAuBiU,OAAvB,EAAgC7P,EAAhC,EAAoC;QAChC,IAAI,UAAA,KAAe,OAAOpE,IAA1B,EAAgC;EAC5BoE,QAAAA,EAAE,GAAGpE,IAAL,CAAA;EACAA,QAAAA,IAAI,GAAGuM,SAAP,CAAA;EACH,OAAA;;QACD,IAAI,UAAA,KAAe,OAAO0H,OAA1B,EAAmC;EAC/B7P,QAAAA,EAAE,GAAG6P,OAAL,CAAA;EACAA,QAAAA,OAAO,GAAG,IAAV,CAAA;EACH,OAAA;;EACD,MAAA,IAAI,cAAc,IAAKpM,CAAAA,UAAnB,IAAiC,QAAa,KAAA,IAAA,CAAKA,UAAvD,EAAmE;EAC/D,QAAA,OAAA;EACH,OAAA;;QACDoM,OAAO,GAAGA,OAAO,IAAI,EAArB,CAAA;EACAA,MAAAA,OAAO,CAACC,QAAR,GAAmB,KAAUD,KAAAA,OAAO,CAACC,QAArC,CAAA;EACA,MAAA,IAAM1Q,MAAM,GAAG;EACXzD,QAAAA,IAAI,EAAEA,IADK;EAEXC,QAAAA,IAAI,EAAEA,IAFK;EAGXiU,QAAAA,OAAO,EAAEA,OAAAA;SAHb,CAAA;EAKA,MAAA,IAAA,CAAK9O,YAAL,CAAkB,cAAlB,EAAkC3B,MAAlC,CAAA,CAAA;EACA,MAAA,IAAA,CAAK0N,WAAL,CAAiBrN,IAAjB,CAAsBL,MAAtB,CAAA,CAAA;EACA,MAAA,IAAIY,EAAJ,EACI,IAAA,CAAKE,IAAL,CAAU,OAAV,EAAmBF,EAAnB,CAAA,CAAA;EACJ,MAAA,IAAA,CAAK4O,KAAL,EAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;;EAzdA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,OAAA;EAAA,IAAA,KAAA,EA0dI,SAAQ,KAAA,GAAA;EAAA,MAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EACJ,MAAA,IAAM/H,KAAK,GAAG,SAARA,KAAQ,GAAM;UAChB,MAAI,CAACjD,OAAL,CAAa,cAAb,CAAA,CAAA;;UACA,MAAI,CAACmK,SAAL,CAAelH,KAAf,EAAA,CAAA;SAFJ,CAAA;;EAIA,MAAA,IAAMkJ,eAAe,GAAG,SAAlBA,eAAkB,GAAM;EAC1B,QAAA,MAAI,CAAC5P,GAAL,CAAS,SAAT,EAAoB4P,eAApB,CAAA,CAAA;;EACA,QAAA,MAAI,CAAC5P,GAAL,CAAS,cAAT,EAAyB4P,eAAzB,CAAA,CAAA;;UACAlJ,KAAK,EAAA,CAAA;SAHT,CAAA;;EAKA,MAAA,IAAMmJ,cAAc,GAAG,SAAjBA,cAAiB,GAAM;EACzB;EACA,QAAA,MAAI,CAAC9P,IAAL,CAAU,SAAV,EAAqB6P,eAArB,CAAA,CAAA;;EACA,QAAA,MAAI,CAAC7P,IAAL,CAAU,cAAV,EAA0B6P,eAA1B,CAAA,CAAA;SAHJ,CAAA;;EAKA,MAAA,IAAI,cAAc,IAAKtM,CAAAA,UAAnB,IAAiC,MAAW,KAAA,IAAA,CAAKA,UAArD,EAAiE;UAC7D,IAAKA,CAAAA,UAAL,GAAkB,SAAlB,CAAA;;EACA,QAAA,IAAI,IAAKqJ,CAAAA,WAAL,CAAiBxP,MAArB,EAA6B;EACzB,UAAA,IAAA,CAAK4C,IAAL,CAAU,OAAV,EAAmB,YAAM;cACrB,IAAI,MAAI,CAACyO,SAAT,EAAoB;gBAChBqB,cAAc,EAAA,CAAA;EACjB,aAFD,MAGK;gBACDnJ,KAAK,EAAA,CAAA;EACR,aAAA;aANL,CAAA,CAAA;EAQH,SATD,MAUK,IAAI,IAAK8H,CAAAA,SAAT,EAAoB;YACrBqB,cAAc,EAAA,CAAA;EACjB,SAFI,MAGA;YACDnJ,KAAK,EAAA,CAAA;EACR,SAAA;EACJ,OAAA;;EACD,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EAlgBA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,SAAA;MAAA,KAmgBI,EAAA,SAAA,OAAA,CAAQzB,GAAR,EAAa;QACTyH,MAAM,CAACsB,qBAAP,GAA+B,KAA/B,CAAA;EACA,MAAA,IAAA,CAAKpN,YAAL,CAAkB,OAAlB,EAA2BqE,GAA3B,CAAA,CAAA;EACA,MAAA,IAAA,CAAKxB,OAAL,CAAa,iBAAb,EAAgCwB,GAAhC,CAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EA5gBA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,SAAA;EAAA,IAAA,KAAA,EA6gBI,SAAQnC,OAAAA,CAAAA,MAAR,EAAgBC,WAAhB,EAA6B;QACzB,IAAI,SAAA,KAAc,IAAKO,CAAAA,UAAnB,IACA,MAAA,KAAW,IAAKA,CAAAA,UADhB,IAEA,SAAA,KAAc,IAAKA,CAAAA,UAFvB,EAEmC;EAC/B;EACA,QAAA,IAAA,CAAKnB,cAAL,CAAoB,IAAKuL,CAAAA,gBAAzB,EAF+B;;EAI/B,QAAA,IAAA,CAAKE,SAAL,CAAexN,kBAAf,CAAkC,OAAlC,EAJ+B;;EAM/B,QAAA,IAAA,CAAKwN,SAAL,CAAelH,KAAf,EAAA,CAN+B;;UAQ/B,IAAKkH,CAAAA,SAAL,CAAexN,kBAAf,EAAA,CAAA;;EACA,QAAA,IAAI,OAAOC,mBAAP,KAA+B,UAAnC,EAA+C;EAC3CA,UAAAA,mBAAmB,CAAC,cAAD,EAAiB,KAAKsN,yBAAtB,EAAiD,KAAjD,CAAnB,CAAA;EACAtN,UAAAA,mBAAmB,CAAC,SAAD,EAAY,KAAKwN,oBAAjB,EAAuC,KAAvC,CAAnB,CAAA;EACH,SAZ8B;;;EAc/B,QAAA,IAAA,CAAKvK,UAAL,GAAkB,QAAlB,CAd+B;;EAgB/B,QAAA,IAAA,CAAKgK,EAAL,GAAU,IAAV,CAhB+B;;UAkB/B,IAAK1M,CAAAA,YAAL,CAAkB,OAAlB,EAA2BkC,MAA3B,EAAmCC,WAAnC,EAlB+B;EAoB/B;;UACA,IAAK4J,CAAAA,WAAL,GAAmB,EAAnB,CAAA;UACA,IAAKC,CAAAA,aAAL,GAAqB,CAArB,CAAA;EACH,OAAA;EACJ,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;EA9iBA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,gBAAA;MAAA,KA+iBI,EAAA,SAAA,cAAA,CAAeW,QAAf,EAAyB;QACrB,IAAMuC,gBAAgB,GAAG,EAAzB,CAAA;QACA,IAAI5S,CAAC,GAAG,CAAR,CAAA;EACA,MAAA,IAAM6S,CAAC,GAAGxC,QAAQ,CAACpQ,MAAnB,CAAA;;EACA,MAAA,OAAOD,CAAC,GAAG6S,CAAX,EAAc7S,CAAC,EAAf,EAAmB;EACf,QAAA,IAAI,CAAC,IAAKmO,CAAAA,UAAL,CAAgBjE,OAAhB,CAAwBmG,QAAQ,CAACrQ,CAAD,CAAhC,CAAL,EACI4S,gBAAgB,CAACxQ,IAAjB,CAAsBiO,QAAQ,CAACrQ,CAAD,CAA9B,CAAA,CAAA;EACP,OAAA;;EACD,MAAA,OAAO4S,gBAAP,CAAA;EACH,KAAA;EAxjBL,GAAA,CAAA,CAAA,CAAA;;EAAA,EAAA,OAAA,MAAA,CAAA;EAAA,CAAA,CAA4BtQ,OAA5B,CAAA,CAAA;AA0jBAkN,UAAM,CAACnN,QAAP,GAAkBA,UAAlB;;AC9jBwBmN,UAAM,CAACnN;;ECD/B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASyQ,GAAT,CAAazI,GAAb,EAAkC;IAAA,IAAhBF,IAAgB,uEAAT,EAAS,CAAA;EAAA,EAAA,IAAL4I,GAAK,GAAA,SAAA,CAAA,MAAA,GAAA,CAAA,GAAA,SAAA,CAAA,CAAA,CAAA,GAAA,SAAA,CAAA;EACrC,EAAA,IAAI/T,GAAG,GAAGqL,GAAV,CADqC;;IAGrC0I,GAAG,GAAGA,GAAG,IAAK,OAAOpK,QAAP,KAAoB,WAApB,IAAmCA,QAAjD,CAAA;EACA,EAAA,IAAI,IAAQ0B,IAAAA,GAAZ,EACIA,GAAG,GAAG0I,GAAG,CAAC1Q,QAAJ,GAAe,IAAf,GAAsB0Q,GAAG,CAACjE,IAAhC,CALiC;;EAOrC,EAAA,IAAI,OAAOzE,GAAP,KAAe,QAAnB,EAA6B;EACzB,IAAA,IAAI,QAAQA,GAAG,CAACnJ,MAAJ,CAAW,CAAX,CAAZ,EAA2B;EACvB,MAAA,IAAI,QAAQmJ,GAAG,CAACnJ,MAAJ,CAAW,CAAX,CAAZ,EAA2B;EACvBmJ,QAAAA,GAAG,GAAG0I,GAAG,CAAC1Q,QAAJ,GAAegI,GAArB,CAAA;EACH,OAFD,MAGK;EACDA,QAAAA,GAAG,GAAG0I,GAAG,CAACjE,IAAJ,GAAWzE,GAAjB,CAAA;EACH,OAAA;EACJ,KAAA;;EACD,IAAA,IAAI,CAAC,qBAAsB2I,CAAAA,IAAtB,CAA2B3I,GAA3B,CAAL,EAAsC;QAClC,IAAI,WAAA,KAAgB,OAAO0I,GAA3B,EAAgC;EAC5B1I,QAAAA,GAAG,GAAG0I,GAAG,CAAC1Q,QAAJ,GAAe,IAAf,GAAsBgI,GAA5B,CAAA;EACH,OAFD,MAGK;UACDA,GAAG,GAAG,aAAaA,GAAnB,CAAA;EACH,OAAA;EACJ,KAhBwB;;;EAkBzBrL,IAAAA,GAAG,GAAGuP,KAAK,CAAClE,GAAD,CAAX,CAAA;EACH,GA1BoC;;;EA4BrC,EAAA,IAAI,CAACrL,GAAG,CAAC6J,IAAT,EAAe;EACX,IAAA,IAAI,cAAcmK,IAAd,CAAmBhU,GAAG,CAACqD,QAAvB,CAAJ,EAAsC;QAClCrD,GAAG,CAAC6J,IAAJ,GAAW,IAAX,CAAA;OADJ,MAGK,IAAI,cAAemK,CAAAA,IAAf,CAAoBhU,GAAG,CAACqD,QAAxB,CAAJ,EAAuC;QACxCrD,GAAG,CAAC6J,IAAJ,GAAW,KAAX,CAAA;EACH,KAAA;EACJ,GAAA;;EACD7J,EAAAA,GAAG,CAACmL,IAAJ,GAAWnL,GAAG,CAACmL,IAAJ,IAAY,GAAvB,CAAA;IACA,IAAMF,IAAI,GAAGjL,GAAG,CAAC8P,IAAJ,CAAS5E,OAAT,CAAiB,GAAjB,CAA0B,KAAA,CAAC,CAAxC,CAAA;EACA,EAAA,IAAM4E,IAAI,GAAG7E,IAAI,GAAG,MAAMjL,GAAG,CAAC8P,IAAV,GAAiB,GAApB,GAA0B9P,GAAG,CAAC8P,IAA/C,CAtCqC;;EAwCrC9P,EAAAA,GAAG,CAACoR,EAAJ,GAASpR,GAAG,CAACqD,QAAJ,GAAe,KAAf,GAAuByM,IAAvB,GAA8B,GAA9B,GAAoC9P,GAAG,CAAC6J,IAAxC,GAA+CsB,IAAxD,CAxCqC;;IA0CrCnL,GAAG,CAACiU,IAAJ,GACIjU,GAAG,CAACqD,QAAJ,GACI,KADJ,GAEIyM,IAFJ,IAGKiE,GAAG,IAAIA,GAAG,CAAClK,IAAJ,KAAa7J,GAAG,CAAC6J,IAAxB,GAA+B,EAA/B,GAAoC,GAAM7J,GAAAA,GAAG,CAAC6J,IAHnD,CADJ,CAAA;EAKA,EAAA,OAAO7J,GAAP,CAAA;EACH;;EC1DD,IAAMH,qBAAqB,GAAG,OAAOC,WAAP,KAAuB,UAArD,CAAA;;EACA,IAAMC,MAAM,GAAG,SAATA,MAAS,CAACC,GAAD,EAAS;EACpB,EAAA,OAAO,OAAOF,WAAW,CAACC,MAAnB,KAA8B,UAA9B,GACDD,WAAW,CAACC,MAAZ,CAAmBC,GAAnB,CADC,GAEDA,GAAG,CAACC,MAAJ,YAAsBH,WAF5B,CAAA;EAGH,CAJD,CAAA;;EAKA,IAAMH,QAAQ,GAAGZ,MAAM,CAACW,SAAP,CAAiBC,QAAlC,CAAA;EACA,IAAMH,cAAc,GAAG,OAAOC,IAAP,KAAgB,UAAhB,IAClB,OAAOA,IAAP,KAAgB,WAAhB,IACGE,QAAQ,CAACC,IAAT,CAAcH,IAAd,MAAwB,0BAFhC,CAAA;EAGA,IAAMyU,cAAc,GAAG,OAAOC,IAAP,KAAgB,UAAhB,IAClB,OAAOA,IAAP,KAAgB,WAAhB,IACGxU,QAAQ,CAACC,IAAT,CAAcuU,IAAd,MAAwB,0BAFhC,CAAA;EAGA;EACA;EACA;EACA;EACA;;EACO,SAASC,QAAT,CAAkBpU,GAAlB,EAAuB;IAC1B,OAASH,qBAAqB,KAAKG,GAAG,YAAYF,WAAf,IAA8BC,MAAM,CAACC,GAAD,CAAzC,CAAtB,IACHR,cAAc,IAAIQ,GAAG,YAAYP,IAD9B,IAEHyU,cAAc,IAAIlU,GAAG,YAAYmU,IAFtC,CAAA;EAGH,CAAA;EACM,SAASE,SAAT,CAAmBrU,GAAnB,EAAwBsU,MAAxB,EAAgC;EACnC,EAAA,IAAI,CAACtU,GAAD,IAAQ,QAAOA,GAAP,CAAA,KAAe,QAA3B,EAAqC;EACjC,IAAA,OAAO,KAAP,CAAA;EACH,GAAA;;EACD,EAAA,IAAI6C,KAAK,CAAC0R,OAAN,CAAcvU,GAAd,CAAJ,EAAwB;EACpB,IAAA,KAAK,IAAIgB,CAAC,GAAG,CAAR,EAAW0F,CAAC,GAAG1G,GAAG,CAACiB,MAAxB,EAAgCD,CAAC,GAAG0F,CAApC,EAAuC1F,CAAC,EAAxC,EAA4C;EACxC,MAAA,IAAIqT,SAAS,CAACrU,GAAG,CAACgB,CAAD,CAAJ,CAAb,EAAuB;EACnB,QAAA,OAAO,IAAP,CAAA;EACH,OAAA;EACJ,KAAA;;EACD,IAAA,OAAO,KAAP,CAAA;EACH,GAAA;;EACD,EAAA,IAAIoT,QAAQ,CAACpU,GAAD,CAAZ,EAAmB;EACf,IAAA,OAAO,IAAP,CAAA;EACH,GAAA;;EACD,EAAA,IAAIA,GAAG,CAACsU,MAAJ,IACA,OAAOtU,GAAG,CAACsU,MAAX,KAAsB,UADtB,IAEAtQ,SAAS,CAAC/C,MAAV,KAAqB,CAFzB,EAE4B;MACxB,OAAOoT,SAAS,CAACrU,GAAG,CAACsU,MAAJ,EAAD,EAAe,IAAf,CAAhB,CAAA;EACH,GAAA;;EACD,EAAA,KAAK,IAAMlV,GAAX,IAAkBY,GAAlB,EAAuB;EACnB,IAAA,IAAIjB,MAAM,CAACW,SAAP,CAAiB4F,cAAjB,CAAgC1F,IAAhC,CAAqCI,GAArC,EAA0CZ,GAA1C,CAAA,IAAkDiV,SAAS,CAACrU,GAAG,CAACZ,GAAD,CAAJ,CAA/D,EAA2E;EACvE,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EACJ,GAAA;;EACD,EAAA,OAAO,KAAP,CAAA;EACH;;EChDD;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASoV,iBAAT,CAA2BzR,MAA3B,EAAmC;IACtC,IAAM0R,OAAO,GAAG,EAAhB,CAAA;EACA,EAAA,IAAMC,UAAU,GAAG3R,MAAM,CAACxD,IAA1B,CAAA;IACA,IAAMoV,IAAI,GAAG5R,MAAb,CAAA;IACA4R,IAAI,CAACpV,IAAL,GAAYqV,kBAAkB,CAACF,UAAD,EAAaD,OAAb,CAA9B,CAAA;EACAE,EAAAA,IAAI,CAACE,WAAL,GAAmBJ,OAAO,CAACxT,MAA3B,CALsC;;IAMtC,OAAO;EAAE8B,IAAAA,MAAM,EAAE4R,IAAV;EAAgBF,IAAAA,OAAO,EAAEA,OAAAA;KAAhC,CAAA;EACH,CAAA;;EACD,SAASG,kBAAT,CAA4BrV,IAA5B,EAAkCkV,OAAlC,EAA2C;EACvC,EAAA,IAAI,CAAClV,IAAL,EACI,OAAOA,IAAP,CAAA;;EACJ,EAAA,IAAI6U,QAAQ,CAAC7U,IAAD,CAAZ,EAAoB;EAChB,IAAA,IAAMuV,WAAW,GAAG;EAAEC,MAAAA,YAAY,EAAE,IAAhB;QAAsB9M,GAAG,EAAEwM,OAAO,CAACxT,MAAAA;OAAvD,CAAA;MACAwT,OAAO,CAACrR,IAAR,CAAa7D,IAAb,CAAA,CAAA;EACA,IAAA,OAAOuV,WAAP,CAAA;KAHJ,MAKK,IAAIjS,KAAK,CAAC0R,OAAN,CAAchV,IAAd,CAAJ,EAAyB;MAC1B,IAAMyV,OAAO,GAAG,IAAInS,KAAJ,CAAUtD,IAAI,CAAC0B,MAAf,CAAhB,CAAA;;EACA,IAAA,KAAK,IAAID,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGzB,IAAI,CAAC0B,MAAzB,EAAiCD,CAAC,EAAlC,EAAsC;EAClCgU,MAAAA,OAAO,CAAChU,CAAD,CAAP,GAAa4T,kBAAkB,CAACrV,IAAI,CAACyB,CAAD,CAAL,EAAUyT,OAAV,CAA/B,CAAA;EACH,KAAA;;EACD,IAAA,OAAOO,OAAP,CAAA;EACH,GANI,MAOA,IAAI,OAAOzV,CAAAA,IAAP,CAAgB,KAAA,QAAhB,IAA4B,EAAEA,IAAI,YAAY+I,IAAlB,CAAhC,EAAyD;MAC1D,IAAM0M,QAAO,GAAG,EAAhB,CAAA;;EACA,IAAA,KAAK,IAAM5V,GAAX,IAAkBG,IAAlB,EAAwB;EACpB,MAAA,IAAIR,MAAM,CAACW,SAAP,CAAiB4F,cAAjB,CAAgC1F,IAAhC,CAAqCL,IAArC,EAA2CH,GAA3C,CAAJ,EAAqD;EACjD4V,QAAAA,QAAO,CAAC5V,GAAD,CAAP,GAAewV,kBAAkB,CAACrV,IAAI,CAACH,GAAD,CAAL,EAAYqV,OAAZ,CAAjC,CAAA;EACH,OAAA;EACJ,KAAA;;EACD,IAAA,OAAOO,QAAP,CAAA;EACH,GAAA;;EACD,EAAA,OAAOzV,IAAP,CAAA;EACH,CAAA;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACO,SAAS0V,iBAAT,CAA2BlS,MAA3B,EAAmC0R,OAAnC,EAA4C;IAC/C1R,MAAM,CAACxD,IAAP,GAAc2V,kBAAkB,CAACnS,MAAM,CAACxD,IAAR,EAAckV,OAAd,CAAhC,CAAA;EACA,EAAA,OAAO1R,MAAM,CAAC8R,WAAd,CAF+C;;EAG/C,EAAA,OAAO9R,MAAP,CAAA;EACH,CAAA;;EACD,SAASmS,kBAAT,CAA4B3V,IAA5B,EAAkCkV,OAAlC,EAA2C;EACvC,EAAA,IAAI,CAAClV,IAAL,EACI,OAAOA,IAAP,CAAA;;EACJ,EAAA,IAAIA,IAAI,IAAIA,IAAI,CAACwV,YAAL,KAAsB,IAAlC,EAAwC;MACpC,IAAMI,YAAY,GAAG,OAAO5V,IAAI,CAAC0I,GAAZ,KAAoB,QAApB,IACjB1I,IAAI,CAAC0I,GAAL,IAAY,CADK,IAEjB1I,IAAI,CAAC0I,GAAL,GAAWwM,OAAO,CAACxT,MAFvB,CAAA;;EAGA,IAAA,IAAIkU,YAAJ,EAAkB;EACd,MAAA,OAAOV,OAAO,CAAClV,IAAI,CAAC0I,GAAN,CAAd,CADc;EAEjB,KAFD,MAGK;EACD,MAAA,MAAM,IAAIlB,KAAJ,CAAU,qBAAV,CAAN,CAAA;EACH,KAAA;KATL,MAWK,IAAIlE,KAAK,CAAC0R,OAAN,CAAchV,IAAd,CAAJ,EAAyB;EAC1B,IAAA,KAAK,IAAIyB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGzB,IAAI,CAAC0B,MAAzB,EAAiCD,CAAC,EAAlC,EAAsC;EAClCzB,MAAAA,IAAI,CAACyB,CAAD,CAAJ,GAAUkU,kBAAkB,CAAC3V,IAAI,CAACyB,CAAD,CAAL,EAAUyT,OAAV,CAA5B,CAAA;EACH,KAAA;EACJ,GAJI,MAKA,IAAI,OAAA,CAAOlV,IAAP,CAAA,KAAgB,QAApB,EAA8B;EAC/B,IAAA,KAAK,IAAMH,GAAX,IAAkBG,IAAlB,EAAwB;EACpB,MAAA,IAAIR,MAAM,CAACW,SAAP,CAAiB4F,cAAjB,CAAgC1F,IAAhC,CAAqCL,IAArC,EAA2CH,GAA3C,CAAJ,EAAqD;EACjDG,QAAAA,IAAI,CAACH,GAAD,CAAJ,GAAY8V,kBAAkB,CAAC3V,IAAI,CAACH,GAAD,CAAL,EAAYqV,OAAZ,CAA9B,CAAA;EACH,OAAA;EACJ,KAAA;EACJ,GAAA;;EACD,EAAA,OAAOlV,IAAP,CAAA;EACH;;EC/ED;EACA;EACA;EACA;EACA;;EACO,IAAM8D,QAAQ,GAAG,CAAjB,CAAA;EACA,IAAI+R,UAAJ,CAAA;;EACP,CAAC,UAAUA,UAAV,EAAsB;IACnBA,UAAU,CAACA,UAAU,CAAC,SAAD,CAAV,GAAwB,CAAzB,CAAV,GAAwC,SAAxC,CAAA;IACAA,UAAU,CAACA,UAAU,CAAC,YAAD,CAAV,GAA2B,CAA5B,CAAV,GAA2C,YAA3C,CAAA;IACAA,UAAU,CAACA,UAAU,CAAC,OAAD,CAAV,GAAsB,CAAvB,CAAV,GAAsC,OAAtC,CAAA;IACAA,UAAU,CAACA,UAAU,CAAC,KAAD,CAAV,GAAoB,CAArB,CAAV,GAAoC,KAApC,CAAA;IACAA,UAAU,CAACA,UAAU,CAAC,eAAD,CAAV,GAA8B,CAA/B,CAAV,GAA8C,eAA9C,CAAA;IACAA,UAAU,CAACA,UAAU,CAAC,cAAD,CAAV,GAA6B,CAA9B,CAAV,GAA6C,cAA7C,CAAA;IACAA,UAAU,CAACA,UAAU,CAAC,YAAD,CAAV,GAA2B,CAA5B,CAAV,GAA2C,YAA3C,CAAA;EACH,CARD,EAQGA,UAAU,KAAKA,UAAU,GAAG,EAAlB,CARb,CAAA,CAAA;EASA;EACA;EACA;;;EACA,IAAaC,OAAb,gBAAA,YAAA;EACI;EACJ;EACA;EACA;EACA;EACI,EAAA,SAAA,OAAA,CAAYC,QAAZ,EAAsB;EAAA,IAAA,eAAA,CAAA,IAAA,EAAA,OAAA,CAAA,CAAA;;MAClB,IAAKA,CAAAA,QAAL,GAAgBA,QAAhB,CAAA;EACH,GAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;;EAdA,EAAA,YAAA,CAAA,OAAA,EAAA,CAAA;EAAA,IAAA,GAAA,EAAA,QAAA;MAAA,KAeI,EAAA,SAAA,MAAA,CAAOtV,GAAP,EAAY;EACR,MAAA,IAAIA,GAAG,CAACV,IAAJ,KAAa8V,UAAU,CAACG,KAAxB,IAAiCvV,GAAG,CAACV,IAAJ,KAAa8V,UAAU,CAACI,GAA7D,EAAkE;EAC9D,QAAA,IAAInB,SAAS,CAACrU,GAAD,CAAb,EAAoB;YAChB,OAAO,IAAA,CAAKyV,cAAL,CAAoB;EACvBnW,YAAAA,IAAI,EAAEU,GAAG,CAACV,IAAJ,KAAa8V,UAAU,CAACG,KAAxB,GACAH,UAAU,CAACM,YADX,GAEAN,UAAU,CAACO,UAHM;cAIvBC,GAAG,EAAE5V,GAAG,CAAC4V,GAJc;cAKvBrW,IAAI,EAAES,GAAG,CAACT,IALa;cAMvB6R,EAAE,EAAEpR,GAAG,CAACoR,EAAAA;EANe,WAApB,CAAP,CAAA;EAQH,SAAA;EACJ,OAAA;;EACD,MAAA,OAAO,CAAC,IAAKyE,CAAAA,cAAL,CAAoB7V,GAApB,CAAD,CAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;;EAhCA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,gBAAA;MAAA,KAiCI,EAAA,SAAA,cAAA,CAAeA,GAAf,EAAoB;EAChB;EACA,MAAA,IAAIwG,GAAG,GAAG,EAAA,GAAKxG,GAAG,CAACV,IAAnB,CAFgB;;EAIhB,MAAA,IAAIU,GAAG,CAACV,IAAJ,KAAa8V,UAAU,CAACM,YAAxB,IACA1V,GAAG,CAACV,IAAJ,KAAa8V,UAAU,CAACO,UAD5B,EACwC;EACpCnP,QAAAA,GAAG,IAAIxG,GAAG,CAAC6U,WAAJ,GAAkB,GAAzB,CAAA;EACH,OAPe;EAShB;;;QACA,IAAI7U,GAAG,CAAC4V,GAAJ,IAAW,QAAQ5V,GAAG,CAAC4V,GAA3B,EAAgC;EAC5BpP,QAAAA,GAAG,IAAIxG,GAAG,CAAC4V,GAAJ,GAAU,GAAjB,CAAA;EACH,OAZe;;;EAchB,MAAA,IAAI,IAAQ5V,IAAAA,GAAG,CAACoR,EAAhB,EAAoB;UAChB5K,GAAG,IAAIxG,GAAG,CAACoR,EAAX,CAAA;EACH,OAhBe;;;EAkBhB,MAAA,IAAI,IAAQpR,IAAAA,GAAG,CAACT,IAAhB,EAAsB;UAClBiH,GAAG,IAAIuM,IAAI,CAAC+C,SAAL,CAAe9V,GAAG,CAACT,IAAnB,EAAyB,IAAK+V,CAAAA,QAA9B,CAAP,CAAA;EACH,OAAA;;EACD,MAAA,OAAO9O,GAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EA5DA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,gBAAA;MAAA,KA6DI,EAAA,SAAA,cAAA,CAAexG,GAAf,EAAoB;EAChB,MAAA,IAAM+V,cAAc,GAAGvB,iBAAiB,CAACxU,GAAD,CAAxC,CAAA;QACA,IAAM2U,IAAI,GAAG,IAAKkB,CAAAA,cAAL,CAAoBE,cAAc,CAAChT,MAAnC,CAAb,CAAA;EACA,MAAA,IAAM0R,OAAO,GAAGsB,cAAc,CAACtB,OAA/B,CAAA;EACAA,MAAAA,OAAO,CAACuB,OAAR,CAAgBrB,IAAhB,EAJgB;;QAKhB,OAAOF,OAAP,CALgB;EAMnB,KAAA;EAnEL,GAAA,CAAA,CAAA,CAAA;;EAAA,EAAA,OAAA,OAAA,CAAA;EAAA,CAAA,EAAA,CAAA;EAqEA;EACA;EACA;EACA;EACA;;EACA,IAAawB,OAAb,gBAAA,UAAA,QAAA,EAAA;EAAA,EAAA,SAAA,CAAA,OAAA,EAAA,QAAA,CAAA,CAAA;;EAAA,EAAA,IAAA,MAAA,GAAA,YAAA,CAAA,OAAA,CAAA,CAAA;;EACI;EACJ;EACA;EACA;EACA;EACI,EAAA,SAAA,OAAA,CAAYC,OAAZ,EAAqB;EAAA,IAAA,IAAA,KAAA,CAAA;;EAAA,IAAA,eAAA,CAAA,IAAA,EAAA,OAAA,CAAA,CAAA;;EACjB,IAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;MACA,KAAKA,CAAAA,OAAL,GAAeA,OAAf,CAAA;EAFiB,IAAA,OAAA,KAAA,CAAA;EAGpB,GAAA;EACD;EACJ;EACA;EACA;EACA;;;EAdA,EAAA,YAAA,CAAA,OAAA,EAAA,CAAA;EAAA,IAAA,GAAA,EAAA,KAAA;MAAA,KAeI,EAAA,SAAA,GAAA,CAAIlW,GAAJ,EAAS;EACL,MAAA,IAAI+C,MAAJ,CAAA;;EACA,MAAA,IAAI,OAAO/C,GAAP,KAAe,QAAnB,EAA6B;UACzB,IAAI,IAAA,CAAKmW,aAAT,EAAwB;EACpB,UAAA,MAAM,IAAIpP,KAAJ,CAAU,iDAAV,CAAN,CAAA;EACH,SAAA;;EACDhE,QAAAA,MAAM,GAAG,IAAA,CAAKqT,YAAL,CAAkBpW,GAAlB,CAAT,CAAA;UACA,IAAMqW,aAAa,GAAGtT,MAAM,CAACzD,IAAP,KAAgB8V,UAAU,CAACM,YAAjD,CAAA;;UACA,IAAIW,aAAa,IAAItT,MAAM,CAACzD,IAAP,KAAgB8V,UAAU,CAACO,UAAhD,EAA4D;EACxD5S,UAAAA,MAAM,CAACzD,IAAP,GAAc+W,aAAa,GAAGjB,UAAU,CAACG,KAAd,GAAsBH,UAAU,CAACI,GAA5D,CADwD;;YAGxD,IAAKW,CAAAA,aAAL,GAAqB,IAAIG,mBAAJ,CAAwBvT,MAAxB,CAArB,CAHwD;;EAKxD,UAAA,IAAIA,MAAM,CAAC8R,WAAP,KAAuB,CAA3B,EAA8B;cAC1B,IAAmB,CAAA,eAAA,CAAA,OAAA,CAAA,SAAA,CAAA,EAAA,cAAA,EAAA,IAAA,CAAA,CAAA,IAAA,CAAA,IAAA,EAAA,SAAnB,EAA8B9R,MAA9B,CAAA,CAAA;EACH,WAAA;EACJ,SARD,MASK;EACD;YACA,IAAmB,CAAA,eAAA,CAAA,OAAA,CAAA,SAAA,CAAA,EAAA,cAAA,EAAA,IAAA,CAAA,CAAA,IAAA,CAAA,IAAA,EAAA,SAAnB,EAA8BA,MAA9B,CAAA,CAAA;EACH,SAAA;SAlBL,MAoBK,IAAIqR,QAAQ,CAACpU,GAAD,CAAR,IAAiBA,GAAG,CAACoB,MAAzB,EAAiC;EAClC;UACA,IAAI,CAAC,IAAK+U,CAAAA,aAAV,EAAyB;EACrB,UAAA,MAAM,IAAIpP,KAAJ,CAAU,kDAAV,CAAN,CAAA;EACH,SAFD,MAGK;EACDhE,UAAAA,MAAM,GAAG,IAAKoT,CAAAA,aAAL,CAAmBI,cAAnB,CAAkCvW,GAAlC,CAAT,CAAA;;EACA,UAAA,IAAI+C,MAAJ,EAAY;EACR;cACA,IAAKoT,CAAAA,aAAL,GAAqB,IAArB,CAAA;;cACA,IAAmB,CAAA,eAAA,CAAA,OAAA,CAAA,SAAA,CAAA,EAAA,cAAA,EAAA,IAAA,CAAA,CAAA,IAAA,CAAA,IAAA,EAAA,SAAnB,EAA8BpT,MAA9B,CAAA,CAAA;EACH,WAAA;EACJ,SAAA;EACJ,OAbI,MAcA;EACD,QAAA,MAAM,IAAIgE,KAAJ,CAAU,gBAAA,GAAmB/G,GAA7B,CAAN,CAAA;EACH,OAAA;EACJ,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;EA5DA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,cAAA;MAAA,KA6DI,EAAA,SAAA,YAAA,CAAawG,GAAb,EAAkB;EACd,MAAA,IAAIxF,CAAC,GAAG,CAAR,CADc;;EAGd,MAAA,IAAMO,CAAC,GAAG;UACNjC,IAAI,EAAEyL,MAAM,CAACvE,GAAG,CAACtE,MAAJ,CAAW,CAAX,CAAD,CAAA;SADhB,CAAA;;QAGA,IAAIkT,UAAU,CAAC7T,CAAC,CAACjC,IAAH,CAAV,KAAuBwM,SAA3B,EAAsC;EAClC,QAAA,MAAM,IAAI/E,KAAJ,CAAU,yBAAyBxF,CAAC,CAACjC,IAArC,CAAN,CAAA;EACH,OARa;;;EAUd,MAAA,IAAIiC,CAAC,CAACjC,IAAF,KAAW8V,UAAU,CAACM,YAAtB,IACAnU,CAAC,CAACjC,IAAF,KAAW8V,UAAU,CAACO,UAD1B,EACsC;EAClC,QAAA,IAAMa,KAAK,GAAGxV,CAAC,GAAG,CAAlB,CAAA;;EACA,QAAA,OAAOwF,GAAG,CAACtE,MAAJ,CAAW,EAAElB,CAAb,CAAA,KAAoB,GAApB,IAA2BA,CAAC,IAAIwF,GAAG,CAACvF,MAA3C,EAAmD,EAAG;;UACtD,IAAMwV,GAAG,GAAGjQ,GAAG,CAACpE,SAAJ,CAAcoU,KAAd,EAAqBxV,CAArB,CAAZ,CAAA;;EACA,QAAA,IAAIyV,GAAG,IAAI1L,MAAM,CAAC0L,GAAD,CAAb,IAAsBjQ,GAAG,CAACtE,MAAJ,CAAWlB,CAAX,CAAA,KAAkB,GAA5C,EAAiD;EAC7C,UAAA,MAAM,IAAI+F,KAAJ,CAAU,qBAAV,CAAN,CAAA;EACH,SAAA;;EACDxF,QAAAA,CAAC,CAACsT,WAAF,GAAgB9J,MAAM,CAAC0L,GAAD,CAAtB,CAAA;EACH,OAnBa;;;QAqBd,IAAI,GAAA,KAAQjQ,GAAG,CAACtE,MAAJ,CAAWlB,CAAC,GAAG,CAAf,CAAZ,EAA+B;EAC3B,QAAA,IAAMwV,MAAK,GAAGxV,CAAC,GAAG,CAAlB,CAAA;;UACA,OAAO,EAAEA,CAAT,EAAY;EACR,UAAA,IAAMyF,CAAC,GAAGD,GAAG,CAACtE,MAAJ,CAAWlB,CAAX,CAAV,CAAA;YACA,IAAI,GAAA,KAAQyF,CAAZ,EACI,MAAA;EACJ,UAAA,IAAIzF,CAAC,KAAKwF,GAAG,CAACvF,MAAd,EACI,MAAA;EACP,SAAA;;UACDM,CAAC,CAACqU,GAAF,GAAQpP,GAAG,CAACpE,SAAJ,CAAcoU,MAAd,EAAqBxV,CAArB,CAAR,CAAA;EACH,OAVD,MAWK;UACDO,CAAC,CAACqU,GAAF,GAAQ,GAAR,CAAA;EACH,OAlCa;;;QAoCd,IAAMc,IAAI,GAAGlQ,GAAG,CAACtE,MAAJ,CAAWlB,CAAC,GAAG,CAAf,CAAb,CAAA;;QACA,IAAI,EAAA,KAAO0V,IAAP,IAAe3L,MAAM,CAAC2L,IAAD,CAAN,IAAgBA,IAAnC,EAAyC;EACrC,QAAA,IAAMF,OAAK,GAAGxV,CAAC,GAAG,CAAlB,CAAA;;UACA,OAAO,EAAEA,CAAT,EAAY;EACR,UAAA,IAAMyF,EAAC,GAAGD,GAAG,CAACtE,MAAJ,CAAWlB,CAAX,CAAV,CAAA;;YACA,IAAI,IAAA,IAAQyF,EAAR,IAAasE,MAAM,CAACtE,EAAD,CAAN,IAAaA,EAA9B,EAAiC;EAC7B,YAAA,EAAEzF,CAAF,CAAA;EACA,YAAA,MAAA;EACH,WAAA;;EACD,UAAA,IAAIA,CAAC,KAAKwF,GAAG,CAACvF,MAAd,EACI,MAAA;EACP,SAAA;;EACDM,QAAAA,CAAC,CAAC6P,EAAF,GAAOrG,MAAM,CAACvE,GAAG,CAACpE,SAAJ,CAAcoU,OAAd,EAAqBxV,CAAC,GAAG,CAAzB,CAAD,CAAb,CAAA;EACH,OAjDa;;;EAmDd,MAAA,IAAIwF,GAAG,CAACtE,MAAJ,CAAW,EAAElB,CAAb,CAAJ,EAAqB;UACjB,IAAM2V,OAAO,GAAG,IAAA,CAAKC,QAAL,CAAcpQ,GAAG,CAACqQ,MAAJ,CAAW7V,CAAX,CAAd,CAAhB,CAAA;;UACA,IAAIiV,OAAO,CAACa,cAAR,CAAuBvV,CAAC,CAACjC,IAAzB,EAA+BqX,OAA/B,CAAJ,EAA6C;YACzCpV,CAAC,CAAChC,IAAF,GAASoX,OAAT,CAAA;EACH,SAFD,MAGK;EACD,UAAA,MAAM,IAAI5P,KAAJ,CAAU,iBAAV,CAAN,CAAA;EACH,SAAA;EACJ,OAAA;;EACD,MAAA,OAAOxF,CAAP,CAAA;EACH,KAAA;EA1HL,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,UAAA;MAAA,KA2HI,EAAA,SAAA,QAAA,CAASiF,GAAT,EAAc;QACV,IAAI;UACA,OAAOuM,IAAI,CAACxD,KAAL,CAAW/I,GAAX,EAAgB,IAAA,CAAK0P,OAArB,CAAP,CAAA;SADJ,CAGA,OAAO/M,CAAP,EAAU;EACN,QAAA,OAAO,KAAP,CAAA;EACH,OAAA;EACJ,KAAA;EAlIL,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,SAAA;EAAA,IAAA,KAAA;EAmJI;EACJ;EACA;MACI,SAAU,OAAA,GAAA;QACN,IAAI,IAAA,CAAKgN,aAAT,EAAwB;UACpB,IAAKA,CAAAA,aAAL,CAAmBY,sBAAnB,EAAA,CAAA;UACA,IAAKZ,CAAAA,aAAL,GAAqB,IAArB,CAAA;EACH,OAAA;EACJ,KAAA;EA3JL,GAAA,CAAA,EAAA,CAAA;EAAA,IAAA,GAAA,EAAA,gBAAA;EAAA,IAAA,KAAA,EAmII,SAAsB7W,cAAAA,CAAAA,IAAtB,EAA4BqX,OAA5B,EAAqC;EACjC,MAAA,QAAQrX,IAAR;UACI,KAAK8V,UAAU,CAAC4B,OAAhB;YACI,OAAO,OAAA,CAAOL,OAAP,CAAA,KAAmB,QAA1B,CAAA;;UACJ,KAAKvB,UAAU,CAAC6B,UAAhB;YACI,OAAON,OAAO,KAAK7K,SAAnB,CAAA;;UACJ,KAAKsJ,UAAU,CAAC8B,aAAhB;YACI,OAAO,OAAOP,OAAP,KAAmB,QAAnB,IAA+B,OAAOA,CAAAA,OAAP,MAAmB,QAAzD,CAAA;;UACJ,KAAKvB,UAAU,CAACG,KAAhB,CAAA;UACA,KAAKH,UAAU,CAACM,YAAhB;YACI,OAAO7S,KAAK,CAAC0R,OAAN,CAAcoC,OAAd,KAA0BA,OAAO,CAAC1V,MAAR,GAAiB,CAAlD,CAAA;;UACJ,KAAKmU,UAAU,CAACI,GAAhB,CAAA;UACA,KAAKJ,UAAU,CAACO,UAAhB;EACI,UAAA,OAAO9S,KAAK,CAAC0R,OAAN,CAAcoC,OAAd,CAAP,CAAA;EAZR,OAAA;EAcH,KAAA;EAlJL,GAAA,CAAA,CAAA,CAAA;;EAAA,EAAA,OAAA,OAAA,CAAA;EAAA,CAAA,CAA6BrT,OAA7B,CAAA,CAAA;EA6JA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;MACMgT;EACF,EAAA,SAAA,mBAAA,CAAYvT,MAAZ,EAAoB;EAAA,IAAA,eAAA,CAAA,IAAA,EAAA,mBAAA,CAAA,CAAA;;MAChB,IAAKA,CAAAA,MAAL,GAAcA,MAAd,CAAA;MACA,IAAK0R,CAAAA,OAAL,GAAe,EAAf,CAAA;MACA,IAAK0C,CAAAA,SAAL,GAAiBpU,MAAjB,CAAA;EACH,GAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;;;;;EACI,IAAA,KAAA,EAAA,SAAA,cAAA,CAAeqU,OAAf,EAAwB;EACpB,MAAA,IAAA,CAAK3C,OAAL,CAAarR,IAAb,CAAkBgU,OAAlB,CAAA,CAAA;;QACA,IAAI,IAAA,CAAK3C,OAAL,CAAaxT,MAAb,KAAwB,IAAKkW,CAAAA,SAAL,CAAetC,WAA3C,EAAwD;EACpD;UACA,IAAM9R,MAAM,GAAGkS,iBAAiB,CAAC,KAAKkC,SAAN,EAAiB,IAAK1C,CAAAA,OAAtB,CAAhC,CAAA;EACA,QAAA,IAAA,CAAKsC,sBAAL,EAAA,CAAA;EACA,QAAA,OAAOhU,MAAP,CAAA;EACH,OAAA;;EACD,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;;;;aACI,SAAyB,sBAAA,GAAA;QACrB,IAAKoU,CAAAA,SAAL,GAAiB,IAAjB,CAAA;QACA,IAAK1C,CAAAA,OAAL,GAAe,EAAf,CAAA;EACH,KAAA;;;;;;;;;;;;;;ECnSE,SAASjR,EAAT,CAAYxD,GAAZ,EAAiBgP,EAAjB,EAAqBrL,EAArB,EAAyB;EAC5B3D,EAAAA,GAAG,CAACwD,EAAJ,CAAOwL,EAAP,EAAWrL,EAAX,CAAA,CAAA;IACA,OAAO,SAAS0T,UAAT,GAAsB;EACzBrX,IAAAA,GAAG,CAAC8D,GAAJ,CAAQkL,EAAR,EAAYrL,EAAZ,CAAA,CAAA;KADJ,CAAA;EAGH;;ECFD;EACA;EACA;EACA;;EACA,IAAM2T,eAAe,GAAGvY,MAAM,CAACwY,MAAP,CAAc;EAClCC,EAAAA,OAAO,EAAE,CADyB;EAElCC,EAAAA,aAAa,EAAE,CAFmB;EAGlCC,EAAAA,UAAU,EAAE,CAHsB;EAIlCC,EAAAA,aAAa,EAAE,CAJmB;EAKlC;EACAC,EAAAA,WAAW,EAAE,CANqB;EAOlC3T,EAAAA,cAAc,EAAE,CAAA;EAPkB,CAAd,CAAxB,CAAA;EASA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,IAAauM,MAAb,gBAAA,UAAA,QAAA,EAAA;EAAA,EAAA,SAAA,CAAA,MAAA,EAAA,QAAA,CAAA,CAAA;;EAAA,EAAA,IAAA,MAAA,GAAA,YAAA,CAAA,MAAA,CAAA,CAAA;;EACI;EACJ;EACA;EACI,EAAA,SAAA,MAAA,CAAYqH,EAAZ,EAAgBjC,GAAhB,EAAqB/P,IAArB,EAA2B;EAAA,IAAA,IAAA,KAAA,CAAA;;EAAA,IAAA,eAAA,CAAA,IAAA,EAAA,MAAA,CAAA,CAAA;;EACvB,IAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;EACA;EACR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;MACQ,KAAKiS,CAAAA,SAAL,GAAiB,KAAjB,CAAA;EACA;EACR;EACA;EACA;;MACQ,KAAKC,CAAAA,SAAL,GAAiB,KAAjB,CAAA;EACA;EACR;EACA;;MACQ,KAAKC,CAAAA,aAAL,GAAqB,EAArB,CAAA;EACA;EACR;EACA;;MACQ,KAAKC,CAAAA,UAAL,GAAkB,EAAlB,CAAA;EACA;EACR;EACA;EACA;EACA;EACA;;MACQ,KAAKC,CAAAA,MAAL,GAAc,EAAd,CAAA;EACA;EACR;EACA;EACA;;MACQ,KAAKC,CAAAA,SAAL,GAAiB,CAAjB,CAAA;MACA,KAAKC,CAAAA,GAAL,GAAW,CAAX,CAAA;MACA,KAAKC,CAAAA,IAAL,GAAY,EAAZ,CAAA;MACA,KAAKC,CAAAA,KAAL,GAAa,EAAb,CAAA;MACA,KAAKT,CAAAA,EAAL,GAAUA,EAAV,CAAA;MACA,KAAKjC,CAAAA,GAAL,GAAWA,GAAX,CAAA;;EACA,IAAA,IAAI/P,IAAI,IAAIA,IAAI,CAAC0S,IAAjB,EAAuB;EACnB,MAAA,KAAA,CAAKA,IAAL,GAAY1S,IAAI,CAAC0S,IAAjB,CAAA;EACH,KAAA;;EACD,IAAA,KAAA,CAAKC,KAAL,GAAa,QAAA,CAAc,EAAd,EAAkB3S,IAAlB,CAAb,CAAA;EACA,IAAA,IAAI,MAAKgS,EAAL,CAAQY,YAAZ,EACI,MAAKzM,IAAL,EAAA,CAAA;EApDmB,IAAA,OAAA,KAAA,CAAA;EAqD1B,GAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EAvEA,EAAA,YAAA,CAAA,MAAA,EAAA,CAAA;EAAA,IAAA,GAAA,EAAA,cAAA;EAAA,IAAA,GAAA,EAwEI,SAAmB,GAAA,GAAA;QACf,OAAO,CAAC,KAAK8L,SAAb,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EA/EA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,WAAA;EAAA,IAAA,KAAA,EAgFI,SAAY,SAAA,GAAA;QACR,IAAI,IAAA,CAAKY,IAAT,EACI,OAAA;QACJ,IAAMb,EAAE,GAAG,IAAA,CAAKA,EAAhB,CAAA;EACA,MAAA,IAAA,CAAKa,IAAL,GAAY,CACRlV,EAAE,CAACqU,EAAD,EAAK,MAAL,EAAa,IAAA,CAAKpJ,MAAL,CAAYzI,IAAZ,CAAiB,IAAjB,CAAb,CADM,EAERxC,EAAE,CAACqU,EAAD,EAAK,QAAL,EAAe,IAAKc,CAAAA,QAAL,CAAc3S,IAAd,CAAmB,IAAnB,CAAf,CAFM,EAGRxC,EAAE,CAACqU,EAAD,EAAK,OAAL,EAAc,IAAK5I,CAAAA,OAAL,CAAajJ,IAAb,CAAkB,IAAlB,CAAd,CAHM,EAIRxC,EAAE,CAACqU,EAAD,EAAK,OAAL,EAAc,IAAA,CAAKhJ,OAAL,CAAa7I,IAAb,CAAkB,IAAlB,CAAd,CAJM,CAAZ,CAAA;EAMH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EA3GA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,QAAA;EAAA,IAAA,GAAA,EA4GI,SAAa,GAAA,GAAA;QACT,OAAO,CAAC,CAAC,IAAA,CAAK0S,IAAd,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAxHA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,SAAA;EAAA,IAAA,KAAA,EAyHI,SAAU,OAAA,GAAA;EACN,MAAA,IAAI,IAAKZ,CAAAA,SAAT,EACI,OAAO,IAAP,CAAA;EACJ,MAAA,IAAA,CAAKc,SAAL,EAAA,CAAA;EACA,MAAA,IAAI,CAAC,IAAA,CAAKf,EAAL,CAAQ,eAAR,CAAL,EACI,IAAA,CAAKA,EAAL,CAAQ7L,IAAR,EAAA,CALE;;EAMN,MAAA,IAAI,WAAW,IAAK6L,CAAAA,EAAL,CAAQgB,WAAvB,EACI,KAAKpK,MAAL,EAAA,CAAA;EACJ,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;;EArIA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,MAAA;EAAA,IAAA,KAAA,EAsII,SAAO,IAAA,GAAA;QACH,OAAO,IAAA,CAAK+I,OAAL,EAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAvJA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,MAAA;EAAA,IAAA,KAAA,EAwJI,SAAc,IAAA,GAAA;EAAA,MAAA,KAAA,IAAA,IAAA,GAAA,SAAA,CAAA,MAAA,EAANhT,IAAM,GAAA,IAAA,KAAA,CAAA,IAAA,CAAA,EAAA,IAAA,GAAA,CAAA,EAAA,IAAA,GAAA,IAAA,EAAA,IAAA,EAAA,EAAA;UAANA,IAAM,CAAA,IAAA,CAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA;EAAA,OAAA;;QACVA,IAAI,CAACwR,OAAL,CAAa,SAAb,CAAA,CAAA;EACA,MAAA,IAAA,CAAKzR,IAAL,CAAUR,KAAV,CAAgB,IAAhB,EAAsBS,IAAtB,CAAA,CAAA;EACA,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EA7KA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,MAAA;MAAA,KA8KI,EAAA,SAAA,IAAA,CAAKwK,EAAL,EAAkB;EACd,MAAA,IAAIsI,eAAe,CAAChS,cAAhB,CAA+B0J,EAA/B,CAAJ,EAAwC;UACpC,MAAM,IAAIjI,KAAJ,CAAU,GAAMiI,GAAAA,EAAE,CAACrP,QAAH,EAAN,GAAsB,4BAAhC,CAAN,CAAA;EACH,OAAA;;EAHa,MAAA,KAAA,IAAA,KAAA,GAAA,SAAA,CAAA,MAAA,EAAN6E,IAAM,GAAA,IAAA,KAAA,CAAA,KAAA,GAAA,CAAA,GAAA,KAAA,GAAA,CAAA,GAAA,CAAA,CAAA,EAAA,KAAA,GAAA,CAAA,EAAA,KAAA,GAAA,KAAA,EAAA,KAAA,EAAA,EAAA;UAANA,IAAM,CAAA,KAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA,KAAA,CAAA,CAAA;EAAA,OAAA;;QAIdA,IAAI,CAACwR,OAAL,CAAahH,EAAb,CAAA,CAAA;;EACA,MAAA,IAAI,IAAKwJ,CAAAA,KAAL,CAAWM,OAAX,IAAsB,CAAC,IAAA,CAAKR,KAAL,CAAWS,SAAlC,IAA+C,CAAC,IAAKT,CAAAA,KAAL,YAApD,EAAyE;UACrE,IAAKU,CAAAA,WAAL,CAAiBxU,IAAjB,CAAA,CAAA;;EACA,QAAA,OAAO,IAAP,CAAA;EACH,OAAA;;EACD,MAAA,IAAMzB,MAAM,GAAG;UACXzD,IAAI,EAAE8V,UAAU,CAACG,KADN;EAEXhW,QAAAA,IAAI,EAAEiF,IAAAA;SAFV,CAAA;QAIAzB,MAAM,CAACyQ,OAAP,GAAiB,EAAjB,CAAA;EACAzQ,MAAAA,MAAM,CAACyQ,OAAP,CAAeC,QAAf,GAA0B,IAAA,CAAK6E,KAAL,CAAW7E,QAAX,KAAwB,KAAlD,CAdc;;QAgBd,IAAI,UAAA,KAAe,OAAOjP,IAAI,CAACA,IAAI,CAACvD,MAAL,GAAc,CAAf,CAA9B,EAAiD;EAC7C,QAAA,IAAMmQ,EAAE,GAAG,IAAKgH,CAAAA,GAAL,EAAX,CAAA;EACA,QAAA,IAAMa,GAAG,GAAGzU,IAAI,CAAC0U,GAAL,EAAZ,CAAA;;EACA,QAAA,IAAA,CAAKC,oBAAL,CAA0B/H,EAA1B,EAA8B6H,GAA9B,CAAA,CAAA;;UACAlW,MAAM,CAACqO,EAAP,GAAYA,EAAZ,CAAA;EACH,OAAA;;QACD,IAAMgI,mBAAmB,GAAG,IAAKvB,CAAAA,EAAL,CAAQwB,MAAR,IACxB,KAAKxB,EAAL,CAAQwB,MAAR,CAAe3H,SADS,IAExB,IAAKmG,CAAAA,EAAL,CAAQwB,MAAR,CAAe3H,SAAf,CAAyBzK,QAF7B,CAAA;QAGA,IAAMqS,aAAa,GAAG,IAAA,CAAKhB,KAAL,CAAA,UAAA,CAAA,KAAwB,CAACc,mBAAD,IAAwB,CAAC,IAAKtB,CAAAA,SAAtD,CAAtB,CAAA;;QACA,IAAIwB,aAAJ,EAAmB,CAAnB,MAEK,IAAI,IAAA,CAAKxB,SAAT,EAAoB;UACrB,IAAKyB,CAAAA,uBAAL,CAA6BxW,MAA7B,CAAA,CAAA;UACA,IAAKA,CAAAA,MAAL,CAAYA,MAAZ,CAAA,CAAA;EACH,OAHI,MAIA;EACD,QAAA,IAAA,CAAKkV,UAAL,CAAgB7U,IAAhB,CAAqBL,MAArB,CAAA,CAAA;EACH,OAAA;;QACD,IAAKuV,CAAAA,KAAL,GAAa,EAAb,CAAA;EACA,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;;EAtNA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,sBAAA;EAAA,IAAA,KAAA,EAuNI,SAAqBlH,oBAAAA,CAAAA,EAArB,EAAyB6H,GAAzB,EAA8B;EAAA,MAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EAC1B,MAAA,IAAIO,EAAJ,CAAA;;QACA,IAAMlN,OAAO,GAAG,CAACkN,EAAE,GAAG,IAAKlB,CAAAA,KAAL,CAAWhM,OAAjB,MAA8B,IAA9B,IAAsCkN,EAAE,KAAK,KAAK,CAAlD,GAAsDA,EAAtD,GAA2D,IAAA,CAAKhB,KAAL,CAAWiB,UAAtF,CAAA;;QACA,IAAInN,OAAO,KAAKR,SAAhB,EAA2B;EACvB,QAAA,IAAA,CAAKuM,IAAL,CAAUjH,EAAV,CAAA,GAAgB6H,GAAhB,CAAA;EACA,QAAA,OAAA;EACH,OANyB;;;EAQ1B,MAAA,IAAMS,KAAK,GAAG,IAAA,CAAK7B,EAAL,CAAQ9R,YAAR,CAAqB,YAAM;EACrC,QAAA,OAAO,MAAI,CAACsS,IAAL,CAAUjH,EAAV,CAAP,CAAA;;EACA,QAAA,KAAK,IAAIpQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,MAAI,CAACiX,UAAL,CAAgBhX,MAApC,EAA4CD,CAAC,EAA7C,EAAiD;YAC7C,IAAI,MAAI,CAACiX,UAAL,CAAgBjX,CAAhB,CAAmBoQ,CAAAA,EAAnB,KAA0BA,EAA9B,EAAkC;EAC9B,YAAA,MAAI,CAAC6G,UAAL,CAAgB3T,MAAhB,CAAuBtD,CAAvB,EAA0B,CAA1B,CAAA,CAAA;EACH,WAAA;EACJ,SAAA;;UACDiY,GAAG,CAACrZ,IAAJ,CAAS,MAAT,EAAe,IAAImH,KAAJ,CAAU,yBAAV,CAAf,CAAA,CAAA;SAPU,EAQXuF,OARW,CAAd,CAAA;;EASA,MAAA,IAAA,CAAK+L,IAAL,CAAUjH,EAAV,CAAA,GAAgB,YAAa;EACzB;EACA,QAAA,MAAI,CAACyG,EAAL,CAAQ5R,cAAR,CAAuByT,KAAvB,CAAA,CAAA;;EAFyB,QAAA,KAAA,IAAA,KAAA,GAAA,SAAA,CAAA,MAAA,EAATlV,IAAS,GAAA,IAAA,KAAA,CAAA,KAAA,CAAA,EAAA,KAAA,GAAA,CAAA,EAAA,KAAA,GAAA,KAAA,EAAA,KAAA,EAAA,EAAA;YAATA,IAAS,CAAA,KAAA,CAAA,GAAA,SAAA,CAAA,KAAA,CAAA,CAAA;EAAA,SAAA;;EAGzByU,QAAAA,GAAG,CAAClV,KAAJ,CAAU,MAAV,EAAiB,CAAA,IAAjB,SAA0BS,IAA1B,CAAA,CAAA,CAAA;SAHJ,CAAA;EAKH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EA7PA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,aAAA;MAAA,KA8PI,EAAA,SAAA,WAAA,CAAYwK,EAAZ,EAAyB;EAAA,MAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EAAA,MAAA,KAAA,IAAA,KAAA,GAAA,SAAA,CAAA,MAAA,EAANxK,IAAM,GAAA,IAAA,KAAA,CAAA,KAAA,GAAA,CAAA,GAAA,KAAA,GAAA,CAAA,GAAA,CAAA,CAAA,EAAA,KAAA,GAAA,CAAA,EAAA,KAAA,GAAA,KAAA,EAAA,KAAA,EAAA,EAAA;UAANA,IAAM,CAAA,KAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA,KAAA,CAAA,CAAA;EAAA,OAAA;;EACrB;EACA,MAAA,IAAMmV,OAAO,GAAG,IAAKrB,CAAAA,KAAL,CAAWhM,OAAX,KAAuBR,SAAvB,IAAoC,IAAK0M,CAAAA,KAAL,CAAWiB,UAAX,KAA0B3N,SAA9E,CAAA;EACA,MAAA,OAAO,IAAI0B,OAAJ,CAAY,UAACC,OAAD,EAAUmM,MAAV,EAAqB;EACpCpV,QAAAA,IAAI,CAACpB,IAAL,CAAU,UAACyW,IAAD,EAAOC,IAAP,EAAgB;EACtB,UAAA,IAAIH,OAAJ,EAAa;cACT,OAAOE,IAAI,GAAGD,MAAM,CAACC,IAAD,CAAT,GAAkBpM,OAAO,CAACqM,IAAD,CAApC,CAAA;EACH,WAFD,MAGK;cACD,OAAOrM,OAAO,CAACoM,IAAD,CAAd,CAAA;EACH,WAAA;WANL,CAAA,CAAA;;EAQA,QAAA,MAAI,CAACtV,IAAL,CAAA,KAAA,CAAA,MAAI,GAAMyK,EAAN,CAAA,CAAA,MAAA,CAAaxK,IAAb,CAAJ,CAAA,CAAA;EACH,OAVM,CAAP,CAAA;EAWH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EAjRA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,aAAA;MAAA,KAkRI,EAAA,SAAA,WAAA,CAAYA,IAAZ,EAAkB;EAAA,MAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EACd,MAAA,IAAIyU,GAAJ,CAAA;;QACA,IAAI,OAAOzU,IAAI,CAACA,IAAI,CAACvD,MAAL,GAAc,CAAf,CAAX,KAAiC,UAArC,EAAiD;EAC7CgY,QAAAA,GAAG,GAAGzU,IAAI,CAAC0U,GAAL,EAAN,CAAA;EACH,OAAA;;EACD,MAAA,IAAMnW,MAAM,GAAG;UACXqO,EAAE,EAAE,IAAK+G,CAAAA,SAAL,EADO;EAEX4B,QAAAA,QAAQ,EAAE,CAFC;EAGXC,QAAAA,OAAO,EAAE,KAHE;EAIXxV,QAAAA,IAAI,EAAJA,IAJW;EAKX8T,QAAAA,KAAK,EAAE,QAAc,CAAA;EAAES,UAAAA,SAAS,EAAE,IAAA;WAA3B,EAAmC,KAAKT,KAAxC,CAAA;SALX,CAAA;EAOA9T,MAAAA,IAAI,CAACpB,IAAL,CAAU,UAAC2F,GAAD,EAA0B;UAChC,IAAIhG,MAAM,KAAK,MAAI,CAACmV,MAAL,CAAY,CAAZ,CAAf,EAA+B;EAC3B;EACA,UAAA,OAAA;EACH,SAAA;;EACD,QAAA,IAAM+B,QAAQ,GAAGlR,GAAG,KAAK,IAAzB,CAAA;;EACA,QAAA,IAAIkR,QAAJ,EAAc;YACV,IAAIlX,MAAM,CAACgX,QAAP,GAAkB,MAAI,CAACvB,KAAL,CAAWM,OAAjC,EAA0C;cACtC,MAAI,CAACZ,MAAL,CAAYlG,KAAZ,EAAA,CAAA;;EACA,YAAA,IAAIiH,GAAJ,EAAS;gBACLA,GAAG,CAAClQ,GAAD,CAAH,CAAA;EACH,aAAA;EACJ,WAAA;EACJ,SAPD,MAQK;YACD,MAAI,CAACmP,MAAL,CAAYlG,KAAZ,EAAA,CAAA;;EACA,UAAA,IAAIiH,GAAJ,EAAS;EAAA,YAAA,KAAA,IAAA,KAAA,GAAA,SAAA,CAAA,MAAA,EAhBEiB,YAgBF,GAAA,IAAA,KAAA,CAAA,KAAA,GAAA,CAAA,GAAA,KAAA,GAAA,CAAA,GAAA,CAAA,CAAA,EAAA,KAAA,GAAA,CAAA,EAAA,KAAA,GAAA,KAAA,EAAA,KAAA,EAAA,EAAA;gBAhBEA,YAgBF,CAAA,KAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA,KAAA,CAAA,CAAA;EAAA,aAAA;;EACLjB,YAAAA,GAAG,CAAH,KAAA,CAAA,KAAA,CAAA,EAAA,CAAI,IAAJ,CAAA,CAAA,MAAA,CAAaiB,YAAb,CAAA,CAAA,CAAA;EACH,WAAA;EACJ,SAAA;;UACDnX,MAAM,CAACiX,OAAP,GAAiB,KAAjB,CAAA;UACA,OAAO,MAAI,CAACG,WAAL,EAAP,CAAA;SArBJ,CAAA,CAAA;;EAuBA,MAAA,IAAA,CAAKjC,MAAL,CAAY9U,IAAZ,CAAiBL,MAAjB,CAAA,CAAA;;EACA,MAAA,IAAA,CAAKoX,WAAL,EAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;EA7TA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,aAAA;EAAA,IAAA,KAAA,EA8TI,SAA2B,WAAA,GAAA;QAAA,IAAfC,KAAe,uEAAP,KAAO,CAAA;;QACvB,IAAI,CAAC,IAAKtC,CAAAA,SAAN,IAAmB,IAAA,CAAKI,MAAL,CAAYjX,MAAZ,KAAuB,CAA9C,EAAiD;EAC7C,QAAA,OAAA;EACH,OAAA;;EACD,MAAA,IAAM8B,MAAM,GAAG,IAAA,CAAKmV,MAAL,CAAY,CAAZ,CAAf,CAAA;;EACA,MAAA,IAAInV,MAAM,CAACiX,OAAP,IAAkB,CAACI,KAAvB,EAA8B;EAC1B,QAAA,OAAA;EACH,OAAA;;QACDrX,MAAM,CAACiX,OAAP,GAAiB,IAAjB,CAAA;EACAjX,MAAAA,MAAM,CAACgX,QAAP,EAAA,CAAA;EACA,MAAA,IAAA,CAAKzB,KAAL,GAAavV,MAAM,CAACuV,KAApB,CAAA;QACA,IAAK/T,CAAAA,IAAL,CAAUR,KAAV,CAAgB,IAAhB,EAAsBhB,MAAM,CAACyB,IAA7B,CAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;EAhVA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,QAAA;MAAA,KAiVI,EAAA,SAAA,MAAA,CAAOzB,OAAP,EAAe;EACXA,MAAAA,OAAM,CAAC6S,GAAP,GAAa,IAAA,CAAKA,GAAlB,CAAA;;EACA,MAAA,IAAA,CAAKiC,EAAL,CAAQwC,OAAR,CAAgBtX,OAAhB,CAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EAzVA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,QAAA;EAAA,IAAA,KAAA,EA0VI,SAAS,MAAA,GAAA;EAAA,MAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EACL,MAAA,IAAI,OAAO,IAAA,CAAKwV,IAAZ,IAAoB,UAAxB,EAAoC;EAChC,QAAA,IAAA,CAAKA,IAAL,CAAU,UAAChZ,IAAD,EAAU;YAChB,MAAI,CAAC+a,kBAAL,CAAwB/a,IAAxB,CAAA,CAAA;WADJ,CAAA,CAAA;EAGH,OAJD,MAKK;UACD,IAAK+a,CAAAA,kBAAL,CAAwB,IAAA,CAAK/B,IAA7B,CAAA,CAAA;EACH,OAAA;EACJ,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;EAzWA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,oBAAA;MAAA,KA0WI,EAAA,SAAA,kBAAA,CAAmBhZ,IAAnB,EAAyB;EACrB,MAAA,IAAA,CAAKwD,MAAL,CAAY;UACRzD,IAAI,EAAE8V,UAAU,CAAC4B,OADT;EAERzX,QAAAA,IAAI,EAAE,IAAA,CAAKgb,IAAL,GACA,QAAc,CAAA;YAAEC,GAAG,EAAE,KAAKD,IAAZ;EAAkBE,UAAAA,MAAM,EAAE,IAAKC,CAAAA,WAAAA;WAA7C,EAA4Dnb,IAA5D,CADA,GAEAA,IAAAA;SAJV,CAAA,CAAA;EAMH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;EAvXA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,SAAA;MAAA,KAwXI,EAAA,SAAA,OAAA,CAAQwJ,GAAR,EAAa;QACT,IAAI,CAAC,IAAK+O,CAAAA,SAAV,EAAqB;EACjB,QAAA,IAAA,CAAKpT,YAAL,CAAkB,eAAlB,EAAmCqE,GAAnC,CAAA,CAAA;EACH,OAAA;EACJ,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;EAnYA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,SAAA;EAAA,IAAA,KAAA,EAoYI,SAAQnC,OAAAA,CAAAA,MAAR,EAAgBC,WAAhB,EAA6B;QACzB,IAAKiR,CAAAA,SAAL,GAAiB,KAAjB,CAAA;EACA,MAAA,OAAO,KAAK1G,EAAZ,CAAA;EACA,MAAA,IAAA,CAAK1M,YAAL,CAAkB,YAAlB,EAAgCkC,MAAhC,EAAwCC,WAAxC,CAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;EA9YA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,UAAA;MAAA,KA+YI,EAAA,SAAA,QAAA,CAAS9D,MAAT,EAAiB;EACb,MAAA,IAAM4X,aAAa,GAAG5X,MAAM,CAAC6S,GAAP,KAAe,KAAKA,GAA1C,CAAA;QACA,IAAI,CAAC+E,aAAL,EACI,OAAA;;QACJ,QAAQ5X,MAAM,CAACzD,IAAf;UACI,KAAK8V,UAAU,CAAC4B,OAAhB;YACI,IAAIjU,MAAM,CAACxD,IAAP,IAAewD,MAAM,CAACxD,IAAP,CAAYsL,GAA/B,EAAoC;EAChC,YAAA,IAAA,CAAK+P,SAAL,CAAe7X,MAAM,CAACxD,IAAP,CAAYsL,GAA3B,EAAgC9H,MAAM,CAACxD,IAAP,CAAYib,GAA5C,CAAA,CAAA;EACH,WAFD,MAGK;cACD,IAAK9V,CAAAA,YAAL,CAAkB,eAAlB,EAAmC,IAAIqC,KAAJ,CAAU,2LAAV,CAAnC,CAAA,CAAA;EACH,WAAA;;EACD,UAAA,MAAA;;UACJ,KAAKqO,UAAU,CAACG,KAAhB,CAAA;UACA,KAAKH,UAAU,CAACM,YAAhB;YACI,IAAKmF,CAAAA,OAAL,CAAa9X,MAAb,CAAA,CAAA;EACA,UAAA,MAAA;;UACJ,KAAKqS,UAAU,CAACI,GAAhB,CAAA;UACA,KAAKJ,UAAU,CAACO,UAAhB;YACI,IAAKmF,CAAAA,KAAL,CAAW/X,MAAX,CAAA,CAAA;EACA,UAAA,MAAA;;UACJ,KAAKqS,UAAU,CAAC6B,UAAhB;EACI,UAAA,IAAA,CAAK8D,YAAL,EAAA,CAAA;EACA,UAAA,MAAA;;UACJ,KAAK3F,UAAU,CAAC8B,aAAhB;EACI,UAAA,IAAA,CAAK8D,OAAL,EAAA,CAAA;EACA,UAAA,IAAMjS,GAAG,GAAG,IAAIhC,KAAJ,CAAUhE,MAAM,CAACxD,IAAP,CAAY0b,OAAtB,CAAZ,CAFJ;;EAIIlS,UAAAA,GAAG,CAACxJ,IAAJ,GAAWwD,MAAM,CAACxD,IAAP,CAAYA,IAAvB,CAAA;EACA,UAAA,IAAA,CAAKmF,YAAL,CAAkB,eAAlB,EAAmCqE,GAAnC,CAAA,CAAA;EACA,UAAA,MAAA;EA1BR,OAAA;EA4BH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;EArbA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,SAAA;MAAA,KAsbI,EAAA,SAAA,OAAA,CAAQhG,MAAR,EAAgB;EACZ,MAAA,IAAMyB,IAAI,GAAGzB,MAAM,CAACxD,IAAP,IAAe,EAA5B,CAAA;;EACA,MAAA,IAAI,IAAQwD,IAAAA,MAAM,CAACqO,EAAnB,EAAuB;UACnB5M,IAAI,CAACpB,IAAL,CAAU,IAAA,CAAK6V,GAAL,CAASlW,MAAM,CAACqO,EAAhB,CAAV,CAAA,CAAA;EACH,OAAA;;QACD,IAAI,IAAA,CAAK0G,SAAT,EAAoB;UAChB,IAAKoD,CAAAA,SAAL,CAAe1W,IAAf,CAAA,CAAA;EACH,OAFD,MAGK;UACD,IAAKwT,CAAAA,aAAL,CAAmB5U,IAAnB,CAAwBrE,MAAM,CAACwY,MAAP,CAAc/S,IAAd,CAAxB,CAAA,CAAA;EACH,OAAA;EACJ,KAAA;EAjcL,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,WAAA;MAAA,KAkcI,EAAA,SAAA,SAAA,CAAUA,IAAV,EAAgB;EACZ,MAAA,IAAI,KAAK2W,aAAL,IAAsB,KAAKA,aAAL,CAAmBla,MAA7C,EAAqD;EACjD,QAAA,IAAM0D,SAAS,GAAG,IAAA,CAAKwW,aAAL,CAAmB1W,KAAnB,EAAlB,CAAA;;EADiD,QAAA,IAAA,SAAA,GAAA,0BAAA,CAE1BE,SAF0B,CAAA;EAAA,YAAA,KAAA,CAAA;;EAAA,QAAA,IAAA;YAEjD,KAAkC,SAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,KAAA,GAAA,SAAA,CAAA,CAAA,EAAA,EAAA,IAAA,GAAA;EAAA,YAAA,IAAvByW,QAAuB,GAAA,KAAA,CAAA,KAAA,CAAA;EAC9BA,YAAAA,QAAQ,CAACrX,KAAT,CAAe,IAAf,EAAqBS,IAArB,CAAA,CAAA;EACH,WAAA;EAJgD,SAAA,CAAA,OAAA,GAAA,EAAA;EAAA,UAAA,SAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA;EAAA,SAAA,SAAA;EAAA,UAAA,SAAA,CAAA,CAAA,EAAA,CAAA;EAAA,SAAA;EAKpD,OAAA;;EACD,MAAA,IAAA,CAAA,eAAA,CAAA,MAAA,CAAA,SAAA,CAAA,EAAA,MAAA,EAAA,IAAA,CAAA,CAAWT,KAAX,CAAiB,IAAjB,EAAuBS,IAAvB,CAAA,CAAA;;EACA,MAAA,IAAI,KAAK+V,IAAL,IAAa/V,IAAI,CAACvD,MAAlB,IAA4B,OAAOuD,IAAI,CAACA,IAAI,CAACvD,MAAL,GAAc,CAAf,CAAX,KAAiC,QAAjE,EAA2E;UACvE,IAAKyZ,CAAAA,WAAL,GAAmBlW,IAAI,CAACA,IAAI,CAACvD,MAAL,GAAc,CAAf,CAAvB,CAAA;EACH,OAAA;EACJ,KAAA;EACD;EACJ;EACA;EACA;EACA;;EAldA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,KAAA;MAAA,KAmdI,EAAA,SAAA,GAAA,CAAImQ,EAAJ,EAAQ;QACJ,IAAMtM,IAAI,GAAG,IAAb,CAAA;QACA,IAAIuW,IAAI,GAAG,KAAX,CAAA;EACA,MAAA,OAAO,YAAmB;EACtB;EACA,QAAA,IAAIA,IAAJ,EACI,OAAA;EACJA,QAAAA,IAAI,GAAG,IAAP,CAAA;;EAJsB,QAAA,KAAA,IAAA,KAAA,GAAA,SAAA,CAAA,MAAA,EAAN7W,IAAM,GAAA,IAAA,KAAA,CAAA,KAAA,CAAA,EAAA,KAAA,GAAA,CAAA,EAAA,KAAA,GAAA,KAAA,EAAA,KAAA,EAAA,EAAA;YAANA,IAAM,CAAA,KAAA,CAAA,GAAA,SAAA,CAAA,KAAA,CAAA,CAAA;EAAA,SAAA;;UAKtBM,IAAI,CAAC/B,MAAL,CAAY;YACRzD,IAAI,EAAE8V,UAAU,CAACI,GADT;EAERpE,UAAAA,EAAE,EAAEA,EAFI;EAGR7R,UAAAA,IAAI,EAAEiF,IAAAA;WAHV,CAAA,CAAA;SALJ,CAAA;EAWH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;EAveA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,OAAA;MAAA,KAweI,EAAA,SAAA,KAAA,CAAMzB,MAAN,EAAc;QACV,IAAMkW,GAAG,GAAG,IAAKZ,CAAAA,IAAL,CAAUtV,MAAM,CAACqO,EAAjB,CAAZ,CAAA;;QACA,IAAI,UAAA,KAAe,OAAO6H,GAA1B,EAA+B;EAC3BA,QAAAA,GAAG,CAAClV,KAAJ,CAAU,IAAV,EAAgBhB,MAAM,CAACxD,IAAvB,CAAA,CAAA;EACA,QAAA,OAAO,KAAK8Y,IAAL,CAAUtV,MAAM,CAACqO,EAAjB,CAAP,CAAA;EACH,OAEA;EACJ,KAAA;EACD;EACJ;EACA;EACA;EACA;;EArfA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,WAAA;EAAA,IAAA,KAAA,EAsfI,SAAUA,SAAAA,CAAAA,EAAV,EAAcoJ,GAAd,EAAmB;QACf,IAAKpJ,CAAAA,EAAL,GAAUA,EAAV,CAAA;EACA,MAAA,IAAA,CAAK2G,SAAL,GAAiByC,GAAG,IAAI,IAAKD,CAAAA,IAAL,KAAcC,GAAtC,CAAA;EACA,MAAA,IAAA,CAAKD,IAAL,GAAYC,GAAZ,CAHe;;QAIf,IAAK1C,CAAAA,SAAL,GAAiB,IAAjB,CAAA;EACA,MAAA,IAAA,CAAKwD,YAAL,EAAA,CAAA;QACA,IAAK5W,CAAAA,YAAL,CAAkB,SAAlB,CAAA,CAAA;;QACA,IAAKyV,CAAAA,WAAL,CAAiB,IAAjB,CAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EAngBA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,cAAA;EAAA,IAAA,KAAA,EAogBI,SAAe,YAAA,GAAA;EAAA,MAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EACX,MAAA,IAAA,CAAKnC,aAAL,CAAmB7Y,OAAnB,CAA2B,UAACqF,IAAD,EAAA;EAAA,QAAA,OAAU,MAAI,CAAC0W,SAAL,CAAe1W,IAAf,CAAV,CAAA;SAA3B,CAAA,CAAA;QACA,IAAKwT,CAAAA,aAAL,GAAqB,EAArB,CAAA;EACA,MAAA,IAAA,CAAKC,UAAL,CAAgB9Y,OAAhB,CAAwB,UAAC4D,MAAD,EAAY;UAChC,MAAI,CAACwW,uBAAL,CAA6BxW,MAA7B,CAAA,CAAA;;UACA,MAAI,CAACA,MAAL,CAAYA,MAAZ,CAAA,CAAA;SAFJ,CAAA,CAAA;QAIA,IAAKkV,CAAAA,UAAL,GAAkB,EAAlB,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EAjhBA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,cAAA;EAAA,IAAA,KAAA,EAkhBI,SAAe,YAAA,GAAA;EACX,MAAA,IAAA,CAAK+C,OAAL,EAAA,CAAA;QACA,IAAKnM,CAAAA,OAAL,CAAa,sBAAb,CAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;EA5hBA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,SAAA;EAAA,IAAA,KAAA,EA6hBI,SAAU,OAAA,GAAA;QACN,IAAI,IAAA,CAAK6J,IAAT,EAAe;EACX;EACA,QAAA,IAAA,CAAKA,IAAL,CAAUvZ,OAAV,CAAkB,UAACkY,UAAD,EAAA;EAAA,UAAA,OAAgBA,UAAU,EAA1B,CAAA;WAAlB,CAAA,CAAA;UACA,IAAKqB,CAAAA,IAAL,GAAY5M,SAAZ,CAAA;EACH,OAAA;;EACD,MAAA,IAAA,CAAK+L,EAAL,CAAQ,UAAR,CAAA,CAAoB,IAApB,CAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EApjBA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,YAAA;EAAA,IAAA,KAAA,EAqjBI,SAAa,UAAA,GAAA;QACT,IAAI,IAAA,CAAKC,SAAT,EAAoB;EAChB,QAAA,IAAA,CAAK/U,MAAL,CAAY;YAAEzD,IAAI,EAAE8V,UAAU,CAAC6B,UAAAA;WAA/B,CAAA,CAAA;EACH,OAHQ;;;EAKT,MAAA,IAAA,CAAK+D,OAAL,EAAA,CAAA;;QACA,IAAI,IAAA,CAAKlD,SAAT,EAAoB;EAChB;UACA,IAAKjJ,CAAAA,OAAL,CAAa,sBAAb,CAAA,CAAA;EACH,OAAA;;EACD,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EArkBA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,OAAA;EAAA,IAAA,KAAA,EAskBI,SAAQ,KAAA,GAAA;QACJ,OAAO,IAAA,CAAK6I,UAAL,EAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAjlBA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,UAAA;MAAA,KAklBI,EAAA,SAAA,QAAA,CAASjE,SAAT,EAAmB;EACf,MAAA,IAAA,CAAK6E,KAAL,CAAW7E,QAAX,GAAsBA,SAAtB,CAAA;EACA,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EA9lBA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,UAAA;EAAA,IAAA,GAAA,EA+lBI,SAAe,GAAA,GAAA;QACX,IAAK6E,CAAAA,KAAL,eAAsB,IAAtB,CAAA;EACA,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EA/mBA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,SAAA;MAAA,KAgnBI,EAAA,SAAA,OAAA,CAAQhM,QAAR,EAAiB;EACb,MAAA,IAAA,CAAKgM,KAAL,CAAWhM,OAAX,GAAqBA,QAArB,CAAA;EACA,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EA9nBA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,OAAA;MAAA,KA+nBI,EAAA,SAAA,KAAA,CAAM8O,QAAN,EAAgB;EACZ,MAAA,IAAA,CAAKD,aAAL,GAAqB,IAAKA,CAAAA,aAAL,IAAsB,EAA3C,CAAA;;EACA,MAAA,IAAA,CAAKA,aAAL,CAAmB/X,IAAnB,CAAwBgY,QAAxB,CAAA,CAAA;;EACA,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EA9oBA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,YAAA;MAAA,KA+oBI,EAAA,SAAA,UAAA,CAAWA,QAAX,EAAqB;EACjB,MAAA,IAAA,CAAKD,aAAL,GAAqB,IAAKA,CAAAA,aAAL,IAAsB,EAA3C,CAAA;;EACA,MAAA,IAAA,CAAKA,aAAL,CAAmBnF,OAAnB,CAA2BoF,QAA3B,CAAA,CAAA;;EACA,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EArqBA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,QAAA;MAAA,KAsqBI,EAAA,SAAA,MAAA,CAAOA,QAAP,EAAiB;QACb,IAAI,CAAC,IAAKD,CAAAA,aAAV,EAAyB;EACrB,QAAA,OAAO,IAAP,CAAA;EACH,OAAA;;EACD,MAAA,IAAIC,QAAJ,EAAc;UACV,IAAMzW,SAAS,GAAG,IAAA,CAAKwW,aAAvB,CAAA;;EACA,QAAA,KAAK,IAAIna,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG2D,SAAS,CAAC1D,MAA9B,EAAsCD,CAAC,EAAvC,EAA2C;EACvC,UAAA,IAAIoa,QAAQ,KAAKzW,SAAS,CAAC3D,CAAD,CAA1B,EAA+B;EAC3B2D,YAAAA,SAAS,CAACL,MAAV,CAAiBtD,CAAjB,EAAoB,CAApB,CAAA,CAAA;EACA,YAAA,OAAO,IAAP,CAAA;EACH,WAAA;EACJ,SAAA;EACJ,OARD,MASK;UACD,IAAKma,CAAAA,aAAL,GAAqB,EAArB,CAAA;EACH,OAAA;;EACD,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;;EA3rBA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,cAAA;EAAA,IAAA,KAAA,EA4rBI,SAAe,YAAA,GAAA;QACX,OAAO,IAAA,CAAKA,aAAL,IAAsB,EAA7B,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EA3sBA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,eAAA;MAAA,KA4sBI,EAAA,SAAA,aAAA,CAAcC,QAAd,EAAwB;EACpB,MAAA,IAAA,CAAKG,qBAAL,GAA6B,IAAKA,CAAAA,qBAAL,IAA8B,EAA3D,CAAA;;EACA,MAAA,IAAA,CAAKA,qBAAL,CAA2BnY,IAA3B,CAAgCgY,QAAhC,CAAA,CAAA;;EACA,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EA7tBA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,oBAAA;MAAA,KA8tBI,EAAA,SAAA,kBAAA,CAAmBA,QAAnB,EAA6B;EACzB,MAAA,IAAA,CAAKG,qBAAL,GAA6B,IAAKA,CAAAA,qBAAL,IAA8B,EAA3D,CAAA;;EACA,MAAA,IAAA,CAAKA,qBAAL,CAA2BvF,OAA3B,CAAmCoF,QAAnC,CAAA,CAAA;;EACA,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EApvBA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,gBAAA;MAAA,KAqvBI,EAAA,SAAA,cAAA,CAAeA,QAAf,EAAyB;QACrB,IAAI,CAAC,IAAKG,CAAAA,qBAAV,EAAiC;EAC7B,QAAA,OAAO,IAAP,CAAA;EACH,OAAA;;EACD,MAAA,IAAIH,QAAJ,EAAc;UACV,IAAMzW,SAAS,GAAG,IAAA,CAAK4W,qBAAvB,CAAA;;EACA,QAAA,KAAK,IAAIva,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG2D,SAAS,CAAC1D,MAA9B,EAAsCD,CAAC,EAAvC,EAA2C;EACvC,UAAA,IAAIoa,QAAQ,KAAKzW,SAAS,CAAC3D,CAAD,CAA1B,EAA+B;EAC3B2D,YAAAA,SAAS,CAACL,MAAV,CAAiBtD,CAAjB,EAAoB,CAApB,CAAA,CAAA;EACA,YAAA,OAAO,IAAP,CAAA;EACH,WAAA;EACJ,SAAA;EACJ,OARD,MASK;UACD,IAAKua,CAAAA,qBAAL,GAA6B,EAA7B,CAAA;EACH,OAAA;;EACD,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;;EA1wBA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,sBAAA;EAAA,IAAA,KAAA,EA2wBI,SAAuB,oBAAA,GAAA;QACnB,OAAO,IAAA,CAAKA,qBAAL,IAA8B,EAArC,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;EApxBA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,yBAAA;MAAA,KAqxBI,EAAA,SAAA,uBAAA,CAAwBxY,MAAxB,EAAgC;EAC5B,MAAA,IAAI,KAAKwY,qBAAL,IAA8B,KAAKA,qBAAL,CAA2Bta,MAA7D,EAAqE;EACjE,QAAA,IAAM0D,SAAS,GAAG,IAAA,CAAK4W,qBAAL,CAA2B9W,KAA3B,EAAlB,CAAA;;EADiE,QAAA,IAAA,UAAA,GAAA,0BAAA,CAE1CE,SAF0C,CAAA;EAAA,YAAA,MAAA,CAAA;;EAAA,QAAA,IAAA;YAEjE,KAAkC,UAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,MAAA,GAAA,UAAA,CAAA,CAAA,EAAA,EAAA,IAAA,GAAA;EAAA,YAAA,IAAvByW,QAAuB,GAAA,MAAA,CAAA,KAAA,CAAA;EAC9BA,YAAAA,QAAQ,CAACrX,KAAT,CAAe,IAAf,EAAqBhB,MAAM,CAACxD,IAA5B,CAAA,CAAA;EACH,WAAA;EAJgE,SAAA,CAAA,OAAA,GAAA,EAAA;EAAA,UAAA,UAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA;EAAA,SAAA,SAAA;EAAA,UAAA,UAAA,CAAA,CAAA,EAAA,CAAA;EAAA,SAAA;EAKpE,OAAA;EACJ,KAAA;EA5xBL,GAAA,CAAA,CAAA,CAAA;;EAAA,EAAA,OAAA,MAAA,CAAA;EAAA,CAAA,CAA4B+D,OAA5B,CAAA;;ECxCA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASkY,OAAT,CAAiB3V,IAAjB,EAAuB;IAC1BA,IAAI,GAAGA,IAAI,IAAI,EAAf,CAAA;EACA,EAAA,IAAA,CAAK4V,EAAL,GAAU5V,IAAI,CAAC6V,GAAL,IAAY,GAAtB,CAAA;EACA,EAAA,IAAA,CAAKC,GAAL,GAAW9V,IAAI,CAAC8V,GAAL,IAAY,KAAvB,CAAA;EACA,EAAA,IAAA,CAAKC,MAAL,GAAc/V,IAAI,CAAC+V,MAAL,IAAe,CAA7B,CAAA;EACA,EAAA,IAAA,CAAKC,MAAL,GAAchW,IAAI,CAACgW,MAAL,GAAc,CAAd,IAAmBhW,IAAI,CAACgW,MAAL,IAAe,CAAlC,GAAsChW,IAAI,CAACgW,MAA3C,GAAoD,CAAlE,CAAA;IACA,IAAKC,CAAAA,QAAL,GAAgB,CAAhB,CAAA;EACH,CAAA;EACD;EACA;EACA;EACA;EACA;EACA;;EACAN,OAAO,CAAC9b,SAAR,CAAkBqc,QAAlB,GAA6B,YAAY;EACrC,EAAA,IAAIN,EAAE,GAAG,IAAKA,CAAAA,EAAL,GAAUpV,IAAI,CAAC2V,GAAL,CAAS,KAAKJ,MAAd,EAAsB,IAAKE,CAAAA,QAAL,EAAtB,CAAnB,CAAA;;IACA,IAAI,IAAA,CAAKD,MAAT,EAAiB;EACb,IAAA,IAAII,IAAI,GAAG5V,IAAI,CAAC6V,MAAL,EAAX,CAAA;EACA,IAAA,IAAIC,SAAS,GAAG9V,IAAI,CAAC8B,KAAL,CAAW8T,IAAI,GAAG,IAAKJ,CAAAA,MAAZ,GAAqBJ,EAAhC,CAAhB,CAAA;MACAA,EAAE,GAAG,CAACpV,IAAI,CAAC8B,KAAL,CAAW8T,IAAI,GAAG,EAAlB,CAAA,GAAwB,CAAzB,KAA+B,CAA/B,GAAmCR,EAAE,GAAGU,SAAxC,GAAoDV,EAAE,GAAGU,SAA9D,CAAA;EACH,GAAA;;IACD,OAAO9V,IAAI,CAACqV,GAAL,CAASD,EAAT,EAAa,IAAA,CAAKE,GAAlB,CAAA,GAAyB,CAAhC,CAAA;EACH,CARD,CAAA;EASA;EACA;EACA;EACA;EACA;;;EACAH,OAAO,CAAC9b,SAAR,CAAkB0c,KAAlB,GAA0B,YAAY;IAClC,IAAKN,CAAAA,QAAL,GAAgB,CAAhB,CAAA;EACH,CAFD,CAAA;EAGA;EACA;EACA;EACA;EACA;;;EACAN,OAAO,CAAC9b,SAAR,CAAkB2c,MAAlB,GAA2B,UAAUX,GAAV,EAAe;IACtC,IAAKD,CAAAA,EAAL,GAAUC,GAAV,CAAA;EACH,CAFD,CAAA;EAGA;EACA;EACA;EACA;EACA;;;EACAF,OAAO,CAAC9b,SAAR,CAAkB4c,MAAlB,GAA2B,UAAUX,GAAV,EAAe;IACtC,IAAKA,CAAAA,GAAL,GAAWA,GAAX,CAAA;EACH,CAFD,CAAA;EAGA;EACA;EACA;EACA;EACA;;;EACAH,OAAO,CAAC9b,SAAR,CAAkB6c,SAAlB,GAA8B,UAAUV,MAAV,EAAkB;IAC5C,IAAKA,CAAAA,MAAL,GAAcA,MAAd,CAAA;EACH,CAFD;;ECzDA,IAAaW,OAAb,gBAAA,UAAA,QAAA,EAAA;EAAA,EAAA,SAAA,CAAA,OAAA,EAAA,QAAA,CAAA,CAAA;;EAAA,EAAA,IAAA,MAAA,GAAA,YAAA,CAAA,OAAA,CAAA,CAAA;;IACI,SAAYnR,OAAAA,CAAAA,GAAZ,EAAiBxF,IAAjB,EAAuB;EAAA,IAAA,IAAA,KAAA,CAAA;;EAAA,IAAA,eAAA,CAAA,IAAA,EAAA,OAAA,CAAA,CAAA;;EACnB,IAAA,IAAI2T,EAAJ,CAAA;;EACA,IAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;MACA,KAAKiD,CAAAA,IAAL,GAAY,EAAZ,CAAA;MACA,KAAK/D,CAAAA,IAAL,GAAY,EAAZ,CAAA;;EACA,IAAA,IAAIrN,GAAG,IAAI,QAAoBA,KAAAA,OAAAA,CAAAA,GAApB,CAAX,EAAoC;EAChCxF,MAAAA,IAAI,GAAGwF,GAAP,CAAA;EACAA,MAAAA,GAAG,GAAGS,SAAN,CAAA;EACH,KAAA;;MACDjG,IAAI,GAAGA,IAAI,IAAI,EAAf,CAAA;EACAA,IAAAA,IAAI,CAACsF,IAAL,GAAYtF,IAAI,CAACsF,IAAL,IAAa,YAAzB,CAAA;MACA,KAAKtF,CAAAA,IAAL,GAAYA,IAAZ,CAAA;MACAD,qBAAqB,CAAA,sBAAA,CAAA,KAAA,CAAA,EAAOC,IAAP,CAArB,CAAA;;EACA,IAAA,KAAA,CAAK6W,YAAL,CAAkB7W,IAAI,CAAC6W,YAAL,KAAsB,KAAxC,CAAA,CAAA;;EACA,IAAA,KAAA,CAAKC,oBAAL,CAA0B9W,IAAI,CAAC8W,oBAAL,IAA6BC,QAAvD,CAAA,CAAA;;EACA,IAAA,KAAA,CAAKC,iBAAL,CAAuBhX,IAAI,CAACgX,iBAAL,IAA0B,IAAjD,CAAA,CAAA;;EACA,IAAA,KAAA,CAAKC,oBAAL,CAA0BjX,IAAI,CAACiX,oBAAL,IAA6B,IAAvD,CAAA,CAAA;;EACA,IAAA,KAAA,CAAKC,mBAAL,CAAyB,CAACvD,EAAE,GAAG3T,IAAI,CAACkX,mBAAX,MAAoC,IAApC,IAA4CvD,EAAE,KAAK,KAAK,CAAxD,GAA4DA,EAA5D,GAAiE,GAA1F,CAAA,CAAA;;EACA,IAAA,KAAA,CAAKwD,OAAL,GAAe,IAAIxB,OAAJ,CAAY;QACvBE,GAAG,EAAE,KAAKmB,CAAAA,iBAAL,EADkB;QAEvBlB,GAAG,EAAE,KAAKmB,CAAAA,oBAAL,EAFkB;QAGvBjB,MAAM,EAAE,MAAKkB,mBAAL,EAAA;EAHe,KAAZ,CAAf,CAAA;;MAKA,KAAKzQ,CAAAA,OAAL,CAAa,IAAA,IAAQzG,IAAI,CAACyG,OAAb,GAAuB,KAAvB,GAA+BzG,IAAI,CAACyG,OAAjD,CAAA,CAAA;;MACA,KAAKuM,CAAAA,WAAL,GAAmB,QAAnB,CAAA;MACA,KAAKxN,CAAAA,GAAL,GAAWA,GAAX,CAAA;;EACA,IAAA,IAAM4R,OAAO,GAAGpX,IAAI,CAACqX,MAAL,IAAeA,MAA/B,CAAA;;EACA,IAAA,KAAA,CAAKC,OAAL,GAAe,IAAIF,OAAO,CAAC5H,OAAZ,EAAf,CAAA;EACA,IAAA,KAAA,CAAK+H,OAAL,GAAe,IAAIH,OAAO,CAAChH,OAAZ,EAAf,CAAA;EACA,IAAA,KAAA,CAAKwC,YAAL,GAAoB5S,IAAI,CAACwX,WAAL,KAAqB,KAAzC,CAAA;EACA,IAAA,IAAI,KAAK5E,CAAAA,YAAT,EACI,KAAA,CAAKzM,IAAL,EAAA,CAAA;EA/Be,IAAA,OAAA,KAAA,CAAA;EAgCtB,GAAA;;EAjCL,EAAA,YAAA,CAAA,OAAA,EAAA,CAAA;EAAA,IAAA,GAAA,EAAA,cAAA;MAAA,KAkCI,EAAA,SAAA,YAAA,CAAasR,CAAb,EAAgB;EACZ,MAAA,IAAI,CAACtZ,SAAS,CAAC/C,MAAf,EACI,OAAO,KAAKsc,aAAZ,CAAA;EACJ,MAAA,IAAA,CAAKA,aAAL,GAAqB,CAAC,CAACD,CAAvB,CAAA;EACA,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EAvCL,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,sBAAA;MAAA,KAwCI,EAAA,SAAA,oBAAA,CAAqBA,CAArB,EAAwB;EACpB,MAAA,IAAIA,CAAC,KAAKxR,SAAV,EACI,OAAO,KAAK0R,qBAAZ,CAAA;QACJ,IAAKA,CAAAA,qBAAL,GAA6BF,CAA7B,CAAA;EACA,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EA7CL,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,mBAAA;MAAA,KA8CI,EAAA,SAAA,iBAAA,CAAkBA,CAAlB,EAAqB;EACjB,MAAA,IAAI9D,EAAJ,CAAA;;EACA,MAAA,IAAI8D,CAAC,KAAKxR,SAAV,EACI,OAAO,KAAK2R,kBAAZ,CAAA;QACJ,IAAKA,CAAAA,kBAAL,GAA0BH,CAA1B,CAAA;QACA,CAAC9D,EAAE,GAAG,IAAKwD,CAAAA,OAAX,MAAwB,IAAxB,IAAgCxD,EAAE,KAAK,KAAK,CAA5C,GAAgD,KAAK,CAArD,GAAyDA,EAAE,CAAC6C,MAAH,CAAUiB,CAAV,CAAzD,CAAA;EACA,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EArDL,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,qBAAA;MAAA,KAsDI,EAAA,SAAA,mBAAA,CAAoBA,CAApB,EAAuB;EACnB,MAAA,IAAI9D,EAAJ,CAAA;;EACA,MAAA,IAAI8D,CAAC,KAAKxR,SAAV,EACI,OAAO,KAAK4R,oBAAZ,CAAA;QACJ,IAAKA,CAAAA,oBAAL,GAA4BJ,CAA5B,CAAA;QACA,CAAC9D,EAAE,GAAG,IAAKwD,CAAAA,OAAX,MAAwB,IAAxB,IAAgCxD,EAAE,KAAK,KAAK,CAA5C,GAAgD,KAAK,CAArD,GAAyDA,EAAE,CAAC+C,SAAH,CAAae,CAAb,CAAzD,CAAA;EACA,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EA7DL,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,sBAAA;MAAA,KA8DI,EAAA,SAAA,oBAAA,CAAqBA,CAArB,EAAwB;EACpB,MAAA,IAAI9D,EAAJ,CAAA;;EACA,MAAA,IAAI8D,CAAC,KAAKxR,SAAV,EACI,OAAO,KAAK6R,qBAAZ,CAAA;QACJ,IAAKA,CAAAA,qBAAL,GAA6BL,CAA7B,CAAA;QACA,CAAC9D,EAAE,GAAG,IAAKwD,CAAAA,OAAX,MAAwB,IAAxB,IAAgCxD,EAAE,KAAK,KAAK,CAA5C,GAAgD,KAAK,CAArD,GAAyDA,EAAE,CAAC8C,MAAH,CAAUgB,CAAV,CAAzD,CAAA;EACA,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EArEL,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,SAAA;MAAA,KAsEI,EAAA,SAAA,OAAA,CAAQA,CAAR,EAAW;EACP,MAAA,IAAI,CAACtZ,SAAS,CAAC/C,MAAf,EACI,OAAO,KAAK2c,QAAZ,CAAA;QACJ,IAAKA,CAAAA,QAAL,GAAgBN,CAAhB,CAAA;EACA,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;EAjFA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,sBAAA;EAAA,IAAA,KAAA,EAkFI,SAAuB,oBAAA,GAAA;EACnB;EACA,MAAA,IAAI,CAAC,IAAA,CAAKO,aAAN,IACA,IAAKN,CAAAA,aADL,IAEA,IAAA,CAAKP,OAAL,CAAalB,QAAb,KAA0B,CAF9B,EAEiC;EAC7B;EACA,QAAA,IAAA,CAAKgC,SAAL,EAAA,CAAA;EACH,OAAA;EACJ,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;EAjGA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,MAAA;MAAA,KAkGI,EAAA,SAAA,IAAA,CAAKna,EAAL,EAAS;EAAA,MAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;QACL,IAAI,CAAC,IAAKkV,CAAAA,WAAL,CAAiB3N,OAAjB,CAAyB,MAAzB,CAAL,EACI,OAAO,IAAP,CAAA;QACJ,IAAKmO,CAAAA,MAAL,GAAc,IAAI0E,QAAJ,CAAW,KAAK1S,GAAhB,EAAqB,IAAKxF,CAAAA,IAA1B,CAAd,CAAA;QACA,IAAMsB,MAAM,GAAG,IAAA,CAAKkS,MAApB,CAAA;QACA,IAAMvU,IAAI,GAAG,IAAb,CAAA;QACA,IAAK+T,CAAAA,WAAL,GAAmB,SAAnB,CAAA;EACA,MAAA,IAAA,CAAKmF,aAAL,GAAqB,KAArB,CAPK;;QASL,IAAMC,cAAc,GAAGza,EAAE,CAAC2D,MAAD,EAAS,MAAT,EAAiB,YAAY;EAClDrC,QAAAA,IAAI,CAAC2J,MAAL,EAAA,CAAA;UACA9K,EAAE,IAAIA,EAAE,EAAR,CAAA;SAFqB,CAAzB,CATK;;QAcL,IAAMua,QAAQ,GAAG1a,EAAE,CAAC2D,MAAD,EAAS,OAAT,EAAkB,UAAC4B,GAAD,EAAS;EAC1CjE,QAAAA,IAAI,CAACiI,OAAL,EAAA,CAAA;UACAjI,IAAI,CAAC+T,WAAL,GAAmB,QAAnB,CAAA;;EACA,QAAA,MAAI,CAACnU,YAAL,CAAkB,OAAlB,EAA2BqE,GAA3B,CAAA,CAAA;;EACA,QAAA,IAAIpF,EAAJ,EAAQ;YACJA,EAAE,CAACoF,GAAD,CAAF,CAAA;EACH,SAFD,MAGK;EACD;EACAjE,UAAAA,IAAI,CAACqZ,oBAAL,EAAA,CAAA;EACH,SAAA;EACJ,OAXkB,CAAnB,CAAA;;QAYA,IAAI,KAAA,KAAU,IAAKP,CAAAA,QAAnB,EAA6B;UACzB,IAAMtR,OAAO,GAAG,IAAA,CAAKsR,QAArB,CAAA;;UACA,IAAItR,OAAO,KAAK,CAAhB,EAAmB;EACf2R,UAAAA,cAAc,GADC;EAElB,SAJwB;;;EAMzB,QAAA,IAAMvE,KAAK,GAAG,IAAK3T,CAAAA,YAAL,CAAkB,YAAM;YAClCkY,cAAc,EAAA,CAAA;YACd9W,MAAM,CAACqD,KAAP,EAAA,CAFkC;;YAIlCrD,MAAM,CAAC5C,IAAP,CAAY,OAAZ,EAAqB,IAAIwC,KAAJ,CAAU,SAAV,CAArB,CAAA,CAAA;WAJU,EAKXuF,OALW,CAAd,CAAA;;EAMA,QAAA,IAAI,IAAKzG,CAAAA,IAAL,CAAU6I,SAAd,EAAyB;EACrBgL,UAAAA,KAAK,CAAC9K,KAAN,EAAA,CAAA;EACH,SAAA;;EACD,QAAA,IAAA,CAAK8J,IAAL,CAAUtV,IAAV,CAAe,SAASiU,UAAT,GAAsB;YACjC1R,YAAY,CAAC+T,KAAD,CAAZ,CAAA;WADJ,CAAA,CAAA;EAGH,OAAA;;EACD,MAAA,IAAA,CAAKhB,IAAL,CAAUtV,IAAV,CAAe6a,cAAf,CAAA,CAAA;EACA,MAAA,IAAA,CAAKvF,IAAL,CAAUtV,IAAV,CAAe8a,QAAf,CAAA,CAAA;EACA,MAAA,OAAO,IAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;EAxJA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,SAAA;MAAA,KAyJI,EAAA,SAAA,OAAA,CAAQva,EAAR,EAAY;EACR,MAAA,OAAO,IAAKqI,CAAAA,IAAL,CAAUrI,EAAV,CAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EAhKA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,QAAA;EAAA,IAAA,KAAA,EAiKI,SAAS,MAAA,GAAA;EACL;QACA,IAAKoJ,CAAAA,OAAL,GAFK;;QAIL,IAAK8L,CAAAA,WAAL,GAAmB,MAAnB,CAAA;EACA,MAAA,IAAA,CAAKnU,YAAL,CAAkB,MAAlB,CAAA,CALK;;QAOL,IAAMyC,MAAM,GAAG,IAAA,CAAKkS,MAApB,CAAA;EACA,MAAA,IAAA,CAAKX,IAAL,CAAUtV,IAAV,CAAeI,EAAE,CAAC2D,MAAD,EAAS,MAAT,EAAiB,KAAKiX,MAAL,CAAYpY,IAAZ,CAAiB,IAAjB,CAAjB,CAAjB,EAA2DxC,EAAE,CAAC2D,MAAD,EAAS,MAAT,EAAiB,KAAKkX,MAAL,CAAYrY,IAAZ,CAAiB,IAAjB,CAAjB,CAA7D,EAAuGxC,EAAE,CAAC2D,MAAD,EAAS,OAAT,EAAkB,IAAA,CAAK8H,OAAL,CAAajJ,IAAb,CAAkB,IAAlB,CAAlB,CAAzG,EAAqJxC,EAAE,CAAC2D,MAAD,EAAS,OAAT,EAAkB,IAAA,CAAK0H,OAAL,CAAa7I,IAAb,CAAkB,IAAlB,CAAlB,CAAvJ,EAAmMxC,EAAE,CAAC,KAAK4Z,OAAN,EAAe,SAAf,EAA0B,KAAKkB,SAAL,CAAetY,IAAf,CAAoB,IAApB,CAA1B,CAArM,CAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EA/KA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,QAAA;EAAA,IAAA,KAAA,EAgLI,SAAS,MAAA,GAAA;QACL,IAAKtB,CAAAA,YAAL,CAAkB,MAAlB,CAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EAvLA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,QAAA;MAAA,KAwLI,EAAA,SAAA,MAAA,CAAOnF,IAAP,EAAa;QACT,IAAI;EACA,QAAA,IAAA,CAAK6d,OAAL,CAAamB,GAAb,CAAiBhf,IAAjB,CAAA,CAAA;SADJ,CAGA,OAAO4J,CAAP,EAAU;EACN,QAAA,IAAA,CAAK0F,OAAL,CAAa,aAAb,EAA4B1F,CAA5B,CAAA,CAAA;EACH,OAAA;EACJ,KAAA;EACD;EACJ;EACA;EACA;EACA;;EApMA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,WAAA;MAAA,KAqMI,EAAA,SAAA,SAAA,CAAUpG,MAAV,EAAkB;EAAA,MAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EACd;EACAuK,MAAAA,QAAQ,CAAC,YAAM;EACX,QAAA,MAAI,CAAC5I,YAAL,CAAkB,QAAlB,EAA4B3B,MAA5B,CAAA,CAAA;SADI,EAEL,IAAKgD,CAAAA,YAFA,CAAR,CAAA;EAGH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EA/MA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,SAAA;MAAA,KAgNI,EAAA,SAAA,OAAA,CAAQgD,GAAR,EAAa;EACT,MAAA,IAAA,CAAKrE,YAAL,CAAkB,OAAlB,EAA2BqE,GAA3B,CAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;EAxNA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,QAAA;EAAA,IAAA,KAAA,EAyNI,SAAO6M,MAAAA,CAAAA,GAAP,EAAY/P,IAAZ,EAAkB;EACd,MAAA,IAAIsB,MAAM,GAAG,IAAA,CAAKsV,IAAL,CAAU7G,GAAV,CAAb,CAAA;;QACA,IAAI,CAACzO,MAAL,EAAa;UACTA,MAAM,GAAG,IAAIqJ,MAAJ,CAAW,IAAX,EAAiBoF,GAAjB,EAAsB/P,IAAtB,CAAT,CAAA;EACA,QAAA,IAAA,CAAK4W,IAAL,CAAU7G,GAAV,CAAA,GAAiBzO,MAAjB,CAAA;SAFJ,MAIK,IAAI,IAAKsR,CAAAA,YAAL,IAAqB,CAACtR,MAAM,CAACqX,MAAjC,EAAyC;EAC1CrX,QAAAA,MAAM,CAACqQ,OAAP,EAAA,CAAA;EACH,OAAA;;EACD,MAAA,OAAOrQ,MAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;EAzOA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,UAAA;MAAA,KA0OI,EAAA,SAAA,QAAA,CAASA,MAAT,EAAiB;QACb,IAAMsV,IAAI,GAAG1d,MAAM,CAACG,IAAP,CAAY,IAAA,CAAKud,IAAjB,CAAb,CAAA;;EACA,MAAA,KAAA,IAAA,EAAA,GAAA,CAAA,EAAA,KAAA,GAAkBA,IAAlB,EAAwB,EAAA,GAAA,KAAA,CAAA,MAAA,EAAA,EAAA,EAAA,EAAA;EAAnB,QAAA,IAAM7G,GAAG,GAAT,KAAA,CAAA,EAAA,CAAA,CAAA;EACD,QAAA,IAAMzO,OAAM,GAAG,IAAA,CAAKsV,IAAL,CAAU7G,GAAV,CAAf,CAAA;;UACA,IAAIzO,OAAM,CAACqX,MAAX,EAAmB;EACf,UAAA,OAAA;EACH,SAAA;EACJ,OAAA;;EACD,MAAA,IAAA,CAAKC,MAAL,EAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;EACA;;EAzPA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,SAAA;MAAA,KA0PI,EAAA,SAAA,OAAA,CAAQ1b,MAAR,EAAgB;QACZ,IAAMH,cAAc,GAAG,IAAKua,CAAAA,OAAL,CAAanV,MAAb,CAAoBjF,MAApB,CAAvB,CAAA;;EACA,MAAA,KAAK,IAAI/B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4B,cAAc,CAAC3B,MAAnC,EAA2CD,CAAC,EAA5C,EAAgD;UAC5C,IAAKqY,CAAAA,MAAL,CAAY7R,KAAZ,CAAkB5E,cAAc,CAAC5B,CAAD,CAAhC,EAAqC+B,MAAM,CAACyQ,OAA5C,CAAA,CAAA;EACH,OAAA;EACJ,KAAA;EACD;EACJ;EACA;EACA;EACA;;EApQA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,SAAA;EAAA,IAAA,KAAA,EAqQI,SAAU,OAAA,GAAA;EACN,MAAA,IAAA,CAAKkF,IAAL,CAAUvZ,OAAV,CAAkB,UAACkY,UAAD,EAAA;EAAA,QAAA,OAAgBA,UAAU,EAA1B,CAAA;SAAlB,CAAA,CAAA;EACA,MAAA,IAAA,CAAKqB,IAAL,CAAUzX,MAAV,GAAmB,CAAnB,CAAA;QACA,IAAKmc,CAAAA,OAAL,CAAapC,OAAb,EAAA,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EA9QA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,QAAA;EAAA,IAAA,KAAA,EA+QI,SAAS,MAAA,GAAA;QACL,IAAKgD,CAAAA,aAAL,GAAqB,IAArB,CAAA;QACA,IAAKH,CAAAA,aAAL,GAAqB,KAArB,CAAA;QACA,IAAKhP,CAAAA,OAAL,CAAa,cAAb,CAAA,CAAA;EACA,MAAA,IAAI,KAAKwK,MAAT,EACI,IAAKA,CAAAA,MAAL,CAAY7O,KAAZ,EAAA,CAAA;EACP,KAAA;EACD;EACJ;EACA;EACA;EACA;;EA1RA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,YAAA;EAAA,IAAA,KAAA,EA2RI,SAAa,UAAA,GAAA;QACT,OAAO,IAAA,CAAKiU,MAAL,EAAP,CAAA;EACH,KAAA;EACD;EACJ;EACA;EACA;EACA;;EAlSA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,SAAA;EAAA,IAAA,KAAA,EAmSI,SAAQ7X,OAAAA,CAAAA,MAAR,EAAgBC,WAAhB,EAA6B;EACzB,MAAA,IAAA,CAAKkG,OAAL,EAAA,CAAA;QACA,IAAKiQ,CAAAA,OAAL,CAAaZ,KAAb,EAAA,CAAA;QACA,IAAKvD,CAAAA,WAAL,GAAmB,QAAnB,CAAA;EACA,MAAA,IAAA,CAAKnU,YAAL,CAAkB,OAAlB,EAA2BkC,MAA3B,EAAmCC,WAAnC,CAAA,CAAA;;EACA,MAAA,IAAI,KAAK0W,aAAL,IAAsB,CAAC,IAAA,CAAKS,aAAhC,EAA+C;EAC3C,QAAA,IAAA,CAAKF,SAAL,EAAA,CAAA;EACH,OAAA;EACJ,KAAA;EACD;EACJ;EACA;EACA;EACA;;EAhTA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,WAAA;EAAA,IAAA,KAAA,EAiTI,SAAY,SAAA,GAAA;EAAA,MAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EACR,MAAA,IAAI,KAAKD,aAAL,IAAsB,KAAKG,aAA/B,EACI,OAAO,IAAP,CAAA;QACJ,IAAMlZ,IAAI,GAAG,IAAb,CAAA;;EACA,MAAA,IAAI,KAAKkY,OAAL,CAAalB,QAAb,IAAyB,IAAA,CAAK0B,qBAAlC,EAAyD;UACrD,IAAKR,CAAAA,OAAL,CAAaZ,KAAb,EAAA,CAAA;UACA,IAAK1X,CAAAA,YAAL,CAAkB,kBAAlB,CAAA,CAAA;UACA,IAAKmZ,CAAAA,aAAL,GAAqB,KAArB,CAAA;EACH,OAJD,MAKK;EACD,QAAA,IAAMa,KAAK,GAAG,IAAA,CAAK1B,OAAL,CAAajB,QAAb,EAAd,CAAA;UACA,IAAK8B,CAAAA,aAAL,GAAqB,IAArB,CAAA;EACA,QAAA,IAAMnE,KAAK,GAAG,IAAK3T,CAAAA,YAAL,CAAkB,YAAM;YAClC,IAAIjB,IAAI,CAACkZ,aAAT,EACI,OAAA;;YACJ,MAAI,CAACtZ,YAAL,CAAkB,mBAAlB,EAAuCI,IAAI,CAACkY,OAAL,CAAalB,QAApD,CAAA,CAHkC;;;YAKlC,IAAIhX,IAAI,CAACkZ,aAAT,EACI,OAAA;EACJlZ,UAAAA,IAAI,CAACkH,IAAL,CAAU,UAACjD,GAAD,EAAS;EACf,YAAA,IAAIA,GAAJ,EAAS;gBACLjE,IAAI,CAAC+Y,aAAL,GAAqB,KAArB,CAAA;EACA/Y,cAAAA,IAAI,CAACgZ,SAAL,EAAA,CAAA;;EACA,cAAA,MAAI,CAACpZ,YAAL,CAAkB,iBAAlB,EAAqCqE,GAArC,CAAA,CAAA;EACH,aAJD,MAKK;EACDjE,cAAAA,IAAI,CAAC6Z,WAAL,EAAA,CAAA;EACH,aAAA;aARL,CAAA,CAAA;WAPU,EAiBXD,KAjBW,CAAd,CAAA;;EAkBA,QAAA,IAAI,IAAK7Y,CAAAA,IAAL,CAAU6I,SAAd,EAAyB;EACrBgL,UAAAA,KAAK,CAAC9K,KAAN,EAAA,CAAA;EACH,SAAA;;EACD,QAAA,IAAA,CAAK8J,IAAL,CAAUtV,IAAV,CAAe,SAASiU,UAAT,GAAsB;YACjC1R,YAAY,CAAC+T,KAAD,CAAZ,CAAA;WADJ,CAAA,CAAA;EAGH,OAAA;EACJ,KAAA;EACD;EACJ;EACA;EACA;EACA;;EA3VA,GAAA,EAAA;EAAA,IAAA,GAAA,EAAA,aAAA;EAAA,IAAA,KAAA,EA4VI,SAAc,WAAA,GAAA;EACV,MAAA,IAAMkF,OAAO,GAAG,IAAK5B,CAAAA,OAAL,CAAalB,QAA7B,CAAA;QACA,IAAK+B,CAAAA,aAAL,GAAqB,KAArB,CAAA;QACA,IAAKb,CAAAA,OAAL,CAAaZ,KAAb,EAAA,CAAA;EACA,MAAA,IAAA,CAAK1X,YAAL,CAAkB,WAAlB,EAA+Bka,OAA/B,CAAA,CAAA;EACH,KAAA;EAjWL,GAAA,CAAA,CAAA,CAAA;;EAAA,EAAA,OAAA,OAAA,CAAA;EAAA,CAAA,CAA6Btb,OAA7B,CAAA;;ECHA;EACA;EACA;;EACA,IAAMub,KAAK,GAAG,EAAd,CAAA;;EACA,SAAS/d,MAAT,CAAgBuK,GAAhB,EAAqBxF,IAArB,EAA2B;EACvB,EAAA,IAAI,OAAOwF,CAAAA,GAAP,CAAe,KAAA,QAAnB,EAA6B;EACzBxF,IAAAA,IAAI,GAAGwF,GAAP,CAAA;EACAA,IAAAA,GAAG,GAAGS,SAAN,CAAA;EACH,GAAA;;IACDjG,IAAI,GAAGA,IAAI,IAAI,EAAf,CAAA;IACA,IAAMiZ,MAAM,GAAGhL,GAAG,CAACzI,GAAD,EAAMxF,IAAI,CAACsF,IAAL,IAAa,YAAnB,CAAlB,CAAA;EACA,EAAA,IAAM0E,MAAM,GAAGiP,MAAM,CAACjP,MAAtB,CAAA;EACA,EAAA,IAAMuB,EAAE,GAAG0N,MAAM,CAAC1N,EAAlB,CAAA;EACA,EAAA,IAAMjG,IAAI,GAAG2T,MAAM,CAAC3T,IAApB,CAAA;EACA,EAAA,IAAMwP,aAAa,GAAGkE,KAAK,CAACzN,EAAD,CAAL,IAAajG,IAAI,IAAI0T,KAAK,CAACzN,EAAD,CAAL,CAAU,MAAV,CAA3C,CAAA;EACA,EAAA,IAAM2N,aAAa,GAAGlZ,IAAI,CAACmZ,QAAL,IAClBnZ,IAAI,CAAC,sBAAD,CADc,IAElB,KAAUA,KAAAA,IAAI,CAACoZ,SAFG,IAGlBtE,aAHJ,CAAA;EAIA,EAAA,IAAI9C,EAAJ,CAAA;;EACA,EAAA,IAAIkH,aAAJ,EAAmB;EACflH,IAAAA,EAAE,GAAG,IAAI2E,OAAJ,CAAY3M,MAAZ,EAAoBhK,IAApB,CAAL,CAAA;EACH,GAFD,MAGK;EACD,IAAA,IAAI,CAACgZ,KAAK,CAACzN,EAAD,CAAV,EAAgB;QACZyN,KAAK,CAACzN,EAAD,CAAL,GAAY,IAAIoL,OAAJ,CAAY3M,MAAZ,EAAoBhK,IAApB,CAAZ,CAAA;EACH,KAAA;;EACDgS,IAAAA,EAAE,GAAGgH,KAAK,CAACzN,EAAD,CAAV,CAAA;EACH,GAAA;;IACD,IAAI0N,MAAM,CAAC5X,KAAP,IAAgB,CAACrB,IAAI,CAACqB,KAA1B,EAAiC;EAC7BrB,IAAAA,IAAI,CAACqB,KAAL,GAAa4X,MAAM,CAAC5O,QAApB,CAAA;EACH,GAAA;;IACD,OAAO2H,EAAE,CAAC1Q,MAAH,CAAU2X,MAAM,CAAC3T,IAAjB,EAAuBtF,IAAvB,CAAP,CAAA;EACH;EAED;;;EACA,QAAA,CAAc/E,MAAd,EAAsB;EAClB0b,EAAAA,OAAO,EAAPA,OADkB;EAElBhM,EAAAA,MAAM,EAANA,MAFkB;EAGlBqH,EAAAA,EAAE,EAAE/W,MAHc;EAIlB0W,EAAAA,OAAO,EAAE1W,MAAAA;EAJS,CAAtB,CAAA;;;;;;;;"}