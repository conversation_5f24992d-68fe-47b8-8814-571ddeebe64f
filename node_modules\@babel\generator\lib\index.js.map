{"version": 3, "names": ["_sourceMap", "require", "_printer", "Generator", "Printer", "constructor", "ast", "opts", "code", "format", "normalizeOptions", "map", "sourceMaps", "SourceMap", "generate", "auxiliaryCommentBefore", "auxiliaryCommentAfter", "shouldPrintComment", "retainLines", "retainFunctionParens", "comments", "compact", "minified", "concise", "indent", "adjustMultilineComment", "style", "jsescOption", "Object", "assign", "quotes", "wrap", "minimal", "recordAndTupleSyntaxType", "topicToken", "decoratorsBeforeExport", "json", "jsonCompatibleStrings", "value", "includes", "length", "console", "error", "filename", "undefined", "CodeGenerator", "_generator", "exports", "gen"], "sources": ["../src/index.ts"], "sourcesContent": ["import SourceMap from \"./source-map\";\nimport Printer from \"./printer\";\nimport type * as t from \"@babel/types\";\nimport type { Opts as jsescOptions } from \"jsesc\";\nimport type { Format } from \"./printer\";\nimport type {\n  RecordAndTuplePluginOptions,\n  PipelineOperatorPluginOptions,\n} from \"@babel/parser\";\nimport type { DecodedSourceMap, Mapping } from \"@jridgewell/gen-mapping\";\n\n/**\n * <PERSON><PERSON>'s code generator, turns an ast into code, maintaining sourcemaps,\n * user preferences, and valid output.\n */\n\nclass Generator extends Printer {\n  constructor(\n    ast: t.Node,\n    opts: GeneratorOptions = {},\n    code: string | { [filename: string]: string },\n  ) {\n    const format = normalizeOptions(code, opts);\n    const map = opts.sourceMaps ? new SourceMap(opts, code) : null;\n    super(format, map);\n\n    this.ast = ast;\n  }\n\n  ast: t.Node;\n\n  /**\n   * Generate code and sourcemap from ast.\n   *\n   * Appends comments that weren't attached to any node to the end of the generated output.\n   */\n\n  generate() {\n    return super.generate(this.ast);\n  }\n}\n\n/**\n * Normalize generator options, setting defaults.\n *\n * - Detects code indentation.\n * - If `opts.compact = \"auto\"` and the code is over 500KB, `compact` will be set to `true`.\n */\n\nfunction normalizeOptions(\n  code: string | { [filename: string]: string },\n  opts: GeneratorOptions,\n): Format {\n  const format: Format = {\n    auxiliaryCommentBefore: opts.auxiliaryCommentBefore,\n    auxiliaryCommentAfter: opts.auxiliaryCommentAfter,\n    shouldPrintComment: opts.shouldPrintComment,\n    retainLines: opts.retainLines,\n    retainFunctionParens: opts.retainFunctionParens,\n    comments: opts.comments == null || opts.comments,\n    compact: opts.compact,\n    minified: opts.minified,\n    concise: opts.concise,\n    indent: {\n      adjustMultilineComment: true,\n      style: \"  \",\n    },\n    jsescOption: {\n      quotes: \"double\",\n      wrap: true,\n      minimal: process.env.BABEL_8_BREAKING ? true : false,\n      ...opts.jsescOption,\n    },\n    recordAndTupleSyntaxType: opts.recordAndTupleSyntaxType,\n    topicToken: opts.topicToken,\n  };\n\n  if (!process.env.BABEL_8_BREAKING) {\n    format.decoratorsBeforeExport = opts.decoratorsBeforeExport;\n    format.jsescOption.json = opts.jsonCompatibleStrings;\n  }\n\n  if (format.minified) {\n    format.compact = true;\n\n    format.shouldPrintComment =\n      format.shouldPrintComment || (() => format.comments);\n  } else {\n    format.shouldPrintComment =\n      format.shouldPrintComment ||\n      (value =>\n        format.comments ||\n        value.includes(\"@license\") ||\n        value.includes(\"@preserve\"));\n  }\n\n  if (format.compact === \"auto\") {\n    format.compact = typeof code === \"string\" && code.length > 500_000; // 500KB\n\n    if (format.compact) {\n      console.error(\n        \"[BABEL] Note: The code generator has deoptimised the styling of \" +\n          `${opts.filename} as it exceeds the max of ${\"500KB\"}.`,\n      );\n    }\n  }\n\n  if (format.compact) {\n    format.indent.adjustMultilineComment = false;\n  }\n\n  const { auxiliaryCommentBefore, auxiliaryCommentAfter, shouldPrintComment } =\n    format;\n\n  if (auxiliaryCommentBefore && !shouldPrintComment(auxiliaryCommentBefore)) {\n    format.auxiliaryCommentBefore = undefined;\n  }\n  if (auxiliaryCommentAfter && !shouldPrintComment(auxiliaryCommentAfter)) {\n    format.auxiliaryCommentAfter = undefined;\n  }\n\n  return format;\n}\n\nexport interface GeneratorOptions {\n  /**\n   * Optional string to add as a block comment at the start of the output file.\n   */\n  auxiliaryCommentBefore?: string;\n\n  /**\n   * Optional string to add as a block comment at the end of the output file.\n   */\n  auxiliaryCommentAfter?: string;\n\n  /**\n   * Function that takes a comment (as a string) and returns true if the comment should be included in the output.\n   * By default, comments are included if `opts.comments` is `true` or if `opts.minified` is `false` and the comment\n   * contains `@preserve` or `@license`.\n   */\n  shouldPrintComment?(comment: string): boolean;\n\n  /**\n   * Attempt to use the same line numbers in the output code as in the source code (helps preserve stack traces).\n   * Defaults to `false`.\n   */\n  retainLines?: boolean;\n\n  /**\n   * Retain parens around function expressions (could be used to change engine parsing behavior)\n   * Defaults to `false`.\n   */\n  retainFunctionParens?: boolean;\n\n  /**\n   * Should comments be included in output? Defaults to `true`.\n   */\n  comments?: boolean;\n\n  /**\n   * Set to true to avoid adding whitespace for formatting. Defaults to the value of `opts.minified`.\n   */\n  compact?: boolean | \"auto\";\n\n  /**\n   * Should the output be minified. Defaults to `false`.\n   */\n  minified?: boolean;\n\n  /**\n   * Set to true to reduce whitespace (but not as much as opts.compact). Defaults to `false`.\n   */\n  concise?: boolean;\n\n  /**\n   * Used in warning messages\n   */\n  filename?: string;\n\n  /**\n   * Enable generating source maps. Defaults to `false`.\n   */\n  sourceMaps?: boolean;\n\n  inputSourceMap?: any;\n\n  /**\n   * A root for all relative URLs in the source map.\n   */\n  sourceRoot?: string;\n\n  /**\n   * The filename for the source code (i.e. the code in the `code` argument).\n   * This will only be used if `code` is a string.\n   */\n  sourceFileName?: string;\n\n  /**\n   * Set to true to run jsesc with \"json\": true to print \"\\u00A9\" vs. \"©\";\n   * @deprecated use `jsescOptions: { json: true }` instead\n   */\n  jsonCompatibleStrings?: boolean;\n\n  /**\n   * Set to true to enable support for experimental decorators syntax before\n   * module exports. If not specified, decorators will be printed in the same\n   * position as they were in the input source code.\n   * @deprecated Removed in Babel 8\n   */\n  decoratorsBeforeExport?: boolean;\n\n  /**\n   * Options for outputting jsesc representation.\n   */\n  jsescOption?: jsescOptions;\n\n  /**\n   * For use with the recordAndTuple token.\n   */\n  recordAndTupleSyntaxType?: RecordAndTuplePluginOptions[\"syntaxType\"];\n  /**\n   * For use with the Hack-style pipe operator.\n   * Changes what token is used for pipe bodies’ topic references.\n   */\n  topicToken?: PipelineOperatorPluginOptions[\"topicToken\"];\n}\n\nexport interface GeneratorResult {\n  code: string;\n  map: {\n    version: number;\n    sources: readonly string[];\n    names: readonly string[];\n    sourceRoot?: string;\n    sourcesContent?: readonly string[];\n    mappings: string;\n    file?: string;\n  } | null;\n  decodedMap: DecodedSourceMap | undefined;\n  rawMappings: Mapping[] | undefined;\n}\n\n/**\n * We originally exported the Generator class above, but to make it extra clear that it is a private API,\n * we have moved that to an internal class instance and simplified the interface to the two public methods\n * that we wish to support.\n */\n\nexport class CodeGenerator {\n  private _generator: Generator;\n  constructor(ast: t.Node, opts?: GeneratorOptions, code?: string) {\n    this._generator = new Generator(ast, opts, code);\n  }\n  generate(): GeneratorResult {\n    return this._generator.generate();\n  }\n}\n\n/**\n * Turns an AST into code, maintaining sourcemaps, user preferences, and valid output.\n * @param ast - the abstract syntax tree from which to generate output code.\n * @param opts - used for specifying options for code generation.\n * @param code - the original source code, used for source maps.\n * @returns - an object containing the output code and source map.\n */\nexport default function generate(\n  ast: t.Node,\n  opts?: GeneratorOptions,\n  code?: string | { [filename: string]: string },\n) {\n  const gen = new Generator(ast, opts, code);\n  return gen.generate();\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AAeA,MAAME,SAAS,SAASC,gBAAO,CAAC;EAC9BC,WAAWA,CACTC,GAAW,EACXC,IAAsB,GAAG,CAAC,CAAC,EAC3BC,IAA6C,EAC7C;IACA,MAAMC,MAAM,GAAGC,gBAAgB,CAACF,IAAI,EAAED,IAAI,CAAC;IAC3C,MAAMI,GAAG,GAAGJ,IAAI,CAACK,UAAU,GAAG,IAAIC,kBAAS,CAACN,IAAI,EAAEC,IAAI,CAAC,GAAG,IAAI;IAC9D,KAAK,CAACC,MAAM,EAAEE,GAAG,CAAC;IAAC,KAKrBL,GAAG;IAHD,IAAI,CAACA,GAAG,GAAGA,GAAG;EAChB;EAUAQ,QAAQA,CAAA,EAAG;IACT,OAAO,KAAK,CAACA,QAAQ,CAAC,IAAI,CAACR,GAAG,CAAC;EACjC;AACF;AASA,SAASI,gBAAgBA,CACvBF,IAA6C,EAC7CD,IAAsB,EACd;EACR,MAAME,MAAc,GAAG;IACrBM,sBAAsB,EAAER,IAAI,CAACQ,sBAAsB;IACnDC,qBAAqB,EAAET,IAAI,CAACS,qBAAqB;IACjDC,kBAAkB,EAAEV,IAAI,CAACU,kBAAkB;IAC3CC,WAAW,EAAEX,IAAI,CAACW,WAAW;IAC7BC,oBAAoB,EAAEZ,IAAI,CAACY,oBAAoB;IAC/CC,QAAQ,EAAEb,IAAI,CAACa,QAAQ,IAAI,IAAI,IAAIb,IAAI,CAACa,QAAQ;IAChDC,OAAO,EAAEd,IAAI,CAACc,OAAO;IACrBC,QAAQ,EAAEf,IAAI,CAACe,QAAQ;IACvBC,OAAO,EAAEhB,IAAI,CAACgB,OAAO;IACrBC,MAAM,EAAE;MACNC,sBAAsB,EAAE,IAAI;MAC5BC,KAAK,EAAE;IACT,CAAC;IACDC,WAAW,EAAAC,MAAA,CAAAC,MAAA;MACTC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAwC;IAAK,GACjDzB,IAAI,CAACoB,WAAW,CACpB;IACDM,wBAAwB,EAAE1B,IAAI,CAAC0B,wBAAwB;IACvDC,UAAU,EAAE3B,IAAI,CAAC2B;EACnB,CAAC;EAEkC;IACjCzB,MAAM,CAAC0B,sBAAsB,GAAG5B,IAAI,CAAC4B,sBAAsB;IAC3D1B,MAAM,CAACkB,WAAW,CAACS,IAAI,GAAG7B,IAAI,CAAC8B,qBAAqB;EACtD;EAEA,IAAI5B,MAAM,CAACa,QAAQ,EAAE;IACnBb,MAAM,CAACY,OAAO,GAAG,IAAI;IAErBZ,MAAM,CAACQ,kBAAkB,GACvBR,MAAM,CAACQ,kBAAkB,KAAK,MAAMR,MAAM,CAACW,QAAQ,CAAC;EACxD,CAAC,MAAM;IACLX,MAAM,CAACQ,kBAAkB,GACvBR,MAAM,CAACQ,kBAAkB,KACxBqB,KAAK,IACJ7B,MAAM,CAACW,QAAQ,IACfkB,KAAK,CAACC,QAAQ,CAAC,UAAU,CAAC,IAC1BD,KAAK,CAACC,QAAQ,CAAC,WAAW,CAAC,CAAC;EAClC;EAEA,IAAI9B,MAAM,CAACY,OAAO,KAAK,MAAM,EAAE;IAC7BZ,MAAM,CAACY,OAAO,GAAG,OAAOb,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACgC,MAAM,GAAG,MAAO;IAElE,IAAI/B,MAAM,CAACY,OAAO,EAAE;MAClBoB,OAAO,CAACC,KAAK,CACX,kEAAkE,GAC/D,GAAEnC,IAAI,CAACoC,QAAS,6BAA4B,OAAQ,GAAE,CAC1D;IACH;EACF;EAEA,IAAIlC,MAAM,CAACY,OAAO,EAAE;IAClBZ,MAAM,CAACe,MAAM,CAACC,sBAAsB,GAAG,KAAK;EAC9C;EAEA,MAAM;IAAEV,sBAAsB;IAAEC,qBAAqB;IAAEC;EAAmB,CAAC,GACzER,MAAM;EAER,IAAIM,sBAAsB,IAAI,CAACE,kBAAkB,CAACF,sBAAsB,CAAC,EAAE;IACzEN,MAAM,CAACM,sBAAsB,GAAG6B,SAAS;EAC3C;EACA,IAAI5B,qBAAqB,IAAI,CAACC,kBAAkB,CAACD,qBAAqB,CAAC,EAAE;IACvEP,MAAM,CAACO,qBAAqB,GAAG4B,SAAS;EAC1C;EAEA,OAAOnC,MAAM;AACf;AA8HO,MAAMoC,aAAa,CAAC;EAEzBxC,WAAWA,CAACC,GAAW,EAAEC,IAAuB,EAAEC,IAAa,EAAE;IAAA,KADzDsC,UAAU;IAEhB,IAAI,CAACA,UAAU,GAAG,IAAI3C,SAAS,CAACG,GAAG,EAAEC,IAAI,EAAEC,IAAI,CAAC;EAClD;EACAM,QAAQA,CAAA,EAAoB;IAC1B,OAAO,IAAI,CAACgC,UAAU,CAAChC,QAAQ,EAAE;EACnC;AACF;AAACiC,OAAA,CAAAF,aAAA,GAAAA,aAAA;AASc,SAAS/B,QAAQA,CAC9BR,GAAW,EACXC,IAAuB,EACvBC,IAA8C,EAC9C;EACA,MAAMwC,GAAG,GAAG,IAAI7C,SAAS,CAACG,GAAG,EAAEC,IAAI,EAAEC,IAAI,CAAC;EAC1C,OAAOwC,GAAG,CAAClC,QAAQ,EAAE;AACvB"}