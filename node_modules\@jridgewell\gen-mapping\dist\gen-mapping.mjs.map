{"version": 3, "file": "gen-mapping.mjs", "sources": ["../../src/gen-mapping.ts"], "sourcesContent": ["import { SetArray, put } from '@jridgewell/set-array';\nimport { encode } from '@jridgewell/sourcemap-codec';\n\nimport type { SourceMapSegment } from './sourcemap-segment';\nimport type { DecodedSourceMap, EncodedSourceMap, Pos, Mapping } from './types';\n\nexport type { DecodedSourceMap, EncodedSourceMap, Mapping };\n\nexport type Options = {\n  file?: string | null;\n  sourceRoot?: string | null;\n};\n\n/**\n * A low-level API to associate a generated position with an original source position. Line and\n * column here are 0-based, unlike `addMapping`.\n */\nexport let addSegment: {\n  (\n    map: GenMapping,\n    genLine: number,\n    genColumn: number,\n    source?: null,\n    sourceLine?: null,\n    sourceColumn?: null,\n    name?: null,\n  ): void;\n  (\n    map: GenMapping,\n    genLine: number,\n    genColumn: number,\n    source: string,\n    sourceLine: number,\n    sourceColumn: number,\n    name?: null,\n  ): void;\n  (\n    map: GenMapping,\n    genLine: number,\n    genColumn: number,\n    source: string,\n    sourceLine: number,\n    sourceColumn: number,\n    name: string,\n  ): void;\n};\n\n/**\n * A high-level API to associate a generated position with an original source position. Line is\n * 1-based, but column is 0-based, due to legacy behavior in `source-map` library.\n */\nexport let addMapping: {\n  (\n    map: GenMapping,\n    mapping: {\n      generated: Pos;\n      source?: null;\n      original?: null;\n      name?: null;\n    },\n  ): void;\n  (\n    map: GenMapping,\n    mapping: {\n      generated: Pos;\n      source: string;\n      original: Pos;\n      name?: null;\n    },\n  ): void;\n  (\n    map: GenMapping,\n    mapping: {\n      generated: Pos;\n      source: string;\n      original: Pos;\n      name: string;\n    },\n  ): void;\n};\n\n/**\n * Adds/removes the content of the source file to the source map.\n */\nexport let setSourceContent: (map: GenMapping, source: string, content: string | null) => void;\n\n/**\n * Returns a sourcemap object (with decoded mappings) suitable for passing to a library that expects\n * a sourcemap, or to JSON.stringify.\n */\nexport let decodedMap: (map: GenMapping) => DecodedSourceMap;\n\n/**\n * Returns a sourcemap object (with encoded mappings) suitable for passing to a library that expects\n * a sourcemap, or to JSON.stringify.\n */\nexport let encodedMap: (map: GenMapping) => EncodedSourceMap;\n\n/**\n * Returns an array of high-level mapping objects for every recorded segment, which could then be\n * passed to the `source-map` library.\n */\nexport let allMappings: (map: GenMapping) => Mapping[];\n\n/**\n * Provides the state to generate a sourcemap.\n */\nexport class GenMapping {\n  private _names = new SetArray();\n  private _sources = new SetArray();\n  private _sourcesContent: (string | null)[] = [];\n  private _mappings: SourceMapSegment[][] = [];\n  declare file: string | null | undefined;\n  declare sourceRoot: string | null | undefined;\n\n  constructor({ file, sourceRoot }: Options = {}) {\n    this.file = file;\n    this.sourceRoot = sourceRoot;\n  }\n\n  static {\n    addSegment = (map, genLine, genColumn, source, sourceLine, sourceColumn, name) => {\n      const {\n        _mappings: mappings,\n        _sources: sources,\n        _sourcesContent: sourcesContent,\n        _names: names,\n      } = map;\n\n      const line = getLine(mappings, genLine);\n      if (source == null) {\n        const seg: SourceMapSegment = [genColumn];\n        const index = getColumnIndex(line, genColumn, seg);\n        return insert(line, index, seg);\n      }\n\n      // Sigh, TypeScript can't figure out sourceLine and sourceColumn aren't nullish if source\n      // isn't nullish.\n      assert<number>(sourceLine);\n      assert<number>(sourceColumn);\n      const sourcesIndex = put(sources, source);\n      const seg: SourceMapSegment = name\n        ? [genColumn, sourcesIndex, sourceLine, sourceColumn, put(names, name)]\n        : [genColumn, sourcesIndex, sourceLine, sourceColumn];\n\n      const index = getColumnIndex(line, genColumn, seg);\n      if (sourcesIndex === sourcesContent.length) sourcesContent[sourcesIndex] = null;\n      insert(line, index, seg);\n    };\n\n    addMapping = (map, mapping) => {\n      const { generated, source, original, name } = mapping;\n      return (addSegment as any)(\n        map,\n        generated.line - 1,\n        generated.column,\n        source,\n        original == null ? undefined : original.line - 1,\n        original?.column,\n        name,\n      );\n    };\n\n    setSourceContent = (map, source, content) => {\n      const { _sources: sources, _sourcesContent: sourcesContent } = map;\n      sourcesContent[put(sources, source)] = content;\n    };\n\n    decodedMap = (map) => {\n      const {\n        file,\n        sourceRoot,\n        _mappings: mappings,\n        _sources: sources,\n        _sourcesContent: sourcesContent,\n        _names: names,\n      } = map;\n\n      return {\n        version: 3,\n        file,\n        names: names.array,\n        sourceRoot: sourceRoot || undefined,\n        sources: sources.array,\n        sourcesContent,\n        mappings,\n      };\n    };\n\n    encodedMap = (map) => {\n      const decoded = decodedMap(map);\n      return {\n        ...decoded,\n        mappings: encode(decoded.mappings as SourceMapSegment[][]),\n      };\n    };\n\n    allMappings = (map) => {\n      const out: Mapping[] = [];\n      const { _mappings: mappings, _sources: sources, _names: names } = map;\n\n      for (let i = 0; i < mappings.length; i++) {\n        const line = mappings[i];\n        for (let j = 0; j < line.length; j++) {\n          const seg = line[j];\n\n          const generated = { line: i + 1, column: seg[0] };\n          let source: string | undefined = undefined;\n          let original: Pos | undefined = undefined;\n          let name: string | undefined = undefined;\n\n          if (seg.length !== 1) {\n            source = sources.array[seg[1]];\n            original = { line: seg[2] + 1, column: seg[3] };\n\n            if (seg.length === 5) name = names.array[seg[4]];\n          }\n\n          out.push({ generated, source, original, name } as Mapping);\n        }\n      }\n\n      return out;\n    };\n  }\n}\n\nfunction assert<T>(_val: unknown): asserts _val is T {\n  // noop.\n}\n\nfunction getLine(mappings: SourceMapSegment[][], index: number): SourceMapSegment[] {\n  for (let i = mappings.length; i <= index; i++) {\n    mappings[i] = [];\n  }\n  return mappings[index];\n}\n\nfunction getColumnIndex(line: SourceMapSegment[], column: number, seg: SourceMapSegment): number {\n  let index = line.length;\n  for (let i = index - 1; i >= 0; i--, index--) {\n    const current = line[i];\n    const col = current[0];\n    if (col > column) continue;\n    if (col < column) break;\n\n    const cmp = compare(current, seg);\n    if (cmp === 0) return index;\n    if (cmp < 0) break;\n  }\n  return index;\n}\n\nfunction compare(a: SourceMapSegment, b: SourceMapSegment): number {\n  let cmp = compareNum(a.length, b.length);\n  if (cmp !== 0) return cmp;\n\n  // We've already checked genColumn\n  if (a.length === 1) return 0;\n\n  cmp = compareNum(a[1], b[1]!);\n  if (cmp !== 0) return cmp;\n  cmp = compareNum(a[2], b[2]!);\n  if (cmp !== 0) return cmp;\n  cmp = compareNum(a[3], b[3]!);\n  if (cmp !== 0) return cmp;\n\n  if (a.length === 4) return 0;\n  return compareNum(a[4], b[4]!);\n}\n\nfunction compareNum(a: number, b: number): number {\n  return a - b;\n}\n\nfunction insert<T>(array: T[], index: number, value: T) {\n  if (index === -1) return;\n  for (let i = array.length; i > index; i--) {\n    array[i] = array[i - 1];\n  }\n  array[index] = value;\n}\n"], "names": [], "mappings": ";;;AAaA;;;AAGG;AACQ,IAAA,WA4BT;AAEF;;;AAGG;AACQ,IAAA,WA4BT;AAEF;;AAEG;AACQ,IAAA,iBAAoF;AAE/F;;;AAGG;AACQ,IAAA,WAAkD;AAE7D;;;AAGG;AACQ,IAAA,WAAkD;AAE7D;;;AAGG;AACQ,IAAA,YAA4C;AAEvD;;AAEG;MACU,UAAU,CAAA;AAQrB,IAAA,WAAA,CAAY,EAAE,IAAI,EAAE,UAAU,KAAc,EAAE,EAAA;AAPtC,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;AACxB,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;QAC1B,IAAe,CAAA,eAAA,GAAsB,EAAE,CAAC;QACxC,IAAS,CAAA,SAAA,GAAyB,EAAE,CAAC;AAK3C,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;KAC9B;AA2GF,CAAA;AAzGC,CAAA,MAAA;AACE,IAAA,UAAU,GAAG,CAAC,GAAG,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,KAAI;AAC/E,QAAA,MAAM,EACJ,SAAS,EAAE,QAAQ,EACnB,QAAQ,EAAE,OAAO,EACjB,eAAe,EAAE,cAAc,EAC/B,MAAM,EAAE,KAAK,GACd,GAAG,GAAG,CAAC;QAER,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACxC,IAAI,MAAM,IAAI,IAAI,EAAE;AAClB,YAAA,MAAM,GAAG,GAAqB,CAAC,SAAS,CAAC,CAAC;YAC1C,MAAM,KAAK,GAAG,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;YACnD,OAAO,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;AACjC,SAAA;QAMD,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC1C,MAAM,GAAG,GAAqB,IAAI;AAChC,cAAE,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;cACrE,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;QAExD,MAAM,KAAK,GAAG,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;AACnD,QAAA,IAAI,YAAY,KAAK,cAAc,CAAC,MAAM;AAAE,YAAA,cAAc,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC;AAChF,QAAA,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;AAC3B,KAAC,CAAC;AAEF,IAAA,UAAU,GAAG,CAAC,GAAG,EAAE,OAAO,KAAI;QAC5B,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACtD,QAAA,OAAQ,UAAkB,CACxB,GAAG,EACH,SAAS,CAAC,IAAI,GAAG,CAAC,EAClB,SAAS,CAAC,MAAM,EAChB,MAAM,EACN,QAAQ,IAAI,IAAI,GAAG,SAAS,GAAG,QAAQ,CAAC,IAAI,GAAG,CAAC,EAChD,QAAQ,KAAR,IAAA,IAAA,QAAQ,uBAAR,QAAQ,CAAE,MAAM,EAChB,IAAI,CACL,CAAC;AACJ,KAAC,CAAC;IAEF,gBAAgB,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,KAAI;QAC1C,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,eAAe,EAAE,cAAc,EAAE,GAAG,GAAG,CAAC;QACnE,cAAc,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC;AACjD,KAAC,CAAC;AAEF,IAAA,UAAU,GAAG,CAAC,GAAG,KAAI;QACnB,MAAM,EACJ,IAAI,EACJ,UAAU,EACV,SAAS,EAAE,QAAQ,EACnB,QAAQ,EAAE,OAAO,EACjB,eAAe,EAAE,cAAc,EAC/B,MAAM,EAAE,KAAK,GACd,GAAG,GAAG,CAAC;QAER,OAAO;AACL,YAAA,OAAO,EAAE,CAAC;YACV,IAAI;YACJ,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,UAAU,EAAE,UAAU,IAAI,SAAS;YACnC,OAAO,EAAE,OAAO,CAAC,KAAK;YACtB,cAAc;YACd,QAAQ;SACT,CAAC;AACJ,KAAC,CAAC;AAEF,IAAA,UAAU,GAAG,CAAC,GAAG,KAAI;AACnB,QAAA,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;QAChC,OACK,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,OAAO,CACV,EAAA,EAAA,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,QAAgC,CAAC,EAC1D,CAAA,CAAA;AACJ,KAAC,CAAC;AAEF,IAAA,WAAW,GAAG,CAAC,GAAG,KAAI;QACpB,MAAM,GAAG,GAAc,EAAE,CAAC;AAC1B,QAAA,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC;AAEtE,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxC,YAAA,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AACzB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACpC,gBAAA,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAEpB,gBAAA,MAAM,SAAS,GAAG,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;gBAClD,IAAI,MAAM,GAAuB,SAAS,CAAC;gBAC3C,IAAI,QAAQ,GAAoB,SAAS,CAAC;gBAC1C,IAAI,IAAI,GAAuB,SAAS,CAAC;AAEzC,gBAAA,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;oBACpB,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B,oBAAA,QAAQ,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;AAEhD,oBAAA,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC;wBAAE,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,iBAAA;AAED,gBAAA,GAAG,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAa,CAAC,CAAC;AAC5D,aAAA;AACF,SAAA;AAED,QAAA,OAAO,GAAG,CAAC;AACb,KAAC,CAAC;AACJ,CAAC,GAAA,CAAA;AAOH,SAAS,OAAO,CAAC,QAA8B,EAAE,KAAa,EAAA;AAC5D,IAAA,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE;AAC7C,QAAA,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AAClB,KAAA;AACD,IAAA,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;AACzB,CAAC;AAED,SAAS,cAAc,CAAC,IAAwB,EAAE,MAAc,EAAE,GAAqB,EAAA;AACrF,IAAA,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,IAAA,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE;AAC5C,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACxB,QAAA,MAAM,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,GAAG,GAAG,MAAM;YAAE,SAAS;QAC3B,IAAI,GAAG,GAAG,MAAM;YAAE,MAAM;QAExB,MAAM,GAAG,GAAG,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QAClC,IAAI,GAAG,KAAK,CAAC;AAAE,YAAA,OAAO,KAAK,CAAC;QAC5B,IAAI,GAAG,GAAG,CAAC;YAAE,MAAM;AACpB,KAAA;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,OAAO,CAAC,CAAmB,EAAE,CAAmB,EAAA;AACvD,IAAA,IAAI,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;IACzC,IAAI,GAAG,KAAK,CAAC;AAAE,QAAA,OAAO,GAAG,CAAC;;AAG1B,IAAA,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC;AAAE,QAAA,OAAO,CAAC,CAAC;AAE7B,IAAA,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;IAC9B,IAAI,GAAG,KAAK,CAAC;AAAE,QAAA,OAAO,GAAG,CAAC;AAC1B,IAAA,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;IAC9B,IAAI,GAAG,KAAK,CAAC;AAAE,QAAA,OAAO,GAAG,CAAC;AAC1B,IAAA,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;IAC9B,IAAI,GAAG,KAAK,CAAC;AAAE,QAAA,OAAO,GAAG,CAAC;AAE1B,IAAA,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC;AAAE,QAAA,OAAO,CAAC,CAAC;AAC7B,IAAA,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;AACjC,CAAC;AAED,SAAS,UAAU,CAAC,CAAS,EAAE,CAAS,EAAA;IACtC,OAAO,CAAC,GAAG,CAAC,CAAC;AACf,CAAC;AAED,SAAS,MAAM,CAAI,KAAU,EAAE,KAAa,EAAE,KAAQ,EAAA;IACpD,IAAI,KAAK,KAAK,CAAC,CAAC;QAAE,OAAO;AACzB,IAAA,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;QACzC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACzB,KAAA;AACD,IAAA,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;AACvB;;;;"}