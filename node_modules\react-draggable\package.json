{"name": "react-draggable", "version": "4.4.5", "description": "React draggable component", "main": "build/cjs/cjs.js", "unpkg": "build/web/react-draggable.min.js", "scripts": {"test": "make test", "test-phantom": "make test-phantom", "test-debug": "karma start --browsers=Chrome --single-run=false --auto-watch=true", "test-firefox": "karma start --browsers=Firefox --single-run=false --auto-watch=true", "test-ie": "karma start --browsers=IE --single-run=false --auto-watch=true", "dev": "make dev", "build": "make clean build", "lint": "make lint", "flow": "flow"}, "files": ["/build", "/typings", "/web/react-draggable.min.js", "/web/react-draggable.min.js.map"], "typings": "./typings/index.d.ts", "types": "./typings/index.d.ts", "repository": {"type": "git", "url": "https://github.com/react-grid-layout/react-draggable.git"}, "keywords": ["react", "draggable", "react-component"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/react-grid-layout/react-draggable/issues"}, "homepage": "https://github.com/react-grid-layout/react-draggable", "devDependencies": {"@babel/cli": "^7.17.6", "@babel/core": "^7.17.9", "@babel/eslint-parser": "^7.17.0", "@babel/plugin-proposal-class-properties": "^7.16.7", "@babel/plugin-transform-flow-comments": "^7.16.7", "@babel/preset-env": "^7.16.11", "@babel/preset-flow": "^7.16.7", "@babel/preset-react": "^7.16.7", "@types/react": "^17.0.19", "@types/react-dom": "^17.0.9", "assert": "^2.0.0", "babel-loader": "^8.2.5", "babel-plugin-transform-inline-environment-variables": "^0.4.3", "eslint": "^8.14.0", "eslint-plugin-react": "^7.29.4", "flow-bin": "^0.176.3", "jasmine-core": "^4.1.0", "karma": "^6.3.19", "karma-chrome-launcher": "^3.1.1", "karma-cli": "2.0.0", "karma-firefox-launcher": "^2.1.2", "karma-ie-launcher": "^1.0.0", "karma-jasmine": "^5.0.0", "karma-phantomjs-launcher": "^1.0.4", "karma-phantomjs-shim": "^1.5.0", "karma-webpack": "^5.0.0", "lodash": "^4.17.4", "phantomjs-prebuilt": "^2.1.16", "pre-commit": "^1.2.2", "process": "^0.11.10", "puppeteer": "^13.6.0", "react": "^16.13.1", "react-dom": "^16.13.1", "react-frame-component": "^5.2.1", "react-test-renderer": "^16.13.1", "semver": "^7.3.7", "static-server": "^3.0.0", "typescript": "^4.6.3", "webpack": "^5.72.0", "webpack-cli": "^4.9.2", "webpack-dev-server": "^4.8.1"}, "resolutions": {"minimist": "^1.2.5"}, "precommit": ["lint", "test"], "dependencies": {"clsx": "^1.1.1", "prop-types": "^15.8.1"}, "peerDependencies": {"react": ">= 16.3.0", "react-dom": ">= 16.3.0"}}