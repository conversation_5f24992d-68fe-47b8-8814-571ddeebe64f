{"name": "@types/react-dom", "version": "18.0.11", "description": "TypeScript definitions for React (react-dom)", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-dom", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://asana.com"}, {"name": "AssureSign", "url": "http://www.assuresign.com"}, {"name": "Microsoft", "url": "https://microsoft.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/MartynasZilinskas", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/theruther4d", "githubUsername": "theruther4d"}, {"name": "<PERSON>", "url": "https://github.com/Jessidhia", "githubUsername": "Jessidhia"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-dom"}, "scripts": {}, "dependencies": {"@types/react": "*"}, "typesPublisherContentHash": "27673d88deb3e7d51dc603fbb3668e21b0cc902c4ff84b6a84de98fd74ad8ce0", "typeScriptVersion": "4.2", "exports": {".": {"types": {"default": "./index.d.ts"}}, "./client": {"types": {"default": "./client.d.ts"}}, "./next": {"types": {"default": "./next.d.ts"}}, "./server": {"types": {"default": "./server.d.ts"}}, "./experimental": {"types": {"default": "./experimental.d.ts"}}, "./test-utils": {"types": {"default": "./test-utils/index.d.ts"}}, "./package.json": "./package.json"}}