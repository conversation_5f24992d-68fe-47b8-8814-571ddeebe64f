{"version": 3, "file": "engine.io.js", "sources": ["../node_modules/engine.io-parser/build/esm/commons.js", "../node_modules/engine.io-parser/build/esm/encodePacket.browser.js", "../node_modules/@socket.io/base64-arraybuffer/dist/base64-arraybuffer.es5.js", "../node_modules/engine.io-parser/build/esm/decodePacket.browser.js", "../node_modules/engine.io-parser/build/esm/index.js", "../node_modules/@socket.io/component-emitter/index.mjs", "../build/esm/globalThis.browser.js", "../build/esm/util.js", "../build/esm/transport.js", "../build/esm/contrib/yeast.js", "../build/esm/contrib/parseqs.js", "../build/esm/contrib/has-cors.js", "../build/esm/transports/xmlhttprequest.browser.js", "../build/esm/transports/polling.js", "../build/esm/transports/websocket-constructor.browser.js", "../build/esm/transports/websocket.js", "../build/esm/transports/index.js", "../build/esm/contrib/parseuri.js", "../build/esm/socket.js", "../build/esm/browser-entrypoint.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + content);\n    };\n    return fileReader.readAsDataURL(data);\n};\nexport default encodePacket;\n", "/*\n * base64-arraybuffer 1.0.1 <https://github.com/niklasvh/base64-arraybuffer>\n * Copyright (c) 2022 <PERSON><PERSON> <https://hertzen.com>\n * Released under MIT License\n */\nvar chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nvar lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (var i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nvar encode = function (arraybuffer) {\n    var bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nvar decode = function (base64) {\n    var bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    var arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n\nexport { decode, encode };\n//# sourceMappingURL=base64-arraybuffer.es5.js.map\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE } from \"./commons.js\";\nimport { decode } from \"@socket.io/base64-arraybuffer\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType)\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1)\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type]\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            return data instanceof ArrayBuffer ? new Blob([data]) : data;\n        case \"arraybuffer\":\n        default:\n            return data; // assuming the data is already an ArrayBuffer\n    }\n};\nexport default decodePacket;\n", "import encodePacket from \"./encodePacket.js\";\nimport decodePacket from \"./decodePacket.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, encodedPacket => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload };\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\n", "import { globalThisShim as globalThis } from \"./globalThis.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nclass TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n}\n", "// imported from https://github.com/unshiftio/yeast\n'use strict';\nconst alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split(''), length = 64, map = {};\nlet seed = 0, i = 0, prev;\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nexport function encode(num) {\n    let encoded = '';\n    do {\n        encoded = alphabet[num % length] + encoded;\n        num = Math.floor(num / length);\n    } while (num > 0);\n    return encoded;\n}\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nexport function decode(str) {\n    let decoded = 0;\n    for (i = 0; i < str.length; i++) {\n        decoded = decoded * length + map[str.charAt(i)];\n    }\n    return decoded;\n}\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nexport function yeast() {\n    const now = encode(+new Date());\n    if (now !== prev)\n        return seed = 0, prev = now;\n    return now + '.' + encode(seed++);\n}\n//\n// Map each character to its index.\n//\nfor (; i < length; i++)\n    map[alphabet[i]] = i;\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "// browser shim for xmlhttprequest module\nimport { hasCORS } from \"../contrib/has-cors.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nexport function XHR(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { encode } from \"../contrib/parseqs.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nimport { XHR as XMLHttpRequest } from \"./xmlhttprequest.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nfunction empty() { }\nconst hasXHR2 = (function () {\n    const xhr = new XMLHttpRequest({\n        xdomain: false,\n    });\n    return null != xhr.responseType;\n})();\nexport class Polling extends Transport {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        this.polling = false;\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n            this.xs = opts.secure !== isSSL;\n        }\n        /**\n         * XHR supports binary\n         */\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this.poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this.polling || !this.writable) {\n            let total = 0;\n            if (this.polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    poll() {\n        this.polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this.polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this.poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        let query = this.query || {};\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        let port = \"\";\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        // avoid port if default for schema\n        if (this.opts.port &&\n            ((\"https\" === schema && Number(this.opts.port) !== 443) ||\n                (\"http\" === schema && Number(this.opts.port) !== 80))) {\n            port = \":\" + this.opts.port;\n        }\n        const encodedQuery = encode(query);\n        const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n        return (schema +\n            \"://\" +\n            (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n            port +\n            this.opts.path +\n            (encodedQuery.length ? \"?\" + encodedQuery : \"\"));\n    }\n    /**\n     * Creates a request.\n     *\n     * @param {String} method\n     * @private\n     */\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd, xs: this.xs }, this.opts);\n        return new Request(this.uri(), opts);\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(uri, opts) {\n        super();\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.method = opts.method || \"GET\";\n        this.uri = uri;\n        this.async = false !== opts.async;\n        this.data = undefined !== opts.data ? opts.data : null;\n        this.create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    create() {\n        const opts = pick(this.opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this.opts.xd;\n        opts.xscheme = !!this.opts.xs;\n        const xhr = (this.xhr = new XMLHttpRequest(opts));\n        try {\n            xhr.open(this.method, this.uri, this.async);\n            try {\n                if (this.opts.extraHeaders) {\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this.opts.extraHeaders) {\n                        if (this.opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this.method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this.opts.withCredentials;\n            }\n            if (this.opts.requestTimeout) {\n                xhr.timeout = this.opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this.onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this.data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this.onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this.index = Request.requestsCount++;\n            Request.requests[this.index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    onError(err) {\n        this.emitReserved(\"error\", err, this.xhr);\n        this.cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    cleanup(fromError) {\n        if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n            return;\n        }\n        this.xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this.xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this.index];\n        }\n        this.xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    onLoad() {\n        const data = this.xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this.cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this.cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\n", "import { globalThisShim as globalThis } from \"../globalThis.js\";\nexport const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const WebSocket = globalThis.WebSocket || globalThis.MozWebSocket;\nexport const usingBrowserWebSocket = true;\nexport const defaultBinaryType = \"arraybuffer\";\n", "import { Transport } from \"../transport.js\";\nimport { encode } from \"../contrib/parseqs.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { pick } from \"../util.js\";\nimport { defaultBinaryType, nextTick, usingBrowserWebSocket, WebSocket, } from \"./websocket-constructor.js\";\nimport { encodePacket } from \"engine.io-parser\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class WS extends Transport {\n    /**\n     * WebSocket transport constructor.\n     *\n     * @param {Object} opts - connection options\n     * @protected\n     */\n    constructor(opts) {\n        super(opts);\n        this.supportsBinary = !opts.forceBase64;\n    }\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        if (!this.check()) {\n            // let probe timeout\n            return;\n        }\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws =\n                usingBrowserWebSocket && !isReactNative\n                    ? protocols\n                        ? new WebSocket(uri, protocols)\n                        : new WebSocket(uri)\n                    : new WebSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType || defaultBinaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // always create a new object (GH-437)\n                const opts = {};\n                if (!usingBrowserWebSocket) {\n                    if (packet.options) {\n                        opts.compress = packet.options.compress;\n                    }\n                    if (this.opts.perMessageDeflate) {\n                        const len = \n                        // @ts-ignore\n                        \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n                        if (len < this.opts.perMessageDeflate.threshold) {\n                            opts.compress = false;\n                        }\n                    }\n                }\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    if (usingBrowserWebSocket) {\n                        // TypeError is thrown when passing the second argument on Safari\n                        this.ws.send(data);\n                    }\n                    else {\n                        this.ws.send(data, opts);\n                    }\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        let query = this.query || {};\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        let port = \"\";\n        // avoid port if default for schema\n        if (this.opts.port &&\n            ((\"wss\" === schema && Number(this.opts.port) !== 443) ||\n                (\"ws\" === schema && Number(this.opts.port) !== 80))) {\n            port = \":\" + this.opts.port;\n        }\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        const encodedQuery = encode(query);\n        const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n        return (schema +\n            \"://\" +\n            (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n            port +\n            this.opts.path +\n            (encodedQuery.length ? \"?\" + encodedQuery : \"\"));\n    }\n    /**\n     * Feature detection for WebSocket.\n     *\n     * @return {Boolean} whether this transport is available.\n     * @private\n     */\n    check() {\n        return !!WebSocket;\n    }\n}\n", "import { Polling } from \"./polling.js\";\nimport { WS } from \"./websocket.js\";\nexport const transports = {\n    websocket: WS,\n    polling: Polling,\n};\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nexport class Socket extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts = {}) {\n        super();\n        this.writeBuffer = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            uri = parse(uri);\n            opts.hostname = uri.host;\n            opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n            opts.port = uri.port;\n            if (uri.query)\n                opts.query = uri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = opts.transports || [\"polling\", \"websocket\"];\n        this.writeBuffer = [];\n        this.prevBufferLen = 0;\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: true,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        // set on handshake\n        this.id = null;\n        this.upgrades = null;\n        this.pingInterval = null;\n        this.pingTimeout = null;\n        // set on heartbeat\n        this.pingTimeoutTimer = null;\n        if (typeof addEventListener === \"function\") {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this.beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this.offlineEventListener = () => {\n                    this.onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                addEventListener(\"offline\", this.offlineEventListener, false);\n            }\n        }\n        this.open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts.transportOptions[name], this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        });\n        return new transports[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    open() {\n        let transport;\n        if (this.opts.rememberUpgrade &&\n            Socket.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1) {\n            transport = \"websocket\";\n        }\n        else if (0 === this.transports.length) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        else {\n            transport = this.transports[0];\n        }\n        this.readyState = \"opening\";\n        // Retry with the next transport if the transport is disabled (jsonp: false)\n        try {\n            transport = this.createTransport(transport);\n        }\n        catch (e) {\n            this.transports.shift();\n            this.open();\n            return;\n        }\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this.onDrain.bind(this))\n            .on(\"packet\", this.onPacket.bind(this))\n            .on(\"error\", this.onError.bind(this))\n            .on(\"close\", (reason) => this.onClose(\"transport close\", reason));\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        Socket.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        transport.open();\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n        // we check for `readyState` in case an `open`\n        // listener already closed the socket\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            let i = 0;\n            const l = this.upgrades.length;\n            for (; i < l; i++) {\n                this.probe(this.upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this.resetPingTimeout();\n                    this.sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this.onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this.upgrades = this.filterUpgrades(data.upgrades);\n        this.pingInterval = data.pingInterval;\n        this.pingTimeout = data.pingTimeout;\n        this.maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this.resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    resetPingTimeout() {\n        this.clearTimeoutFn(this.pingTimeoutTimer);\n        this.pingTimeoutTimer = this.setTimeoutFn(() => {\n            this.onClose(\"ping timeout\");\n        }, this.pingInterval + this.pingTimeout);\n        if (this.opts.autoUnref) {\n            this.pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    onDrain() {\n        this.writeBuffer.splice(0, this.prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this.prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this.getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this.prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    getWritablePackets() {\n        const shouldCheckPayloadSize = this.maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this.maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    send(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this.onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    onError(err) {\n        Socket.priorWebsocketSuccess = false;\n        this.emitReserved(\"error\", err);\n        this.onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this.pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (typeof removeEventListener === \"function\") {\n                removeEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n                removeEventListener(\"offline\", this.offlineEventListener, false);\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this.prevBufferLen = 0;\n        }\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        let i = 0;\n        const j = upgrades.length;\n        for (; i < j; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\nSocket.protocol = protocol;\n", "import { Socket } from \"./socket.js\";\nexport default (uri, opts) => new Socket(uri, opts);\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "obj", "buffer", "encodePacket", "supportsBinary", "callback", "encodeBlobAsBase64", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "i", "decode", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "packetType", "length", "decoded", "base64", "SEPARATOR", "String", "fromCharCode", "encodePayload", "packets", "encodedPackets", "Array", "count", "packet", "join", "decodePayload", "encodedPayload", "decodedPacket", "push", "protocol", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "_callbacks", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "callbacks", "cb", "splice", "emit", "args", "slice", "len", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "globalThisShim", "self", "window", "Function", "pick", "attr", "reduce", "acc", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "globalThis", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "setTimeoutFn", "bind", "clearTimeoutFn", "BASE64_OVERHEAD", "byteLength", "utf8Length", "Math", "ceil", "size", "str", "c", "l", "charCodeAt", "TransportError", "reason", "description", "context", "Error", "Transport", "writable", "query", "socket", "readyState", "doOpen", "doClose", "onClose", "write", "onPacket", "details", "onPause", "alphabet", "map", "seed", "prev", "encode", "num", "encoded", "floor", "yeast", "now", "Date", "encodeURIComponent", "qs", "qry", "pairs", "pair", "decodeURIComponent", "value", "XMLHttpRequest", "err", "hasCORS", "XHR", "xdomain", "e", "concat", "empty", "hasXHR2", "xhr", "responseType", "Polling", "polling", "location", "isSSL", "port", "xd", "hostname", "xs", "secure", "forceBase64", "poll", "pause", "total", "doPoll", "onOpen", "close", "doWrite", "schema", "timestampRequests", "timestampParam", "sid", "b64", "Number", "<PERSON><PERSON><PERSON><PERSON>", "ipv6", "indexOf", "path", "Request", "uri", "req", "request", "method", "xhrStatus", "onError", "onData", "pollXhr", "async", "undefined", "xscheme", "open", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "withCredentials", "requestTimeout", "timeout", "onreadystatechange", "status", "onLoad", "send", "document", "index", "requestsCount", "requests", "cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "terminationEvent", "nextTick", "isPromiseAvailable", "Promise", "resolve", "then", "WebSocket", "MozWebSocket", "usingBrowserWebSocket", "defaultBinaryType", "isReactNative", "navigator", "product", "toLowerCase", "WS", "check", "protocols", "headers", "ws", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "lastPacket", "transports", "websocket", "re", "parts", "parse", "src", "b", "replace", "m", "exec", "source", "host", "authority", "ipv6uri", "pathNames", "query<PERSON><PERSON>", "regx", "names", "$0", "$1", "$2", "Socket", "writeBuffer", "prevBufferLen", "agent", "upgrade", "rememberUpgrade", "addTrailingSlash", "rejectUnauthorized", "perMessageDeflate", "threshold", "transportOptions", "closeOnBeforeunload", "id", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "beforeunloadEventListener", "transport", "offlineEventListener", "name", "EIO", "priorWebsocketSuccess", "createTransport", "shift", "setTransport", "onDrain", "failed", "onTransportOpen", "msg", "upgrading", "flush", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "probe", "onHandshake", "JSON", "resetPingTimeout", "sendPacket", "code", "filterUpgrades", "maxPayload", "getWritablePackets", "shouldCheckPayloadSize", "payloadSize", "options", "compress", "cleanupAndClose", "waitForUpgrade", "filteredUpgrades", "j"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA,IAAMA,YAAY,GAAGC,MAAM,CAACC,MAAP,CAAc,IAAd,CAArB;;EACAF,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB;EACAA,YAAY,CAAC,OAAD,CAAZ,GAAwB,GAAxB;EACAA,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB;EACAA,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB;EACAA,YAAY,CAAC,SAAD,CAAZ,GAA0B,GAA1B;EACAA,YAAY,CAAC,SAAD,CAAZ,GAA0B,GAA1B;EACAA,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB;EACA,IAAMG,oBAAoB,GAAGF,MAAM,CAACC,MAAP,CAAc,IAAd,CAA7B;EACAD,MAAM,CAACG,IAAP,CAAYJ,YAAZ,EAA0BK,OAA1B,CAAkC,UAAAC,GAAG,EAAI;EACrCH,EAAAA,oBAAoB,CAACH,YAAY,CAACM,GAAD,CAAb,CAApB,GAA0CA,GAA1C;EACH,CAFD;EAGA,IAAMC,YAAY,GAAG;EAAEC,EAAAA,IAAI,EAAE,OAAR;EAAiBC,EAAAA,IAAI,EAAE;EAAvB,CAArB;;ECXA,IAAMC,cAAc,GAAG,OAAOC,IAAP,KAAgB,UAAhB,IAClB,OAAOA,IAAP,KAAgB,WAAhB,IACGV,MAAM,CAACW,SAAP,CAAiBC,QAAjB,CAA0BC,IAA1B,CAA+BH,IAA/B,MAAyC,0BAFjD;EAGA,IAAMI,uBAAqB,GAAG,OAAOC,WAAP,KAAuB,UAArD;;EAEA,IAAMC,MAAM,GAAG,SAATA,MAAS,CAAAC,GAAG,EAAI;EAClB,SAAO,OAAOF,WAAW,CAACC,MAAnB,KAA8B,UAA9B,GACDD,WAAW,CAACC,MAAZ,CAAmBC,GAAnB,CADC,GAEDA,GAAG,IAAIA,GAAG,CAACC,MAAJ,YAAsBH,WAFnC;EAGH,CAJD;;EAKA,IAAMI,YAAY,GAAG,SAAfA,YAAe,OAAiBC,cAAjB,EAAiCC,QAAjC,EAA8C;EAAA,MAA3Cd,IAA2C,QAA3CA,IAA2C;EAAA,MAArCC,IAAqC,QAArCA,IAAqC;;EAC/D,MAAIC,cAAc,IAAID,IAAI,YAAYE,IAAtC,EAA4C;EACxC,QAAIU,cAAJ,EAAoB;EAChB,aAAOC,QAAQ,CAACb,IAAD,CAAf;EACH,KAFD,MAGK;EACD,aAAOc,kBAAkB,CAACd,IAAD,EAAOa,QAAP,CAAzB;EACH;EACJ,GAPD,MAQK,IAAIP,uBAAqB,KACzBN,IAAI,YAAYO,WAAhB,IAA+BC,MAAM,CAACR,IAAD,CADZ,CAAzB,EAC8C;EAC/C,QAAIY,cAAJ,EAAoB;EAChB,aAAOC,QAAQ,CAACb,IAAD,CAAf;EACH,KAFD,MAGK;EACD,aAAOc,kBAAkB,CAAC,IAAIZ,IAAJ,CAAS,CAACF,IAAD,CAAT,CAAD,EAAmBa,QAAnB,CAAzB;EACH;EACJ,GAjB8D;;;EAmB/D,SAAOA,QAAQ,CAACtB,YAAY,CAACQ,IAAD,CAAZ,IAAsBC,IAAI,IAAI,EAA9B,CAAD,CAAf;EACH,CApBD;;EAqBA,IAAMc,kBAAkB,GAAG,SAArBA,kBAAqB,CAACd,IAAD,EAAOa,QAAP,EAAoB;EAC3C,MAAME,UAAU,GAAG,IAAIC,UAAJ,EAAnB;;EACAD,EAAAA,UAAU,CAACE,MAAX,GAAoB,YAAY;EAC5B,QAAMC,OAAO,GAAGH,UAAU,CAACI,MAAX,CAAkBC,KAAlB,CAAwB,GAAxB,EAA6B,CAA7B,CAAhB;EACAP,IAAAA,QAAQ,CAAC,MAAMK,OAAP,CAAR;EACH,GAHD;;EAIA,SAAOH,UAAU,CAACM,aAAX,CAAyBrB,IAAzB,CAAP;EACH,CAPD;;;;;;;EChCA,IAAM,KAAK,GAAG,kEAAd;;EAGA,IAAM,MAAM,GAAG,OAAO,UAAP,KAAsB,WAAtB,GAAoC,EAApC,GAAyC,IAAI,UAAJ,CAAe,GAAf,CAAxD;;EACA,KAAK,IAAIsB,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAG,KAAK,CAAC,MAA1B,EAAkCA,GAAC,EAAnC,EAAuC;EACnC,EAAA,MAAM,CAAC,KAAK,CAAC,UAAN,CAAiBA,GAAjB,CAAD,CAAN,GAA8BA,GAA9B;EACH;;MAwBYC,QAAM,GAAG,SAAT,MAAS,CAAC,MAAD,EAAe;EACjC,MAAI,YAAY,GAAG,MAAM,CAAC,MAAP,GAAgB,IAAnC;EAAA,MACI,GAAG,GAAG,MAAM,CAAC,MADjB;EAAA,MAEI,CAFJ;EAAA,MAGI,CAAC,GAAG,CAHR;EAAA,MAII,QAJJ;EAAA,MAKI,QALJ;EAAA,MAMI,QANJ;EAAA,MAOI,QAPJ;;EASA,MAAI,MAAM,CAAC,MAAM,CAAC,MAAP,GAAgB,CAAjB,CAAN,KAA8B,GAAlC,EAAuC;EACnC,IAAA,YAAY;;EACZ,QAAI,MAAM,CAAC,MAAM,CAAC,MAAP,GAAgB,CAAjB,CAAN,KAA8B,GAAlC,EAAuC;EACnC,MAAA,YAAY;EACf;EACJ;;EAED,MAAM,WAAW,GAAG,IAAI,WAAJ,CAAgB,YAAhB,CAApB;EAAA,MACI,KAAK,GAAG,IAAI,UAAJ,CAAe,WAAf,CADZ;;EAGA,OAAK,CAAC,GAAG,CAAT,EAAY,CAAC,GAAG,GAAhB,EAAqB,CAAC,IAAI,CAA1B,EAA6B;EACzB,IAAA,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,UAAP,CAAkB,CAAlB,CAAD,CAAjB;EACA,IAAA,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,UAAP,CAAkB,CAAC,GAAG,CAAtB,CAAD,CAAjB;EACA,IAAA,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,UAAP,CAAkB,CAAC,GAAG,CAAtB,CAAD,CAAjB;EACA,IAAA,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,UAAP,CAAkB,CAAC,GAAG,CAAtB,CAAD,CAAjB;EAEA,IAAA,KAAK,CAAC,CAAC,EAAF,CAAL,GAAc,QAAQ,IAAI,CAAb,GAAmB,QAAQ,IAAI,CAA5C;EACA,IAAA,KAAK,CAAC,CAAC,EAAF,CAAL,GAAc,CAAC,QAAQ,GAAG,EAAZ,KAAmB,CAApB,GAA0B,QAAQ,IAAI,CAAnD;EACA,IAAA,KAAK,CAAC,CAAC,EAAF,CAAL,GAAc,CAAC,QAAQ,GAAG,CAAZ,KAAkB,CAAnB,GAAyB,QAAQ,GAAG,EAAjD;EACH;;EAED,SAAO,WAAP;EACJ;;EC5DA,IAAMjB,qBAAqB,GAAG,OAAOC,WAAP,KAAuB,UAArD;;EACA,IAAMiB,YAAY,GAAG,SAAfA,YAAe,CAACC,aAAD,EAAgBC,UAAhB,EAA+B;EAChD,MAAI,OAAOD,aAAP,KAAyB,QAA7B,EAAuC;EACnC,WAAO;EACH1B,MAAAA,IAAI,EAAE,SADH;EAEHC,MAAAA,IAAI,EAAE2B,SAAS,CAACF,aAAD,EAAgBC,UAAhB;EAFZ,KAAP;EAIH;;EACD,MAAM3B,IAAI,GAAG0B,aAAa,CAACG,MAAd,CAAqB,CAArB,CAAb;;EACA,MAAI7B,IAAI,KAAK,GAAb,EAAkB;EACd,WAAO;EACHA,MAAAA,IAAI,EAAE,SADH;EAEHC,MAAAA,IAAI,EAAE6B,kBAAkB,CAACJ,aAAa,CAACK,SAAd,CAAwB,CAAxB,CAAD,EAA6BJ,UAA7B;EAFrB,KAAP;EAIH;;EACD,MAAMK,UAAU,GAAGrC,oBAAoB,CAACK,IAAD,CAAvC;;EACA,MAAI,CAACgC,UAAL,EAAiB;EACb,WAAOjC,YAAP;EACH;;EACD,SAAO2B,aAAa,CAACO,MAAd,GAAuB,CAAvB,GACD;EACEjC,IAAAA,IAAI,EAAEL,oBAAoB,CAACK,IAAD,CAD5B;EAEEC,IAAAA,IAAI,EAAEyB,aAAa,CAACK,SAAd,CAAwB,CAAxB;EAFR,GADC,GAKD;EACE/B,IAAAA,IAAI,EAAEL,oBAAoB,CAACK,IAAD;EAD5B,GALN;EAQH,CA1BD;;EA2BA,IAAM8B,kBAAkB,GAAG,SAArBA,kBAAqB,CAAC7B,IAAD,EAAO0B,UAAP,EAAsB;EAC7C,MAAIpB,qBAAJ,EAA2B;EACvB,QAAM2B,OAAO,GAAGV,QAAM,CAACvB,IAAD,CAAtB;EACA,WAAO2B,SAAS,CAACM,OAAD,EAAUP,UAAV,CAAhB;EACH,GAHD,MAIK;EACD,WAAO;EAAEQ,MAAAA,MAAM,EAAE,IAAV;EAAgBlC,MAAAA,IAAI,EAAJA;EAAhB,KAAP,CADC;EAEJ;EACJ,CARD;;EASA,IAAM2B,SAAS,GAAG,SAAZA,SAAY,CAAC3B,IAAD,EAAO0B,UAAP,EAAsB;EACpC,UAAQA,UAAR;EACI,SAAK,MAAL;EACI,aAAO1B,IAAI,YAAYO,WAAhB,GAA8B,IAAIL,IAAJ,CAAS,CAACF,IAAD,CAAT,CAA9B,GAAiDA,IAAxD;;EACJ,SAAK,aAAL;EACA;EACI,aAAOA,IAAP;EAAa;EALrB;EAOH,CARD;;ECrCA,IAAMmC,SAAS,GAAGC,MAAM,CAACC,YAAP,CAAoB,EAApB,CAAlB;;EACA,IAAMC,aAAa,GAAG,SAAhBA,aAAgB,CAACC,OAAD,EAAU1B,QAAV,EAAuB;EACzC;EACA,MAAMmB,MAAM,GAAGO,OAAO,CAACP,MAAvB;EACA,MAAMQ,cAAc,GAAG,IAAIC,KAAJ,CAAUT,MAAV,CAAvB;EACA,MAAIU,KAAK,GAAG,CAAZ;EACAH,EAAAA,OAAO,CAAC3C,OAAR,CAAgB,UAAC+C,MAAD,EAASrB,CAAT,EAAe;EAC3B;EACAX,IAAAA,YAAY,CAACgC,MAAD,EAAS,KAAT,EAAgB,UAAAlB,aAAa,EAAI;EACzCe,MAAAA,cAAc,CAAClB,CAAD,CAAd,GAAoBG,aAApB;;EACA,UAAI,EAAEiB,KAAF,KAAYV,MAAhB,EAAwB;EACpBnB,QAAAA,QAAQ,CAAC2B,cAAc,CAACI,IAAf,CAAoBT,SAApB,CAAD,CAAR;EACH;EACJ,KALW,CAAZ;EAMH,GARD;EASH,CAdD;;EAeA,IAAMU,aAAa,GAAG,SAAhBA,aAAgB,CAACC,cAAD,EAAiBpB,UAAjB,EAAgC;EAClD,MAAMc,cAAc,GAAGM,cAAc,CAAC1B,KAAf,CAAqBe,SAArB,CAAvB;EACA,MAAMI,OAAO,GAAG,EAAhB;;EACA,OAAK,IAAIjB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkB,cAAc,CAACR,MAAnC,EAA2CV,CAAC,EAA5C,EAAgD;EAC5C,QAAMyB,aAAa,GAAGvB,YAAY,CAACgB,cAAc,CAAClB,CAAD,CAAf,EAAoBI,UAApB,CAAlC;EACAa,IAAAA,OAAO,CAACS,IAAR,CAAaD,aAAb;;EACA,QAAIA,aAAa,CAAChD,IAAd,KAAuB,OAA3B,EAAoC;EAChC;EACH;EACJ;;EACD,SAAOwC,OAAP;EACH,CAXD;;EAYO,IAAMU,QAAQ,GAAG,CAAjB;;EC9BP;EACA;EACA;EACA;EACA;EAEO,SAASC,OAAT,CAAiBzC,GAAjB,EAAsB;EAC3B,MAAIA,GAAJ,EAAS,OAAO0C,KAAK,CAAC1C,GAAD,CAAZ;EACV;EAED;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,SAAS0C,KAAT,CAAe1C,GAAf,EAAoB;EAClB,OAAK,IAAIZ,GAAT,IAAgBqD,OAAO,CAAC/C,SAAxB,EAAmC;EACjCM,IAAAA,GAAG,CAACZ,GAAD,CAAH,GAAWqD,OAAO,CAAC/C,SAAR,CAAkBN,GAAlB,CAAX;EACD;;EACD,SAAOY,GAAP;EACD;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EAEAyC,OAAO,CAAC/C,SAAR,CAAkBiD,EAAlB,GACAF,OAAO,CAAC/C,SAAR,CAAkBkD,gBAAlB,GAAqC,UAASC,KAAT,EAAgBC,EAAhB,EAAmB;EACtD,OAAKC,UAAL,GAAkB,KAAKA,UAAL,IAAmB,EAArC;EACA,GAAC,KAAKA,UAAL,CAAgB,MAAMF,KAAtB,IAA+B,KAAKE,UAAL,CAAgB,MAAMF,KAAtB,KAAgC,EAAhE,EACGN,IADH,CACQO,EADR;EAEA,SAAO,IAAP;EACD,CAND;EAQA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EAEAL,OAAO,CAAC/C,SAAR,CAAkBsD,IAAlB,GAAyB,UAASH,KAAT,EAAgBC,EAAhB,EAAmB;EAC1C,WAASH,EAAT,GAAc;EACZ,SAAKM,GAAL,CAASJ,KAAT,EAAgBF,EAAhB;EACAG,IAAAA,EAAE,CAACI,KAAH,CAAS,IAAT,EAAeC,SAAf;EACD;;EAEDR,EAAAA,EAAE,CAACG,EAAH,GAAQA,EAAR;EACA,OAAKH,EAAL,CAAQE,KAAR,EAAeF,EAAf;EACA,SAAO,IAAP;EACD,CATD;EAWA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EAEAF,OAAO,CAAC/C,SAAR,CAAkBuD,GAAlB,GACAR,OAAO,CAAC/C,SAAR,CAAkB0D,cAAlB,GACAX,OAAO,CAAC/C,SAAR,CAAkB2D,kBAAlB,GACAZ,OAAO,CAAC/C,SAAR,CAAkB4D,mBAAlB,GAAwC,UAAST,KAAT,EAAgBC,EAAhB,EAAmB;EACzD,OAAKC,UAAL,GAAkB,KAAKA,UAAL,IAAmB,EAArC,CADyD;;EAIzD,MAAI,KAAKI,SAAS,CAAC5B,MAAnB,EAA2B;EACzB,SAAKwB,UAAL,GAAkB,EAAlB;EACA,WAAO,IAAP;EACD,GAPwD;;;EAUzD,MAAIQ,SAAS,GAAG,KAAKR,UAAL,CAAgB,MAAMF,KAAtB,CAAhB;EACA,MAAI,CAACU,SAAL,EAAgB,OAAO,IAAP,CAXyC;;EAczD,MAAI,KAAKJ,SAAS,CAAC5B,MAAnB,EAA2B;EACzB,WAAO,KAAKwB,UAAL,CAAgB,MAAMF,KAAtB,CAAP;EACA,WAAO,IAAP;EACD,GAjBwD;;;EAoBzD,MAAIW,EAAJ;;EACA,OAAK,IAAI3C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0C,SAAS,CAAChC,MAA9B,EAAsCV,CAAC,EAAvC,EAA2C;EACzC2C,IAAAA,EAAE,GAAGD,SAAS,CAAC1C,CAAD,CAAd;;EACA,QAAI2C,EAAE,KAAKV,EAAP,IAAaU,EAAE,CAACV,EAAH,KAAUA,EAA3B,EAA+B;EAC7BS,MAAAA,SAAS,CAACE,MAAV,CAAiB5C,CAAjB,EAAoB,CAApB;EACA;EACD;EACF,GA3BwD;EA8BzD;;;EACA,MAAI0C,SAAS,CAAChC,MAAV,KAAqB,CAAzB,EAA4B;EAC1B,WAAO,KAAKwB,UAAL,CAAgB,MAAMF,KAAtB,CAAP;EACD;;EAED,SAAO,IAAP;EACD,CAvCD;EAyCA;EACA;EACA;EACA;EACA;EACA;EACA;;;EAEAJ,OAAO,CAAC/C,SAAR,CAAkBgE,IAAlB,GAAyB,UAASb,KAAT,EAAe;EACtC,OAAKE,UAAL,GAAkB,KAAKA,UAAL,IAAmB,EAArC;EAEA,MAAIY,IAAI,GAAG,IAAI3B,KAAJ,CAAUmB,SAAS,CAAC5B,MAAV,GAAmB,CAA7B,CAAX;EAAA,MACIgC,SAAS,GAAG,KAAKR,UAAL,CAAgB,MAAMF,KAAtB,CADhB;;EAGA,OAAK,IAAIhC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGsC,SAAS,CAAC5B,MAA9B,EAAsCV,CAAC,EAAvC,EAA2C;EACzC8C,IAAAA,IAAI,CAAC9C,CAAC,GAAG,CAAL,CAAJ,GAAcsC,SAAS,CAACtC,CAAD,CAAvB;EACD;;EAED,MAAI0C,SAAJ,EAAe;EACbA,IAAAA,SAAS,GAAGA,SAAS,CAACK,KAAV,CAAgB,CAAhB,CAAZ;;EACA,SAAK,IAAI/C,CAAC,GAAG,CAAR,EAAWgD,GAAG,GAAGN,SAAS,CAAChC,MAAhC,EAAwCV,CAAC,GAAGgD,GAA5C,EAAiD,EAAEhD,CAAnD,EAAsD;EACpD0C,MAAAA,SAAS,CAAC1C,CAAD,CAAT,CAAaqC,KAAb,CAAmB,IAAnB,EAAyBS,IAAzB;EACD;EACF;;EAED,SAAO,IAAP;EACD,CAlBD;;;EAqBAlB,OAAO,CAAC/C,SAAR,CAAkBoE,YAAlB,GAAiCrB,OAAO,CAAC/C,SAAR,CAAkBgE,IAAnD;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAjB,OAAO,CAAC/C,SAAR,CAAkBqE,SAAlB,GAA8B,UAASlB,KAAT,EAAe;EAC3C,OAAKE,UAAL,GAAkB,KAAKA,UAAL,IAAmB,EAArC;EACA,SAAO,KAAKA,UAAL,CAAgB,MAAMF,KAAtB,KAAgC,EAAvC;EACD,CAHD;EAKA;EACA;EACA;EACA;EACA;EACA;EACA;;;EAEAJ,OAAO,CAAC/C,SAAR,CAAkBsE,YAAlB,GAAiC,UAASnB,KAAT,EAAe;EAC9C,SAAO,CAAC,CAAE,KAAKkB,SAAL,CAAelB,KAAf,EAAsBtB,MAAhC;EACD,CAFD;;ECtKO,IAAM0C,cAAc,GAAI,YAAM;EACjC,MAAI,OAAOC,IAAP,KAAgB,WAApB,EAAiC;EAC7B,WAAOA,IAAP;EACH,GAFD,MAGK,IAAI,OAAOC,MAAP,KAAkB,WAAtB,EAAmC;EACpC,WAAOA,MAAP;EACH,GAFI,MAGA;EACD,WAAOC,QAAQ,CAAC,aAAD,CAAR,EAAP;EACH;EACJ,CAV6B,EAAvB;;ECCA,SAASC,IAAT,CAAcrE,GAAd,EAA4B;EAAA,oCAANsE,IAAM;EAANA,IAAAA,IAAM;EAAA;;EAC/B,SAAOA,IAAI,CAACC,MAAL,CAAY,UAACC,GAAD,EAAMC,CAAN,EAAY;EAC3B,QAAIzE,GAAG,CAAC0E,cAAJ,CAAmBD,CAAnB,CAAJ,EAA2B;EACvBD,MAAAA,GAAG,CAACC,CAAD,CAAH,GAASzE,GAAG,CAACyE,CAAD,CAAZ;EACH;;EACD,WAAOD,GAAP;EACH,GALM,EAKJ,EALI,CAAP;EAMH;;EAED,IAAMG,kBAAkB,GAAGC,cAAU,CAACC,UAAtC;EACA,IAAMC,oBAAoB,GAAGF,cAAU,CAACG,YAAxC;EACO,SAASC,qBAAT,CAA+BhF,GAA/B,EAAoCiF,IAApC,EAA0C;EAC7C,MAAIA,IAAI,CAACC,eAAT,EAA0B;EACtBlF,IAAAA,GAAG,CAACmF,YAAJ,GAAmBR,kBAAkB,CAACS,IAAnB,CAAwBR,cAAxB,CAAnB;EACA5E,IAAAA,GAAG,CAACqF,cAAJ,GAAqBP,oBAAoB,CAACM,IAArB,CAA0BR,cAA1B,CAArB;EACH,GAHD,MAIK;EACD5E,IAAAA,GAAG,CAACmF,YAAJ,GAAmBP,cAAU,CAACC,UAAX,CAAsBO,IAAtB,CAA2BR,cAA3B,CAAnB;EACA5E,IAAAA,GAAG,CAACqF,cAAJ,GAAqBT,cAAU,CAACG,YAAX,CAAwBK,IAAxB,CAA6BR,cAA7B,CAArB;EACH;EACJ;;EAED,IAAMU,eAAe,GAAG,IAAxB;;EAEO,SAASC,UAAT,CAAoBvF,GAApB,EAAyB;EAC5B,MAAI,OAAOA,GAAP,KAAe,QAAnB,EAA6B;EACzB,WAAOwF,UAAU,CAACxF,GAAD,CAAjB;EACH,GAH2B;;;EAK5B,SAAOyF,IAAI,CAACC,IAAL,CAAU,CAAC1F,GAAG,CAACuF,UAAJ,IAAkBvF,GAAG,CAAC2F,IAAvB,IAA+BL,eAAzC,CAAP;EACH;;EACD,SAASE,UAAT,CAAoBI,GAApB,EAAyB;EACrB,MAAIC,CAAC,GAAG,CAAR;EAAA,MAAWtE,MAAM,GAAG,CAApB;;EACA,OAAK,IAAIV,CAAC,GAAG,CAAR,EAAWiF,CAAC,GAAGF,GAAG,CAACrE,MAAxB,EAAgCV,CAAC,GAAGiF,CAApC,EAAuCjF,CAAC,EAAxC,EAA4C;EACxCgF,IAAAA,CAAC,GAAGD,GAAG,CAACG,UAAJ,CAAelF,CAAf,CAAJ;;EACA,QAAIgF,CAAC,GAAG,IAAR,EAAc;EACVtE,MAAAA,MAAM,IAAI,CAAV;EACH,KAFD,MAGK,IAAIsE,CAAC,GAAG,KAAR,EAAe;EAChBtE,MAAAA,MAAM,IAAI,CAAV;EACH,KAFI,MAGA,IAAIsE,CAAC,GAAG,MAAJ,IAAcA,CAAC,IAAI,MAAvB,EAA+B;EAChCtE,MAAAA,MAAM,IAAI,CAAV;EACH,KAFI,MAGA;EACDV,MAAAA,CAAC;EACDU,MAAAA,MAAM,IAAI,CAAV;EACH;EACJ;;EACD,SAAOA,MAAP;EACH;;MChDKyE;;;;;EACF,0BAAYC,MAAZ,EAAoBC,WAApB,EAAiCC,OAAjC,EAA0C;EAAA;;EAAA;;EACtC,8BAAMF,MAAN;EACA,UAAKC,WAAL,GAAmBA,WAAnB;EACA,UAAKC,OAAL,GAAeA,OAAf;EACA,UAAK7G,IAAL,GAAY,gBAAZ;EAJsC;EAKzC;;;mCANwB8G;;MAQhBC,SAAb;EAAA;;EAAA;;EACI;EACJ;EACA;EACA;EACA;EACA;EACI,qBAAYpB,IAAZ,EAAkB;EAAA;;EAAA;;EACd;EACA,WAAKqB,QAAL,GAAgB,KAAhB;EACAtB,IAAAA,qBAAqB,iCAAOC,IAAP,CAArB;EACA,WAAKA,IAAL,GAAYA,IAAZ;EACA,WAAKsB,KAAL,GAAatB,IAAI,CAACsB,KAAlB;EACA,WAAKC,MAAL,GAAcvB,IAAI,CAACuB,MAAnB;EANc;EAOjB;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EAvBA;EAAA;EAAA,4BAwBYP,MAxBZ,EAwBoBC,WAxBpB,EAwBiCC,OAxBjC,EAwB0C;EAClC,kFAAmB,OAAnB,EAA4B,IAAIH,cAAJ,CAAmBC,MAAnB,EAA2BC,WAA3B,EAAwCC,OAAxC,CAA5B;;EACA,aAAO,IAAP;EACH;EACD;EACJ;EACA;;EA9BA;EAAA;EAAA,2BA+BW;EACH,WAAKM,UAAL,GAAkB,SAAlB;EACA,WAAKC,MAAL;EACA,aAAO,IAAP;EACH;EACD;EACJ;EACA;;EAtCA;EAAA;EAAA,4BAuCY;EACJ,UAAI,KAAKD,UAAL,KAAoB,SAApB,IAAiC,KAAKA,UAAL,KAAoB,MAAzD,EAAiE;EAC7D,aAAKE,OAAL;EACA,aAAKC,OAAL;EACH;;EACD,aAAO,IAAP;EACH;EACD;EACJ;EACA;EACA;EACA;;EAlDA;EAAA;EAAA,yBAmDS9E,OAnDT,EAmDkB;EACV,UAAI,KAAK2E,UAAL,KAAoB,MAAxB,EAAgC;EAC5B,aAAKI,KAAL,CAAW/E,OAAX;EACH;EAIJ;EACD;EACJ;EACA;EACA;EACA;;EA/DA;EAAA;EAAA,6BAgEa;EACL,WAAK2E,UAAL,GAAkB,MAAlB;EACA,WAAKH,QAAL,GAAgB,IAAhB;;EACA,kFAAmB,MAAnB;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;EA1EA;EAAA;EAAA,2BA2EW/G,IA3EX,EA2EiB;EACT,UAAM2C,MAAM,GAAGnB,YAAY,CAACxB,IAAD,EAAO,KAAKiH,MAAL,CAAYvF,UAAnB,CAA3B;EACA,WAAK6F,QAAL,CAAc5E,MAAd;EACH;EACD;EACJ;EACA;EACA;EACA;;EAnFA;EAAA;EAAA,6BAoFaA,MApFb,EAoFqB;EACb,kFAAmB,QAAnB,EAA6BA,MAA7B;EACH;EACD;EACJ;EACA;EACA;EACA;;EA3FA;EAAA;EAAA,4BA4FY6E,OA5FZ,EA4FqB;EACb,WAAKN,UAAL,GAAkB,QAAlB;;EACA,kFAAmB,OAAnB,EAA4BM,OAA5B;EACH;EACD;EACJ;EACA;EACA;EACA;;EApGA;EAAA;EAAA,0BAqGUC,OArGV,EAqGmB;EArGnB;;EAAA;EAAA,EAA+BvE,OAA/B;;ECXA;;EAEA,IAAMwE,QAAQ,GAAG,mEAAmEtG,KAAnE,CAAyE,EAAzE,CAAjB;EAAA,IAA+FY,MAAM,GAAG,EAAxG;EAAA,IAA4G2F,GAAG,GAAG,EAAlH;EACA,IAAIC,IAAI,GAAG,CAAX;EAAA,IAActG,CAAC,GAAG,CAAlB;EAAA,IAAqBuG,IAArB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASC,QAAT,CAAgBC,GAAhB,EAAqB;EACxB,MAAIC,OAAO,GAAG,EAAd;;EACA,KAAG;EACCA,IAAAA,OAAO,GAAGN,QAAQ,CAACK,GAAG,GAAG/F,MAAP,CAAR,GAAyBgG,OAAnC;EACAD,IAAAA,GAAG,GAAG7B,IAAI,CAAC+B,KAAL,CAAWF,GAAG,GAAG/F,MAAjB,CAAN;EACH,GAHD,QAGS+F,GAAG,GAAG,CAHf;;EAIA,SAAOC,OAAP;EACH;EAeD;EACA;EACA;EACA;EACA;EACA;;EACO,SAASE,KAAT,GAAiB;EACpB,MAAMC,GAAG,GAAGL,QAAM,CAAC,CAAC,IAAIM,IAAJ,EAAF,CAAlB;EACA,MAAID,GAAG,KAAKN,IAAZ,EACI,OAAOD,IAAI,GAAG,CAAP,EAAUC,IAAI,GAAGM,GAAxB;EACJ,SAAOA,GAAG,GAAG,GAAN,GAAYL,QAAM,CAACF,IAAI,EAAL,CAAzB;EACH;EAED;EACA;;EACA,OAAOtG,CAAC,GAAGU,MAAX,EAAmBV,CAAC,EAApB;EACIqG,EAAAA,GAAG,CAACD,QAAQ,CAACpG,CAAD,CAAT,CAAH,GAAmBA,CAAnB;EADJ;;EChDA;;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASwG,MAAT,CAAgBrH,GAAhB,EAAqB;EACxB,MAAI4F,GAAG,GAAG,EAAV;;EACA,OAAK,IAAI/E,CAAT,IAAcb,GAAd,EAAmB;EACf,QAAIA,GAAG,CAAC0E,cAAJ,CAAmB7D,CAAnB,CAAJ,EAA2B;EACvB,UAAI+E,GAAG,CAACrE,MAAR,EACIqE,GAAG,IAAI,GAAP;EACJA,MAAAA,GAAG,IAAIgC,kBAAkB,CAAC/G,CAAD,CAAlB,GAAwB,GAAxB,GAA8B+G,kBAAkB,CAAC5H,GAAG,CAACa,CAAD,CAAJ,CAAvD;EACH;EACJ;;EACD,SAAO+E,GAAP;EACH;EACD;EACA;EACA;EACA;EACA;EACA;;EACO,SAAS9E,MAAT,CAAgB+G,EAAhB,EAAoB;EACvB,MAAIC,GAAG,GAAG,EAAV;EACA,MAAIC,KAAK,GAAGF,EAAE,CAAClH,KAAH,CAAS,GAAT,CAAZ;;EACA,OAAK,IAAIE,CAAC,GAAG,CAAR,EAAWiF,CAAC,GAAGiC,KAAK,CAACxG,MAA1B,EAAkCV,CAAC,GAAGiF,CAAtC,EAAyCjF,CAAC,EAA1C,EAA8C;EAC1C,QAAImH,IAAI,GAAGD,KAAK,CAAClH,CAAD,CAAL,CAASF,KAAT,CAAe,GAAf,CAAX;EACAmH,IAAAA,GAAG,CAACG,kBAAkB,CAACD,IAAI,CAAC,CAAD,CAAL,CAAnB,CAAH,GAAmCC,kBAAkB,CAACD,IAAI,CAAC,CAAD,CAAL,CAArD;EACH;;EACD,SAAOF,GAAP;EACH;;ECjCD;EACA,IAAII,KAAK,GAAG,KAAZ;;EACA,IAAI;EACAA,EAAAA,KAAK,GAAG,OAAOC,cAAP,KAA0B,WAA1B,IACJ,qBAAqB,IAAIA,cAAJ,EADzB;EAEH,CAHD,CAIA,OAAOC,GAAP,EAAY;EAER;EACH;;EACM,IAAMC,OAAO,GAAGH,KAAhB;;ECVP;EAGO,SAASI,GAAT,CAAarD,IAAb,EAAmB;EACtB,MAAMsD,OAAO,GAAGtD,IAAI,CAACsD,OAArB,CADsB;;EAGtB,MAAI;EACA,QAAI,gBAAgB,OAAOJ,cAAvB,KAA0C,CAACI,OAAD,IAAYF,OAAtD,CAAJ,EAAoE;EAChE,aAAO,IAAIF,cAAJ,EAAP;EACH;EACJ,GAJD,CAKA,OAAOK,CAAP,EAAU;;EACV,MAAI,CAACD,OAAL,EAAc;EACV,QAAI;EACA,aAAO,IAAI3D,cAAU,CAAC,CAAC,QAAD,EAAW6D,MAAX,CAAkB,QAAlB,EAA4BtG,IAA5B,CAAiC,GAAjC,CAAD,CAAd,CAAsD,mBAAtD,CAAP;EACH,KAFD,CAGA,OAAOqG,CAAP,EAAU;EACb;EACJ;;ECVD,SAASE,KAAT,GAAiB;;EACjB,IAAMC,OAAO,GAAI,YAAY;EACzB,MAAMC,GAAG,GAAG,IAAIT,GAAJ,CAAmB;EAC3BI,IAAAA,OAAO,EAAE;EADkB,GAAnB,CAAZ;EAGA,SAAO,QAAQK,GAAG,CAACC,YAAnB;EACH,CALe,EAAhB;;MAMaC,OAAb;EAAA;;EAAA;;EACI;EACJ;EACA;EACA;EACA;EACA;EACI,mBAAY7D,IAAZ,EAAkB;EAAA;;EAAA;;EACd,8BAAMA,IAAN;EACA,UAAK8D,OAAL,GAAe,KAAf;;EACA,QAAI,OAAOC,QAAP,KAAoB,WAAxB,EAAqC;EACjC,UAAMC,KAAK,GAAG,aAAaD,QAAQ,CAACxG,QAApC;EACA,UAAI0G,IAAI,GAAGF,QAAQ,CAACE,IAApB,CAFiC;;EAIjC,UAAI,CAACA,IAAL,EAAW;EACPA,QAAAA,IAAI,GAAGD,KAAK,GAAG,KAAH,GAAW,IAAvB;EACH;;EACD,YAAKE,EAAL,GACK,OAAOH,QAAP,KAAoB,WAApB,IACG/D,IAAI,CAACmE,QAAL,KAAkBJ,QAAQ,CAACI,QAD/B,IAEIF,IAAI,KAAKjE,IAAI,CAACiE,IAHtB;EAIA,YAAKG,EAAL,GAAUpE,IAAI,CAACqE,MAAL,KAAgBL,KAA1B;EACH;EACD;EACR;EACA;;;EACQ,QAAMM,WAAW,GAAGtE,IAAI,IAAIA,IAAI,CAACsE,WAAjC;EACA,UAAKpJ,cAAL,GAAsBwI,OAAO,IAAI,CAACY,WAAlC;EApBc;EAqBjB;;EA5BL;EAAA;;EAgCI;EACJ;EACA;EACA;EACA;EACA;EArCA,6BAsCa;EACL,WAAKC,IAAL;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;;EA9CA;EAAA;EAAA,0BA+CUxC,OA/CV,EA+CmB;EAAA;;EACX,WAAKP,UAAL,GAAkB,SAAlB;;EACA,UAAMgD,KAAK,GAAG,SAARA,KAAQ,GAAM;EAChB,QAAA,MAAI,CAAChD,UAAL,GAAkB,QAAlB;EACAO,QAAAA,OAAO;EACV,OAHD;;EAIA,UAAI,KAAK+B,OAAL,IAAgB,CAAC,KAAKzC,QAA1B,EAAoC;EAChC,YAAIoD,KAAK,GAAG,CAAZ;;EACA,YAAI,KAAKX,OAAT,EAAkB;EACdW,UAAAA,KAAK;EACL,eAAK1G,IAAL,CAAU,cAAV,EAA0B,YAAY;EAClC,cAAE0G,KAAF,IAAWD,KAAK,EAAhB;EACH,WAFD;EAGH;;EACD,YAAI,CAAC,KAAKnD,QAAV,EAAoB;EAChBoD,UAAAA,KAAK;EACL,eAAK1G,IAAL,CAAU,OAAV,EAAmB,YAAY;EAC3B,cAAE0G,KAAF,IAAWD,KAAK,EAAhB;EACH,WAFD;EAGH;EACJ,OAdD,MAeK;EACDA,QAAAA,KAAK;EACR;EACJ;EACD;EACJ;EACA;EACA;EACA;;EA5EA;EAAA;EAAA,2BA6EW;EACH,WAAKV,OAAL,GAAe,IAAf;EACA,WAAKY,MAAL;EACA,WAAK7F,YAAL,CAAkB,MAAlB;EACH;EACD;EACJ;EACA;EACA;EACA;;EAtFA;EAAA;EAAA,2BAuFWvE,IAvFX,EAuFiB;EAAA;;EACT,UAAMa,QAAQ,GAAG,SAAXA,QAAW,CAAC8B,MAAD,EAAY;EACzB;EACA,YAAI,cAAc,MAAI,CAACuE,UAAnB,IAAiCvE,MAAM,CAAC5C,IAAP,KAAgB,MAArD,EAA6D;EACzD,UAAA,MAAI,CAACsK,MAAL;EACH,SAJwB;;;EAMzB,YAAI,YAAY1H,MAAM,CAAC5C,IAAvB,EAA6B;EACzB,UAAA,MAAI,CAACsH,OAAL,CAAa;EAAEV,YAAAA,WAAW,EAAE;EAAf,WAAb;;EACA,iBAAO,KAAP;EACH,SATwB;;;EAWzB,QAAA,MAAI,CAACY,QAAL,CAAc5E,MAAd;EACH,OAZD,CADS;;;EAeTE,MAAAA,aAAa,CAAC7C,IAAD,EAAO,KAAKiH,MAAL,CAAYvF,UAAnB,CAAb,CAA4C9B,OAA5C,CAAoDiB,QAApD,EAfS;;EAiBT,UAAI,aAAa,KAAKqG,UAAtB,EAAkC;EAC9B;EACA,aAAKsC,OAAL,GAAe,KAAf;EACA,aAAKjF,YAAL,CAAkB,cAAlB;;EACA,YAAI,WAAW,KAAK2C,UAApB,EAAgC;EAC5B,eAAK+C,IAAL;EACH;EAGJ;EACJ;EACD;EACJ;EACA;EACA;EACA;;EAvHA;EAAA;EAAA,8BAwHc;EAAA;;EACN,UAAMK,KAAK,GAAG,SAARA,KAAQ,GAAM;EAChB,QAAA,MAAI,CAAChD,KAAL,CAAW,CAAC;EAAEvH,UAAAA,IAAI,EAAE;EAAR,SAAD,CAAX;EACH,OAFD;;EAGA,UAAI,WAAW,KAAKmH,UAApB,EAAgC;EAC5BoD,QAAAA,KAAK;EACR,OAFD,MAGK;EACD;EACA;EACA,aAAK7G,IAAL,CAAU,MAAV,EAAkB6G,KAAlB;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;EACA;;EA1IA;EAAA;EAAA,0BA2IU/H,OA3IV,EA2ImB;EAAA;;EACX,WAAKwE,QAAL,GAAgB,KAAhB;EACAzE,MAAAA,aAAa,CAACC,OAAD,EAAU,UAACvC,IAAD,EAAU;EAC7B,QAAA,MAAI,CAACuK,OAAL,CAAavK,IAAb,EAAmB,YAAM;EACrB,UAAA,MAAI,CAAC+G,QAAL,GAAgB,IAAhB;;EACA,UAAA,MAAI,CAACxC,YAAL,CAAkB,OAAlB;EACH,SAHD;EAIH,OALY,CAAb;EAMH;EACD;EACJ;EACA;EACA;EACA;;EAxJA;EAAA;EAAA,0BAyJU;EACF,UAAIyC,KAAK,GAAG,KAAKA,KAAL,IAAc,EAA1B;EACA,UAAMwD,MAAM,GAAG,KAAK9E,IAAL,CAAUqE,MAAV,GAAmB,OAAnB,GAA6B,MAA5C;EACA,UAAIJ,IAAI,GAAG,EAAX,CAHE;;EAKF,UAAI,UAAU,KAAKjE,IAAL,CAAU+E,iBAAxB,EAA2C;EACvCzD,QAAAA,KAAK,CAAC,KAAKtB,IAAL,CAAUgF,cAAX,CAAL,GAAkCxC,KAAK,EAAvC;EACH;;EACD,UAAI,CAAC,KAAKtH,cAAN,IAAwB,CAACoG,KAAK,CAAC2D,GAAnC,EAAwC;EACpC3D,QAAAA,KAAK,CAAC4D,GAAN,GAAY,CAAZ;EACH,OAVC;;;EAYF,UAAI,KAAKlF,IAAL,CAAUiE,IAAV,KACE,YAAYa,MAAZ,IAAsBK,MAAM,CAAC,KAAKnF,IAAL,CAAUiE,IAAX,CAAN,KAA2B,GAAlD,IACI,WAAWa,MAAX,IAAqBK,MAAM,CAAC,KAAKnF,IAAL,CAAUiE,IAAX,CAAN,KAA2B,EAFrD,CAAJ,EAE+D;EAC3DA,QAAAA,IAAI,GAAG,MAAM,KAAKjE,IAAL,CAAUiE,IAAvB;EACH;;EACD,UAAMmB,YAAY,GAAGhD,MAAM,CAACd,KAAD,CAA3B;EACA,UAAM+D,IAAI,GAAG,KAAKrF,IAAL,CAAUmE,QAAV,CAAmBmB,OAAnB,CAA2B,GAA3B,MAAoC,CAAC,CAAlD;EACA,aAAQR,MAAM,GACV,KADI,IAEHO,IAAI,GAAG,MAAM,KAAKrF,IAAL,CAAUmE,QAAhB,GAA2B,GAA9B,GAAoC,KAAKnE,IAAL,CAAUmE,QAF/C,IAGJF,IAHI,GAIJ,KAAKjE,IAAL,CAAUuF,IAJN,IAKHH,YAAY,CAAC9I,MAAb,GAAsB,MAAM8I,YAA5B,GAA2C,EALxC,CAAR;EAMH;EACD;EACJ;EACA;EACA;EACA;EACA;;EAxLA;EAAA;EAAA,8BAyLuB;EAAA,UAAXpF,IAAW,uEAAJ,EAAI;;EACf,eAAcA,IAAd,EAAoB;EAAEkE,QAAAA,EAAE,EAAE,KAAKA,EAAX;EAAeE,QAAAA,EAAE,EAAE,KAAKA;EAAxB,OAApB,EAAkD,KAAKpE,IAAvD;;EACA,aAAO,IAAIwF,OAAJ,CAAY,KAAKC,GAAL,EAAZ,EAAwBzF,IAAxB,CAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;EAnMA;EAAA;EAAA,4BAoMY1F,IApMZ,EAoMkBuD,EApMlB,EAoMsB;EAAA;;EACd,UAAM6H,GAAG,GAAG,KAAKC,OAAL,CAAa;EACrBC,QAAAA,MAAM,EAAE,MADa;EAErBtL,QAAAA,IAAI,EAAEA;EAFe,OAAb,CAAZ;EAIAoL,MAAAA,GAAG,CAAChI,EAAJ,CAAO,SAAP,EAAkBG,EAAlB;EACA6H,MAAAA,GAAG,CAAChI,EAAJ,CAAO,OAAP,EAAgB,UAACmI,SAAD,EAAY3E,OAAZ,EAAwB;EACpC,QAAA,MAAI,CAAC4E,OAAL,CAAa,gBAAb,EAA+BD,SAA/B,EAA0C3E,OAA1C;EACH,OAFD;EAGH;EACD;EACJ;EACA;EACA;EACA;;EAlNA;EAAA;EAAA,6BAmNa;EAAA;;EACL,UAAMwE,GAAG,GAAG,KAAKC,OAAL,EAAZ;EACAD,MAAAA,GAAG,CAAChI,EAAJ,CAAO,MAAP,EAAe,KAAKqI,MAAL,CAAY5F,IAAZ,CAAiB,IAAjB,CAAf;EACAuF,MAAAA,GAAG,CAAChI,EAAJ,CAAO,OAAP,EAAgB,UAACmI,SAAD,EAAY3E,OAAZ,EAAwB;EACpC,QAAA,MAAI,CAAC4E,OAAL,CAAa,gBAAb,EAA+BD,SAA/B,EAA0C3E,OAA1C;EACH,OAFD;EAGA,WAAK8E,OAAL,GAAeN,GAAf;EACH;EA1NL;EAAA;EAAA,wBA6Be;EACP,aAAO,SAAP;EACH;EA/BL;;EAAA;EAAA,EAA6BtE,SAA7B;MA4NaoE,OAAb;EAAA;;EAAA;;EACI;EACJ;EACA;EACA;EACA;EACA;EACI,mBAAYC,GAAZ,EAAiBzF,IAAjB,EAAuB;EAAA;;EAAA;;EACnB;EACAD,IAAAA,qBAAqB,iCAAOC,IAAP,CAArB;EACA,WAAKA,IAAL,GAAYA,IAAZ;EACA,WAAK4F,MAAL,GAAc5F,IAAI,CAAC4F,MAAL,IAAe,KAA7B;EACA,WAAKH,GAAL,GAAWA,GAAX;EACA,WAAKQ,KAAL,GAAa,UAAUjG,IAAI,CAACiG,KAA5B;EACA,WAAK3L,IAAL,GAAY4L,SAAS,KAAKlG,IAAI,CAAC1F,IAAnB,GAA0B0F,IAAI,CAAC1F,IAA/B,GAAsC,IAAlD;;EACA,WAAKP,MAAL;;EARmB;EAStB;EACD;EACJ;EACA;EACA;EACA;;;EArBA;EAAA;EAAA,6BAsBa;EAAA;;EACL,UAAMiG,IAAI,GAAGZ,IAAI,CAAC,KAAKY,IAAN,EAAY,OAAZ,EAAqB,KAArB,EAA4B,KAA5B,EAAmC,YAAnC,EAAiD,MAAjD,EAAyD,IAAzD,EAA+D,SAA/D,EAA0E,oBAA1E,EAAgG,WAAhG,CAAjB;EACAA,MAAAA,IAAI,CAACsD,OAAL,GAAe,CAAC,CAAC,KAAKtD,IAAL,CAAUkE,EAA3B;EACAlE,MAAAA,IAAI,CAACmG,OAAL,GAAe,CAAC,CAAC,KAAKnG,IAAL,CAAUoE,EAA3B;EACA,UAAMT,GAAG,GAAI,KAAKA,GAAL,GAAW,IAAIT,GAAJ,CAAmBlD,IAAnB,CAAxB;;EACA,UAAI;EACA2D,QAAAA,GAAG,CAACyC,IAAJ,CAAS,KAAKR,MAAd,EAAsB,KAAKH,GAA3B,EAAgC,KAAKQ,KAArC;;EACA,YAAI;EACA,cAAI,KAAKjG,IAAL,CAAUqG,YAAd,EAA4B;EACxB1C,YAAAA,GAAG,CAAC2C,qBAAJ,IAA6B3C,GAAG,CAAC2C,qBAAJ,CAA0B,IAA1B,CAA7B;;EACA,iBAAK,IAAI1K,CAAT,IAAc,KAAKoE,IAAL,CAAUqG,YAAxB,EAAsC;EAClC,kBAAI,KAAKrG,IAAL,CAAUqG,YAAV,CAAuB5G,cAAvB,CAAsC7D,CAAtC,CAAJ,EAA8C;EAC1C+H,gBAAAA,GAAG,CAAC4C,gBAAJ,CAAqB3K,CAArB,EAAwB,KAAKoE,IAAL,CAAUqG,YAAV,CAAuBzK,CAAvB,CAAxB;EACH;EACJ;EACJ;EACJ,SATD,CAUA,OAAO2H,CAAP,EAAU;;EACV,YAAI,WAAW,KAAKqC,MAApB,EAA4B;EACxB,cAAI;EACAjC,YAAAA,GAAG,CAAC4C,gBAAJ,CAAqB,cAArB,EAAqC,0BAArC;EACH,WAFD,CAGA,OAAOhD,CAAP,EAAU;EACb;;EACD,YAAI;EACAI,UAAAA,GAAG,CAAC4C,gBAAJ,CAAqB,QAArB,EAA+B,KAA/B;EACH,SAFD,CAGA,OAAOhD,CAAP,EAAU,EAtBV;;;EAwBA,YAAI,qBAAqBI,GAAzB,EAA8B;EAC1BA,UAAAA,GAAG,CAAC6C,eAAJ,GAAsB,KAAKxG,IAAL,CAAUwG,eAAhC;EACH;;EACD,YAAI,KAAKxG,IAAL,CAAUyG,cAAd,EAA8B;EAC1B9C,UAAAA,GAAG,CAAC+C,OAAJ,GAAc,KAAK1G,IAAL,CAAUyG,cAAxB;EACH;;EACD9C,QAAAA,GAAG,CAACgD,kBAAJ,GAAyB,YAAM;EAC3B,cAAI,MAAMhD,GAAG,CAACnC,UAAd,EACI;;EACJ,cAAI,QAAQmC,GAAG,CAACiD,MAAZ,IAAsB,SAASjD,GAAG,CAACiD,MAAvC,EAA+C;EAC3C,YAAA,MAAI,CAACC,MAAL;EACH,WAFD,MAGK;EACD;EACA;EACA,YAAA,MAAI,CAAC3G,YAAL,CAAkB,YAAM;EACpB,cAAA,MAAI,CAAC4F,OAAL,CAAa,OAAOnC,GAAG,CAACiD,MAAX,KAAsB,QAAtB,GAAiCjD,GAAG,CAACiD,MAArC,GAA8C,CAA3D;EACH,aAFD,EAEG,CAFH;EAGH;EACJ,SAbD;;EAcAjD,QAAAA,GAAG,CAACmD,IAAJ,CAAS,KAAKxM,IAAd;EACH,OA7CD,CA8CA,OAAOiJ,CAAP,EAAU;EACN;EACA;EACA;EACA,aAAKrD,YAAL,CAAkB,YAAM;EACpB,UAAA,MAAI,CAAC4F,OAAL,CAAavC,CAAb;EACH,SAFD,EAEG,CAFH;EAGA;EACH;;EACD,UAAI,OAAOwD,QAAP,KAAoB,WAAxB,EAAqC;EACjC,aAAKC,KAAL,GAAaxB,OAAO,CAACyB,aAAR,EAAb;EACAzB,QAAAA,OAAO,CAAC0B,QAAR,CAAiB,KAAKF,KAAtB,IAA+B,IAA/B;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;;EA3FA;EAAA;EAAA,4BA4FY7D,GA5FZ,EA4FiB;EACT,WAAKtE,YAAL,CAAkB,OAAlB,EAA2BsE,GAA3B,EAAgC,KAAKQ,GAArC;EACA,WAAKwD,OAAL,CAAa,IAAb;EACH;EACD;EACJ;EACA;EACA;EACA;;EApGA;EAAA;EAAA,4BAqGYC,SArGZ,EAqGuB;EACf,UAAI,gBAAgB,OAAO,KAAKzD,GAA5B,IAAmC,SAAS,KAAKA,GAArD,EAA0D;EACtD;EACH;;EACD,WAAKA,GAAL,CAASgD,kBAAT,GAA8BlD,KAA9B;;EACA,UAAI2D,SAAJ,EAAe;EACX,YAAI;EACA,eAAKzD,GAAL,CAAS0D,KAAT;EACH,SAFD,CAGA,OAAO9D,CAAP,EAAU;EACb;;EACD,UAAI,OAAOwD,QAAP,KAAoB,WAAxB,EAAqC;EACjC,eAAOvB,OAAO,CAAC0B,QAAR,CAAiB,KAAKF,KAAtB,CAAP;EACH;;EACD,WAAKrD,GAAL,GAAW,IAAX;EACH;EACD;EACJ;EACA;EACA;EACA;;EAzHA;EAAA;EAAA,6BA0Ha;EACL,UAAMrJ,IAAI,GAAG,KAAKqJ,GAAL,CAAS2D,YAAtB;;EACA,UAAIhN,IAAI,KAAK,IAAb,EAAmB;EACf,aAAKuE,YAAL,CAAkB,MAAlB,EAA0BvE,IAA1B;EACA,aAAKuE,YAAL,CAAkB,SAAlB;EACA,aAAKsI,OAAL;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;;EAtIA;EAAA;EAAA,4BAuIY;EACJ,WAAKA,OAAL;EACH;EAzIL;;EAAA;EAAA,EAA6B3J,OAA7B;EA2IAgI,OAAO,CAACyB,aAAR,GAAwB,CAAxB;EACAzB,OAAO,CAAC0B,QAAR,GAAmB,EAAnB;EACA;EACA;EACA;EACA;EACA;;EACA,IAAI,OAAOH,QAAP,KAAoB,WAAxB,EAAqC;EACjC;EACA,MAAI,OAAOQ,WAAP,KAAuB,UAA3B,EAAuC;EACnC;EACAA,IAAAA,WAAW,CAAC,UAAD,EAAaC,aAAb,CAAX;EACH,GAHD,MAIK,IAAI,OAAO7J,gBAAP,KAA4B,UAAhC,EAA4C;EAC7C,QAAM8J,gBAAgB,GAAG,gBAAgB9H,cAAhB,GAA6B,UAA7B,GAA0C,QAAnE;EACAhC,IAAAA,gBAAgB,CAAC8J,gBAAD,EAAmBD,aAAnB,EAAkC,KAAlC,CAAhB;EACH;EACJ;;EACD,SAASA,aAAT,GAAyB;EACrB,OAAK,IAAI5L,CAAT,IAAc4J,OAAO,CAAC0B,QAAtB,EAAgC;EAC5B,QAAI1B,OAAO,CAAC0B,QAAR,CAAiBzH,cAAjB,CAAgC7D,CAAhC,CAAJ,EAAwC;EACpC4J,MAAAA,OAAO,CAAC0B,QAAR,CAAiBtL,CAAjB,EAAoByL,KAApB;EACH;EACJ;EACJ;;EC7YM,IAAMK,QAAQ,GAAI,YAAM;EAC3B,MAAMC,kBAAkB,GAAG,OAAOC,OAAP,KAAmB,UAAnB,IAAiC,OAAOA,OAAO,CAACC,OAAf,KAA2B,UAAvF;;EACA,MAAIF,kBAAJ,EAAwB;EACpB,WAAO,UAACpJ,EAAD;EAAA,aAAQqJ,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuBvJ,EAAvB,CAAR;EAAA,KAAP;EACH,GAFD,MAGK;EACD,WAAO,UAACA,EAAD,EAAK2B,YAAL;EAAA,aAAsBA,YAAY,CAAC3B,EAAD,EAAK,CAAL,CAAlC;EAAA,KAAP;EACH;EACJ,CARuB,EAAjB;EASA,IAAMwJ,SAAS,GAAGpI,cAAU,CAACoI,SAAX,IAAwBpI,cAAU,CAACqI,YAArD;EACA,IAAMC,qBAAqB,GAAG,IAA9B;EACA,IAAMC,iBAAiB,GAAG,aAA1B;;ECLP,IAAMC,aAAa,GAAG,OAAOC,SAAP,KAAqB,WAArB,IAClB,OAAOA,SAAS,CAACC,OAAjB,KAA6B,QADX,IAElBD,SAAS,CAACC,OAAV,CAAkBC,WAAlB,OAAoC,aAFxC;MAGaC,EAAb;EAAA;;EAAA;;EACI;EACJ;EACA;EACA;EACA;EACA;EACI,cAAYvI,IAAZ,EAAkB;EAAA;;EAAA;;EACd,8BAAMA,IAAN;EACA,UAAK9E,cAAL,GAAsB,CAAC8E,IAAI,CAACsE,WAA5B;EAFc;EAGjB;;EAVL;EAAA;EAAA,6BAca;EACL,UAAI,CAAC,KAAKkE,KAAL,EAAL,EAAmB;EACf;EACA;EACH;;EACD,UAAM/C,GAAG,GAAG,KAAKA,GAAL,EAAZ;EACA,UAAMgD,SAAS,GAAG,KAAKzI,IAAL,CAAUyI,SAA5B,CANK;;EAQL,UAAMzI,IAAI,GAAGmI,aAAa,GACpB,EADoB,GAEpB/I,IAAI,CAAC,KAAKY,IAAN,EAAY,OAAZ,EAAqB,mBAArB,EAA0C,KAA1C,EAAiD,KAAjD,EAAwD,YAAxD,EAAsE,MAAtE,EAA8E,IAA9E,EAAoF,SAApF,EAA+F,oBAA/F,EAAqH,cAArH,EAAqI,iBAArI,EAAwJ,QAAxJ,EAAkK,YAAlK,EAAgL,QAAhL,EAA0L,qBAA1L,CAFV;;EAGA,UAAI,KAAKA,IAAL,CAAUqG,YAAd,EAA4B;EACxBrG,QAAAA,IAAI,CAAC0I,OAAL,GAAe,KAAK1I,IAAL,CAAUqG,YAAzB;EACH;;EACD,UAAI;EACA,aAAKsC,EAAL,GACIV,qBAAqB,IAAI,CAACE,aAA1B,GACMM,SAAS,GACL,IAAIV,SAAJ,CAActC,GAAd,EAAmBgD,SAAnB,CADK,GAEL,IAAIV,SAAJ,CAActC,GAAd,CAHV,GAIM,IAAIsC,SAAJ,CAActC,GAAd,EAAmBgD,SAAnB,EAA8BzI,IAA9B,CALV;EAMH,OAPD,CAQA,OAAOmD,GAAP,EAAY;EACR,eAAO,KAAKtE,YAAL,CAAkB,OAAlB,EAA2BsE,GAA3B,CAAP;EACH;;EACD,WAAKwF,EAAL,CAAQ3M,UAAR,GAAqB,KAAKuF,MAAL,CAAYvF,UAAZ,IAA0BkM,iBAA/C;EACA,WAAKU,iBAAL;EACH;EACD;EACJ;EACA;EACA;EACA;;EA9CA;EAAA;EAAA,wCA+CwB;EAAA;;EAChB,WAAKD,EAAL,CAAQE,MAAR,GAAiB,YAAM;EACnB,YAAI,MAAI,CAAC7I,IAAL,CAAU8I,SAAd,EAAyB;EACrB,UAAA,MAAI,CAACH,EAAL,CAAQI,OAAR,CAAgBC,KAAhB;EACH;;EACD,QAAA,MAAI,CAACrE,MAAL;EACH,OALD;;EAMA,WAAKgE,EAAL,CAAQM,OAAR,GAAkB,UAACC,UAAD;EAAA,eAAgB,MAAI,CAACvH,OAAL,CAAa;EAC3CV,UAAAA,WAAW,EAAE,6BAD8B;EAE3CC,UAAAA,OAAO,EAAEgI;EAFkC,SAAb,CAAhB;EAAA,OAAlB;;EAIA,WAAKP,EAAL,CAAQQ,SAAR,GAAoB,UAACC,EAAD;EAAA,eAAQ,MAAI,CAACrD,MAAL,CAAYqD,EAAE,CAAC9O,IAAf,CAAR;EAAA,OAApB;;EACA,WAAKqO,EAAL,CAAQU,OAAR,GAAkB,UAAC9F,CAAD;EAAA,eAAO,MAAI,CAACuC,OAAL,CAAa,iBAAb,EAAgCvC,CAAhC,CAAP;EAAA,OAAlB;EACH;EA5DL;EAAA;EAAA,0BA6DU1G,OA7DV,EA6DmB;EAAA;;EACX,WAAKwE,QAAL,GAAgB,KAAhB,CADW;EAGX;;EAHW,iCAIFzF,CAJE;EAKP,YAAMqB,MAAM,GAAGJ,OAAO,CAACjB,CAAD,CAAtB;EACA,YAAM0N,UAAU,GAAG1N,CAAC,KAAKiB,OAAO,CAACP,MAAR,GAAiB,CAA1C;EACArB,QAAAA,YAAY,CAACgC,MAAD,EAAS,MAAI,CAAC/B,cAAd,EAA8B,UAACZ,IAAD,EAAU;EAChD;EACA,cAAM0F,IAAI,GAAG,EAAb;EAeA;EACA;;;EACA,cAAI;EACA,gBAAIiI,qBAAJ,EAA2B;EACvB;EACA,cAAA,MAAI,CAACU,EAAL,CAAQ7B,IAAR,CAAaxM,IAAb;EACH;EAIJ,WARD,CASA,OAAOiJ,CAAP,EAAU;;EAEV,cAAI+F,UAAJ,EAAgB;EACZ;EACA;EACA5B,YAAAA,QAAQ,CAAC,YAAM;EACX,cAAA,MAAI,CAACrG,QAAL,GAAgB,IAAhB;;EACA,cAAA,MAAI,CAACxC,YAAL,CAAkB,OAAlB;EACH,aAHO,EAGL,MAAI,CAACqB,YAHA,CAAR;EAIH;EACJ,SAtCW,CAAZ;EAPO;;EAIX,WAAK,IAAItE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGiB,OAAO,CAACP,MAA5B,EAAoCV,CAAC,EAArC,EAAyC;EAAA,cAAhCA,CAAgC;EA0CxC;EACJ;EA5GL;EAAA;EAAA,8BA6Gc;EACN,UAAI,OAAO,KAAK+M,EAAZ,KAAmB,WAAvB,EAAoC;EAChC,aAAKA,EAAL,CAAQ/D,KAAR;EACA,aAAK+D,EAAL,GAAU,IAAV;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;;EAvHA;EAAA;EAAA,0BAwHU;EACF,UAAIrH,KAAK,GAAG,KAAKA,KAAL,IAAc,EAA1B;EACA,UAAMwD,MAAM,GAAG,KAAK9E,IAAL,CAAUqE,MAAV,GAAmB,KAAnB,GAA2B,IAA1C;EACA,UAAIJ,IAAI,GAAG,EAAX,CAHE;;EAKF,UAAI,KAAKjE,IAAL,CAAUiE,IAAV,KACE,UAAUa,MAAV,IAAoBK,MAAM,CAAC,KAAKnF,IAAL,CAAUiE,IAAX,CAAN,KAA2B,GAAhD,IACI,SAASa,MAAT,IAAmBK,MAAM,CAAC,KAAKnF,IAAL,CAAUiE,IAAX,CAAN,KAA2B,EAFnD,CAAJ,EAE6D;EACzDA,QAAAA,IAAI,GAAG,MAAM,KAAKjE,IAAL,CAAUiE,IAAvB;EACH,OATC;;;EAWF,UAAI,KAAKjE,IAAL,CAAU+E,iBAAd,EAAiC;EAC7BzD,QAAAA,KAAK,CAAC,KAAKtB,IAAL,CAAUgF,cAAX,CAAL,GAAkCxC,KAAK,EAAvC;EACH,OAbC;;;EAeF,UAAI,CAAC,KAAKtH,cAAV,EAA0B;EACtBoG,QAAAA,KAAK,CAAC4D,GAAN,GAAY,CAAZ;EACH;;EACD,UAAME,YAAY,GAAGhD,MAAM,CAACd,KAAD,CAA3B;EACA,UAAM+D,IAAI,GAAG,KAAKrF,IAAL,CAAUmE,QAAV,CAAmBmB,OAAnB,CAA2B,GAA3B,MAAoC,CAAC,CAAlD;EACA,aAAQR,MAAM,GACV,KADI,IAEHO,IAAI,GAAG,MAAM,KAAKrF,IAAL,CAAUmE,QAAhB,GAA2B,GAA9B,GAAoC,KAAKnE,IAAL,CAAUmE,QAF/C,IAGJF,IAHI,GAIJ,KAAKjE,IAAL,CAAUuF,IAJN,IAKHH,YAAY,CAAC9I,MAAb,GAAsB,MAAM8I,YAA5B,GAA2C,EALxC,CAAR;EAMH;EACD;EACJ;EACA;EACA;EACA;EACA;;EAxJA;EAAA;EAAA,4BAyJY;EACJ,aAAO,CAAC,CAAC2C,SAAT;EACH;EA3JL;EAAA;EAAA,wBAWe;EACP,aAAO,WAAP;EACH;EAbL;;EAAA;EAAA,EAAwB3G,SAAxB;;ECRO,IAAMmI,UAAU,GAAG;EACtBC,EAAAA,SAAS,EAAEjB,EADW;EAEtBzE,EAAAA,OAAO,EAAED;EAFa,CAAnB;;ECFP;;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAM4F,EAAE,GAAG,qPAAX;EACA,IAAMC,KAAK,GAAG,CACV,QADU,EACA,UADA,EACY,WADZ,EACyB,UADzB,EACqC,MADrC,EAC6C,UAD7C,EACyD,MADzD,EACiE,MADjE,EACyE,UADzE,EACqF,MADrF,EAC6F,WAD7F,EAC0G,MAD1G,EACkH,OADlH,EAC2H,QAD3H,CAAd;EAGO,SAASC,KAAT,CAAehJ,GAAf,EAAoB;EACvB,MAAMiJ,GAAG,GAAGjJ,GAAZ;EAAA,MAAiBkJ,CAAC,GAAGlJ,GAAG,CAAC2E,OAAJ,CAAY,GAAZ,CAArB;EAAA,MAAuC/B,CAAC,GAAG5C,GAAG,CAAC2E,OAAJ,CAAY,GAAZ,CAA3C;;EACA,MAAIuE,CAAC,IAAI,CAAC,CAAN,IAAWtG,CAAC,IAAI,CAAC,CAArB,EAAwB;EACpB5C,IAAAA,GAAG,GAAGA,GAAG,CAACvE,SAAJ,CAAc,CAAd,EAAiByN,CAAjB,IAAsBlJ,GAAG,CAACvE,SAAJ,CAAcyN,CAAd,EAAiBtG,CAAjB,EAAoBuG,OAApB,CAA4B,IAA5B,EAAkC,GAAlC,CAAtB,GAA+DnJ,GAAG,CAACvE,SAAJ,CAAcmH,CAAd,EAAiB5C,GAAG,CAACrE,MAArB,CAArE;EACH;;EACD,MAAIyN,CAAC,GAAGN,EAAE,CAACO,IAAH,CAAQrJ,GAAG,IAAI,EAAf,CAAR;EAAA,MAA4B8E,GAAG,GAAG,EAAlC;EAAA,MAAsC7J,CAAC,GAAG,EAA1C;;EACA,SAAOA,CAAC,EAAR,EAAY;EACR6J,IAAAA,GAAG,CAACiE,KAAK,CAAC9N,CAAD,CAAN,CAAH,GAAgBmO,CAAC,CAACnO,CAAD,CAAD,IAAQ,EAAxB;EACH;;EACD,MAAIiO,CAAC,IAAI,CAAC,CAAN,IAAWtG,CAAC,IAAI,CAAC,CAArB,EAAwB;EACpBkC,IAAAA,GAAG,CAACwE,MAAJ,GAAaL,GAAb;EACAnE,IAAAA,GAAG,CAACyE,IAAJ,GAAWzE,GAAG,CAACyE,IAAJ,CAAS9N,SAAT,CAAmB,CAAnB,EAAsBqJ,GAAG,CAACyE,IAAJ,CAAS5N,MAAT,GAAkB,CAAxC,EAA2CwN,OAA3C,CAAmD,IAAnD,EAAyD,GAAzD,CAAX;EACArE,IAAAA,GAAG,CAAC0E,SAAJ,GAAgB1E,GAAG,CAAC0E,SAAJ,CAAcL,OAAd,CAAsB,GAAtB,EAA2B,EAA3B,EAA+BA,OAA/B,CAAuC,GAAvC,EAA4C,EAA5C,EAAgDA,OAAhD,CAAwD,IAAxD,EAA8D,GAA9D,CAAhB;EACArE,IAAAA,GAAG,CAAC2E,OAAJ,GAAc,IAAd;EACH;;EACD3E,EAAAA,GAAG,CAAC4E,SAAJ,GAAgBA,SAAS,CAAC5E,GAAD,EAAMA,GAAG,CAAC,MAAD,CAAT,CAAzB;EACAA,EAAAA,GAAG,CAAC6E,QAAJ,GAAeA,QAAQ,CAAC7E,GAAD,EAAMA,GAAG,CAAC,OAAD,CAAT,CAAvB;EACA,SAAOA,GAAP;EACH;;EACD,SAAS4E,SAAT,CAAmBtP,GAAnB,EAAwBwK,IAAxB,EAA8B;EAC1B,MAAMgF,IAAI,GAAG,UAAb;EAAA,MAAyBC,KAAK,GAAGjF,IAAI,CAACuE,OAAL,CAAaS,IAAb,EAAmB,GAAnB,EAAwB7O,KAAxB,CAA8B,GAA9B,CAAjC;;EACA,MAAI6J,IAAI,CAAC5G,KAAL,CAAW,CAAX,EAAc,CAAd,KAAoB,GAApB,IAA2B4G,IAAI,CAACjJ,MAAL,KAAgB,CAA/C,EAAkD;EAC9CkO,IAAAA,KAAK,CAAChM,MAAN,CAAa,CAAb,EAAgB,CAAhB;EACH;;EACD,MAAI+G,IAAI,CAAC5G,KAAL,CAAW,CAAC,CAAZ,KAAkB,GAAtB,EAA2B;EACvB6L,IAAAA,KAAK,CAAChM,MAAN,CAAagM,KAAK,CAAClO,MAAN,GAAe,CAA5B,EAA+B,CAA/B;EACH;;EACD,SAAOkO,KAAP;EACH;;EACD,SAASF,QAAT,CAAkB7E,GAAlB,EAAuBnE,KAAvB,EAA8B;EAC1B,MAAMhH,IAAI,GAAG,EAAb;EACAgH,EAAAA,KAAK,CAACwI,OAAN,CAAc,2BAAd,EAA2C,UAAUW,EAAV,EAAcC,EAAd,EAAkBC,EAAlB,EAAsB;EAC7D,QAAID,EAAJ,EAAQ;EACJpQ,MAAAA,IAAI,CAACoQ,EAAD,CAAJ,GAAWC,EAAX;EACH;EACJ,GAJD;EAKA,SAAOrQ,IAAP;EACH;;MCtDYsQ,MAAb;EAAA;;EAAA;;EACI;EACJ;EACA;EACA;EACA;EACA;EACI,kBAAYnF,GAAZ,EAA4B;EAAA;;EAAA,QAAXzF,IAAW,uEAAJ,EAAI;;EAAA;;EACxB;EACA,UAAK6K,WAAL,GAAmB,EAAnB;;EACA,QAAIpF,GAAG,IAAI,qBAAoBA,GAApB,CAAX,EAAoC;EAChCzF,MAAAA,IAAI,GAAGyF,GAAP;EACAA,MAAAA,GAAG,GAAG,IAAN;EACH;;EACD,QAAIA,GAAJ,EAAS;EACLA,MAAAA,GAAG,GAAGkE,KAAK,CAAClE,GAAD,CAAX;EACAzF,MAAAA,IAAI,CAACmE,QAAL,GAAgBsB,GAAG,CAACyE,IAApB;EACAlK,MAAAA,IAAI,CAACqE,MAAL,GAAcoB,GAAG,CAAClI,QAAJ,KAAiB,OAAjB,IAA4BkI,GAAG,CAAClI,QAAJ,KAAiB,KAA3D;EACAyC,MAAAA,IAAI,CAACiE,IAAL,GAAYwB,GAAG,CAACxB,IAAhB;EACA,UAAIwB,GAAG,CAACnE,KAAR,EACItB,IAAI,CAACsB,KAAL,GAAamE,GAAG,CAACnE,KAAjB;EACP,KAPD,MAQK,IAAItB,IAAI,CAACkK,IAAT,EAAe;EAChBlK,MAAAA,IAAI,CAACmE,QAAL,GAAgBwF,KAAK,CAAC3J,IAAI,CAACkK,IAAN,CAAL,CAAiBA,IAAjC;EACH;;EACDnK,IAAAA,qBAAqB,gCAAOC,IAAP,CAArB;EACA,UAAKqE,MAAL,GACI,QAAQrE,IAAI,CAACqE,MAAb,GACMrE,IAAI,CAACqE,MADX,GAEM,OAAON,QAAP,KAAoB,WAApB,IAAmC,aAAaA,QAAQ,CAACxG,QAHnE;;EAIA,QAAIyC,IAAI,CAACmE,QAAL,IAAiB,CAACnE,IAAI,CAACiE,IAA3B,EAAiC;EAC7B;EACAjE,MAAAA,IAAI,CAACiE,IAAL,GAAY,MAAKI,MAAL,GAAc,KAAd,GAAsB,IAAlC;EACH;;EACD,UAAKF,QAAL,GACInE,IAAI,CAACmE,QAAL,KACK,OAAOJ,QAAP,KAAoB,WAApB,GAAkCA,QAAQ,CAACI,QAA3C,GAAsD,WAD3D,CADJ;EAGA,UAAKF,IAAL,GACIjE,IAAI,CAACiE,IAAL,KACK,OAAOF,QAAP,KAAoB,WAApB,IAAmCA,QAAQ,CAACE,IAA5C,GACKF,QAAQ,CAACE,IADd,GAEK,MAAKI,MAAL,GACI,KADJ,GAEI,IALd,CADJ;EAOA,UAAKkF,UAAL,GAAkBvJ,IAAI,CAACuJ,UAAL,IAAmB,CAAC,SAAD,EAAY,WAAZ,CAArC;EACA,UAAKsB,WAAL,GAAmB,EAAnB;EACA,UAAKC,aAAL,GAAqB,CAArB;EACA,UAAK9K,IAAL,GAAY,SAAc;EACtBuF,MAAAA,IAAI,EAAE,YADgB;EAEtBwF,MAAAA,KAAK,EAAE,KAFe;EAGtBvE,MAAAA,eAAe,EAAE,KAHK;EAItBwE,MAAAA,OAAO,EAAE,IAJa;EAKtBhG,MAAAA,cAAc,EAAE,GALM;EAMtBiG,MAAAA,eAAe,EAAE,KANK;EAOtBC,MAAAA,gBAAgB,EAAE,IAPI;EAQtBC,MAAAA,kBAAkB,EAAE,IARE;EAStBC,MAAAA,iBAAiB,EAAE;EACfC,QAAAA,SAAS,EAAE;EADI,OATG;EAYtBC,MAAAA,gBAAgB,EAAE,EAZI;EAatBC,MAAAA,mBAAmB,EAAE;EAbC,KAAd,EAcTvL,IAdS,CAAZ;EAeA,UAAKA,IAAL,CAAUuF,IAAV,GACI,MAAKvF,IAAL,CAAUuF,IAAV,CAAeuE,OAAf,CAAuB,KAAvB,EAA8B,EAA9B,KACK,MAAK9J,IAAL,CAAUkL,gBAAV,GAA6B,GAA7B,GAAmC,EADxC,CADJ;;EAGA,QAAI,OAAO,MAAKlL,IAAL,CAAUsB,KAAjB,KAA2B,QAA/B,EAAyC;EACrC,YAAKtB,IAAL,CAAUsB,KAAV,GAAkBzF,MAAM,CAAC,MAAKmE,IAAL,CAAUsB,KAAX,CAAxB;EACH,KA5DuB;;;EA8DxB,UAAKkK,EAAL,GAAU,IAAV;EACA,UAAKC,QAAL,GAAgB,IAAhB;EACA,UAAKC,YAAL,GAAoB,IAApB;EACA,UAAKC,WAAL,GAAmB,IAAnB,CAjEwB;;EAmExB,UAAKC,gBAAL,GAAwB,IAAxB;;EACA,QAAI,OAAOjO,gBAAP,KAA4B,UAAhC,EAA4C;EACxC,UAAI,MAAKqC,IAAL,CAAUuL,mBAAd,EAAmC;EAC/B;EACA;EACA;EACA,cAAKM,yBAAL,GAAiC,YAAM;EACnC,cAAI,MAAKC,SAAT,EAAoB;EAChB;EACA,kBAAKA,SAAL,CAAe1N,kBAAf;;EACA,kBAAK0N,SAAL,CAAelH,KAAf;EACH;EACJ,SAND;;EAOAjH,QAAAA,gBAAgB,CAAC,cAAD,EAAiB,MAAKkO,yBAAtB,EAAiD,KAAjD,CAAhB;EACH;;EACD,UAAI,MAAK1H,QAAL,KAAkB,WAAtB,EAAmC;EAC/B,cAAK4H,oBAAL,GAA4B,YAAM;EAC9B,gBAAKpK,OAAL,CAAa,iBAAb,EAAgC;EAC5BV,YAAAA,WAAW,EAAE;EADe,WAAhC;EAGH,SAJD;;EAKAtD,QAAAA,gBAAgB,CAAC,SAAD,EAAY,MAAKoO,oBAAjB,EAAuC,KAAvC,CAAhB;EACH;EACJ;;EACD,UAAK3F,IAAL;;EA3FwB;EA4F3B;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;;;EA1GA;EAAA;EAAA,oCA2GoB4F,IA3GpB,EA2G0B;EAClB,UAAM1K,KAAK,GAAG,SAAc,EAAd,EAAkB,KAAKtB,IAAL,CAAUsB,KAA5B,CAAd,CADkB;;;EAGlBA,MAAAA,KAAK,CAAC2K,GAAN,GAAY1O,QAAZ,CAHkB;;EAKlB+D,MAAAA,KAAK,CAACwK,SAAN,GAAkBE,IAAlB,CALkB;;EAOlB,UAAI,KAAKR,EAAT,EACIlK,KAAK,CAAC2D,GAAN,GAAY,KAAKuG,EAAjB;;EACJ,UAAMxL,IAAI,GAAG,SAAc,EAAd,EAAkB,KAAKA,IAAL,CAAUsL,gBAAV,CAA2BU,IAA3B,CAAlB,EAAoD,KAAKhM,IAAzD,EAA+D;EACxEsB,QAAAA,KAAK,EAALA,KADwE;EAExEC,QAAAA,MAAM,EAAE,IAFgE;EAGxE4C,QAAAA,QAAQ,EAAE,KAAKA,QAHyD;EAIxEE,QAAAA,MAAM,EAAE,KAAKA,MAJ2D;EAKxEJ,QAAAA,IAAI,EAAE,KAAKA;EAL6D,OAA/D,CAAb;;EAOA,aAAO,IAAIsF,UAAU,CAACyC,IAAD,CAAd,CAAqBhM,IAArB,CAAP;EACH;EACD;EACJ;EACA;EACA;EACA;;EAjIA;EAAA;EAAA,2BAkIW;EAAA;;EACH,UAAI8L,SAAJ;;EACA,UAAI,KAAK9L,IAAL,CAAUiL,eAAV,IACAL,MAAM,CAACsB,qBADP,IAEA,KAAK3C,UAAL,CAAgBjE,OAAhB,CAAwB,WAAxB,MAAyC,CAAC,CAF9C,EAEiD;EAC7CwG,QAAAA,SAAS,GAAG,WAAZ;EACH,OAJD,MAKK,IAAI,MAAM,KAAKvC,UAAL,CAAgBjN,MAA1B,EAAkC;EACnC;EACA,aAAK4D,YAAL,CAAkB,YAAM;EACpB,UAAA,MAAI,CAACrB,YAAL,CAAkB,OAAlB,EAA2B,yBAA3B;EACH,SAFD,EAEG,CAFH;EAGA;EACH,OANI,MAOA;EACDiN,QAAAA,SAAS,GAAG,KAAKvC,UAAL,CAAgB,CAAhB,CAAZ;EACH;;EACD,WAAK/H,UAAL,GAAkB,SAAlB,CAjBG;;EAmBH,UAAI;EACAsK,QAAAA,SAAS,GAAG,KAAKK,eAAL,CAAqBL,SAArB,CAAZ;EACH,OAFD,CAGA,OAAOvI,CAAP,EAAU;EACN,aAAKgG,UAAL,CAAgB6C,KAAhB;EACA,aAAKhG,IAAL;EACA;EACH;;EACD0F,MAAAA,SAAS,CAAC1F,IAAV;EACA,WAAKiG,YAAL,CAAkBP,SAAlB;EACH;EACD;EACJ;EACA;EACA;EACA;;EApKA;EAAA;EAAA,iCAqKiBA,SArKjB,EAqK4B;EAAA;;EACpB,UAAI,KAAKA,SAAT,EAAoB;EAChB,aAAKA,SAAL,CAAe1N,kBAAf;EACH,OAHmB;;;EAKpB,WAAK0N,SAAL,GAAiBA,SAAjB,CALoB;;EAOpBA,MAAAA,SAAS,CACJpO,EADL,CACQ,OADR,EACiB,KAAK4O,OAAL,CAAanM,IAAb,CAAkB,IAAlB,CADjB,EAEKzC,EAFL,CAEQ,QAFR,EAEkB,KAAKmE,QAAL,CAAc1B,IAAd,CAAmB,IAAnB,CAFlB,EAGKzC,EAHL,CAGQ,OAHR,EAGiB,KAAKoI,OAAL,CAAa3F,IAAb,CAAkB,IAAlB,CAHjB,EAIKzC,EAJL,CAIQ,OAJR,EAIiB,UAACsD,MAAD;EAAA,eAAY,MAAI,CAACW,OAAL,CAAa,iBAAb,EAAgCX,MAAhC,CAAZ;EAAA,OAJjB;EAKH;EACD;EACJ;EACA;EACA;EACA;EACA;;EAvLA;EAAA;EAAA,0BAwLUgL,IAxLV,EAwLgB;EAAA;;EACR,UAAIF,SAAS,GAAG,KAAKK,eAAL,CAAqBH,IAArB,CAAhB;EACA,UAAIO,MAAM,GAAG,KAAb;EACA3B,MAAAA,MAAM,CAACsB,qBAAP,GAA+B,KAA/B;;EACA,UAAMM,eAAe,GAAG,SAAlBA,eAAkB,GAAM;EAC1B,YAAID,MAAJ,EACI;EACJT,QAAAA,SAAS,CAAChF,IAAV,CAAe,CAAC;EAAEzM,UAAAA,IAAI,EAAE,MAAR;EAAgBC,UAAAA,IAAI,EAAE;EAAtB,SAAD,CAAf;EACAwR,QAAAA,SAAS,CAAC/N,IAAV,CAAe,QAAf,EAAyB,UAAC0O,GAAD,EAAS;EAC9B,cAAIF,MAAJ,EACI;;EACJ,cAAI,WAAWE,GAAG,CAACpS,IAAf,IAAuB,YAAYoS,GAAG,CAACnS,IAA3C,EAAiD;EAC7C,YAAA,MAAI,CAACoS,SAAL,GAAiB,IAAjB;;EACA,YAAA,MAAI,CAAC7N,YAAL,CAAkB,WAAlB,EAA+BiN,SAA/B;;EACA,gBAAI,CAACA,SAAL,EACI;EACJlB,YAAAA,MAAM,CAACsB,qBAAP,GAA+B,gBAAgBJ,SAAS,CAACE,IAAzD;;EACA,YAAA,MAAI,CAACF,SAAL,CAAetH,KAAf,CAAqB,YAAM;EACvB,kBAAI+H,MAAJ,EACI;EACJ,kBAAI,aAAa,MAAI,CAAC/K,UAAtB,EACI;EACJ2F,cAAAA,OAAO;;EACP,cAAA,MAAI,CAACkF,YAAL,CAAkBP,SAAlB;;EACAA,cAAAA,SAAS,CAAChF,IAAV,CAAe,CAAC;EAAEzM,gBAAAA,IAAI,EAAE;EAAR,eAAD,CAAf;;EACA,cAAA,MAAI,CAACwE,YAAL,CAAkB,SAAlB,EAA6BiN,SAA7B;;EACAA,cAAAA,SAAS,GAAG,IAAZ;EACA,cAAA,MAAI,CAACY,SAAL,GAAiB,KAAjB;;EACA,cAAA,MAAI,CAACC,KAAL;EACH,aAZD;EAaH,WAnBD,MAoBK;EACD,gBAAMxJ,GAAG,GAAG,IAAIhC,KAAJ,CAAU,aAAV,CAAZ,CADC;;EAGDgC,YAAAA,GAAG,CAAC2I,SAAJ,GAAgBA,SAAS,CAACE,IAA1B;;EACA,YAAA,MAAI,CAACnN,YAAL,CAAkB,cAAlB,EAAkCsE,GAAlC;EACH;EACJ,SA7BD;EA8BH,OAlCD;;EAmCA,eAASyJ,eAAT,GAA2B;EACvB,YAAIL,MAAJ,EACI,OAFmB;;EAIvBA,QAAAA,MAAM,GAAG,IAAT;EACApF,QAAAA,OAAO;EACP2E,QAAAA,SAAS,CAAClH,KAAV;EACAkH,QAAAA,SAAS,GAAG,IAAZ;EACH,OA/CO;;;EAiDR,UAAMzC,OAAO,GAAG,SAAVA,OAAU,CAAClG,GAAD,EAAS;EACrB,YAAM0J,KAAK,GAAG,IAAI1L,KAAJ,CAAU,kBAAkBgC,GAA5B,CAAd,CADqB;;EAGrB0J,QAAAA,KAAK,CAACf,SAAN,GAAkBA,SAAS,CAACE,IAA5B;EACAY,QAAAA,eAAe;;EACf,QAAA,MAAI,CAAC/N,YAAL,CAAkB,cAAlB,EAAkCgO,KAAlC;EACH,OAND;;EAOA,eAASC,gBAAT,GAA4B;EACxBzD,QAAAA,OAAO,CAAC,kBAAD,CAAP;EACH,OA1DO;;;EA4DR,eAASJ,OAAT,GAAmB;EACfI,QAAAA,OAAO,CAAC,eAAD,CAAP;EACH,OA9DO;;;EAgER,eAAS0D,SAAT,CAAmBC,EAAnB,EAAuB;EACnB,YAAIlB,SAAS,IAAIkB,EAAE,CAAChB,IAAH,KAAYF,SAAS,CAACE,IAAvC,EAA6C;EACzCY,UAAAA,eAAe;EAClB;EACJ,OApEO;;;EAsER,UAAMzF,OAAO,GAAG,SAAVA,OAAU,GAAM;EAClB2E,QAAAA,SAAS,CAAC3N,cAAV,CAAyB,MAAzB,EAAiCqO,eAAjC;EACAV,QAAAA,SAAS,CAAC3N,cAAV,CAAyB,OAAzB,EAAkCkL,OAAlC;EACAyC,QAAAA,SAAS,CAAC3N,cAAV,CAAyB,OAAzB,EAAkC2O,gBAAlC;;EACA,QAAA,MAAI,CAAC9O,GAAL,CAAS,OAAT,EAAkBiL,OAAlB;;EACA,QAAA,MAAI,CAACjL,GAAL,CAAS,WAAT,EAAsB+O,SAAtB;EACH,OAND;;EAOAjB,MAAAA,SAAS,CAAC/N,IAAV,CAAe,MAAf,EAAuByO,eAAvB;EACAV,MAAAA,SAAS,CAAC/N,IAAV,CAAe,OAAf,EAAwBsL,OAAxB;EACAyC,MAAAA,SAAS,CAAC/N,IAAV,CAAe,OAAf,EAAwB+O,gBAAxB;EACA,WAAK/O,IAAL,CAAU,OAAV,EAAmBkL,OAAnB;EACA,WAAKlL,IAAL,CAAU,WAAV,EAAuBgP,SAAvB;EACAjB,MAAAA,SAAS,CAAC1F,IAAV;EACH;EACD;EACJ;EACA;EACA;EACA;;EAhRA;EAAA;EAAA,6BAiRa;EACL,WAAK5E,UAAL,GAAkB,MAAlB;EACAoJ,MAAAA,MAAM,CAACsB,qBAAP,GAA+B,gBAAgB,KAAKJ,SAAL,CAAeE,IAA9D;EACA,WAAKnN,YAAL,CAAkB,MAAlB;EACA,WAAK8N,KAAL,GAJK;EAML;;EACA,UAAI,WAAW,KAAKnL,UAAhB,IAA8B,KAAKxB,IAAL,CAAUgL,OAA5C,EAAqD;EACjD,YAAIpP,CAAC,GAAG,CAAR;EACA,YAAMiF,CAAC,GAAG,KAAK4K,QAAL,CAAcnP,MAAxB;;EACA,eAAOV,CAAC,GAAGiF,CAAX,EAAcjF,CAAC,EAAf,EAAmB;EACf,eAAKqR,KAAL,CAAW,KAAKxB,QAAL,CAAc7P,CAAd,CAAX;EACH;EACJ;EACJ;EACD;EACJ;EACA;EACA;EACA;;EApSA;EAAA;EAAA,6BAqSaqB,MArSb,EAqSqB;EACb,UAAI,cAAc,KAAKuE,UAAnB,IACA,WAAW,KAAKA,UADhB,IAEA,cAAc,KAAKA,UAFvB,EAEmC;EAC/B,aAAK3C,YAAL,CAAkB,QAAlB,EAA4B5B,MAA5B,EAD+B;;EAG/B,aAAK4B,YAAL,CAAkB,WAAlB;;EACA,gBAAQ5B,MAAM,CAAC5C,IAAf;EACI,eAAK,MAAL;EACI,iBAAK6S,WAAL,CAAiBC,IAAI,CAACxD,KAAL,CAAW1M,MAAM,CAAC3C,IAAlB,CAAjB;EACA;;EACJ,eAAK,MAAL;EACI,iBAAK8S,gBAAL;EACA,iBAAKC,UAAL,CAAgB,MAAhB;EACA,iBAAKxO,YAAL,CAAkB,MAAlB;EACA,iBAAKA,YAAL,CAAkB,MAAlB;EACA;;EACJ,eAAK,OAAL;EACI,gBAAMsE,GAAG,GAAG,IAAIhC,KAAJ,CAAU,cAAV,CAAZ,CADJ;;EAGIgC,YAAAA,GAAG,CAACmK,IAAJ,GAAWrQ,MAAM,CAAC3C,IAAlB;EACA,iBAAKwL,OAAL,CAAa3C,GAAb;EACA;;EACJ,eAAK,SAAL;EACI,iBAAKtE,YAAL,CAAkB,MAAlB,EAA0B5B,MAAM,CAAC3C,IAAjC;EACA,iBAAKuE,YAAL,CAAkB,SAAlB,EAA6B5B,MAAM,CAAC3C,IAApC;EACA;EAnBR;EAqBH;EAGJ;EACD;EACJ;EACA;EACA;EACA;EACA;;EA1UA;EAAA;EAAA,gCA2UgBA,IA3UhB,EA2UsB;EACd,WAAKuE,YAAL,CAAkB,WAAlB,EAA+BvE,IAA/B;EACA,WAAKkR,EAAL,GAAUlR,IAAI,CAAC2K,GAAf;EACA,WAAK6G,SAAL,CAAexK,KAAf,CAAqB2D,GAArB,GAA2B3K,IAAI,CAAC2K,GAAhC;EACA,WAAKwG,QAAL,GAAgB,KAAK8B,cAAL,CAAoBjT,IAAI,CAACmR,QAAzB,CAAhB;EACA,WAAKC,YAAL,GAAoBpR,IAAI,CAACoR,YAAzB;EACA,WAAKC,WAAL,GAAmBrR,IAAI,CAACqR,WAAxB;EACA,WAAK6B,UAAL,GAAkBlT,IAAI,CAACkT,UAAvB;EACA,WAAK7I,MAAL,GARc;;EAUd,UAAI,aAAa,KAAKnD,UAAtB,EACI;EACJ,WAAK4L,gBAAL;EACH;EACD;EACJ;EACA;EACA;EACA;;EA7VA;EAAA;EAAA,uCA8VuB;EAAA;;EACf,WAAKhN,cAAL,CAAoB,KAAKwL,gBAAzB;EACA,WAAKA,gBAAL,GAAwB,KAAK1L,YAAL,CAAkB,YAAM;EAC5C,QAAA,MAAI,CAACyB,OAAL,CAAa,cAAb;EACH,OAFuB,EAErB,KAAK+J,YAAL,GAAoB,KAAKC,WAFJ,CAAxB;;EAGA,UAAI,KAAK3L,IAAL,CAAU8I,SAAd,EAAyB;EACrB,aAAK8C,gBAAL,CAAsB5C,KAAtB;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;;EA3WA;EAAA;EAAA,8BA4Wc;EACN,WAAK6B,WAAL,CAAiBrM,MAAjB,CAAwB,CAAxB,EAA2B,KAAKsM,aAAhC,EADM;EAGN;EACA;;EACA,WAAKA,aAAL,GAAqB,CAArB;;EACA,UAAI,MAAM,KAAKD,WAAL,CAAiBvO,MAA3B,EAAmC;EAC/B,aAAKuC,YAAL,CAAkB,OAAlB;EACH,OAFD,MAGK;EACD,aAAK8N,KAAL;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;;EA7XA;EAAA;EAAA,4BA8XY;EACJ,UAAI,aAAa,KAAKnL,UAAlB,IACA,KAAKsK,SAAL,CAAezK,QADf,IAEA,CAAC,KAAKqL,SAFN,IAGA,KAAK7B,WAAL,CAAiBvO,MAHrB,EAG6B;EACzB,YAAMO,OAAO,GAAG,KAAK4Q,kBAAL,EAAhB;EACA,aAAK3B,SAAL,CAAehF,IAAf,CAAoBjK,OAApB,EAFyB;EAIzB;;EACA,aAAKiO,aAAL,GAAqBjO,OAAO,CAACP,MAA7B;EACA,aAAKuC,YAAL,CAAkB,OAAlB;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;EACA;;EAhZA;EAAA;EAAA,yCAiZyB;EACjB,UAAM6O,sBAAsB,GAAG,KAAKF,UAAL,IAC3B,KAAK1B,SAAL,CAAeE,IAAf,KAAwB,SADG,IAE3B,KAAKnB,WAAL,CAAiBvO,MAAjB,GAA0B,CAF9B;;EAGA,UAAI,CAACoR,sBAAL,EAA6B;EACzB,eAAO,KAAK7C,WAAZ;EACH;;EACD,UAAI8C,WAAW,GAAG,CAAlB,CAPiB;;EAQjB,WAAK,IAAI/R,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKiP,WAAL,CAAiBvO,MAArC,EAA6CV,CAAC,EAA9C,EAAkD;EAC9C,YAAMtB,IAAI,GAAG,KAAKuQ,WAAL,CAAiBjP,CAAjB,EAAoBtB,IAAjC;;EACA,YAAIA,IAAJ,EAAU;EACNqT,UAAAA,WAAW,IAAIrN,UAAU,CAAChG,IAAD,CAAzB;EACH;;EACD,YAAIsB,CAAC,GAAG,CAAJ,IAAS+R,WAAW,GAAG,KAAKH,UAAhC,EAA4C;EACxC,iBAAO,KAAK3C,WAAL,CAAiBlM,KAAjB,CAAuB,CAAvB,EAA0B/C,CAA1B,CAAP;EACH;;EACD+R,QAAAA,WAAW,IAAI,CAAf,CAR8C;EASjD;;EACD,aAAO,KAAK9C,WAAZ;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;;EA5aA;EAAA;EAAA,0BA6aU4B,GA7aV,EA6aemB,OA7af,EA6awB/P,EA7axB,EA6a4B;EACpB,WAAKwP,UAAL,CAAgB,SAAhB,EAA2BZ,GAA3B,EAAgCmB,OAAhC,EAAyC/P,EAAzC;EACA,aAAO,IAAP;EACH;EAhbL;EAAA;EAAA,yBAibS4O,GAjbT,EAibcmB,OAjbd,EAibuB/P,EAjbvB,EAib2B;EACnB,WAAKwP,UAAL,CAAgB,SAAhB,EAA2BZ,GAA3B,EAAgCmB,OAAhC,EAAyC/P,EAAzC;EACA,aAAO,IAAP;EACH;EACD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EA7bA;EAAA;EAAA,+BA8bexD,IA9bf,EA8bqBC,IA9brB,EA8b2BsT,OA9b3B,EA8boC/P,EA9bpC,EA8bwC;EAChC,UAAI,eAAe,OAAOvD,IAA1B,EAAgC;EAC5BuD,QAAAA,EAAE,GAAGvD,IAAL;EACAA,QAAAA,IAAI,GAAG4L,SAAP;EACH;;EACD,UAAI,eAAe,OAAO0H,OAA1B,EAAmC;EAC/B/P,QAAAA,EAAE,GAAG+P,OAAL;EACAA,QAAAA,OAAO,GAAG,IAAV;EACH;;EACD,UAAI,cAAc,KAAKpM,UAAnB,IAAiC,aAAa,KAAKA,UAAvD,EAAmE;EAC/D;EACH;;EACDoM,MAAAA,OAAO,GAAGA,OAAO,IAAI,EAArB;EACAA,MAAAA,OAAO,CAACC,QAAR,GAAmB,UAAUD,OAAO,CAACC,QAArC;EACA,UAAM5Q,MAAM,GAAG;EACX5C,QAAAA,IAAI,EAAEA,IADK;EAEXC,QAAAA,IAAI,EAAEA,IAFK;EAGXsT,QAAAA,OAAO,EAAEA;EAHE,OAAf;EAKA,WAAK/O,YAAL,CAAkB,cAAlB,EAAkC5B,MAAlC;EACA,WAAK4N,WAAL,CAAiBvN,IAAjB,CAAsBL,MAAtB;EACA,UAAIY,EAAJ,EACI,KAAKE,IAAL,CAAU,OAAV,EAAmBF,EAAnB;EACJ,WAAK8O,KAAL;EACH;EACD;EACJ;EACA;;EAzdA;EAAA;EAAA,4BA0dY;EAAA;;EACJ,UAAM/H,KAAK,GAAG,SAARA,KAAQ,GAAM;EAChB,QAAA,MAAI,CAACjD,OAAL,CAAa,cAAb;;EACA,QAAA,MAAI,CAACmK,SAAL,CAAelH,KAAf;EACH,OAHD;;EAIA,UAAMkJ,eAAe,GAAG,SAAlBA,eAAkB,GAAM;EAC1B,QAAA,MAAI,CAAC9P,GAAL,CAAS,SAAT,EAAoB8P,eAApB;;EACA,QAAA,MAAI,CAAC9P,GAAL,CAAS,cAAT,EAAyB8P,eAAzB;;EACAlJ,QAAAA,KAAK;EACR,OAJD;;EAKA,UAAMmJ,cAAc,GAAG,SAAjBA,cAAiB,GAAM;EACzB;EACA,QAAA,MAAI,CAAChQ,IAAL,CAAU,SAAV,EAAqB+P,eAArB;;EACA,QAAA,MAAI,CAAC/P,IAAL,CAAU,cAAV,EAA0B+P,eAA1B;EACH,OAJD;;EAKA,UAAI,cAAc,KAAKtM,UAAnB,IAAiC,WAAW,KAAKA,UAArD,EAAiE;EAC7D,aAAKA,UAAL,GAAkB,SAAlB;;EACA,YAAI,KAAKqJ,WAAL,CAAiBvO,MAArB,EAA6B;EACzB,eAAKyB,IAAL,CAAU,OAAV,EAAmB,YAAM;EACrB,gBAAI,MAAI,CAAC2O,SAAT,EAAoB;EAChBqB,cAAAA,cAAc;EACjB,aAFD,MAGK;EACDnJ,cAAAA,KAAK;EACR;EACJ,WAPD;EAQH,SATD,MAUK,IAAI,KAAK8H,SAAT,EAAoB;EACrBqB,UAAAA,cAAc;EACjB,SAFI,MAGA;EACDnJ,UAAAA,KAAK;EACR;EACJ;;EACD,aAAO,IAAP;EACH;EACD;EACJ;EACA;EACA;EACA;;EAlgBA;EAAA;EAAA,4BAmgBYzB,GAngBZ,EAmgBiB;EACTyH,MAAAA,MAAM,CAACsB,qBAAP,GAA+B,KAA/B;EACA,WAAKrN,YAAL,CAAkB,OAAlB,EAA2BsE,GAA3B;EACA,WAAKxB,OAAL,CAAa,iBAAb,EAAgCwB,GAAhC;EACH;EACD;EACJ;EACA;EACA;EACA;;EA5gBA;EAAA;EAAA,4BA6gBYnC,MA7gBZ,EA6gBoBC,WA7gBpB,EA6gBiC;EACzB,UAAI,cAAc,KAAKO,UAAnB,IACA,WAAW,KAAKA,UADhB,IAEA,cAAc,KAAKA,UAFvB,EAEmC;EAC/B;EACA,aAAKpB,cAAL,CAAoB,KAAKwL,gBAAzB,EAF+B;;EAI/B,aAAKE,SAAL,CAAe1N,kBAAf,CAAkC,OAAlC,EAJ+B;;EAM/B,aAAK0N,SAAL,CAAelH,KAAf,GAN+B;;EAQ/B,aAAKkH,SAAL,CAAe1N,kBAAf;;EACA,YAAI,OAAOC,mBAAP,KAA+B,UAAnC,EAA+C;EAC3CA,UAAAA,mBAAmB,CAAC,cAAD,EAAiB,KAAKwN,yBAAtB,EAAiD,KAAjD,CAAnB;EACAxN,UAAAA,mBAAmB,CAAC,SAAD,EAAY,KAAK0N,oBAAjB,EAAuC,KAAvC,CAAnB;EACH,SAZ8B;;;EAc/B,aAAKvK,UAAL,GAAkB,QAAlB,CAd+B;;EAgB/B,aAAKgK,EAAL,GAAU,IAAV,CAhB+B;;EAkB/B,aAAK3M,YAAL,CAAkB,OAAlB,EAA2BmC,MAA3B,EAAmCC,WAAnC,EAlB+B;EAoB/B;;EACA,aAAK4J,WAAL,GAAmB,EAAnB;EACA,aAAKC,aAAL,GAAqB,CAArB;EACH;EACJ;EACD;EACJ;EACA;EACA;EACA;EACA;;EA9iBA;EAAA;EAAA,mCA+iBmBW,QA/iBnB,EA+iB6B;EACrB,UAAMuC,gBAAgB,GAAG,EAAzB;EACA,UAAIpS,CAAC,GAAG,CAAR;EACA,UAAMqS,CAAC,GAAGxC,QAAQ,CAACnP,MAAnB;;EACA,aAAOV,CAAC,GAAGqS,CAAX,EAAcrS,CAAC,EAAf,EAAmB;EACf,YAAI,CAAC,KAAK2N,UAAL,CAAgBjE,OAAhB,CAAwBmG,QAAQ,CAAC7P,CAAD,CAAhC,CAAL,EACIoS,gBAAgB,CAAC1Q,IAAjB,CAAsBmO,QAAQ,CAAC7P,CAAD,CAA9B;EACP;;EACD,aAAOoS,gBAAP;EACH;EAxjBL;;EAAA;EAAA,EAA4BxQ,OAA5B;EA0jBAoN,MAAM,CAACrN,QAAP,GAAkBA,QAAlB;;AC/jBA,2BAAe,UAACkI,GAAD,EAAMzF,IAAN;EAAA,SAAe,IAAI4K,MAAJ,CAAWnF,GAAX,EAAgBzF,IAAhB,CAAf;EAAA,CAAf;;;;;;;;"}