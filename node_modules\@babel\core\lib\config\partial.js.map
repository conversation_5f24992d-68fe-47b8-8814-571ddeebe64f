{"version": 3, "names": ["_path", "data", "require", "_gensync", "_plugin", "_util", "_item", "_config<PERSON><PERSON>n", "_environment", "_options", "_files", "_resolveTargets", "_excluded", "_objectWithoutPropertiesLoose", "source", "excluded", "target", "sourceKeys", "Object", "keys", "key", "i", "length", "indexOf", "resolveRootMode", "rootDir", "rootMode", "upwardRootDir", "findConfigUpwards", "assign", "Error", "ROOT_CONFIG_FILENAMES", "join", "code", "dirname", "loadPrivatePartialConfig", "inputOpts", "Array", "isArray", "args", "validate", "envName", "getEnv", "cwd", "root", "caller", "cloneInputAst", "absoluteCwd", "path", "resolve", "absoluteRootDir", "filename", "undefined", "showConfigPath", "resolveShowConfigPath", "context", "showConfig", "config<PERSON><PERSON><PERSON>", "buildRootChain", "merged", "assumptions", "options", "for<PERSON>ach", "opts", "mergeOptions", "targets", "resolveTargets", "babelrc", "configFile", "browserslistConfigFile", "passPerPreset", "plugins", "map", "descriptor", "createItemFromDescriptor", "presets", "fileHandling", "ignore", "config", "files", "loadPartialConfig", "gens<PERSON>", "showIgnoredFiles", "_opts", "result", "item", "value", "Plugin", "PartialConfig", "filepath", "exports", "constructor", "babelignore", "freeze", "hasFilesystemConfig", "prototype"], "sources": ["../../src/config/partial.ts"], "sourcesContent": ["import path from \"path\";\nimport gensync from \"gensync\";\nimport type { <PERSON><PERSON> } from \"gensync\";\nimport Plugin from \"./plugin\";\nimport { mergeOptions } from \"./util\";\nimport { createItemFromDescriptor } from \"./item\";\nimport { buildRoot<PERSON>hain } from \"./config-chain\";\nimport type { ConfigContext, FileHandling } from \"./config-chain\";\nimport { getEnv } from \"./helpers/environment\";\nimport { validate } from \"./validation/options\";\n\nimport type {\n  ValidatedOptions,\n  NormalizedOptions,\n  RootMode,\n} from \"./validation/options\";\n\nimport {\n  findConfigUpwards,\n  resolveShowConfigPath,\n  ROOT_CONFIG_FILENAMES,\n} from \"./files\";\nimport type { ConfigFile, IgnoreFile } from \"./files\";\nimport { resolveTargets } from \"./resolve-targets\";\n\nfunction resolveRootMode(rootDir: string, rootMode: RootMode): string {\n  switch (rootMode) {\n    case \"root\":\n      return rootDir;\n\n    case \"upward-optional\": {\n      const upwardRootDir = findConfigUpwards(rootDir);\n      return upwardRootDir === null ? rootDir : upwardRootDir;\n    }\n\n    case \"upward\": {\n      const upwardRootDir = findConfigUpwards(rootDir);\n      if (upwardRootDir !== null) return upwardRootDir;\n\n      throw Object.assign(\n        new Error(\n          `Babel was run with rootMode:\"upward\" but a root could not ` +\n            `be found when searching upward from \"${rootDir}\".\\n` +\n            `One of the following config files must be in the directory tree: ` +\n            `\"${ROOT_CONFIG_FILENAMES.join(\", \")}\".`,\n        ) as any,\n        {\n          code: \"BABEL_ROOT_NOT_FOUND\",\n          dirname: rootDir,\n        },\n      );\n    }\n    default:\n      throw new Error(`Assertion failure - unknown rootMode value.`);\n  }\n}\n\ntype PrivPartialConfig = {\n  options: NormalizedOptions;\n  context: ConfigContext;\n  fileHandling: FileHandling;\n  ignore: IgnoreFile | void;\n  babelrc: ConfigFile | void;\n  config: ConfigFile | void;\n  files: Set<string>;\n};\n\nexport default function* loadPrivatePartialConfig(\n  inputOpts: unknown,\n): Handler<PrivPartialConfig | null> {\n  if (\n    inputOpts != null &&\n    (typeof inputOpts !== \"object\" || Array.isArray(inputOpts))\n  ) {\n    throw new Error(\"Babel options must be an object, null, or undefined\");\n  }\n\n  const args = inputOpts ? validate(\"arguments\", inputOpts) : {};\n\n  const {\n    envName = getEnv(),\n    cwd = \".\",\n    root: rootDir = \".\",\n    rootMode = \"root\",\n    caller,\n    cloneInputAst = true,\n  } = args;\n  const absoluteCwd = path.resolve(cwd);\n  const absoluteRootDir = resolveRootMode(\n    path.resolve(absoluteCwd, rootDir),\n    rootMode,\n  );\n\n  const filename =\n    typeof args.filename === \"string\"\n      ? path.resolve(cwd, args.filename)\n      : undefined;\n\n  const showConfigPath = yield* resolveShowConfigPath(absoluteCwd);\n\n  const context: ConfigContext = {\n    filename,\n    cwd: absoluteCwd,\n    root: absoluteRootDir,\n    envName,\n    caller,\n    showConfig: showConfigPath === filename,\n  };\n\n  const configChain = yield* buildRootChain(args, context);\n  if (!configChain) return null;\n\n  const merged: ValidatedOptions = {\n    assumptions: {},\n  };\n  configChain.options.forEach(opts => {\n    mergeOptions(merged as any, opts);\n  });\n\n  const options: NormalizedOptions = {\n    ...merged,\n    targets: resolveTargets(merged, absoluteRootDir),\n\n    // Tack the passes onto the object itself so that, if this object is\n    // passed back to Babel a second time, it will be in the right structure\n    // to not change behavior.\n    cloneInputAst,\n    babelrc: false,\n    configFile: false,\n    browserslistConfigFile: false,\n    passPerPreset: false,\n    envName: context.envName,\n    cwd: context.cwd,\n    root: context.root,\n    rootMode: \"root\",\n    filename:\n      typeof context.filename === \"string\" ? context.filename : undefined,\n\n    plugins: configChain.plugins.map(descriptor =>\n      createItemFromDescriptor(descriptor),\n    ),\n    presets: configChain.presets.map(descriptor =>\n      createItemFromDescriptor(descriptor),\n    ),\n  };\n\n  return {\n    options,\n    context,\n    fileHandling: configChain.fileHandling,\n    ignore: configChain.ignore,\n    babelrc: configChain.babelrc,\n    config: configChain.config,\n    files: configChain.files,\n  };\n}\n\ntype LoadPartialConfigOpts = {\n  showIgnoredFiles?: boolean;\n};\n\nexport const loadPartialConfig = gensync(function* (\n  opts?: LoadPartialConfigOpts,\n): Handler<PartialConfig | null> {\n  let showIgnoredFiles = false;\n  // We only extract showIgnoredFiles if opts is an object, so that\n  // loadPrivatePartialConfig can throw the appropriate error if it's not.\n  if (typeof opts === \"object\" && opts !== null && !Array.isArray(opts)) {\n    ({ showIgnoredFiles, ...opts } = opts);\n  }\n\n  const result: PrivPartialConfig | undefined | null =\n    yield* loadPrivatePartialConfig(opts);\n  if (!result) return null;\n\n  const { options, babelrc, ignore, config, fileHandling, files } = result;\n\n  if (fileHandling === \"ignored\" && !showIgnoredFiles) {\n    return null;\n  }\n\n  (options.plugins || []).forEach(item => {\n    // @ts-expect-error todo(flow->ts): better type annotation for `item.value`\n    if (item.value instanceof Plugin) {\n      throw new Error(\n        \"Passing cached plugin instances is not supported in \" +\n          \"babel.loadPartialConfig()\",\n      );\n    }\n  });\n\n  return new PartialConfig(\n    options,\n    babelrc ? babelrc.filepath : undefined,\n    ignore ? ignore.filepath : undefined,\n    config ? config.filepath : undefined,\n    fileHandling,\n    files,\n  );\n});\n\nexport type { PartialConfig };\n\nclass PartialConfig {\n  /**\n   * These properties are public, so any changes to them should be considered\n   * a breaking change to Babel's API.\n   */\n  options: NormalizedOptions;\n  babelrc: string | void;\n  babelignore: string | void;\n  config: string | void;\n  fileHandling: FileHandling;\n  files: Set<string>;\n\n  constructor(\n    options: NormalizedOptions,\n    babelrc: string | void,\n    ignore: string | void,\n    config: string | void,\n    fileHandling: FileHandling,\n    files: Set<string>,\n  ) {\n    this.options = options;\n    this.babelignore = ignore;\n    this.babelrc = babelrc;\n    this.config = config;\n    this.fileHandling = fileHandling;\n    this.files = files;\n\n    // Freeze since this is a public API and it should be extremely obvious that\n    // reassigning properties on here does nothing.\n    Object.freeze(this);\n  }\n\n  /**\n   * Returns true if there is a config file in the filesystem for this config.\n   */\n  hasFilesystemConfig(): boolean {\n    return this.babelrc !== undefined || this.config !== undefined;\n  }\n}\nObject.freeze(PartialConfig.prototype);\n"], "mappings": ";;;;;;;AAAA,SAAAA,MAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,KAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,SAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,QAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAG,OAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,YAAA,GAAAL,OAAA;AAEA,IAAAM,YAAA,GAAAN,OAAA;AACA,IAAAO,QAAA,GAAAP,OAAA;AAQA,IAAAQ,MAAA,GAAAR,OAAA;AAMA,IAAAS,eAAA,GAAAT,OAAA;AAAmD,MAAAU,SAAA;AAAA,SAAAC,8BAAAC,MAAA,EAAAC,QAAA,QAAAD,MAAA,yBAAAE,MAAA,WAAAC,UAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAL,MAAA,OAAAM,GAAA,EAAAC,CAAA,OAAAA,CAAA,MAAAA,CAAA,GAAAJ,UAAA,CAAAK,MAAA,EAAAD,CAAA,MAAAD,GAAA,GAAAH,UAAA,CAAAI,CAAA,OAAAN,QAAA,CAAAQ,OAAA,CAAAH,GAAA,kBAAAJ,MAAA,CAAAI,GAAA,IAAAN,MAAA,CAAAM,GAAA,YAAAJ,MAAA;AAEnD,SAASQ,eAAeA,CAACC,OAAe,EAAEC,QAAkB,EAAU;EACpE,QAAQA,QAAQ;IACd,KAAK,MAAM;MACT,OAAOD,OAAO;IAEhB,KAAK,iBAAiB;MAAE;QACtB,MAAME,aAAa,GAAG,IAAAC,wBAAiB,EAACH,OAAO,CAAC;QAChD,OAAOE,aAAa,KAAK,IAAI,GAAGF,OAAO,GAAGE,aAAa;MACzD;IAEA,KAAK,QAAQ;MAAE;QACb,MAAMA,aAAa,GAAG,IAAAC,wBAAiB,EAACH,OAAO,CAAC;QAChD,IAAIE,aAAa,KAAK,IAAI,EAAE,OAAOA,aAAa;QAEhD,MAAMT,MAAM,CAACW,MAAM,CACjB,IAAIC,KAAK,CACN,4DAA2D,GACzD,wCAAuCL,OAAQ,MAAK,GACpD,mEAAkE,GAClE,IAAGM,4BAAqB,CAACC,IAAI,CAAC,IAAI,CAAE,IAAG,CAC3C,EACD;UACEC,IAAI,EAAE,sBAAsB;UAC5BC,OAAO,EAAET;QACX,CAAC,CACF;MACH;IACA;MACE,MAAM,IAAIK,KAAK,CAAE,6CAA4C,CAAC;EAAC;AAErE;AAYe,UAAUK,wBAAwBA,CAC/CC,SAAkB,EACiB;EACnC,IACEA,SAAS,IAAI,IAAI,KAChB,OAAOA,SAAS,KAAK,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,CAAC,EAC3D;IACA,MAAM,IAAIN,KAAK,CAAC,qDAAqD,CAAC;EACxE;EAEA,MAAMS,IAAI,GAAGH,SAAS,GAAG,IAAAI,iBAAQ,EAAC,WAAW,EAAEJ,SAAS,CAAC,GAAG,CAAC,CAAC;EAE9D,MAAM;IACJK,OAAO,GAAG,IAAAC,mBAAM,GAAE;IAClBC,GAAG,GAAG,GAAG;IACTC,IAAI,EAAEnB,OAAO,GAAG,GAAG;IACnBC,QAAQ,GAAG,MAAM;IACjBmB,MAAM;IACNC,aAAa,GAAG;EAClB,CAAC,GAAGP,IAAI;EACR,MAAMQ,WAAW,GAAGC,OAAI,CAACC,OAAO,CAACN,GAAG,CAAC;EACrC,MAAMO,eAAe,GAAG1B,eAAe,CACrCwB,OAAI,CAACC,OAAO,CAACF,WAAW,EAAEtB,OAAO,CAAC,EAClCC,QAAQ,CACT;EAED,MAAMyB,QAAQ,GACZ,OAAOZ,IAAI,CAACY,QAAQ,KAAK,QAAQ,GAC7BH,OAAI,CAACC,OAAO,CAACN,GAAG,EAAEJ,IAAI,CAACY,QAAQ,CAAC,GAChCC,SAAS;EAEf,MAAMC,cAAc,GAAG,OAAO,IAAAC,4BAAqB,EAACP,WAAW,CAAC;EAEhE,MAAMQ,OAAsB,GAAG;IAC7BJ,QAAQ;IACRR,GAAG,EAAEI,WAAW;IAChBH,IAAI,EAAEM,eAAe;IACrBT,OAAO;IACPI,MAAM;IACNW,UAAU,EAAEH,cAAc,KAAKF;EACjC,CAAC;EAED,MAAMM,WAAW,GAAG,OAAO,IAAAC,2BAAc,EAACnB,IAAI,EAAEgB,OAAO,CAAC;EACxD,IAAI,CAACE,WAAW,EAAE,OAAO,IAAI;EAE7B,MAAME,MAAwB,GAAG;IAC/BC,WAAW,EAAE,CAAC;EAChB,CAAC;EACDH,WAAW,CAACI,OAAO,CAACC,OAAO,CAACC,IAAI,IAAI;IAClC,IAAAC,kBAAY,EAACL,MAAM,EAASI,IAAI,CAAC;EACnC,CAAC,CAAC;EAEF,MAAMF,OAA0B,GAAA3C,MAAA,CAAAW,MAAA,KAC3B8B,MAAM;IACTM,OAAO,EAAE,IAAAC,8BAAc,EAACP,MAAM,EAAET,eAAe,CAAC;IAKhDJ,aAAa;IACbqB,OAAO,EAAE,KAAK;IACdC,UAAU,EAAE,KAAK;IACjBC,sBAAsB,EAAE,KAAK;IAC7BC,aAAa,EAAE,KAAK;IACpB7B,OAAO,EAAEc,OAAO,CAACd,OAAO;IACxBE,GAAG,EAAEY,OAAO,CAACZ,GAAG;IAChBC,IAAI,EAAEW,OAAO,CAACX,IAAI;IAClBlB,QAAQ,EAAE,MAAM;IAChByB,QAAQ,EACN,OAAOI,OAAO,CAACJ,QAAQ,KAAK,QAAQ,GAAGI,OAAO,CAACJ,QAAQ,GAAGC,SAAS;IAErEmB,OAAO,EAAEd,WAAW,CAACc,OAAO,CAACC,GAAG,CAACC,UAAU,IACzC,IAAAC,8BAAwB,EAACD,UAAU,CAAC,CACrC;IACDE,OAAO,EAAElB,WAAW,CAACkB,OAAO,CAACH,GAAG,CAACC,UAAU,IACzC,IAAAC,8BAAwB,EAACD,UAAU,CAAC;EACrC,EACF;EAED,OAAO;IACLZ,OAAO;IACPN,OAAO;IACPqB,YAAY,EAAEnB,WAAW,CAACmB,YAAY;IACtCC,MAAM,EAAEpB,WAAW,CAACoB,MAAM;IAC1BV,OAAO,EAAEV,WAAW,CAACU,OAAO;IAC5BW,MAAM,EAAErB,WAAW,CAACqB,MAAM;IAC1BC,KAAK,EAAEtB,WAAW,CAACsB;EACrB,CAAC;AACH;AAMO,MAAMC,iBAAiB,GAAGC,UAAO,CAAC,WACvClB,IAA4B,EACG;EAC/B,IAAImB,gBAAgB,GAAG,KAAK;EAG5B,IAAI,OAAOnB,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,IAAI,CAAC1B,KAAK,CAACC,OAAO,CAACyB,IAAI,CAAC,EAAE;IAAA,IAAAoB,KAAA,GACpCpB,IAAI;IAAA,CAApC;MAAEmB;IAA0B,CAAC,GAAAC,KAAO;IAAbpB,IAAI,GAAAlD,6BAAA,CAAAsE,KAAA,EAAAvE,SAAA;IAAAuE,KAAA;EAC9B;EAEA,MAAMC,MAA4C,GAChD,OAAOjD,wBAAwB,CAAC4B,IAAI,CAAC;EACvC,IAAI,CAACqB,MAAM,EAAE,OAAO,IAAI;EAExB,MAAM;IAAEvB,OAAO;IAAEM,OAAO;IAAEU,MAAM;IAAEC,MAAM;IAAEF,YAAY;IAAEG;EAAM,CAAC,GAAGK,MAAM;EAExE,IAAIR,YAAY,KAAK,SAAS,IAAI,CAACM,gBAAgB,EAAE;IACnD,OAAO,IAAI;EACb;EAEA,CAACrB,OAAO,CAACU,OAAO,IAAI,EAAE,EAAET,OAAO,CAACuB,IAAI,IAAI;IAEtC,IAAIA,IAAI,CAACC,KAAK,YAAYC,eAAM,EAAE;MAChC,MAAM,IAAIzD,KAAK,CACb,sDAAsD,GACpD,2BAA2B,CAC9B;IACH;EACF,CAAC,CAAC;EAEF,OAAO,IAAI0D,aAAa,CACtB3B,OAAO,EACPM,OAAO,GAAGA,OAAO,CAACsB,QAAQ,GAAGrC,SAAS,EACtCyB,MAAM,GAAGA,MAAM,CAACY,QAAQ,GAAGrC,SAAS,EACpC0B,MAAM,GAAGA,MAAM,CAACW,QAAQ,GAAGrC,SAAS,EACpCwB,YAAY,EACZG,KAAK,CACN;AACH,CAAC,CAAC;AAACW,OAAA,CAAAV,iBAAA,GAAAA,iBAAA;AAIH,MAAMQ,aAAa,CAAC;EAYlBG,WAAWA,CACT9B,OAA0B,EAC1BM,OAAsB,EACtBU,MAAqB,EACrBC,MAAqB,EACrBF,YAA0B,EAC1BG,KAAkB,EAClB;IAAA,KAdFlB,OAAO;IAAA,KACPM,OAAO;IAAA,KACPyB,WAAW;IAAA,KACXd,MAAM;IAAA,KACNF,YAAY;IAAA,KACZG,KAAK;IAUH,IAAI,CAAClB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC+B,WAAW,GAAGf,MAAM;IACzB,IAAI,CAACV,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACW,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACF,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACG,KAAK,GAAGA,KAAK;IAIlB7D,MAAM,CAAC2E,MAAM,CAAC,IAAI,CAAC;EACrB;EAKAC,mBAAmBA,CAAA,EAAY;IAC7B,OAAO,IAAI,CAAC3B,OAAO,KAAKf,SAAS,IAAI,IAAI,CAAC0B,MAAM,KAAK1B,SAAS;EAChE;AACF;AACAlC,MAAM,CAAC2E,MAAM,CAACL,aAAa,CAACO,SAAS,CAAC;AAAC"}