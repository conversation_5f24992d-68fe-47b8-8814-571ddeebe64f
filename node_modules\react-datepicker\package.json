{"author": "HackerOne", "name": "react-datepicker", "description": "A simple and reusable datepicker component for React", "version": "4.11.0", "license": "MIT", "homepage": "https://github.com/Hacker0x01/react-datepicker", "main": "dist/index.js", "browser": "dist/react-datepicker.min.js", "module": "dist/es/index.js", "unpkg": "dist/react-datepicker.min.js", "style": "dist/react-datepicker.min.css", "files": ["*.md", "dist", "lib", "es", "src/stylesheets"], "sideEffects": ["**/*.css"], "keywords": ["react", "datepicker", "calendar", "date", "react-component"], "repository": {"type": "git", "url": "git://github.com/Hacker0x01/react-datepicker.git"}, "bugs": {"url": "https://github.com/Hacker0x01/react-datepicker/issues"}, "devDependencies": {"@babel/core": "^7.5.5", "@babel/helpers": "^7.5.5", "@babel/plugin-external-helpers": "^7.2.0", "@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/preset-env": "^7.5.5", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.0.0", "@wojtekmaj/enzyme-adapter-react-17": "^0.6.1", "axe-core": "^4.4.1", "babel-eslint": "^10.0.3", "babel-loader": "^8.0.6", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-react-transform": "^3.0.0", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "babel-preset-airbnb": "^5.0.0", "chai": "^4.2.0", "codecov": "^3.5.0", "core-js": "^3.2.1", "cross-env": "^7.0.2", "css-loader": "^5.0.0", "enzyme": "^3.10.0", "eslint": "^7.4.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-react": "^7.14.3", "express": "^4.17.1", "flow-bin": "^0.132.0", "highlight.js": "^11.0.1", "husky": "7", "istanbul-instrumenter-loader": "^3.0.1", "karma": "^6.0.1", "karma-chai": "^0.1.0", "karma-coverage": "^2.0.1", "karma-firefox-launcher": "^2.0.0", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-sinon": "^1.0.5", "karma-sourcemap-loader": "^0.3.7", "karma-webpack": "^4.0.2", "lint-staged": "^13.0.3", "mocha": "^10.0.0", "npm-run-all": "^4.1.5", "prettier": "^2.0.5", "react": "^17.0.2", "react-docgen": "^5.3.0", "react-dom": "^17.0.2", "react-test-renderer": "^17.0.2", "react-transform-hmr": "^1.0.4", "rollup": "^2.32.0", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-commonjs": "^10.0.2", "rollup-plugin-filesize": "^9.0.2", "rollup-plugin-local-resolve": "^1.0.7", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-peer-deps-external": "^2.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-uglify": "^6.0.4", "sass": "1.56.1", "sass-loader": "^10.2.1", "sinon": "^14.0.0", "slugify": "^1.3.4", "style-loader": "^2.0.0", "stylelint": "^14.1.0", "stylelint-config-standard": "^24.0.0", "stylelint-scss": "^4.0.0", "webpack": "^4.39.2", "webpack-cli": "^4.7.0", "webpack-dev-middleware": "^5.0.0", "webpack-dev-server": "^3.8.0", "webpack-hot-middleware": "^2.25.0"}, "peerDependencies": {"react": "^16.9.0 || ^17 || ^18", "react-dom": "^16.9.0 || ^17 || ^18"}, "dependencies": {"@popperjs/core": "^2.9.2", "classnames": "^2.2.6", "date-fns": "^2.24.0", "prop-types": "^15.7.2", "react-onclickoutside": "^6.12.2", "react-popper": "^2.3.0"}, "scripts": {"eslint": "eslint --ext .js,.jsx src test", "flow": "flow", "precommit": "lint-staged --allow-empty", "sass-lint": "stylelint 'src/stylesheets/*.scss'", "lint": "run-p eslint flow sass-lint", "prettier": "prettier --write '**/*.{js,jsx}'", "start": "yarn --cwd docs-site install && yarn --cwd docs-site start", "test": "cross-env NODE_ENV=test karma start karma.conf.js --single-run", "test:watch": "cross-env NODE_ENV=test karma start karma.conf.js --watch", "build": "cross-env NODE_ENV=production run-p build:** && run-p css:**", "build-dev": "cross-env NODE_ENV=development run-p build:** && run-p css:**", "css:prod": "sass --style compressed src/stylesheets/datepicker.scss > dist/react-datepicker.min.css", "css:modules:prod": "sass --style compressed src/stylesheets/datepicker-cssmodules.scss | tee dist/react-datepicker-cssmodules.min.css dist/react-datepicker-min.module.css", "css:dev": "sass --style expanded src/stylesheets/datepicker.scss > dist/react-datepicker.css", "css:modules:dev": "sass --style expanded src/stylesheets/datepicker-cssmodules.scss | tee dist/react-datepicker-cssmodules.css dist/react-datepicker.module.css", "build:js": "rollup -c", "js:dev": "rollup -cw", "prepare": "husky install"}, "lint-staged": {"*.{js,jsx,json,css,scss,md}": ["prettier --write", "git add"]}}