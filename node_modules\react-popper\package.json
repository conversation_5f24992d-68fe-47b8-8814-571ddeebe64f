{"name": "react-popper", "version": "2.3.0", "description": "Official library to use <PERSON><PERSON> on React projects", "license": "MIT", "author": "<PERSON> <<EMAIL>> (http://souporserious.com)", "contributors": ["<PERSON> <<EMAIL>> (https://fezvrasta.github.io)"], "homepage": "https://popper.js.org/react-popper", "main": "lib/cjs/index.js", "module": "lib/esm/index.js", "typings": "typings/react-popper.d.ts", "sideEffects": false, "files": ["/dist", "/lib", "/typings/react-popper.d.ts"], "scripts": {"build": "yarn build:clean && yarn build:esm && yarn build:cjs && yarn build:umd && yarn build:flow", "build:clean": "rimraf dist/ && rimraf lib/", "build:umd": "rollup -c && rimraf dist/index.esm.js", "build:esm": "cross-env BABEL_ENV=esm babel src --out-dir lib/esm", "build:cjs": "cross-env BABEL_ENV=cjs babel src --out-dir lib/cjs", "build:flow": "flow-copy-source --ignore '{__typings__/*,*.test}.js' src lib/cjs", "demo:dev": "parcel --out-dir demo/dist demo/index.html", "demo:build": "parcel build --out-dir demo/dist demo/index.html --public-url=/react-popper", "demo:deploy": "yarn demo:build && gh-pages -d demo/dist", "test": "yarn test:eslint && yarn test:flow && yarn test:ts && yarn test:jest", "test:ts": "tsc --project ./typings/tests", "test:flow": "flow check", "test:jest": "jest", "test:eslint": "eslint src", "prepare": "yarn build", "precommit": "pretty-quick --staged && test", "prepublishOnly": "git-branch-is master"}, "jest": {"setupFilesAfterEnv": ["<rootDir>jest.setup.js"]}, "repository": {"type": "git", "url": "https://github.com/popperjs/react-popper"}, "bugs": {"url": "https://github.com/popperjs/react-popper/issues"}, "keywords": ["react", "react-popper", "popper<PERSON>s", "component", "drop", "tooltip", "popover"], "peerDependencies": {"@popperjs/core": "^2.0.0", "react": "^16.8.0 || ^17 || ^18", "react-dom": "^16.8.0 || ^17 || ^18"}, "dependencies": {"react-fast-compare": "^3.0.1", "warning": "^4.0.2"}, "devDependencies": {"@atomico/rollup-plugin-sizes": "^1.1.3", "@babel/cli": "^7.8.4", "@babel/core": "^7.9.0", "@babel/plugin-transform-modules-commonjs": "^7.9.0", "@babel/polyfill": "^7.8.7", "@babel/preset-env": "^7.9.0", "@babel/preset-flow": "^7.9.0", "@babel/preset-react": "^7.9.4", "@emotion/core": "^10.0.28", "@emotion/styled": "^10.0.27", "@popperjs/core": "^2.3.3", "@rollup/plugin-commonjs": "^11.0.2", "@rollup/plugin-node-resolve": "^7.1.1", "@rollup/plugin-replace": "^2.3.1", "@testing-library/react": "^13.1.1", "@testing-library/react-hooks": "^8.0.0", "@types/react": "^16.9.29", "babel-eslint": "^10.1.0", "babel-jest": "^25.2.4", "cross-env": "^7.0.2", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.1", "eslint-plugin-flowtype": "^4.7.0", "eslint-plugin-jest": "^23.8.2", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^3.0.0", "flow-bin": "^0.176.2", "flow-copy-source": "^2.0.9", "gh-pages": "^2.2.0", "git-branch-is": "^3.1.0", "jest": "^25.2.4", "parcel-bundler": "^1.12.4", "prettier": "^2.0.2", "pretty-quick": "^2.0.1", "react": "18.0.0", "react-dom": "^18.0.0", "react-spring": "^8.0.27", "react-test-renderer": "^18.0.0", "rimraf": "^3.0.2", "rollup": "^2.3.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^5.3.0", "typescript": "^3.8.3"}}