{"name": "mockdate", "version": "3.0.5", "description": "A JavaScript mock Date object that can be used to change when \"now\" is.", "main": "lib/mockdate.js", "types": "lib/mockdate.d.ts", "dependencies": {}, "devDependencies": {"mocha": "7.1.2", "rollup": "2.42.4", "rollup-plugin-typescript2": "0.30.0", "should": "13.2.3", "typescript": "3.9.3"}, "scripts": {"build": "npm run build:js && npm run build:types", "build:types": "tsc -t esnext --moduleResolution node -d --emitDeclarationOnly --outFile lib/mockdate.d.ts src/mockdate.ts", "build:js": "rollup -c rollup.config.js", "test": "npm run build && mocha", "prepare": "npm run build", "prepublishOnly": "npm run test"}, "repository": {"type": "git", "url": "https://github.com/boblauer/MockDate.git"}, "keywords": ["date", "mock", "test"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/boblauer/MockDate/issues"}, "homepage": "https://github.com/boblauer/MockDate", "spm": {"main": "lib/mockdate.js"}}