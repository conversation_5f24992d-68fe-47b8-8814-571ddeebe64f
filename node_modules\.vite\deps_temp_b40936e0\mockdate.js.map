{"version": 3, "sources": ["../../mockdate/lib/mockdate.js"], "sourcesContent": ["(function (global, factory) {\n    typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :\n    typeof define === 'function' && define.amd ? define(['exports'], factory) :\n    (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.MockDate = {}));\n}(this, (function (exports) { 'use strict';\n\n    /*! *****************************************************************************\r\n    Copyright (c) Microsoft Corporation.\r\n\r\n    Permission to use, copy, modify, and/or distribute this software for any\r\n    purpose with or without fee is hereby granted.\r\n\r\n    THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\n    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\n    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\n    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\n    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\n    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\n    PERFORMANCE OF THIS SOFTWARE.\r\n    ***************************************************************************** */\r\n    /* global Reflect, Promise */\r\n\r\n    var extendStatics = function(d, b) {\r\n        extendStatics = Object.setPrototypeOf ||\r\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n        return extendStatics(d, b);\r\n    };\r\n\r\n    function __extends(d, b) {\r\n        if (typeof b !== \"function\" && b !== null)\r\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n        extendStatics(d, b);\r\n        function __() { this.constructor = d; }\r\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n    }\n\n    var RealDate = Date;\r\n    var now = null;\r\n    var MockDate = /** @class */ (function (_super) {\r\n        __extends(Date, _super);\r\n        function Date(y, m, d, h, M, s, ms) {\r\n            _super.call(this) || this;\r\n            var date;\r\n            switch (arguments.length) {\r\n                case 0:\r\n                    if (now !== null) {\r\n                        date = new RealDate(now.valueOf());\r\n                    }\r\n                    else {\r\n                        date = new RealDate();\r\n                    }\r\n                    break;\r\n                case 1:\r\n                    date = new RealDate(y);\r\n                    break;\r\n                default:\r\n                    d = typeof d === 'undefined' ? 1 : d;\r\n                    h = h || 0;\r\n                    M = M || 0;\r\n                    s = s || 0;\r\n                    ms = ms || 0;\r\n                    date = new RealDate(y, m, d, h, M, s, ms);\r\n                    break;\r\n            }\r\n            return date;\r\n        }\r\n        return Date;\r\n    }(RealDate));\r\n    MockDate.prototype = RealDate.prototype;\r\n    MockDate.UTC = RealDate.UTC;\r\n    MockDate.now = function () {\r\n        return new MockDate().valueOf();\r\n    };\r\n    MockDate.parse = function (dateString) {\r\n        return RealDate.parse(dateString);\r\n    };\r\n    MockDate.toString = function () {\r\n        return RealDate.toString();\r\n    };\r\n    function set(date) {\r\n        var dateObj = new Date(date.valueOf());\r\n        if (isNaN(dateObj.getTime())) {\r\n            throw new TypeError('mockdate: The time set is an invalid date: ' + date);\r\n        }\r\n        // @ts-ignore\r\n        Date = MockDate;\r\n        now = dateObj.valueOf();\r\n    }\r\n    function reset() {\r\n        Date = RealDate;\r\n    }\r\n    var mockdate = {\r\n        set: set,\r\n        reset: reset,\r\n    };\n\n    exports.default = mockdate;\n    exports.reset = reset;\n    exports.set = set;\n\n    Object.defineProperty(exports, '__esModule', { value: true });\n\n})));\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,KAAC,SAAU,QAAQ,SAAS;AACxB,aAAO,YAAY,YAAY,OAAO,WAAW,cAAc,QAAQ,OAAO,IAC9E,OAAO,WAAW,cAAc,OAAO,MAAM,OAAO,CAAC,SAAS,GAAG,OAAO,KACvE,SAAS,OAAO,eAAe,cAAc,aAAa,UAAU,MAAM,QAAQ,OAAO,WAAW,CAAC,CAAC;AAAA,IAC3G,GAAE,SAAO,SAAUA,UAAS;AAAE;AAkB1B,UAAI,gBAAgB,SAAS,GAAG,GAAG;AAC/B,wBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,UAAAD,GAAE,YAAYC;AAAA,QAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,mBAAS,KAAKA;AAAG,gBAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC;AAAG,cAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,QAAG;AACpG,eAAO,cAAc,GAAG,CAAC;AAAA,MAC7B;AAEA,eAAS,UAAU,GAAG,GAAG;AACrB,YAAI,OAAO,MAAM,cAAc,MAAM;AACjC,gBAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,sBAAc,GAAG,CAAC;AAClB,iBAAS,KAAK;AAAE,eAAK,cAAc;AAAA,QAAG;AACtC,UAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AAAA,MACtF;AAEA,UAAI,WAAW;AACf,UAAI,MAAM;AACV,UAAI;AAAA;AAAA,QAA0B,SAAU,QAAQ;AAC5C,oBAAUC,OAAM,MAAM;AACtB,mBAASA,MAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI;AAChC,mBAAO,KAAK,IAAI,KAAK;AACrB,gBAAI;AACJ,oBAAQ,UAAU,QAAQ;AAAA,cACtB,KAAK;AACD,oBAAI,QAAQ,MAAM;AACd,yBAAO,IAAI,SAAS,IAAI,QAAQ,CAAC;AAAA,gBACrC,OACK;AACD,yBAAO,IAAI,SAAS;AAAA,gBACxB;AACA;AAAA,cACJ,KAAK;AACD,uBAAO,IAAI,SAAS,CAAC;AACrB;AAAA,cACJ;AACI,oBAAI,OAAO,MAAM,cAAc,IAAI;AACnC,oBAAI,KAAK;AACT,oBAAI,KAAK;AACT,oBAAI,KAAK;AACT,qBAAK,MAAM;AACX,uBAAO,IAAI,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;AACxC;AAAA,YACR;AACA,mBAAO;AAAA,UACX;AACA,iBAAOA;AAAA,QACX,EAAE,QAAQ;AAAA;AACV,eAAS,YAAY,SAAS;AAC9B,eAAS,MAAM,SAAS;AACxB,eAAS,MAAM,WAAY;AACvB,eAAO,IAAI,SAAS,EAAE,QAAQ;AAAA,MAClC;AACA,eAAS,QAAQ,SAAU,YAAY;AACnC,eAAO,SAAS,MAAM,UAAU;AAAA,MACpC;AACA,eAAS,WAAW,WAAY;AAC5B,eAAO,SAAS,SAAS;AAAA,MAC7B;AACA,eAAS,IAAI,MAAM;AACf,YAAI,UAAU,IAAI,KAAK,KAAK,QAAQ,CAAC;AACrC,YAAI,MAAM,QAAQ,QAAQ,CAAC,GAAG;AAC1B,gBAAM,IAAI,UAAU,gDAAgD,IAAI;AAAA,QAC5E;AAEA,eAAO;AACP,cAAM,QAAQ,QAAQ;AAAA,MAC1B;AACA,eAAS,QAAQ;AACb,eAAO;AAAA,MACX;AACA,UAAI,WAAW;AAAA,QACX;AAAA,QACA;AAAA,MACJ;AAEA,MAAAH,SAAQ,UAAU;AAClB,MAAAA,SAAQ,QAAQ;AAChB,MAAAA,SAAQ,MAAM;AAEd,aAAO,eAAeA,UAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAAA,IAEhE,CAAE;AAAA;AAAA;", "names": ["exports", "d", "b", "Date"]}