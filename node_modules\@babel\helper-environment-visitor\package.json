{"name": "@babel/helper-environment-visitor", "version": "7.18.9", "description": "Helper visitor to only visit nodes in the current 'this' context", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-environment-visitor"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-environment-visitor", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "devDependencies": {"@babel/traverse": "^7.18.9", "@babel/types": "^7.18.9"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}