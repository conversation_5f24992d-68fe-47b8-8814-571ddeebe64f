{"version": 3, "file": "socket.io.esm.min.js", "sources": ["../node_modules/engine.io-parser/build/esm/commons.js", "../node_modules/engine.io-parser/build/esm/encodePacket.browser.js", "../node_modules/engine.io-parser/build/esm/contrib/base64-arraybuffer.js", "../node_modules/engine.io-parser/build/esm/decodePacket.browser.js", "../node_modules/engine.io-parser/build/esm/index.js", "../node_modules/@socket.io/component-emitter/index.mjs", "../node_modules/engine.io-client/build/esm/globalThis.browser.js", "../node_modules/engine.io-client/build/esm/util.js", "../node_modules/engine.io-client/build/esm/transport.js", "../node_modules/engine.io-client/build/esm/contrib/yeast.js", "../node_modules/engine.io-client/build/esm/contrib/parseqs.js", "../node_modules/engine.io-client/build/esm/contrib/has-cors.js", "../node_modules/engine.io-client/build/esm/transports/xmlhttprequest.browser.js", "../node_modules/engine.io-client/build/esm/transports/polling.js", "../node_modules/engine.io-client/build/esm/transports/websocket-constructor.browser.js", "../node_modules/engine.io-client/build/esm/transports/websocket.js", "../node_modules/engine.io-client/build/esm/transports/index.js", "../node_modules/engine.io-client/build/esm/contrib/parseuri.js", "../node_modules/engine.io-client/build/esm/socket.js", "../node_modules/socket.io-parser/build/esm/is-binary.js", "../node_modules/socket.io-parser/build/esm/binary.js", "../node_modules/socket.io-parser/build/esm/index.js", "../build/esm/on.js", "../build/esm/socket.js", "../build/esm/contrib/backo2.js", "../build/esm/manager.js", "../build/esm/index.js", "../build/esm/url.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + content);\n    };\n    return fileReader.readAsDataURL(data);\n};\nexport default encodePacket;\n", "const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = (arraybuffer) => {\n    let bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nexport const decode = (base64) => {\n    let bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    const arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType)\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1)\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type]\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            return data instanceof ArrayBuffer ? new Blob([data]) : data;\n        case \"arraybuffer\":\n        default:\n            return data; // assuming the data is already an ArrayBuffer\n    }\n};\nexport default decodePacket;\n", "import encodePacket from \"./encodePacket.js\";\nimport decodePacket from \"./decodePacket.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, encodedPacket => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload };\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\n", "import { globalThisShim as globalThis } from \"./globalThis.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nclass TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n}\n", "// imported from https://github.com/unshiftio/yeast\n'use strict';\nconst alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split(''), length = 64, map = {};\nlet seed = 0, i = 0, prev;\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nexport function encode(num) {\n    let encoded = '';\n    do {\n        encoded = alphabet[num % length] + encoded;\n        num = Math.floor(num / length);\n    } while (num > 0);\n    return encoded;\n}\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nexport function decode(str) {\n    let decoded = 0;\n    for (i = 0; i < str.length; i++) {\n        decoded = decoded * length + map[str.charAt(i)];\n    }\n    return decoded;\n}\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nexport function yeast() {\n    const now = encode(+new Date());\n    if (now !== prev)\n        return seed = 0, prev = now;\n    return now + '.' + encode(seed++);\n}\n//\n// Map each character to its index.\n//\nfor (; i < length; i++)\n    map[alphabet[i]] = i;\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "// browser shim for xmlhttprequest module\nimport { hasCORS } from \"../contrib/has-cors.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nexport function XHR(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { encode } from \"../contrib/parseqs.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nimport { XHR as XMLHttpRequest } from \"./xmlhttprequest.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nfunction empty() { }\nconst hasXHR2 = (function () {\n    const xhr = new XMLHttpRequest({\n        xdomain: false,\n    });\n    return null != xhr.responseType;\n})();\nexport class Polling extends Transport {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        this.polling = false;\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n            this.xs = opts.secure !== isSSL;\n        }\n        /**\n         * XHR supports binary\n         */\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this.poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this.polling || !this.writable) {\n            let total = 0;\n            if (this.polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    poll() {\n        this.polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this.polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this.poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        let query = this.query || {};\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        let port = \"\";\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        // avoid port if default for schema\n        if (this.opts.port &&\n            ((\"https\" === schema && Number(this.opts.port) !== 443) ||\n                (\"http\" === schema && Number(this.opts.port) !== 80))) {\n            port = \":\" + this.opts.port;\n        }\n        const encodedQuery = encode(query);\n        const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n        return (schema +\n            \"://\" +\n            (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n            port +\n            this.opts.path +\n            (encodedQuery.length ? \"?\" + encodedQuery : \"\"));\n    }\n    /**\n     * Creates a request.\n     *\n     * @param {String} method\n     * @private\n     */\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd, xs: this.xs }, this.opts);\n        return new Request(this.uri(), opts);\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(uri, opts) {\n        super();\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.method = opts.method || \"GET\";\n        this.uri = uri;\n        this.async = false !== opts.async;\n        this.data = undefined !== opts.data ? opts.data : null;\n        this.create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    create() {\n        const opts = pick(this.opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this.opts.xd;\n        opts.xscheme = !!this.opts.xs;\n        const xhr = (this.xhr = new XMLHttpRequest(opts));\n        try {\n            xhr.open(this.method, this.uri, this.async);\n            try {\n                if (this.opts.extraHeaders) {\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this.opts.extraHeaders) {\n                        if (this.opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this.method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this.opts.withCredentials;\n            }\n            if (this.opts.requestTimeout) {\n                xhr.timeout = this.opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this.onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this.data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this.onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this.index = Request.requestsCount++;\n            Request.requests[this.index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    onError(err) {\n        this.emitReserved(\"error\", err, this.xhr);\n        this.cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    cleanup(fromError) {\n        if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n            return;\n        }\n        this.xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this.xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this.index];\n        }\n        this.xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    onLoad() {\n        const data = this.xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this.cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this.cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\n", "import { globalThisShim as globalThis } from \"../globalThis.js\";\nexport const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const WebSocket = globalThis.WebSocket || globalThis.MozWebSocket;\nexport const usingBrowserWebSocket = true;\nexport const defaultBinaryType = \"arraybuffer\";\n", "import { Transport } from \"../transport.js\";\nimport { encode } from \"../contrib/parseqs.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { pick } from \"../util.js\";\nimport { defaultBinaryType, nextTick, usingBrowserWebSocket, WebSocket, } from \"./websocket-constructor.js\";\nimport { encodePacket } from \"engine.io-parser\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class WS extends Transport {\n    /**\n     * WebSocket transport constructor.\n     *\n     * @param {Object} opts - connection options\n     * @protected\n     */\n    constructor(opts) {\n        super(opts);\n        this.supportsBinary = !opts.forceBase64;\n    }\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        if (!this.check()) {\n            // let probe timeout\n            return;\n        }\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws =\n                usingBrowserWebSocket && !isReactNative\n                    ? protocols\n                        ? new WebSocket(uri, protocols)\n                        : new WebSocket(uri)\n                    : new WebSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType || defaultBinaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // always create a new object (GH-437)\n                const opts = {};\n                if (!usingBrowserWebSocket) {\n                    if (packet.options) {\n                        opts.compress = packet.options.compress;\n                    }\n                    if (this.opts.perMessageDeflate) {\n                        const len = \n                        // @ts-ignore\n                        \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n                        if (len < this.opts.perMessageDeflate.threshold) {\n                            opts.compress = false;\n                        }\n                    }\n                }\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    if (usingBrowserWebSocket) {\n                        // TypeError is thrown when passing the second argument on Safari\n                        this.ws.send(data);\n                    }\n                    else {\n                        this.ws.send(data, opts);\n                    }\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        let query = this.query || {};\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        let port = \"\";\n        // avoid port if default for schema\n        if (this.opts.port &&\n            ((\"wss\" === schema && Number(this.opts.port) !== 443) ||\n                (\"ws\" === schema && Number(this.opts.port) !== 80))) {\n            port = \":\" + this.opts.port;\n        }\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        const encodedQuery = encode(query);\n        const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n        return (schema +\n            \"://\" +\n            (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n            port +\n            this.opts.path +\n            (encodedQuery.length ? \"?\" + encodedQuery : \"\"));\n    }\n    /**\n     * Feature detection for WebSocket.\n     *\n     * @return {Boolean} whether this transport is available.\n     * @private\n     */\n    check() {\n        return !!WebSocket;\n    }\n}\n", "import { Polling } from \"./polling.js\";\nimport { WS } from \"./websocket.js\";\nexport const transports = {\n    websocket: WS,\n    polling: Polling,\n};\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nexport class Socket extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts = {}) {\n        super();\n        this.writeBuffer = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            uri = parse(uri);\n            opts.hostname = uri.host;\n            opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n            opts.port = uri.port;\n            if (uri.query)\n                opts.query = uri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = opts.transports || [\"polling\", \"websocket\"];\n        this.writeBuffer = [];\n        this.prevBufferLen = 0;\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: true,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        // set on handshake\n        this.id = null;\n        this.upgrades = null;\n        this.pingInterval = null;\n        this.pingTimeout = null;\n        // set on heartbeat\n        this.pingTimeoutTimer = null;\n        if (typeof addEventListener === \"function\") {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this.beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this.offlineEventListener = () => {\n                    this.onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                addEventListener(\"offline\", this.offlineEventListener, false);\n            }\n        }\n        this.open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts.transportOptions[name], this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        });\n        return new transports[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    open() {\n        let transport;\n        if (this.opts.rememberUpgrade &&\n            Socket.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1) {\n            transport = \"websocket\";\n        }\n        else if (0 === this.transports.length) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        else {\n            transport = this.transports[0];\n        }\n        this.readyState = \"opening\";\n        // Retry with the next transport if the transport is disabled (jsonp: false)\n        try {\n            transport = this.createTransport(transport);\n        }\n        catch (e) {\n            this.transports.shift();\n            this.open();\n            return;\n        }\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this.onDrain.bind(this))\n            .on(\"packet\", this.onPacket.bind(this))\n            .on(\"error\", this.onError.bind(this))\n            .on(\"close\", (reason) => this.onClose(\"transport close\", reason));\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        Socket.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        transport.open();\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n        // we check for `readyState` in case an `open`\n        // listener already closed the socket\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            let i = 0;\n            const l = this.upgrades.length;\n            for (; i < l; i++) {\n                this.probe(this.upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this.resetPingTimeout();\n                    this.sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this.onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this.upgrades = this.filterUpgrades(data.upgrades);\n        this.pingInterval = data.pingInterval;\n        this.pingTimeout = data.pingTimeout;\n        this.maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this.resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    resetPingTimeout() {\n        this.clearTimeoutFn(this.pingTimeoutTimer);\n        this.pingTimeoutTimer = this.setTimeoutFn(() => {\n            this.onClose(\"ping timeout\");\n        }, this.pingInterval + this.pingTimeout);\n        if (this.opts.autoUnref) {\n            this.pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    onDrain() {\n        this.writeBuffer.splice(0, this.prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this.prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this.getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this.prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    getWritablePackets() {\n        const shouldCheckPayloadSize = this.maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this.maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    send(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this.onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    onError(err) {\n        Socket.priorWebsocketSuccess = false;\n        this.emitReserved(\"error\", err);\n        this.onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this.pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (typeof removeEventListener === \"function\") {\n                removeEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n                removeEventListener(\"offline\", this.offlineEventListener, false);\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this.prevBufferLen = 0;\n        }\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        let i = 0;\n        const j = upgrades.length;\n        for (; i < j; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\nSocket.protocol = protocol;\n", "const withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeFile = typeof File === \"function\" ||\n    (typeof File !== \"undefined\" &&\n        toString.call(File) === \"[object FileConstructor]\");\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nexport function isBinary(obj) {\n    return ((withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj))) ||\n        (withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File));\n}\nexport function hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for (let i = 0, l = obj.length; i < l; i++) {\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON &&\n        typeof obj.toJSON === \"function\" &&\n        arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\n", "import { isBinary } from \"./is-binary.js\";\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nexport function deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return { packet: pack, buffers: buffers };\n}\nfunction _deconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (isBinary(data)) {\n        const placeholder = { _placeholder: true, num: buffers.length };\n        buffers.push(data);\n        return placeholder;\n    }\n    else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for (let i = 0; i < data.length; i++) {\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    }\n    else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nexport function reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    delete packet.attachments; // no longer useful\n    return packet;\n}\nfunction _reconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (data && data._placeholder === true) {\n        const isIndexValid = typeof data.num === \"number\" &&\n            data.num >= 0 &&\n            data.num < buffers.length;\n        if (isIndexValid) {\n            return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n        }\n        else {\n            throw new Error(\"illegal attachments\");\n        }\n    }\n    else if (Array.isArray(data)) {\n        for (let i = 0; i < data.length; i++) {\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    }\n    else if (typeof data === \"object\") {\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n", "import { Emitter } from \"@socket.io/component-emitter\";\nimport { deconstructPacket, reconstructPacket } from \"./binary.js\";\nimport { isBinary, hasBinary } from \"./is-binary.js\";\n/**\n * Protocol version.\n *\n * @public\n */\nexport const protocol = 5;\nexport var PacketType;\n(function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType || (PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nexport class Encoder {\n    /**\n     * Encoder constructor\n     *\n     * @param {function} replacer - custom replacer to pass down to JSON.parse\n     */\n    constructor(replacer) {\n        this.replacer = replacer;\n    }\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */\n    encode(obj) {\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if (hasBinary(obj)) {\n                return this.encodeAsBinary({\n                    type: obj.type === PacketType.EVENT\n                        ? PacketType.BINARY_EVENT\n                        : PacketType.BINARY_ACK,\n                    nsp: obj.nsp,\n                    data: obj.data,\n                    id: obj.id,\n                });\n            }\n        }\n        return [this.encodeAsString(obj)];\n    }\n    /**\n     * Encode packet as string.\n     */\n    encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT ||\n            obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data, this.replacer);\n        }\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */\n    encodeAsBinary(obj) {\n        const deconstruction = deconstructPacket(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nexport class Decoder extends Emitter {\n    /**\n     * Decoder constructor\n     *\n     * @param {function} reviver - custom reviver to pass down to JSON.stringify\n     */\n    constructor(reviver) {\n        super();\n        this.reviver = reviver;\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */\n    add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            if (this.reconstructor) {\n                throw new Error(\"got plaintext data when reconstructing a packet\");\n            }\n            packet = this.decodeString(obj);\n            const isBinaryEvent = packet.type === PacketType.BINARY_EVENT;\n            if (isBinaryEvent || packet.type === PacketType.BINARY_ACK) {\n                packet.type = isBinaryEvent ? PacketType.EVENT : PacketType.ACK;\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n            else {\n                // non-binary full packet\n                super.emitReserved(\"decoded\", packet);\n            }\n        }\n        else if (isBinary(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            }\n            else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n        }\n        else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */\n    decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0)),\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT ||\n            p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while (str.charAt(++i) !== \"-\" && i != str.length) { }\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (\",\" === c)\n                    break;\n                if (i === str.length)\n                    break;\n            }\n            p.nsp = str.substring(start, i);\n        }\n        else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length)\n                    break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = this.tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            }\n            else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n        return p;\n    }\n    tryParse(str) {\n        try {\n            return JSON.parse(str, this.reviver);\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    static isPayloadValid(type, payload) {\n        switch (type) {\n            case PacketType.CONNECT:\n                return typeof payload === \"object\";\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || typeof payload === \"object\";\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return Array.isArray(payload) && payload.length > 0;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */\n    destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n            this.reconstructor = null;\n        }\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n    constructor(packet) {\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */\n    takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = reconstructPacket(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */\n    finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\n", "export function on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n", "import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */\nexport class Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     */\n    constructor(io, nsp, opts) {\n        super();\n        /**\n         * Whether the socket is currently connected to the server.\n         *\n         * @example\n         * const socket = io();\n         *\n         * socket.on(\"connect\", () => {\n         *   console.log(socket.connected); // true\n         * });\n         *\n         * socket.on(\"disconnect\", () => {\n         *   console.log(socket.connected); // false\n         * });\n         */\n        this.connected = false;\n        /**\n         * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n         * be transmitted by the server.\n         */\n        this.recovered = false;\n        /**\n         * Buffer for packets received before the CONNECT packet\n         */\n        this.receiveBuffer = [];\n        /**\n         * Buffer for packets that will be sent once the socket is connected\n         */\n        this.sendBuffer = [];\n        /**\n         * The queue of packets to be sent with retry in case of failure.\n         *\n         * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n         * @private\n         */\n        this._queue = [];\n        /**\n         * A sequence to generate the ID of the {@link QueuedPacket}.\n         * @private\n         */\n        this._queueSeq = 0;\n        this.ids = 0;\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        this._opts = Object.assign({}, opts);\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.disconnected); // false\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.disconnected); // true\n     * });\n     */\n    get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on(io, \"open\", this.onopen.bind(this)),\n            on(io, \"packet\", this.onpacket.bind(this)),\n            on(io, \"error\", this.onerror.bind(this)),\n            on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n     *\n     * @example\n     * const socket = io();\n     *\n     * console.log(socket.active); // true\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   if (reason === \"io server disconnect\") {\n     *     // the disconnection was initiated by the server, you need to manually reconnect\n     *     console.log(socket.active); // false\n     *   }\n     *   // else the socket will automatically try to reconnect\n     *   console.log(socket.active); // true\n     * });\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @example\n     * const socket = io({\n     *   autoConnect: false\n     * });\n     *\n     * socket.connect();\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for {@link connect()}.\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * This method mimics the WebSocket.send() method.\n     *\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n     *\n     * @example\n     * socket.send(\"hello\");\n     *\n     * // this is equivalent to\n     * socket.emit(\"message\", \"hello\");\n     *\n     * @return self\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @example\n     * socket.emit(\"hello\", \"world\");\n     *\n     * // all serializable datastructures are supported (no need to call JSON.stringify)\n     * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n     *\n     * // with an acknowledgement from the server\n     * socket.emit(\"hello\", \"world\", (val) => {\n     *   // ...\n     * });\n     *\n     * @return self\n     */\n    emit(ev, ...args) {\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n            this._addToQueue(args);\n            return this;\n        }\n        const packet = {\n            type: PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = this.io.engine &&\n            this.io.engine.transport &&\n            this.io.engine.transport.writable;\n        const discardPacket = this.flags.volatile && (!isTransportWritable || !this.connected);\n        if (discardPacket) {\n        }\n        else if (this.connected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */\n    _registerAckCallback(id, ack) {\n        var _a;\n        const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(() => {\n            delete this.acks[id];\n            for (let i = 0; i < this.sendBuffer.length; i++) {\n                if (this.sendBuffer[i].id === id) {\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        this.acks[id] = (...args) => {\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, [null, ...args]);\n        };\n    }\n    /**\n     * Emits an event and waits for an acknowledgement\n     *\n     * @example\n     * // without timeout\n     * const response = await socket.emitWithAck(\"hello\", \"world\");\n     *\n     * // with a specific timeout\n     * try {\n     *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n     * } catch (err) {\n     *   // the server did not acknowledge the event in the given delay\n     * }\n     *\n     * @return a Promise that will be fulfilled when the server acknowledges the event\n     */\n    emitWithAck(ev, ...args) {\n        // the timeout flag is optional\n        const withErr = this.flags.timeout !== undefined || this._opts.ackTimeout !== undefined;\n        return new Promise((resolve, reject) => {\n            args.push((arg1, arg2) => {\n                if (withErr) {\n                    return arg1 ? reject(arg1) : resolve(arg2);\n                }\n                else {\n                    return resolve(arg1);\n                }\n            });\n            this.emit(ev, ...args);\n        });\n    }\n    /**\n     * Add the packet to the queue.\n     * @param args\n     * @private\n     */\n    _addToQueue(args) {\n        let ack;\n        if (typeof args[args.length - 1] === \"function\") {\n            ack = args.pop();\n        }\n        const packet = {\n            id: this._queueSeq++,\n            tryCount: 0,\n            pending: false,\n            args,\n            flags: Object.assign({ fromQueue: true }, this.flags),\n        };\n        args.push((err, ...responseArgs) => {\n            if (packet !== this._queue[0]) {\n                // the packet has already been acknowledged\n                return;\n            }\n            const hasError = err !== null;\n            if (hasError) {\n                if (packet.tryCount > this._opts.retries) {\n                    this._queue.shift();\n                    if (ack) {\n                        ack(err);\n                    }\n                }\n            }\n            else {\n                this._queue.shift();\n                if (ack) {\n                    ack(null, ...responseArgs);\n                }\n            }\n            packet.pending = false;\n            return this._drainQueue();\n        });\n        this._queue.push(packet);\n        this._drainQueue();\n    }\n    /**\n     * Send the first packet of the queue, and wait for an acknowledgement from the server.\n     * @param force - whether to resend a packet that has not been acknowledged yet\n     *\n     * @private\n     */\n    _drainQueue(force = false) {\n        if (!this.connected || this._queue.length === 0) {\n            return;\n        }\n        const packet = this._queue[0];\n        if (packet.pending && !force) {\n            return;\n        }\n        packet.pending = true;\n        packet.tryCount++;\n        this.flags = packet.flags;\n        this.emit.apply(this, packet.args);\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this._sendConnectPacket(data);\n            });\n        }\n        else {\n            this._sendConnectPacket(this.auth);\n        }\n    }\n    /**\n     * Sends a CONNECT packet to initiate the Socket.IO session.\n     *\n     * @param data\n     * @private\n     */\n    _sendConnectPacket(data) {\n        this.packet({\n            type: PacketType.CONNECT,\n            data: this._pid\n                ? Object.assign({ pid: this._pid, offset: this._lastOffset }, data)\n                : data,\n        });\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */\n    onclose(reason, description) {\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    this.onconnect(packet.data.sid, packet.data.pid);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        if (null != packet.id) {\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n        if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n            this._lastOffset = args[args.length - 1];\n        }\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            self.packet({\n                type: PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowlegement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (\"function\" === typeof ack) {\n            ack.apply(this, packet.data);\n            delete this.acks[packet.id];\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id, pid) {\n        this.id = id;\n        this.recovered = pid && this._pid === pid;\n        this._pid = pid; // defined only if connection state recovery is enabled\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n        this._drainQueue(true);\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n     *\n     * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   // console.log(reason); prints \"io client disconnect\"\n     * });\n     *\n     * socket.disconnect();\n     *\n     * @return self\n     */\n    disconnect() {\n        if (this.connected) {\n            this.packet({ type: PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for {@link disconnect()}.\n     *\n     * @return self\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @example\n     * socket.compress(false).emit(\"hello\");\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @example\n     * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n     *\n     * @returns self\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * @example\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     *\n     * @returns self\n     */\n    timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @example\n     * socket.onAny((event, ...args) => {\n     *   console.log(`got ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @example\n     * socket.prependAny((event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * }\n     *\n     * socket.onAny(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAny(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAny();\n     *\n     * @param listener\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * }\n     *\n     * socket.onAnyOutgoing(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAnyOutgoing(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAnyOutgoing();\n     *\n     * @param [listener] - the catch-all listener (optional)\n     */\n    offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */\n    notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n", "/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\nexport function Backoff(opts) {\n    opts = opts || {};\n    this.ms = opts.min || 100;\n    this.max = opts.max || 10000;\n    this.factor = opts.factor || 2;\n    this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n    this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\nBackoff.prototype.duration = function () {\n    var ms = this.ms * Math.pow(this.factor, this.attempts++);\n    if (this.jitter) {\n        var rand = Math.random();\n        var deviation = Math.floor(rand * this.jitter * ms);\n        ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n    }\n    return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\nBackoff.prototype.reset = function () {\n    this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */\nBackoff.prototype.setMin = function (min) {\n    this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */\nBackoff.prototype.setMax = function (max) {\n    this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */\nBackoff.prototype.setJitter = function (jitter) {\n    this.jitter = jitter;\n};\n", "import { Socket as Engine, installTimerFunctions, nextTick, } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\nexport class Manager extends Emitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        installTimerFunctions(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n        this.engine = new Engine(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        // emit `error`\n        const errorSub = on(socket, \"error\", (err) => {\n            self.cleanup();\n            self._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                self.maybeReconnectOnOpen();\n            }\n        });\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            if (timeout === 0) {\n                openSubDestroy(); // prevents a race condition with the 'open' event\n            }\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n                openSubDestroy();\n                socket.close();\n                // @ts-ignore\n                socket.emit(\"error\", new Error(\"timeout\"));\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        try {\n            this.decoder.add(data);\n        }\n        catch (e) {\n            this.onclose(\"parse error\", e);\n        }\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n        nextTick(() => {\n            this.emitReserved(\"packet\", packet);\n        }, this.setTimeoutFn);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        else if (this._autoConnect && !socket.active) {\n            socket.connect();\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n        if (this.engine)\n            this.engine.close();\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called upon engine close.\n     *\n     * @private\n     */\n    onclose(reason, description) {\n        this.cleanup();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n", "import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\n/**\n * Managers cache.\n */\nconst cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n        io = new Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n            cache[id] = new Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager,\n    Socket,\n    io: lookup,\n    connect: lookup,\n});\n/**\n * Protocol version.\n *\n * @public\n */\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default, };\n", "import { parse } from \"engine.io-client\";\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nexport function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        obj = parse(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "encodePacket", "supportsBinary", "callback", "encodeBlobAsBase64", "obj", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "chars", "lookup", "Uint8Array", "i", "length", "charCodeAt", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "decoded", "base64", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "len", "p", "arraybuffer", "bytes", "decode", "SEPARATOR", "String", "fromCharCode", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "this", "_callbacks", "push", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "cb", "callbacks", "splice", "emit", "args", "Array", "slice", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "globalThisShim", "self", "window", "Function", "pick", "attr", "reduce", "acc", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "globalThis", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "setTimeoutFn", "bind", "clearTimeoutFn", "TransportError", "Error", "constructor", "reason", "description", "context", "super", "Transport", "writable", "query", "socket", "onError", "open", "readyState", "doOpen", "close", "doClose", "onClose", "send", "packets", "write", "onOpen", "onData", "packet", "onPacket", "details", "pause", "onPause", "alphabet", "map", "prev", "seed", "encode", "num", "encoded", "Math", "floor", "yeast", "now", "Date", "str", "encodeURIComponent", "value", "XMLHttpRequest", "err", "hasCORS", "XHR", "xdomain", "e", "concat", "join", "empty", "hasXHR2", "responseType", "Request", "uri", "method", "async", "undefined", "xd", "xscheme", "xs", "xhr", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "withCredentials", "requestTimeout", "timeout", "onreadystatechange", "status", "onLoad", "document", "index", "requestsCount", "requests", "cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "nextTick", "Promise", "resolve", "then", "WebSocket", "MozWebSocket", "isReactNative", "navigator", "product", "toLowerCase", "transports", "websocket", "forceBase64", "name", "check", "protocols", "headers", "ws", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "lastPacket", "schema", "secure", "port", "Number", "timestampRequests", "timestampParam", "b64", "<PERSON><PERSON><PERSON><PERSON>", "hostname", "indexOf", "path", "polling", "location", "isSSL", "protocol", "poll", "total", "doPoll", "encodedPayload", "encodedPackets", "decodedPacket", "decodePayload", "count", "encodePayload", "doWrite", "sid", "request", "assign", "req", "xhrStatus", "pollXhr", "re", "parts", "parse", "src", "b", "replace", "m", "exec", "source", "host", "authority", "ipv6uri", "pathNames", "regx", "names", "query<PERSON><PERSON>", "$0", "$1", "$2", "Socket", "writeBuffer", "prevBufferLen", "agent", "upgrade", "rememberUpgrade", "addTrailingSlash", "rejectUnauthorized", "perMessageDeflate", "threshold", "transportOptions", "closeOnBeforeunload", "qs", "qry", "pairs", "l", "pair", "decodeURIComponent", "id", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "beforeunloadEventListener", "transport", "offlineEventListener", "createTransport", "EIO", "priorWebsocketSuccess", "shift", "setTransport", "onDrain", "probe", "failed", "onTransportOpen", "msg", "upgrading", "flush", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "onHandshake", "JSON", "resetPingTimeout", "sendPacket", "code", "filterUpgrades", "maxPayload", "getWritablePackets", "payloadSize", "c", "utf8Length", "ceil", "byteLength", "size", "options", "compress", "cleanupAndClose", "waitForUpgrade", "filteredUpgrades", "j", "withNativeFile", "File", "isBinary", "hasBinary", "toJSON", "isArray", "deconstructPacket", "buffers", "packetData", "pack", "_deconstructPacket", "attachments", "placeholder", "_placeholder", "newData", "reconstructPacket", "_reconstructPacket", "PacketType", "Decoder", "reviver", "add", "reconstructor", "decodeString", "isBinaryEvent", "BINARY_EVENT", "BINARY_ACK", "EVENT", "ACK", "BinaryReconstructor", "takeBinaryData", "start", "buf", "nsp", "next", "payload", "try<PERSON><PERSON><PERSON>", "substr", "isPayloadValid", "static", "CONNECT", "DISCONNECT", "CONNECT_ERROR", "destroy", "finishedReconstruction", "reconPack", "binData", "replacer", "encodeAsString", "encodeAsBinary", "stringify", "deconstruction", "unshift", "RESERVED_EVENTS", "freeze", "connect", "connect_error", "disconnect", "disconnecting", "newListener", "io", "connected", "recovered", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "_queue", "_queueSeq", "ids", "acks", "flags", "auth", "_opts", "_autoConnect", "disconnected", "subEvents", "subs", "onpacket", "active", "_readyState", "retries", "fromQueue", "volatile", "_addToQueue", "ack", "pop", "_registerAckCallback", "isTransportWritable", "engine", "notifyOutgoingListeners", "_a", "ackTimeout", "timer", "emitWithAck", "withErr", "reject", "arg1", "arg2", "tryCount", "pending", "responseArgs", "_drainQueue", "force", "_packet", "_sendConnectPacket", "_pid", "pid", "offset", "_lastOffset", "onconnect", "onevent", "onack", "ondisconnect", "message", "emitEvent", "_anyListeners", "listener", "sent", "emitBuffered", "subDestroy", "onAny", "prependAny", "offAny", "listenersAny", "onAnyOutgoing", "_anyOutgoingListeners", "prependAnyOutgoing", "offAnyOutgoing", "listenersAnyOutgoing", "Backoff", "ms", "min", "max", "factor", "jitter", "attempts", "duration", "pow", "rand", "random", "deviation", "reset", "setMin", "setMax", "setJitter", "Manager", "nsps", "reconnection", "reconnectionAttempts", "Infinity", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "_parser", "parser", "encoder", "Encoder", "decoder", "autoConnect", "v", "_reconnection", "_reconnectionAttempts", "_reconnectionDelay", "_randomizationFactor", "_reconnectionDelayMax", "_timeout", "maybeReconnectOnOpen", "_reconnecting", "reconnect", "Engine", "skipReconnect", "openSubDestroy", "errorSub", "onping", "ondata", "ondecoded", "_destroy", "_close", "delay", "onreconnect", "attempt", "cache", "parsed", "loc", "test", "href", "url", "sameNamespace", "forceNew", "multiplex"], "mappings": ";;;;;AAAA,MAAMA,EAAeC,OAAOC,OAAO,MACnCF,EAAmB,KAAI,IACvBA,EAAoB,MAAI,IACxBA,EAAmB,KAAI,IACvBA,EAAmB,KAAI,IACvBA,EAAsB,QAAI,IAC1BA,EAAsB,QAAI,IAC1BA,EAAmB,KAAI,IACvB,MAAMG,EAAuBF,OAAOC,OAAO,MAC3CD,OAAOG,KAAKJ,GAAcK,SAAQC,IAC9BH,EAAqBH,EAAaM,IAAQA,CAAG,IAEjD,MAAMC,EAAe,CAAEC,KAAM,QAASC,KAAM,gBCXtCC,EAAiC,mBAATC,MACT,oBAATA,MACqC,6BAAzCV,OAAOW,UAAUC,SAASC,KAAKH,MACjCI,EAA+C,mBAAhBC,YAO/BC,EAAe,EAAGT,OAAMC,QAAQS,EAAgBC,KAClD,OAAIT,GAAkBD,aAAgBE,KAC9BO,EACOC,EAASV,GAGTW,EAAmBX,EAAMU,GAG/BJ,IACJN,aAAgBO,cAfVK,EAegCZ,EAdN,mBAAvBO,YAAYM,OACpBN,YAAYM,OAAOD,GACnBA,GAAOA,EAAIE,kBAAkBP,cAa3BE,EACOC,EAASV,GAGTW,EAAmB,IAAIT,KAAK,CAACF,IAAQU,GAI7CA,EAASnB,EAAaQ,IAASC,GAAQ,KAxBnCY,KAwBuC,EAEhDD,EAAqB,CAACX,EAAMU,KAC9B,MAAMK,EAAa,IAAIC,WAKvB,OAJAD,EAAWE,OAAS,WAChB,MAAMC,EAAUH,EAAWI,OAAOC,MAAM,KAAK,GAC7CV,EAAS,IAAMQ,EACvB,EACWH,EAAWM,cAAcrB,EAAK,ECtCnCsB,EAAQ,mEAERC,EAA+B,oBAAfC,WAA6B,GAAK,IAAIA,WAAW,KACvE,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAMI,OAAQD,IAC9BF,EAAOD,EAAMK,WAAWF,IAAMA,EAkB3B,MCpBDnB,EAA+C,mBAAhBC,YAC/BqB,EAAe,CAACC,EAAeC,KACjC,GAA6B,iBAAlBD,EACP,MAAO,CACH9B,KAAM,UACNC,KAAM+B,EAAUF,EAAeC,IAGvC,MAAM/B,EAAO8B,EAAcG,OAAO,GAClC,GAAa,MAATjC,EACA,MAAO,CACHA,KAAM,UACNC,KAAMiC,EAAmBJ,EAAcK,UAAU,GAAIJ,IAI7D,OADmBpC,EAAqBK,GAIjC8B,EAAcH,OAAS,EACxB,CACE3B,KAAML,EAAqBK,GAC3BC,KAAM6B,EAAcK,UAAU,IAEhC,CACEnC,KAAML,EAAqBK,IARxBD,CASN,EAEHmC,EAAqB,CAACjC,EAAM8B,KAC9B,GAAIxB,EAAuB,CACvB,MAAM6B,EDVQ,CAACC,IACnB,IAA8DX,EAAUY,EAAUC,EAAUC,EAAUC,EAAlGC,EAA+B,IAAhBL,EAAOV,OAAegB,EAAMN,EAAOV,OAAWiB,EAAI,EACnC,MAA9BP,EAAOA,EAAOV,OAAS,KACvBe,IACkC,MAA9BL,EAAOA,EAAOV,OAAS,IACvBe,KAGR,MAAMG,EAAc,IAAIrC,YAAYkC,GAAeI,EAAQ,IAAIrB,WAAWoB,GAC1E,IAAKnB,EAAI,EAAGA,EAAIiB,EAAKjB,GAAK,EACtBY,EAAWd,EAAOa,EAAOT,WAAWF,IACpCa,EAAWf,EAAOa,EAAOT,WAAWF,EAAI,IACxCc,EAAWhB,EAAOa,EAAOT,WAAWF,EAAI,IACxCe,EAAWjB,EAAOa,EAAOT,WAAWF,EAAI,IACxCoB,EAAMF,KAAQN,GAAY,EAAMC,GAAY,EAC5CO,EAAMF,MAAoB,GAAXL,IAAkB,EAAMC,GAAY,EACnDM,EAAMF,MAAoB,EAAXJ,IAAiB,EAAiB,GAAXC,EAE1C,OAAOI,CAAW,ECREE,CAAO9C,GACvB,OAAO+B,EAAUI,EAASL,EAC7B,CAEG,MAAO,CAAEM,QAAQ,EAAMpC,OAC1B,EAEC+B,EAAY,CAAC/B,EAAM8B,IAEZ,SADDA,GAEO9B,aAAgBO,YAAc,IAAIL,KAAK,CAACF,IAGxCA,EC3Cb+C,EAAYC,OAAOC,aAAa,ICI/B,SAASC,EAAQtC,GACtB,GAAIA,EAAK,OAWX,SAAeA,GACb,IAAK,IAAIf,KAAOqD,EAAQ/C,UACtBS,EAAIf,GAAOqD,EAAQ/C,UAAUN,GAE/B,OAAOe,CACT,CAhBkBuC,CAAMvC,EACxB,CA0BAsC,EAAQ/C,UAAUiD,GAClBF,EAAQ/C,UAAUkD,iBAAmB,SAASC,EAAOC,GAInD,OAHAC,KAAKC,WAAaD,KAAKC,YAAc,CAAA,GACpCD,KAAKC,WAAW,IAAMH,GAASE,KAAKC,WAAW,IAAMH,IAAU,IAC7DI,KAAKH,GACDC,IACT,EAYAN,EAAQ/C,UAAUwD,KAAO,SAASL,EAAOC,GACvC,SAASH,IACPI,KAAKI,IAAIN,EAAOF,GAChBG,EAAGM,MAAML,KAAMM,UAChB,CAID,OAFAV,EAAGG,GAAKA,EACRC,KAAKJ,GAAGE,EAAOF,GACRI,IACT,EAYAN,EAAQ/C,UAAUyD,IAClBV,EAAQ/C,UAAU4D,eAClBb,EAAQ/C,UAAU6D,mBAClBd,EAAQ/C,UAAU8D,oBAAsB,SAASX,EAAOC,GAItD,GAHAC,KAAKC,WAAaD,KAAKC,YAAc,CAAA,EAGjC,GAAKK,UAAUpC,OAEjB,OADA8B,KAAKC,WAAa,GACXD,KAIT,IAUIU,EAVAC,EAAYX,KAAKC,WAAW,IAAMH,GACtC,IAAKa,EAAW,OAAOX,KAGvB,GAAI,GAAKM,UAAUpC,OAEjB,cADO8B,KAAKC,WAAW,IAAMH,GACtBE,KAKT,IAAK,IAAI/B,EAAI,EAAGA,EAAI0C,EAAUzC,OAAQD,IAEpC,IADAyC,EAAKC,EAAU1C,MACJ8B,GAAMW,EAAGX,KAAOA,EAAI,CAC7BY,EAAUC,OAAO3C,EAAG,GACpB,KACD,CASH,OAJyB,IAArB0C,EAAUzC,eACL8B,KAAKC,WAAW,IAAMH,GAGxBE,IACT,EAUAN,EAAQ/C,UAAUkE,KAAO,SAASf,GAChCE,KAAKC,WAAaD,KAAKC,YAAc,CAAA,EAKrC,IAHA,IAAIa,EAAO,IAAIC,MAAMT,UAAUpC,OAAS,GACpCyC,EAAYX,KAAKC,WAAW,IAAMH,GAE7B7B,EAAI,EAAGA,EAAIqC,UAAUpC,OAAQD,IACpC6C,EAAK7C,EAAI,GAAKqC,UAAUrC,GAG1B,GAAI0C,EAEG,CAAI1C,EAAI,EAAb,IAAK,IAAWiB,GADhByB,EAAYA,EAAUK,MAAM,IACI9C,OAAQD,EAAIiB,IAAOjB,EACjD0C,EAAU1C,GAAGoC,MAAML,KAAMc,EADK5C,CAKlC,OAAO8B,IACT,EAGAN,EAAQ/C,UAAUsE,aAAevB,EAAQ/C,UAAUkE,KAUnDnB,EAAQ/C,UAAUuE,UAAY,SAASpB,GAErC,OADAE,KAAKC,WAAaD,KAAKC,YAAc,CAAA,EAC9BD,KAAKC,WAAW,IAAMH,IAAU,EACzC,EAUAJ,EAAQ/C,UAAUwE,aAAe,SAASrB,GACxC,QAAUE,KAAKkB,UAAUpB,GAAO5B,MAClC,ECxKO,MAAMkD,EACW,oBAATC,KACAA,KAEgB,oBAAXC,OACLA,OAGAC,SAAS,cAATA,GCPR,SAASC,EAAKpE,KAAQqE,GACzB,OAAOA,EAAKC,QAAO,CAACC,EAAKC,KACjBxE,EAAIyE,eAAeD,KACnBD,EAAIC,GAAKxE,EAAIwE,IAEVD,IACR,CAAE,EACT,CAEA,MAAMG,EAAqBC,EAAWC,WAChCC,EAAuBF,EAAWG,aACjC,SAASC,EAAsB/E,EAAKgF,GACnCA,EAAKC,iBACLjF,EAAIkF,aAAeR,EAAmBS,KAAKR,GAC3C3E,EAAIoF,eAAiBP,EAAqBM,KAAKR,KAG/C3E,EAAIkF,aAAeP,EAAWC,WAAWO,KAAKR,GAC9C3E,EAAIoF,eAAiBT,EAAWG,aAAaK,KAAKR,GAE1D,CClBA,MAAMU,UAAuBC,MACzBC,YAAYC,EAAQC,EAAaC,GAC7BC,MAAMH,GACN5C,KAAK6C,YAAcA,EACnB7C,KAAK8C,QAAUA,EACf9C,KAAKzD,KAAO,gBACf,EAEE,MAAMyG,UAAkBtD,EAO3BiD,YAAYP,GACRW,QACA/C,KAAKiD,UAAW,EAChBd,EAAsBnC,KAAMoC,GAC5BpC,KAAKoC,KAAOA,EACZpC,KAAKkD,MAAQd,EAAKc,MAClBlD,KAAKmD,OAASf,EAAKe,MACtB,CAUDC,QAAQR,EAAQC,EAAaC,GAEzB,OADAC,MAAM9B,aAAa,QAAS,IAAIwB,EAAeG,EAAQC,EAAaC,IAC7D9C,IACV,CAIDqD,OAGI,OAFArD,KAAKsD,WAAa,UAClBtD,KAAKuD,SACEvD,IACV,CAIDwD,QAKI,MAJwB,YAApBxD,KAAKsD,YAAgD,SAApBtD,KAAKsD,aACtCtD,KAAKyD,UACLzD,KAAK0D,WAEF1D,IACV,CAMD2D,KAAKC,GACuB,SAApB5D,KAAKsD,YACLtD,KAAK6D,MAAMD,EAKlB,CAMDE,SACI9D,KAAKsD,WAAa,OAClBtD,KAAKiD,UAAW,EAChBF,MAAM9B,aAAa,OACtB,CAOD8C,OAAOvH,GACH,MAAMwH,EAAS5F,EAAa5B,EAAMwD,KAAKmD,OAAO7E,YAC9C0B,KAAKiE,SAASD,EACjB,CAMDC,SAASD,GACLjB,MAAM9B,aAAa,SAAU+C,EAChC,CAMDN,QAAQQ,GACJlE,KAAKsD,WAAa,SAClBP,MAAM9B,aAAa,QAASiD,EAC/B,CAMDC,MAAMC,GAAY,EC9GtB,MAAMC,EAAW,mEAAmEzG,MAAM,IAAkB0G,EAAM,GAClH,IAAqBC,EAAjBC,EAAO,EAAGvG,EAAI,EAQX,SAASwG,EAAOC,GACnB,IAAIC,EAAU,GACd,GACIA,EAAUN,EAASK,EAZ6E,IAY7DC,EACnCD,EAAME,KAAKC,MAAMH,EAb+E,UAc3FA,EAAM,GACf,OAAOC,CACX,CAqBO,SAASG,IACZ,MAAMC,EAAMN,GAAQ,IAAIO,MACxB,OAAID,IAAQR,GACDC,EAAO,EAAGD,EAAOQ,GACrBA,EAAM,IAAMN,EAAOD,IAC9B,CAIA,KAAOvG,EA9CiG,GA8CrFA,IACfqG,EAAID,EAASpG,IAAMA,ECzChB,SAASwG,EAAOrH,GACnB,IAAI6H,EAAM,GACV,IAAK,IAAIhH,KAAKb,EACNA,EAAIyE,eAAe5D,KACfgH,EAAI/G,SACJ+G,GAAO,KACXA,GAAOC,mBAAmBjH,GAAK,IAAMiH,mBAAmB9H,EAAIa,KAGpE,OAAOgH,CACX,CCjBA,IAAIE,GAAQ,EACZ,IACIA,EAAkC,oBAAnBC,gBACX,oBAAqB,IAAIA,cAKjC,CAHA,MAAOC,GAGP,CACO,MAAMC,EAAUH,ECPhB,SAASI,EAAInD,GAChB,MAAMoD,EAAUpD,EAAKoD,QAErB,IACI,GAAI,oBAAuBJ,kBAAoBI,GAAWF,GACtD,OAAO,IAAIF,cAGN,CAAb,MAAOK,GAAM,CACb,IAAKD,EACD,IACI,OAAO,IAAIzD,EAAW,CAAC,UAAU2D,OAAO,UAAUC,KAAK,OAAM,oBAEpD,CAAb,MAAOF,GAAM,CAErB,CCVA,SAASG,IAAW,CACpB,MAAMC,EAIK,MAHK,IAAIT,EAAe,CAC3BI,SAAS,IAEMM,aA8NhB,MAAMC,UAAgBrG,EAOzBiD,YAAYqD,EAAK5D,GACbW,QACAZ,EAAsBnC,KAAMoC,GAC5BpC,KAAKoC,KAAOA,EACZpC,KAAKiG,OAAS7D,EAAK6D,QAAU,MAC7BjG,KAAKgG,IAAMA,EACXhG,KAAKkG,OAAQ,IAAU9D,EAAK8D,MAC5BlG,KAAKxD,UAAO2J,IAAc/D,EAAK5F,KAAO4F,EAAK5F,KAAO,KAClDwD,KAAK/D,QACR,CAMDA,SACI,MAAMmG,EAAOZ,EAAKxB,KAAKoC,KAAM,QAAS,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,aACjHA,EAAKoD,UAAYxF,KAAKoC,KAAKgE,GAC3BhE,EAAKiE,UAAYrG,KAAKoC,KAAKkE,GAC3B,MAAMC,EAAOvG,KAAKuG,IAAM,IAAInB,EAAehD,GAC3C,IACImE,EAAIlD,KAAKrD,KAAKiG,OAAQjG,KAAKgG,IAAKhG,KAAKkG,OACrC,IACI,GAAIlG,KAAKoC,KAAKoE,aAAc,CACxBD,EAAIE,uBAAyBF,EAAIE,uBAAsB,GACvD,IAAK,IAAIxI,KAAK+B,KAAKoC,KAAKoE,aAChBxG,KAAKoC,KAAKoE,aAAa3E,eAAe5D,IACtCsI,EAAIG,iBAAiBzI,EAAG+B,KAAKoC,KAAKoE,aAAavI,GAG1D,CAEQ,CAAb,MAAOwH,GAAM,CACb,GAAI,SAAWzF,KAAKiG,OAChB,IACIM,EAAIG,iBAAiB,eAAgB,2BAE5B,CAAb,MAAOjB,GAAM,CAEjB,IACIc,EAAIG,iBAAiB,SAAU,MAEtB,CAAb,MAAOjB,GAAM,CAET,oBAAqBc,IACrBA,EAAII,gBAAkB3G,KAAKoC,KAAKuE,iBAEhC3G,KAAKoC,KAAKwE,iBACVL,EAAIM,QAAU7G,KAAKoC,KAAKwE,gBAE5BL,EAAIO,mBAAqB,KACjB,IAAMP,EAAIjD,aAEV,MAAQiD,EAAIQ,QAAU,OAASR,EAAIQ,OACnC/G,KAAKgH,SAKLhH,KAAKsC,cAAa,KACdtC,KAAKoD,QAA8B,iBAAfmD,EAAIQ,OAAsBR,EAAIQ,OAAS,EAAE,GAC9D,GACN,EAELR,EAAI5C,KAAK3D,KAAKxD,KAUjB,CARD,MAAOiJ,GAOH,YAHAzF,KAAKsC,cAAa,KACdtC,KAAKoD,QAAQqC,EAAE,GAChB,EAEN,CACuB,oBAAbwB,WACPjH,KAAKkH,MAAQnB,EAAQoB,gBACrBpB,EAAQqB,SAASpH,KAAKkH,OAASlH,KAEtC,CAMDoD,QAAQiC,GACJrF,KAAKiB,aAAa,QAASoE,EAAKrF,KAAKuG,KACrCvG,KAAKqH,SAAQ,EAChB,CAMDA,QAAQC,GACJ,QAAI,IAAuBtH,KAAKuG,KAAO,OAASvG,KAAKuG,IAArD,CAIA,GADAvG,KAAKuG,IAAIO,mBAAqBlB,EAC1B0B,EACA,IACItH,KAAKuG,IAAIgB,OAEA,CAAb,MAAO9B,GAAM,CAEO,oBAAbwB,iBACAlB,EAAQqB,SAASpH,KAAKkH,OAEjClH,KAAKuG,IAAM,IAXV,CAYJ,CAMDS,SACI,MAAMxK,EAAOwD,KAAKuG,IAAIiB,aACT,OAAThL,IACAwD,KAAKiB,aAAa,OAAQzE,GAC1BwD,KAAKiB,aAAa,WAClBjB,KAAKqH,UAEZ,CAMDE,QACIvH,KAAKqH,SACR,EASL,GAPAtB,EAAQoB,cAAgB,EACxBpB,EAAQqB,SAAW,CAAA,EAMK,oBAAbH,SAEP,GAA2B,mBAAhBQ,YAEPA,YAAY,WAAYC,QAEvB,GAAgC,mBAArB7H,iBAAiC,CAE7CA,iBADyB,eAAgBkC,EAAa,WAAa,SAChC2F,GAAe,EACrD,CAEL,SAASA,IACL,IAAK,IAAIzJ,KAAK8H,EAAQqB,SACdrB,EAAQqB,SAASvF,eAAe5D,IAChC8H,EAAQqB,SAASnJ,GAAGsJ,OAGhC,CC7YO,MAAMI,EACqC,mBAAZC,SAAqD,mBAApBA,QAAQC,QAE/DnH,GAAOkH,QAAQC,UAAUC,KAAKpH,GAG/B,CAACA,EAAI4B,IAAiBA,EAAa5B,EAAI,GAGzCqH,EAAYhG,EAAWgG,WAAahG,EAAWiG,aCHtDC,EAAqC,oBAAdC,WACI,iBAAtBA,UAAUC,SACmB,gBAApCD,UAAUC,QAAQC,cCPf,MAAMC,EAAa,CACtBC,UDOG,cAAiBtF,EAOpBL,YAAYP,GACRW,MAAMX,GACNpC,KAAK/C,gBAAkBmF,EAAKmG,WAC/B,CACGC,WACA,MAAO,WACV,CACDjF,SACI,IAAKvD,KAAKyI,QAEN,OAEJ,MAAMzC,EAAMhG,KAAKgG,MACX0C,EAAY1I,KAAKoC,KAAKsG,UAEtBtG,EAAO6F,EACP,CAAE,EACFzG,EAAKxB,KAAKoC,KAAM,QAAS,oBAAqB,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,eAAgB,kBAAmB,SAAU,aAAc,SAAU,uBAChMpC,KAAKoC,KAAKoE,eACVpE,EAAKuG,QAAU3I,KAAKoC,KAAKoE,cAE7B,IACIxG,KAAK4I,GACyBX,EAIpB,IAAIF,EAAU/B,EAAK0C,EAAWtG,GAH9BsG,EACI,IAAIX,EAAU/B,EAAK0C,GACnB,IAAIX,EAAU/B,EAK/B,CAFD,MAAOX,GACH,OAAOrF,KAAKiB,aAAa,QAASoE,EACrC,CACDrF,KAAK4I,GAAGtK,WAAa0B,KAAKmD,OAAO7E,YDrCR,cCsCzB0B,KAAK6I,mBACR,CAMDA,oBACI7I,KAAK4I,GAAGE,OAAS,KACT9I,KAAKoC,KAAK2G,WACV/I,KAAK4I,GAAGI,QAAQC,QAEpBjJ,KAAK8D,QAAQ,EAEjB9D,KAAK4I,GAAGM,QAAWC,GAAenJ,KAAK0D,QAAQ,CAC3Cb,YAAa,8BACbC,QAASqG,IAEbnJ,KAAK4I,GAAGQ,UAAaC,GAAOrJ,KAAK+D,OAAOsF,EAAG7M,MAC3CwD,KAAK4I,GAAGU,QAAW7D,GAAMzF,KAAKoD,QAAQ,kBAAmBqC,EAC5D,CACD5B,MAAMD,GACF5D,KAAKiD,UAAW,EAGhB,IAAK,IAAIhF,EAAI,EAAGA,EAAI2F,EAAQ1F,OAAQD,IAAK,CACrC,MAAM+F,EAASJ,EAAQ3F,GACjBsL,EAAatL,IAAM2F,EAAQ1F,OAAS,EAC1ClB,EAAagH,EAAQhE,KAAK/C,gBAAiBT,IAmBvC,IAGQwD,KAAK4I,GAAGjF,KAAKnH,EAOpB,CADD,MAAOiJ,GACN,CACG8D,GAGA5B,GAAS,KACL3H,KAAKiD,UAAW,EAChBjD,KAAKiB,aAAa,QAAQ,GAC3BjB,KAAKsC,aACX,GAER,CACJ,CACDmB,eAC2B,IAAZzD,KAAK4I,KACZ5I,KAAK4I,GAAGpF,QACRxD,KAAK4I,GAAK,KAEjB,CAMD5C,MACI,IAAI9C,EAAQlD,KAAKkD,OAAS,GAC1B,MAAMsG,EAASxJ,KAAKoC,KAAKqH,OAAS,MAAQ,KAC1C,IAAIC,EAAO,GAEP1J,KAAKoC,KAAKsH,OACR,QAAUF,GAAqC,MAA3BG,OAAO3J,KAAKoC,KAAKsH,OAClC,OAASF,GAAqC,KAA3BG,OAAO3J,KAAKoC,KAAKsH,SACzCA,EAAO,IAAM1J,KAAKoC,KAAKsH,MAGvB1J,KAAKoC,KAAKwH,oBACV1G,EAAMlD,KAAKoC,KAAKyH,gBAAkB/E,KAGjC9E,KAAK/C,iBACNiG,EAAM4G,IAAM,GAEhB,MAAMC,EAAetF,EAAOvB,GAE5B,OAAQsG,EACJ,QAF8C,IAArCxJ,KAAKoC,KAAK4H,SAASC,QAAQ,KAG5B,IAAMjK,KAAKoC,KAAK4H,SAAW,IAAMhK,KAAKoC,KAAK4H,UACnDN,EACA1J,KAAKoC,KAAK8H,MACTH,EAAa7L,OAAS,IAAM6L,EAAe,GACnD,CAODtB,QACI,QAASV,CACZ,GCjKDoC,QHWG,cAAsBnH,EAOzBL,YAAYP,GAGR,GAFAW,MAAMX,GACNpC,KAAKmK,SAAU,EACS,oBAAbC,SAA0B,CACjC,MAAMC,EAAQ,WAAaD,SAASE,SACpC,IAAIZ,EAAOU,SAASV,KAEfA,IACDA,EAAOW,EAAQ,MAAQ,MAE3BrK,KAAKoG,GACoB,oBAAbgE,UACJhI,EAAK4H,WAAaI,SAASJ,UAC3BN,IAAStH,EAAKsH,KACtB1J,KAAKsG,GAAKlE,EAAKqH,SAAWY,CAC7B,CAID,MAAM9B,EAAcnG,GAAQA,EAAKmG,YACjCvI,KAAK/C,eAAiB4I,IAAY0C,CACrC,CACGC,WACA,MAAO,SACV,CAODjF,SACIvD,KAAKuK,MACR,CAODpG,MAAMC,GACFpE,KAAKsD,WAAa,UAClB,MAAMa,EAAQ,KACVnE,KAAKsD,WAAa,SAClBc,GAAS,EAEb,GAAIpE,KAAKmK,UAAYnK,KAAKiD,SAAU,CAChC,IAAIuH,EAAQ,EACRxK,KAAKmK,UACLK,IACAxK,KAAKG,KAAK,gBAAgB,aACpBqK,GAASrG,GAC/B,KAEiBnE,KAAKiD,WACNuH,IACAxK,KAAKG,KAAK,SAAS,aACbqK,GAASrG,GAC/B,IAES,MAEGA,GAEP,CAMDoG,OACIvK,KAAKmK,SAAU,EACfnK,KAAKyK,SACLzK,KAAKiB,aAAa,OACrB,CAMD8C,OAAOvH,GTpFW,EAACkO,EAAgBpM,KACnC,MAAMqM,EAAiBD,EAAe9M,MAAM2B,GACtCqE,EAAU,GAChB,IAAK,IAAI3F,EAAI,EAAGA,EAAI0M,EAAezM,OAAQD,IAAK,CAC5C,MAAM2M,EAAgBxM,EAAauM,EAAe1M,GAAIK,GAEtD,GADAsF,EAAQ1D,KAAK0K,GACc,UAAvBA,EAAcrO,KACd,KAEP,CACD,OAAOqH,CAAO,ESyFViH,CAAcrO,EAAMwD,KAAKmD,OAAO7E,YAAYlC,SAd1B4H,IAMd,GAJI,YAAchE,KAAKsD,YAA8B,SAAhBU,EAAOzH,MACxCyD,KAAK8D,SAGL,UAAYE,EAAOzH,KAEnB,OADAyD,KAAK0D,QAAQ,CAAEb,YAAa,oCACrB,EAGX7C,KAAKiE,SAASD,EAAO,IAKrB,WAAahE,KAAKsD,aAElBtD,KAAKmK,SAAU,EACfnK,KAAKiB,aAAa,gBACd,SAAWjB,KAAKsD,YAChBtD,KAAKuK,OAKhB,CAMD9G,UACI,MAAMD,EAAQ,KACVxD,KAAK6D,MAAM,CAAC,CAAEtH,KAAM,UAAW,EAE/B,SAAWyD,KAAKsD,WAChBE,IAKAxD,KAAKG,KAAK,OAAQqD,EAEzB,CAODK,MAAMD,GACF5D,KAAKiD,UAAW,ETxJF,EAACW,EAAS1G,KAE5B,MAAMgB,EAAS0F,EAAQ1F,OACjByM,EAAiB,IAAI5J,MAAM7C,GACjC,IAAI4M,EAAQ,EACZlH,EAAQxH,SAAQ,CAAC4H,EAAQ/F,KAErBjB,EAAagH,GAAQ,GAAO3F,IACxBsM,EAAe1M,GAAKI,IACdyM,IAAU5M,GACZhB,EAASyN,EAAehF,KAAKpG,GAChC,GACH,GACJ,ES4IEwL,CAAcnH,GAAUpH,IACpBwD,KAAKgL,QAAQxO,GAAM,KACfwD,KAAKiD,UAAW,EAChBjD,KAAKiB,aAAa,QAAQ,GAC5B,GAET,CAMD+E,MACI,IAAI9C,EAAQlD,KAAKkD,OAAS,GAC1B,MAAMsG,EAASxJ,KAAKoC,KAAKqH,OAAS,QAAU,OAC5C,IAAIC,EAAO,IAEP,IAAU1J,KAAKoC,KAAKwH,oBACpB1G,EAAMlD,KAAKoC,KAAKyH,gBAAkB/E,KAEjC9E,KAAK/C,gBAAmBiG,EAAM+H,MAC/B/H,EAAM4G,IAAM,GAGZ9J,KAAKoC,KAAKsH,OACR,UAAYF,GAAqC,MAA3BG,OAAO3J,KAAKoC,KAAKsH,OACpC,SAAWF,GAAqC,KAA3BG,OAAO3J,KAAKoC,KAAKsH,SAC3CA,EAAO,IAAM1J,KAAKoC,KAAKsH,MAE3B,MAAMK,EAAetF,EAAOvB,GAE5B,OAAQsG,EACJ,QAF8C,IAArCxJ,KAAKoC,KAAK4H,SAASC,QAAQ,KAG5B,IAAMjK,KAAKoC,KAAK4H,SAAW,IAAMhK,KAAKoC,KAAK4H,UACnDN,EACA1J,KAAKoC,KAAK8H,MACTH,EAAa7L,OAAS,IAAM6L,EAAe,GACnD,CAODmB,QAAQ9I,EAAO,IAEX,OADApG,OAAOmP,OAAO/I,EAAM,CAAEgE,GAAIpG,KAAKoG,GAAIE,GAAItG,KAAKsG,IAAMtG,KAAKoC,MAChD,IAAI2D,EAAQ/F,KAAKgG,MAAO5D,EAClC,CAQD4I,QAAQxO,EAAMuD,GACV,MAAMqL,EAAMpL,KAAKkL,QAAQ,CACrBjF,OAAQ,OACRzJ,KAAMA,IAEV4O,EAAIxL,GAAG,UAAWG,GAClBqL,EAAIxL,GAAG,SAAS,CAACyL,EAAWvI,KACxB9C,KAAKoD,QAAQ,iBAAkBiI,EAAWvI,EAAQ,GAEzD,CAMD2H,SACI,MAAMW,EAAMpL,KAAKkL,UACjBE,EAAIxL,GAAG,OAAQI,KAAK+D,OAAOxB,KAAKvC,OAChCoL,EAAIxL,GAAG,SAAS,CAACyL,EAAWvI,KACxB9C,KAAKoD,QAAQ,iBAAkBiI,EAAWvI,EAAQ,IAEtD9C,KAAKsL,QAAUF,CAClB,IItNCG,EAAK,sPACLC,EAAQ,CACV,SAAU,WAAY,YAAa,WAAY,OAAQ,WAAY,OAAQ,OAAQ,WAAY,OAAQ,YAAa,OAAQ,QAAS,UAElI,SAASC,EAAMxG,GAClB,MAAMyG,EAAMzG,EAAK0G,EAAI1G,EAAIgF,QAAQ,KAAMxE,EAAIR,EAAIgF,QAAQ,MAC7C,GAAN0B,IAAiB,GAANlG,IACXR,EAAMA,EAAIvG,UAAU,EAAGiN,GAAK1G,EAAIvG,UAAUiN,EAAGlG,GAAGmG,QAAQ,KAAM,KAAO3G,EAAIvG,UAAU+G,EAAGR,EAAI/G,SAE9F,IAAI2N,EAAIN,EAAGO,KAAK7G,GAAO,IAAKe,EAAM,CAAA,EAAI/H,EAAI,GAC1C,KAAOA,KACH+H,EAAIwF,EAAMvN,IAAM4N,EAAE5N,IAAM,GAU5B,OARU,GAAN0N,IAAiB,GAANlG,IACXO,EAAI+F,OAASL,EACb1F,EAAIgG,KAAOhG,EAAIgG,KAAKtN,UAAU,EAAGsH,EAAIgG,KAAK9N,OAAS,GAAG0N,QAAQ,KAAM,KACpE5F,EAAIiG,UAAYjG,EAAIiG,UAAUL,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAAIA,QAAQ,KAAM,KAC9E5F,EAAIkG,SAAU,GAElBlG,EAAImG,UAIR,SAAmB/O,EAAK8M,GACpB,MAAMkC,EAAO,WAAYC,EAAQnC,EAAK0B,QAAQQ,EAAM,KAAKxO,MAAM,KACvC,KAApBsM,EAAKlJ,MAAM,EAAG,IAA6B,IAAhBkJ,EAAKhM,QAChCmO,EAAMzL,OAAO,EAAG,GAEE,KAAlBsJ,EAAKlJ,OAAO,IACZqL,EAAMzL,OAAOyL,EAAMnO,OAAS,EAAG,GAEnC,OAAOmO,CACX,CAboBF,CAAUnG,EAAKA,EAAU,MACzCA,EAAIsG,SAaR,SAAkBtG,EAAK9C,GACnB,MAAM1G,EAAO,CAAA,EAMb,OALA0G,EAAM0I,QAAQ,6BAA6B,SAAUW,EAAIC,EAAIC,GACrDD,IACAhQ,EAAKgQ,GAAMC,EAEvB,IACWjQ,CACX,CArBmB8P,CAAStG,EAAKA,EAAW,OACjCA,CACX,CCnCO,MAAM0G,UAAehN,EAOxBiD,YAAYqD,EAAK5D,EAAO,IACpBW,QACA/C,KAAK2M,YAAc,GACf3G,GAAO,iBAAoBA,IAC3B5D,EAAO4D,EACPA,EAAM,MAENA,GACAA,EAAMyF,EAAMzF,GACZ5D,EAAK4H,SAAWhE,EAAIgG,KACpB5J,EAAKqH,OAA0B,UAAjBzD,EAAIsE,UAAyC,QAAjBtE,EAAIsE,SAC9ClI,EAAKsH,KAAO1D,EAAI0D,KACZ1D,EAAI9C,QACJd,EAAKc,MAAQ8C,EAAI9C,QAEhBd,EAAK4J,OACV5J,EAAK4H,SAAWyB,EAAMrJ,EAAK4J,MAAMA,MAErC7J,EAAsBnC,KAAMoC,GAC5BpC,KAAKyJ,OACD,MAAQrH,EAAKqH,OACPrH,EAAKqH,OACe,oBAAbW,UAA4B,WAAaA,SAASE,SAC/DlI,EAAK4H,WAAa5H,EAAKsH,OAEvBtH,EAAKsH,KAAO1J,KAAKyJ,OAAS,MAAQ,MAEtCzJ,KAAKgK,SACD5H,EAAK4H,WACoB,oBAAbI,SAA2BA,SAASJ,SAAW,aAC/DhK,KAAK0J,KACDtH,EAAKsH,OACoB,oBAAbU,UAA4BA,SAASV,KACvCU,SAASV,KACT1J,KAAKyJ,OACD,MACA,MAClBzJ,KAAKqI,WAAajG,EAAKiG,YAAc,CAAC,UAAW,aACjDrI,KAAK2M,YAAc,GACnB3M,KAAK4M,cAAgB,EACrB5M,KAAKoC,KAAOpG,OAAOmP,OAAO,CACtBjB,KAAM,aACN2C,OAAO,EACPlG,iBAAiB,EACjBmG,SAAS,EACTjD,eAAgB,IAChBkD,iBAAiB,EACjBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAmB,CACfC,UAAW,MAEfC,iBAAkB,CAAE,EACpBC,qBAAqB,GACtBjL,GACHpC,KAAKoC,KAAK8H,KACNlK,KAAKoC,KAAK8H,KAAK0B,QAAQ,MAAO,KACzB5L,KAAKoC,KAAK4K,iBAAmB,IAAM,IACb,iBAApBhN,KAAKoC,KAAKc,QACjBlD,KAAKoC,KAAKc,MR/Cf,SAAgBoK,GACnB,IAAIC,EAAM,CAAA,EACNC,EAAQF,EAAG1P,MAAM,KACrB,IAAK,IAAIK,EAAI,EAAGwP,EAAID,EAAMtP,OAAQD,EAAIwP,EAAGxP,IAAK,CAC1C,IAAIyP,EAAOF,EAAMvP,GAAGL,MAAM,KAC1B2P,EAAII,mBAAmBD,EAAK,KAAOC,mBAAmBD,EAAK,GAC9D,CACD,OAAOH,CACX,CQuC8BjO,CAAOU,KAAKoC,KAAKc,QAGvClD,KAAK4N,GAAK,KACV5N,KAAK6N,SAAW,KAChB7N,KAAK8N,aAAe,KACpB9N,KAAK+N,YAAc,KAEnB/N,KAAKgO,iBAAmB,KACQ,mBAArBnO,mBACHG,KAAKoC,KAAKiL,sBAIVrN,KAAKiO,0BAA4B,KACzBjO,KAAKkO,YAELlO,KAAKkO,UAAU1N,qBACfR,KAAKkO,UAAU1K,QAClB,EAEL3D,iBAAiB,eAAgBG,KAAKiO,2BAA2B,IAE/C,cAAlBjO,KAAKgK,WACLhK,KAAKmO,qBAAuB,KACxBnO,KAAK0D,QAAQ,kBAAmB,CAC5Bb,YAAa,2BACf,EAENhD,iBAAiB,UAAWG,KAAKmO,sBAAsB,KAG/DnO,KAAKqD,MACR,CAQD+K,gBAAgB5F,GACZ,MAAMtF,EAAQlH,OAAOmP,OAAO,CAAE,EAAEnL,KAAKoC,KAAKc,OAE1CA,EAAMmL,IdtFU,EcwFhBnL,EAAMgL,UAAY1F,EAEdxI,KAAK4N,KACL1K,EAAM+H,IAAMjL,KAAK4N,IACrB,MAAMxL,EAAOpG,OAAOmP,OAAO,GAAInL,KAAKoC,KAAKgL,iBAAiB5E,GAAOxI,KAAKoC,KAAM,CACxEc,QACAC,OAAQnD,KACRgK,SAAUhK,KAAKgK,SACfP,OAAQzJ,KAAKyJ,OACbC,KAAM1J,KAAK0J,OAEf,OAAO,IAAIrB,EAAWG,GAAMpG,EAC/B,CAMDiB,OACI,IAAI6K,EACJ,GAAIlO,KAAKoC,KAAK2K,iBACVL,EAAO4B,wBACmC,IAA1CtO,KAAKqI,WAAW4B,QAAQ,aACxBiE,EAAY,gBAEX,IAAI,IAAMlO,KAAKqI,WAAWnK,OAK3B,YAHA8B,KAAKsC,cAAa,KACdtC,KAAKiB,aAAa,QAAS,0BAA0B,GACtD,GAIHiN,EAAYlO,KAAKqI,WAAW,EAC/B,CACDrI,KAAKsD,WAAa,UAElB,IACI4K,EAAYlO,KAAKoO,gBAAgBF,EAMpC,CAJD,MAAOzI,GAGH,OAFAzF,KAAKqI,WAAWkG,aAChBvO,KAAKqD,MAER,CACD6K,EAAU7K,OACVrD,KAAKwO,aAAaN,EACrB,CAMDM,aAAaN,GACLlO,KAAKkO,WACLlO,KAAKkO,UAAU1N,qBAGnBR,KAAKkO,UAAYA,EAEjBA,EACKtO,GAAG,QAASI,KAAKyO,QAAQlM,KAAKvC,OAC9BJ,GAAG,SAAUI,KAAKiE,SAAS1B,KAAKvC,OAChCJ,GAAG,QAASI,KAAKoD,QAAQb,KAAKvC,OAC9BJ,GAAG,SAAUgD,GAAW5C,KAAK0D,QAAQ,kBAAmBd,IAChE,CAOD8L,MAAMlG,GACF,IAAI0F,EAAYlO,KAAKoO,gBAAgB5F,GACjCmG,GAAS,EACbjC,EAAO4B,uBAAwB,EAC/B,MAAMM,EAAkB,KAChBD,IAEJT,EAAUvK,KAAK,CAAC,CAAEpH,KAAM,OAAQC,KAAM,WACtC0R,EAAU/N,KAAK,UAAW0O,IACtB,IAAIF,EAEJ,GAAI,SAAWE,EAAItS,MAAQ,UAAYsS,EAAIrS,KAAM,CAG7C,GAFAwD,KAAK8O,WAAY,EACjB9O,KAAKiB,aAAa,YAAaiN,IAC1BA,EACD,OACJxB,EAAO4B,sBAAwB,cAAgBJ,EAAU1F,KACzDxI,KAAKkO,UAAU/J,OAAM,KACbwK,GAEA,WAAa3O,KAAKsD,aAEtB+D,IACArH,KAAKwO,aAAaN,GAClBA,EAAUvK,KAAK,CAAC,CAAEpH,KAAM,aACxByD,KAAKiB,aAAa,UAAWiN,GAC7BA,EAAY,KACZlO,KAAK8O,WAAY,EACjB9O,KAAK+O,QAAO,GAEnB,KACI,CACD,MAAM1J,EAAM,IAAI3C,MAAM,eAEtB2C,EAAI6I,UAAYA,EAAU1F,KAC1BxI,KAAKiB,aAAa,eAAgBoE,EACrC,KACH,EAEN,SAAS2J,IACDL,IAGJA,GAAS,EACTtH,IACA6G,EAAU1K,QACV0K,EAAY,KACf,CAED,MAAM5E,EAAWjE,IACb,MAAM4J,EAAQ,IAAIvM,MAAM,gBAAkB2C,GAE1C4J,EAAMf,UAAYA,EAAU1F,KAC5BwG,IACAhP,KAAKiB,aAAa,eAAgBgO,EAAM,EAE5C,SAASC,IACL5F,EAAQ,mBACX,CAED,SAASJ,IACLI,EAAQ,gBACX,CAED,SAAS6F,EAAUC,GACXlB,GAAakB,EAAG5G,OAAS0F,EAAU1F,MACnCwG,GAEP,CAED,MAAM3H,EAAU,KACZ6G,EAAU3N,eAAe,OAAQqO,GACjCV,EAAU3N,eAAe,QAAS+I,GAClC4E,EAAU3N,eAAe,QAAS2O,GAClClP,KAAKI,IAAI,QAAS8I,GAClBlJ,KAAKI,IAAI,YAAa+O,EAAU,EAEpCjB,EAAU/N,KAAK,OAAQyO,GACvBV,EAAU/N,KAAK,QAASmJ,GACxB4E,EAAU/N,KAAK,QAAS+O,GACxBlP,KAAKG,KAAK,QAAS+I,GACnBlJ,KAAKG,KAAK,YAAagP,GACvBjB,EAAU7K,MACb,CAMDS,SAOI,GANA9D,KAAKsD,WAAa,OAClBoJ,EAAO4B,sBAAwB,cAAgBtO,KAAKkO,UAAU1F,KAC9DxI,KAAKiB,aAAa,QAClBjB,KAAK+O,QAGD,SAAW/O,KAAKsD,YAActD,KAAKoC,KAAK0K,QAAS,CACjD,IAAI7O,EAAI,EACR,MAAMwP,EAAIzN,KAAK6N,SAAS3P,OACxB,KAAOD,EAAIwP,EAAGxP,IACV+B,KAAK0O,MAAM1O,KAAK6N,SAAS5P,GAEhC,CACJ,CAMDgG,SAASD,GACL,GAAI,YAAchE,KAAKsD,YACnB,SAAWtD,KAAKsD,YAChB,YAActD,KAAKsD,WAInB,OAHAtD,KAAKiB,aAAa,SAAU+C,GAE5BhE,KAAKiB,aAAa,aACV+C,EAAOzH,MACX,IAAK,OACDyD,KAAKqP,YAAYC,KAAK7D,MAAMzH,EAAOxH,OACnC,MACJ,IAAK,OACDwD,KAAKuP,mBACLvP,KAAKwP,WAAW,QAChBxP,KAAKiB,aAAa,QAClBjB,KAAKiB,aAAa,QAClB,MACJ,IAAK,QACD,MAAMoE,EAAM,IAAI3C,MAAM,gBAEtB2C,EAAIoK,KAAOzL,EAAOxH,KAClBwD,KAAKoD,QAAQiC,GACb,MACJ,IAAK,UACDrF,KAAKiB,aAAa,OAAQ+C,EAAOxH,MACjCwD,KAAKiB,aAAa,UAAW+C,EAAOxH,MAMnD,CAOD6S,YAAY7S,GACRwD,KAAKiB,aAAa,YAAazE,GAC/BwD,KAAK4N,GAAKpR,EAAKyO,IACfjL,KAAKkO,UAAUhL,MAAM+H,IAAMzO,EAAKyO,IAChCjL,KAAK6N,SAAW7N,KAAK0P,eAAelT,EAAKqR,UACzC7N,KAAK8N,aAAetR,EAAKsR,aACzB9N,KAAK+N,YAAcvR,EAAKuR,YACxB/N,KAAK2P,WAAanT,EAAKmT,WACvB3P,KAAK8D,SAED,WAAa9D,KAAKsD,YAEtBtD,KAAKuP,kBACR,CAMDA,mBACIvP,KAAKwC,eAAexC,KAAKgO,kBACzBhO,KAAKgO,iBAAmBhO,KAAKsC,cAAa,KACtCtC,KAAK0D,QAAQ,eAAe,GAC7B1D,KAAK8N,aAAe9N,KAAK+N,aACxB/N,KAAKoC,KAAK2G,WACV/I,KAAKgO,iBAAiB/E,OAE7B,CAMDwF,UACIzO,KAAK2M,YAAY/L,OAAO,EAAGZ,KAAK4M,eAIhC5M,KAAK4M,cAAgB,EACjB,IAAM5M,KAAK2M,YAAYzO,OACvB8B,KAAKiB,aAAa,SAGlBjB,KAAK+O,OAEZ,CAMDA,QACI,GAAI,WAAa/O,KAAKsD,YAClBtD,KAAKkO,UAAUjL,WACdjD,KAAK8O,WACN9O,KAAK2M,YAAYzO,OAAQ,CACzB,MAAM0F,EAAU5D,KAAK4P,qBACrB5P,KAAKkO,UAAUvK,KAAKC,GAGpB5D,KAAK4M,cAAgBhJ,EAAQ1F,OAC7B8B,KAAKiB,aAAa,QACrB,CACJ,CAOD2O,qBAII,KAH+B5P,KAAK2P,YACR,YAAxB3P,KAAKkO,UAAU1F,MACfxI,KAAK2M,YAAYzO,OAAS,GAE1B,OAAO8B,KAAK2M,YAEhB,IAAIkD,EAAc,EAClB,IAAK,IAAI5R,EAAI,EAAGA,EAAI+B,KAAK2M,YAAYzO,OAAQD,IAAK,CAC9C,MAAMzB,EAAOwD,KAAK2M,YAAY1O,GAAGzB,KAIjC,GAHIA,IACAqT,GXxYO,iBADIzS,EWyYeZ,GXlY1C,SAAoByI,GAChB,IAAI6K,EAAI,EAAG5R,EAAS,EACpB,IAAK,IAAID,EAAI,EAAGwP,EAAIxI,EAAI/G,OAAQD,EAAIwP,EAAGxP,IACnC6R,EAAI7K,EAAI9G,WAAWF,GACf6R,EAAI,IACJ5R,GAAU,EAEL4R,EAAI,KACT5R,GAAU,EAEL4R,EAAI,OAAUA,GAAK,MACxB5R,GAAU,GAGVD,IACAC,GAAU,GAGlB,OAAOA,CACX,CAxBe6R,CAAW3S,GAGfwH,KAAKoL,KAPQ,MAOF5S,EAAI6S,YAAc7S,EAAI8S,QWsY5BjS,EAAI,GAAK4R,EAAc7P,KAAK2P,WAC5B,OAAO3P,KAAK2M,YAAY3L,MAAM,EAAG/C,GAErC4R,GAAe,CAClB,CX/YF,IAAoBzS,EWgZnB,OAAO4C,KAAK2M,WACf,CASD9I,MAAMgL,EAAKsB,EAASpQ,GAEhB,OADAC,KAAKwP,WAAW,UAAWX,EAAKsB,EAASpQ,GAClCC,IACV,CACD2D,KAAKkL,EAAKsB,EAASpQ,GAEf,OADAC,KAAKwP,WAAW,UAAWX,EAAKsB,EAASpQ,GAClCC,IACV,CAUDwP,WAAWjT,EAAMC,EAAM2T,EAASpQ,GAS5B,GARI,mBAAsBvD,IACtBuD,EAAKvD,EACLA,OAAO2J,GAEP,mBAAsBgK,IACtBpQ,EAAKoQ,EACLA,EAAU,MAEV,YAAcnQ,KAAKsD,YAAc,WAAatD,KAAKsD,WACnD,QAEJ6M,EAAUA,GAAW,IACbC,UAAW,IAAUD,EAAQC,SACrC,MAAMpM,EAAS,CACXzH,KAAMA,EACNC,KAAMA,EACN2T,QAASA,GAEbnQ,KAAKiB,aAAa,eAAgB+C,GAClChE,KAAK2M,YAAYzM,KAAK8D,GAClBjE,GACAC,KAAKG,KAAK,QAASJ,GACvBC,KAAK+O,OACR,CAIDvL,QACI,MAAMA,EAAQ,KACVxD,KAAK0D,QAAQ,gBACb1D,KAAKkO,UAAU1K,OAAO,EAEpB6M,EAAkB,KACpBrQ,KAAKI,IAAI,UAAWiQ,GACpBrQ,KAAKI,IAAI,eAAgBiQ,GACzB7M,GAAO,EAEL8M,EAAiB,KAEnBtQ,KAAKG,KAAK,UAAWkQ,GACrBrQ,KAAKG,KAAK,eAAgBkQ,EAAgB,EAqB9C,MAnBI,YAAcrQ,KAAKsD,YAAc,SAAWtD,KAAKsD,aACjDtD,KAAKsD,WAAa,UACdtD,KAAK2M,YAAYzO,OACjB8B,KAAKG,KAAK,SAAS,KACXH,KAAK8O,UACLwB,IAGA9M,GACH,IAGAxD,KAAK8O,UACVwB,IAGA9M,KAGDxD,IACV,CAMDoD,QAAQiC,GACJqH,EAAO4B,uBAAwB,EAC/BtO,KAAKiB,aAAa,QAASoE,GAC3BrF,KAAK0D,QAAQ,kBAAmB2B,EACnC,CAMD3B,QAAQd,EAAQC,GACR,YAAc7C,KAAKsD,YACnB,SAAWtD,KAAKsD,YAChB,YAActD,KAAKsD,aAEnBtD,KAAKwC,eAAexC,KAAKgO,kBAEzBhO,KAAKkO,UAAU1N,mBAAmB,SAElCR,KAAKkO,UAAU1K,QAEfxD,KAAKkO,UAAU1N,qBACoB,mBAAxBC,sBACPA,oBAAoB,eAAgBT,KAAKiO,2BAA2B,GACpExN,oBAAoB,UAAWT,KAAKmO,sBAAsB,IAG9DnO,KAAKsD,WAAa,SAElBtD,KAAK4N,GAAK,KAEV5N,KAAKiB,aAAa,QAAS2B,EAAQC,GAGnC7C,KAAK2M,YAAc,GACnB3M,KAAK4M,cAAgB,EAE5B,CAOD8C,eAAe7B,GACX,MAAM0C,EAAmB,GACzB,IAAItS,EAAI,EACR,MAAMuS,EAAI3C,EAAS3P,OACnB,KAAOD,EAAIuS,EAAGvS,KACL+B,KAAKqI,WAAW4B,QAAQ4D,EAAS5P,KAClCsS,EAAiBrQ,KAAK2N,EAAS5P,IAEvC,OAAOsS,CACV,EAEL7D,EAAOpC,SdliBiB,Ee9BxB,MAAMxN,EAA+C,mBAAhBC,YAM/BH,EAAWZ,OAAOW,UAAUC,SAC5BH,EAAiC,mBAATC,MACT,oBAATA,MACoB,6BAAxBE,EAASC,KAAKH,MAChB+T,EAAiC,mBAATC,MACT,oBAATA,MACoB,6BAAxB9T,EAASC,KAAK6T,MAMf,SAASC,EAASvT,GACrB,OAASN,IAA0BM,aAAeL,aAlBvC,CAACK,GACyB,mBAAvBL,YAAYM,OACpBN,YAAYM,OAAOD,GACnBA,EAAIE,kBAAkBP,YAeqCM,CAAOD,KACnEX,GAAkBW,aAAeV,MACjC+T,GAAkBrT,aAAesT,IAC1C,CACO,SAASE,EAAUxT,EAAKyT,GAC3B,IAAKzT,GAAsB,iBAARA,EACf,OAAO,EAEX,GAAI2D,MAAM+P,QAAQ1T,GAAM,CACpB,IAAK,IAAIa,EAAI,EAAGwP,EAAIrQ,EAAIc,OAAQD,EAAIwP,EAAGxP,IACnC,GAAI2S,EAAUxT,EAAIa,IACd,OAAO,EAGf,OAAO,CACV,CACD,GAAI0S,EAASvT,GACT,OAAO,EAEX,GAAIA,EAAIyT,QACkB,mBAAfzT,EAAIyT,QACU,IAArBvQ,UAAUpC,OACV,OAAO0S,EAAUxT,EAAIyT,UAAU,GAEnC,IAAK,MAAMxU,KAAOe,EACd,GAAIpB,OAAOW,UAAUkF,eAAehF,KAAKO,EAAKf,IAAQuU,EAAUxT,EAAIf,IAChE,OAAO,EAGf,OAAO,CACX,CCzCO,SAAS0U,EAAkB/M,GAC9B,MAAMgN,EAAU,GACVC,EAAajN,EAAOxH,KACpB0U,EAAOlN,EAGb,OAFAkN,EAAK1U,KAAO2U,EAAmBF,EAAYD,GAC3CE,EAAKE,YAAcJ,EAAQ9S,OACpB,CAAE8F,OAAQkN,EAAMF,QAASA,EACpC,CACA,SAASG,EAAmB3U,EAAMwU,GAC9B,IAAKxU,EACD,OAAOA,EACX,GAAImU,EAASnU,GAAO,CAChB,MAAM6U,EAAc,CAAEC,cAAc,EAAM5M,IAAKsM,EAAQ9S,QAEvD,OADA8S,EAAQ9Q,KAAK1D,GACN6U,CACV,CACI,GAAItQ,MAAM+P,QAAQtU,GAAO,CAC1B,MAAM+U,EAAU,IAAIxQ,MAAMvE,EAAK0B,QAC/B,IAAK,IAAID,EAAI,EAAGA,EAAIzB,EAAK0B,OAAQD,IAC7BsT,EAAQtT,GAAKkT,EAAmB3U,EAAKyB,GAAI+S,GAE7C,OAAOO,CACV,CACI,GAAoB,iBAAT/U,KAAuBA,aAAgBwI,MAAO,CAC1D,MAAMuM,EAAU,CAAA,EAChB,IAAK,MAAMlV,KAAOG,EACVR,OAAOW,UAAUkF,eAAehF,KAAKL,EAAMH,KAC3CkV,EAAQlV,GAAO8U,EAAmB3U,EAAKH,GAAM2U,IAGrD,OAAOO,CACV,CACD,OAAO/U,CACX,CASO,SAASgV,EAAkBxN,EAAQgN,GAGtC,OAFAhN,EAAOxH,KAAOiV,GAAmBzN,EAAOxH,KAAMwU,UACvChN,EAAOoN,YACPpN,CACX,CACA,SAASyN,GAAmBjV,EAAMwU,GAC9B,IAAKxU,EACD,OAAOA,EACX,GAAIA,IAA8B,IAAtBA,EAAK8U,aAAuB,CAIpC,GAHyC,iBAAb9U,EAAKkI,KAC7BlI,EAAKkI,KAAO,GACZlI,EAAKkI,IAAMsM,EAAQ9S,OAEnB,OAAO8S,EAAQxU,EAAKkI,KAGpB,MAAM,IAAIhC,MAAM,sBAEvB,CACI,GAAI3B,MAAM+P,QAAQtU,GACnB,IAAK,IAAIyB,EAAI,EAAGA,EAAIzB,EAAK0B,OAAQD,IAC7BzB,EAAKyB,GAAKwT,GAAmBjV,EAAKyB,GAAI+S,QAGzC,GAAoB,iBAATxU,EACZ,IAAK,MAAMH,KAAOG,EACVR,OAAOW,UAAUkF,eAAehF,KAAKL,EAAMH,KAC3CG,EAAKH,GAAOoV,GAAmBjV,EAAKH,GAAM2U,IAItD,OAAOxU,CACX,CC1EY,MAAC8N,GAAW,EACjB,IAAIoH,IACX,SAAWA,GACPA,EAAWA,EAAoB,QAAI,GAAK,UACxCA,EAAWA,EAAuB,WAAI,GAAK,aAC3CA,EAAWA,EAAkB,MAAI,GAAK,QACtCA,EAAWA,EAAgB,IAAI,GAAK,MACpCA,EAAWA,EAA0B,cAAI,GAAK,gBAC9CA,EAAWA,EAAyB,aAAI,GAAK,eAC7CA,EAAWA,EAAuB,WAAI,GAAK,YAC9C,CARD,CAQGA,KAAeA,GAAa,CAAE,IA8E1B,MAAMC,WAAgBjS,EAMzBiD,YAAYiP,GACR7O,QACA/C,KAAK4R,QAAUA,CAClB,CAMDC,IAAIzU,GACA,IAAI4G,EACJ,GAAmB,iBAAR5G,EAAkB,CACzB,GAAI4C,KAAK8R,cACL,MAAM,IAAIpP,MAAM,mDAEpBsB,EAAShE,KAAK+R,aAAa3U,GAC3B,MAAM4U,EAAgBhO,EAAOzH,OAASmV,GAAWO,aAC7CD,GAAiBhO,EAAOzH,OAASmV,GAAWQ,YAC5ClO,EAAOzH,KAAOyV,EAAgBN,GAAWS,MAAQT,GAAWU,IAE5DpS,KAAK8R,cAAgB,IAAIO,GAAoBrO,GAElB,IAAvBA,EAAOoN,aACPrO,MAAM9B,aAAa,UAAW+C,IAKlCjB,MAAM9B,aAAa,UAAW+C,EAErC,KACI,KAAI2M,EAASvT,KAAQA,EAAIwB,OAe1B,MAAM,IAAI8D,MAAM,iBAAmBtF,GAbnC,IAAK4C,KAAK8R,cACN,MAAM,IAAIpP,MAAM,oDAGhBsB,EAAShE,KAAK8R,cAAcQ,eAAelV,GACvC4G,IAEAhE,KAAK8R,cAAgB,KACrB/O,MAAM9B,aAAa,UAAW+C,GAMzC,CACJ,CAOD+N,aAAa9M,GACT,IAAIhH,EAAI,EAER,MAAMkB,EAAI,CACN5C,KAAMoN,OAAO1E,EAAIzG,OAAO,KAE5B,QAA2B2H,IAAvBuL,GAAWvS,EAAE5C,MACb,MAAM,IAAImG,MAAM,uBAAyBvD,EAAE5C,MAG/C,GAAI4C,EAAE5C,OAASmV,GAAWO,cACtB9S,EAAE5C,OAASmV,GAAWQ,WAAY,CAClC,MAAMK,EAAQtU,EAAI,EAClB,KAA2B,MAApBgH,EAAIzG,SAASP,IAAcA,GAAKgH,EAAI/G,SAC3C,MAAMsU,EAAMvN,EAAIvG,UAAU6T,EAAOtU,GACjC,GAAIuU,GAAO7I,OAAO6I,IAA0B,MAAlBvN,EAAIzG,OAAOP,GACjC,MAAM,IAAIyE,MAAM,uBAEpBvD,EAAEiS,YAAczH,OAAO6I,EAC1B,CAED,GAAI,MAAQvN,EAAIzG,OAAOP,EAAI,GAAI,CAC3B,MAAMsU,EAAQtU,EAAI,EAClB,OAASA,GAAG,CAER,GAAI,MADMgH,EAAIzG,OAAOP,GAEjB,MACJ,GAAIA,IAAMgH,EAAI/G,OACV,KACP,CACDiB,EAAEsT,IAAMxN,EAAIvG,UAAU6T,EAAOtU,EAChC,MAEGkB,EAAEsT,IAAM,IAGZ,MAAMC,EAAOzN,EAAIzG,OAAOP,EAAI,GAC5B,GAAI,KAAOyU,GAAQ/I,OAAO+I,IAASA,EAAM,CACrC,MAAMH,EAAQtU,EAAI,EAClB,OAASA,GAAG,CACR,MAAM6R,EAAI7K,EAAIzG,OAAOP,GACrB,GAAI,MAAQ6R,GAAKnG,OAAOmG,IAAMA,EAAG,GAC3B7R,EACF,KACH,CACD,GAAIA,IAAMgH,EAAI/G,OACV,KACP,CACDiB,EAAEyO,GAAKjE,OAAO1E,EAAIvG,UAAU6T,EAAOtU,EAAI,GAC1C,CAED,GAAIgH,EAAIzG,SAASP,GAAI,CACjB,MAAM0U,EAAU3S,KAAK4S,SAAS3N,EAAI4N,OAAO5U,IACzC,IAAI0T,GAAQmB,eAAe3T,EAAE5C,KAAMoW,GAI/B,MAAM,IAAIjQ,MAAM,mBAHhBvD,EAAE3C,KAAOmW,CAKhB,CACD,OAAOxT,CACV,CACDyT,SAAS3N,GACL,IACI,OAAOqK,KAAK7D,MAAMxG,EAAKjF,KAAK4R,QAI/B,CAFD,MAAOnM,GACH,OAAO,CACV,CACJ,CACDsN,sBAAsBxW,EAAMoW,GACxB,OAAQpW,GACJ,KAAKmV,GAAWsB,QACZ,MAA0B,iBAAZL,EAClB,KAAKjB,GAAWuB,WACZ,YAAmB9M,IAAZwM,EACX,KAAKjB,GAAWwB,cACZ,MAA0B,iBAAZP,GAA2C,iBAAZA,EACjD,KAAKjB,GAAWS,MAChB,KAAKT,GAAWO,aACZ,OAAOlR,MAAM+P,QAAQ6B,IAAYA,EAAQzU,OAAS,EACtD,KAAKwT,GAAWU,IAChB,KAAKV,GAAWQ,WACZ,OAAOnR,MAAM+P,QAAQ6B,GAEhC,CAIDQ,UACQnT,KAAK8R,gBACL9R,KAAK8R,cAAcsB,yBACnBpT,KAAK8R,cAAgB,KAE5B,EAUL,MAAMO,GACF1P,YAAYqB,GACRhE,KAAKgE,OAASA,EACdhE,KAAKgR,QAAU,GACfhR,KAAKqT,UAAYrP,CACpB,CASDsO,eAAegB,GAEX,GADAtT,KAAKgR,QAAQ9Q,KAAKoT,GACdtT,KAAKgR,QAAQ9S,SAAW8B,KAAKqT,UAAUjC,YAAa,CAEpD,MAAMpN,EAASwN,EAAkBxR,KAAKqT,UAAWrT,KAAKgR,SAEtD,OADAhR,KAAKoT,yBACEpP,CACV,CACD,OAAO,IACV,CAIDoP,yBACIpT,KAAKqT,UAAY,KACjBrT,KAAKgR,QAAU,EAClB,gDA3RmB,sCAcjB,MAMHrO,YAAY4Q,GACRvT,KAAKuT,SAAWA,CACnB,CAOD9O,OAAOrH,GACH,OAAIA,EAAIb,OAASmV,GAAWS,OAAS/U,EAAIb,OAASmV,GAAWU,MACrDxB,EAAUxT,GAWX,CAAC4C,KAAKwT,eAAepW,IAVb4C,KAAKyT,eAAe,CACvBlX,KAAMa,EAAIb,OAASmV,GAAWS,MACxBT,GAAWO,aACXP,GAAWQ,WACjBO,IAAKrV,EAAIqV,IACTjW,KAAMY,EAAIZ,KACVoR,GAAIxQ,EAAIwQ,IAKvB,CAID4F,eAAepW,GAEX,IAAI6H,EAAM,GAAK7H,EAAIb,KAmBnB,OAjBIa,EAAIb,OAASmV,GAAWO,cACxB7U,EAAIb,OAASmV,GAAWQ,aACxBjN,GAAO7H,EAAIgU,YAAc,KAIzBhU,EAAIqV,KAAO,MAAQrV,EAAIqV,MACvBxN,GAAO7H,EAAIqV,IAAM,KAGjB,MAAQrV,EAAIwQ,KACZ3I,GAAO7H,EAAIwQ,IAGX,MAAQxQ,EAAIZ,OACZyI,GAAOqK,KAAKoE,UAAUtW,EAAIZ,KAAMwD,KAAKuT,WAElCtO,CACV,CAMDwO,eAAerW,GACX,MAAMuW,EAAiB5C,EAAkB3T,GACnC8T,EAAOlR,KAAKwT,eAAeG,EAAe3P,QAC1CgN,EAAU2C,EAAe3C,QAE/B,OADAA,EAAQ4C,QAAQ1C,GACTF,CACV,gBCzFE,SAASpR,GAAGxC,EAAKiM,EAAItJ,GAExB,OADA3C,EAAIwC,GAAGyJ,EAAItJ,GACJ,WACH3C,EAAIgD,IAAIiJ,EAAItJ,EACpB,CACA,CCEA,MAAM8T,GAAkB7X,OAAO8X,OAAO,CAClCC,QAAS,EACTC,cAAe,EACfC,WAAY,EACZC,cAAe,EAEfC,YAAa,EACb5T,eAAgB,IA0Bb,MAAMmM,WAAehN,EAIxBiD,YAAYyR,EAAI3B,EAAKrQ,GACjBW,QAeA/C,KAAKqU,WAAY,EAKjBrU,KAAKsU,WAAY,EAIjBtU,KAAKuU,cAAgB,GAIrBvU,KAAKwU,WAAa,GAOlBxU,KAAKyU,OAAS,GAKdzU,KAAK0U,UAAY,EACjB1U,KAAK2U,IAAM,EACX3U,KAAK4U,KAAO,GACZ5U,KAAK6U,MAAQ,GACb7U,KAAKoU,GAAKA,EACVpU,KAAKyS,IAAMA,EACPrQ,GAAQA,EAAK0S,OACb9U,KAAK8U,KAAO1S,EAAK0S,MAErB9U,KAAK+U,MAAQ/Y,OAAOmP,OAAO,CAAE,EAAE/I,GAC3BpC,KAAKoU,GAAGY,cACRhV,KAAKqD,MACZ,CAeG4R,mBACA,OAAQjV,KAAKqU,SAChB,CAMDa,YACI,GAAIlV,KAAKmV,KACL,OACJ,MAAMf,EAAKpU,KAAKoU,GAChBpU,KAAKmV,KAAO,CACRvV,GAAGwU,EAAI,OAAQpU,KAAK8I,OAAOvG,KAAKvC,OAChCJ,GAAGwU,EAAI,SAAUpU,KAAKoV,SAAS7S,KAAKvC,OACpCJ,GAAGwU,EAAI,QAASpU,KAAKsJ,QAAQ/G,KAAKvC,OAClCJ,GAAGwU,EAAI,QAASpU,KAAKkJ,QAAQ3G,KAAKvC,OAEzC,CAkBGqV,aACA,QAASrV,KAAKmV,IACjB,CAWDpB,UACI,OAAI/T,KAAKqU,YAETrU,KAAKkV,YACAlV,KAAKoU,GAAkB,eACxBpU,KAAKoU,GAAG/Q,OACR,SAAWrD,KAAKoU,GAAGkB,aACnBtV,KAAK8I,UALE9I,IAOd,CAIDqD,OACI,OAAOrD,KAAK+T,SACf,CAgBDpQ,QAAQ7C,GAGJ,OAFAA,EAAK8S,QAAQ,WACb5T,KAAKa,KAAKR,MAAML,KAAMc,GACfd,IACV,CAkBDa,KAAKwI,KAAOvI,GACR,GAAI+S,GAAgBhS,eAAewH,GAC/B,MAAM,IAAI3G,MAAM,IAAM2G,EAAGzM,WAAa,8BAG1C,GADAkE,EAAK8S,QAAQvK,GACTrJ,KAAK+U,MAAMQ,UAAYvV,KAAK6U,MAAMW,YAAcxV,KAAK6U,MAAMY,SAE3D,OADAzV,KAAK0V,YAAY5U,GACVd,KAEX,MAAMgE,EAAS,CACXzH,KAAMmV,GAAWS,MACjB3V,KAAMsE,EAEVkD,QAAiB,IAGjB,GAFAA,EAAOmM,QAAQC,UAAmC,IAAxBpQ,KAAK6U,MAAMzE,SAEjC,mBAAsBtP,EAAKA,EAAK5C,OAAS,GAAI,CAC7C,MAAM0P,EAAK5N,KAAK2U,MACVgB,EAAM7U,EAAK8U,MACjB5V,KAAK6V,qBAAqBjI,EAAI+H,GAC9B3R,EAAO4J,GAAKA,CACf,CACD,MAAMkI,EAAsB9V,KAAKoU,GAAG2B,QAChC/V,KAAKoU,GAAG2B,OAAO7H,WACflO,KAAKoU,GAAG2B,OAAO7H,UAAUjL,SAY7B,OAXsBjD,KAAK6U,MAAMY,YAAcK,IAAwB9V,KAAKqU,aAGnErU,KAAKqU,WACVrU,KAAKgW,wBAAwBhS,GAC7BhE,KAAKgE,OAAOA,IAGZhE,KAAKwU,WAAWtU,KAAK8D,IAEzBhE,KAAK6U,MAAQ,GACN7U,IACV,CAID6V,qBAAqBjI,EAAI+H,GACrB,IAAIM,EACJ,MAAMpP,EAAwC,QAA7BoP,EAAKjW,KAAK6U,MAAMhO,eAA4B,IAAPoP,EAAgBA,EAAKjW,KAAK+U,MAAMmB,WACtF,QAAgB/P,IAAZU,EAEA,YADA7G,KAAK4U,KAAKhH,GAAM+H,GAIpB,MAAMQ,EAAQnW,KAAKoU,GAAG9R,cAAa,YACxBtC,KAAK4U,KAAKhH,GACjB,IAAK,IAAI3P,EAAI,EAAGA,EAAI+B,KAAKwU,WAAWtW,OAAQD,IACpC+B,KAAKwU,WAAWvW,GAAG2P,KAAOA,GAC1B5N,KAAKwU,WAAW5T,OAAO3C,EAAG,GAGlC0X,EAAI9Y,KAAKmD,KAAM,IAAI0C,MAAM,2BAA2B,GACrDmE,GACH7G,KAAK4U,KAAKhH,GAAM,IAAI9M,KAEhBd,KAAKoU,GAAG5R,eAAe2T,GACvBR,EAAItV,MAAML,KAAM,CAAC,QAASc,GAAM,CAEvC,CAiBDsV,YAAY/M,KAAOvI,GAEf,MAAMuV,OAAiClQ,IAAvBnG,KAAK6U,MAAMhO,cAAmDV,IAA1BnG,KAAK+U,MAAMmB,WAC/D,OAAO,IAAItO,SAAQ,CAACC,EAASyO,KACzBxV,EAAKZ,MAAK,CAACqW,EAAMC,IACTH,EACOE,EAAOD,EAAOC,GAAQ1O,EAAQ2O,GAG9B3O,EAAQ0O,KAGvBvW,KAAKa,KAAKwI,KAAOvI,EAAK,GAE7B,CAMD4U,YAAY5U,GACR,IAAI6U,EACiC,mBAA1B7U,EAAKA,EAAK5C,OAAS,KAC1ByX,EAAM7U,EAAK8U,OAEf,MAAM5R,EAAS,CACX4J,GAAI5N,KAAK0U,YACT+B,SAAU,EACVC,SAAS,EACT5V,OACA+T,MAAO7Y,OAAOmP,OAAO,CAAEqK,WAAW,GAAQxV,KAAK6U,QAEnD/T,EAAKZ,MAAK,CAACmF,KAAQsR,KACf,GAAI3S,IAAWhE,KAAKyU,OAAO,GAEvB,OAkBJ,OAhByB,OAARpP,EAETrB,EAAOyS,SAAWzW,KAAK+U,MAAMQ,UAC7BvV,KAAKyU,OAAOlG,QACRoH,GACAA,EAAItQ,KAKZrF,KAAKyU,OAAOlG,QACRoH,GACAA,EAAI,QAASgB,IAGrB3S,EAAO0S,SAAU,EACV1W,KAAK4W,aAAa,IAE7B5W,KAAKyU,OAAOvU,KAAK8D,GACjBhE,KAAK4W,aACR,CAODA,YAAYC,GAAQ,GAChB,IAAK7W,KAAKqU,WAAoC,IAAvBrU,KAAKyU,OAAOvW,OAC/B,OAEJ,MAAM8F,EAAShE,KAAKyU,OAAO,GACvBzQ,EAAO0S,UAAYG,IAGvB7S,EAAO0S,SAAU,EACjB1S,EAAOyS,WACPzW,KAAK6U,MAAQ7Q,EAAO6Q,MACpB7U,KAAKa,KAAKR,MAAML,KAAMgE,EAAOlD,MAChC,CAODkD,OAAOA,GACHA,EAAOyO,IAAMzS,KAAKyS,IAClBzS,KAAKoU,GAAG0C,QAAQ9S,EACnB,CAMD8E,SAC4B,mBAAb9I,KAAK8U,KACZ9U,KAAK8U,MAAMtY,IACPwD,KAAK+W,mBAAmBva,EAAK,IAIjCwD,KAAK+W,mBAAmB/W,KAAK8U,KAEpC,CAODiC,mBAAmBva,GACfwD,KAAKgE,OAAO,CACRzH,KAAMmV,GAAWsB,QACjBxW,KAAMwD,KAAKgX,KACLhb,OAAOmP,OAAO,CAAE8L,IAAKjX,KAAKgX,KAAME,OAAQlX,KAAKmX,aAAe3a,GAC5DA,GAEb,CAOD8M,QAAQjE,GACCrF,KAAKqU,WACNrU,KAAKiB,aAAa,gBAAiBoE,EAE1C,CAQD6D,QAAQtG,EAAQC,GACZ7C,KAAKqU,WAAY,SACVrU,KAAK4N,GACZ5N,KAAKiB,aAAa,aAAc2B,EAAQC,EAC3C,CAODuS,SAASpR,GAEL,GADsBA,EAAOyO,MAAQzS,KAAKyS,IAG1C,OAAQzO,EAAOzH,MACX,KAAKmV,GAAWsB,QACRhP,EAAOxH,MAAQwH,EAAOxH,KAAKyO,IAC3BjL,KAAKoX,UAAUpT,EAAOxH,KAAKyO,IAAKjH,EAAOxH,KAAKya,KAG5CjX,KAAKiB,aAAa,gBAAiB,IAAIyB,MAAM,8LAEjD,MACJ,KAAKgP,GAAWS,MAChB,KAAKT,GAAWO,aACZjS,KAAKqX,QAAQrT,GACb,MACJ,KAAK0N,GAAWU,IAChB,KAAKV,GAAWQ,WACZlS,KAAKsX,MAAMtT,GACX,MACJ,KAAK0N,GAAWuB,WACZjT,KAAKuX,eACL,MACJ,KAAK7F,GAAWwB,cACZlT,KAAKmT,UACL,MAAM9N,EAAM,IAAI3C,MAAMsB,EAAOxH,KAAKgb,SAElCnS,EAAI7I,KAAOwH,EAAOxH,KAAKA,KACvBwD,KAAKiB,aAAa,gBAAiBoE,GAG9C,CAODgS,QAAQrT,GACJ,MAAMlD,EAAOkD,EAAOxH,MAAQ,GACxB,MAAQwH,EAAO4J,IACf9M,EAAKZ,KAAKF,KAAK2V,IAAI3R,EAAO4J,KAE1B5N,KAAKqU,UACLrU,KAAKyX,UAAU3W,GAGfd,KAAKuU,cAAcrU,KAAKlE,OAAO8X,OAAOhT,GAE7C,CACD2W,UAAU3W,GACN,GAAId,KAAK0X,eAAiB1X,KAAK0X,cAAcxZ,OAAQ,CACjD,MAAMgD,EAAYlB,KAAK0X,cAAc1W,QACrC,IAAK,MAAM2W,KAAYzW,EACnByW,EAAStX,MAAML,KAAMc,EAE5B,CACDiC,MAAMlC,KAAKR,MAAML,KAAMc,GACnBd,KAAKgX,MAAQlW,EAAK5C,QAA2C,iBAA1B4C,EAAKA,EAAK5C,OAAS,KACtD8B,KAAKmX,YAAcrW,EAAKA,EAAK5C,OAAS,GAE7C,CAMDyX,IAAI/H,GACA,MAAMvM,EAAOrB,KACb,IAAI4X,GAAO,EACX,OAAO,YAAa9W,GAEZ8W,IAEJA,GAAO,EACPvW,EAAK2C,OAAO,CACRzH,KAAMmV,GAAWU,IACjBxE,GAAIA,EACJpR,KAAMsE,IAEtB,CACK,CAODwW,MAAMtT,GACF,MAAM2R,EAAM3V,KAAK4U,KAAK5Q,EAAO4J,IACzB,mBAAsB+H,IACtBA,EAAItV,MAAML,KAAMgE,EAAOxH,aAChBwD,KAAK4U,KAAK5Q,EAAO4J,IAI/B,CAMDwJ,UAAUxJ,EAAIqJ,GACVjX,KAAK4N,GAAKA,EACV5N,KAAKsU,UAAY2C,GAAOjX,KAAKgX,OAASC,EACtCjX,KAAKgX,KAAOC,EACZjX,KAAKqU,WAAY,EACjBrU,KAAK6X,eACL7X,KAAKiB,aAAa,WAClBjB,KAAK4W,aAAY,EACpB,CAMDiB,eACI7X,KAAKuU,cAAcnY,SAAS0E,GAASd,KAAKyX,UAAU3W,KACpDd,KAAKuU,cAAgB,GACrBvU,KAAKwU,WAAWpY,SAAS4H,IACrBhE,KAAKgW,wBAAwBhS,GAC7BhE,KAAKgE,OAAOA,EAAO,IAEvBhE,KAAKwU,WAAa,EACrB,CAMD+C,eACIvX,KAAKmT,UACLnT,KAAKkJ,QAAQ,uBAChB,CAQDiK,UACQnT,KAAKmV,OAELnV,KAAKmV,KAAK/Y,SAAS0b,GAAeA,MAClC9X,KAAKmV,UAAOhP,GAEhBnG,KAAKoU,GAAa,SAAEpU,KACvB,CAiBDiU,aAUI,OATIjU,KAAKqU,WACLrU,KAAKgE,OAAO,CAAEzH,KAAMmV,GAAWuB,aAGnCjT,KAAKmT,UACDnT,KAAKqU,WAELrU,KAAKkJ,QAAQ,wBAEVlJ,IACV,CAMDwD,QACI,OAAOxD,KAAKiU,YACf,CAUD7D,SAASA,GAEL,OADApQ,KAAK6U,MAAMzE,SAAWA,EACfpQ,IACV,CAUGyV,eAEA,OADAzV,KAAK6U,MAAMY,UAAW,EACfzV,IACV,CAcD6G,QAAQA,GAEJ,OADA7G,KAAK6U,MAAMhO,QAAUA,EACd7G,IACV,CAYD+X,MAAMJ,GAGF,OAFA3X,KAAK0X,cAAgB1X,KAAK0X,eAAiB,GAC3C1X,KAAK0X,cAAcxX,KAAKyX,GACjB3X,IACV,CAYDgY,WAAWL,GAGP,OAFA3X,KAAK0X,cAAgB1X,KAAK0X,eAAiB,GAC3C1X,KAAK0X,cAAc9D,QAAQ+D,GACpB3X,IACV,CAmBDiY,OAAON,GACH,IAAK3X,KAAK0X,cACN,OAAO1X,KAEX,GAAI2X,EAAU,CACV,MAAMzW,EAAYlB,KAAK0X,cACvB,IAAK,IAAIzZ,EAAI,EAAGA,EAAIiD,EAAUhD,OAAQD,IAClC,GAAI0Z,IAAazW,EAAUjD,GAEvB,OADAiD,EAAUN,OAAO3C,EAAG,GACb+B,IAGlB,MAEGA,KAAK0X,cAAgB,GAEzB,OAAO1X,IACV,CAKDkY,eACI,OAAOlY,KAAK0X,eAAiB,EAChC,CAcDS,cAAcR,GAGV,OAFA3X,KAAKoY,sBAAwBpY,KAAKoY,uBAAyB,GAC3DpY,KAAKoY,sBAAsBlY,KAAKyX,GACzB3X,IACV,CAcDqY,mBAAmBV,GAGf,OAFA3X,KAAKoY,sBAAwBpY,KAAKoY,uBAAyB,GAC3DpY,KAAKoY,sBAAsBxE,QAAQ+D,GAC5B3X,IACV,CAmBDsY,eAAeX,GACX,IAAK3X,KAAKoY,sBACN,OAAOpY,KAEX,GAAI2X,EAAU,CACV,MAAMzW,EAAYlB,KAAKoY,sBACvB,IAAK,IAAIna,EAAI,EAAGA,EAAIiD,EAAUhD,OAAQD,IAClC,GAAI0Z,IAAazW,EAAUjD,GAEvB,OADAiD,EAAUN,OAAO3C,EAAG,GACb+B,IAGlB,MAEGA,KAAKoY,sBAAwB,GAEjC,OAAOpY,IACV,CAKDuY,uBACI,OAAOvY,KAAKoY,uBAAyB,EACxC,CAQDpC,wBAAwBhS,GACpB,GAAIhE,KAAKoY,uBAAyBpY,KAAKoY,sBAAsBla,OAAQ,CACjE,MAAMgD,EAAYlB,KAAKoY,sBAAsBpX,QAC7C,IAAK,MAAM2W,KAAYzW,EACnByW,EAAStX,MAAML,KAAMgE,EAAOxH,KAEnC,CACJ,ECzzBE,SAASgc,GAAQpW,GACpBA,EAAOA,GAAQ,GACfpC,KAAKyY,GAAKrW,EAAKsW,KAAO,IACtB1Y,KAAK2Y,IAAMvW,EAAKuW,KAAO,IACvB3Y,KAAK4Y,OAASxW,EAAKwW,QAAU,EAC7B5Y,KAAK6Y,OAASzW,EAAKyW,OAAS,GAAKzW,EAAKyW,QAAU,EAAIzW,EAAKyW,OAAS,EAClE7Y,KAAK8Y,SAAW,CACpB,CAOAN,GAAQ7b,UAAUoc,SAAW,WACzB,IAAIN,EAAKzY,KAAKyY,GAAK7T,KAAKoU,IAAIhZ,KAAK4Y,OAAQ5Y,KAAK8Y,YAC9C,GAAI9Y,KAAK6Y,OAAQ,CACb,IAAII,EAAOrU,KAAKsU,SACZC,EAAYvU,KAAKC,MAAMoU,EAAOjZ,KAAK6Y,OAASJ,GAChDA,EAAoC,IAAN,EAAxB7T,KAAKC,MAAa,GAAPoU,IAAuBR,EAAKU,EAAYV,EAAKU,CACjE,CACD,OAAgC,EAAzBvU,KAAK8T,IAAID,EAAIzY,KAAK2Y,IAC7B,EAMAH,GAAQ7b,UAAUyc,MAAQ,WACtBpZ,KAAK8Y,SAAW,CACpB,EAMAN,GAAQ7b,UAAU0c,OAAS,SAAUX,GACjC1Y,KAAKyY,GAAKC,CACd,EAMAF,GAAQ7b,UAAU2c,OAAS,SAAUX,GACjC3Y,KAAK2Y,IAAMA,CACf,EAMAH,GAAQ7b,UAAU4c,UAAY,SAAUV,GACpC7Y,KAAK6Y,OAASA,CAClB,EC3DO,MAAMW,WAAgB9Z,EACzBiD,YAAYqD,EAAK5D,GACb,IAAI6T,EACJlT,QACA/C,KAAKyZ,KAAO,GACZzZ,KAAKmV,KAAO,GACRnP,GAAO,iBAAoBA,IAC3B5D,EAAO4D,EACPA,OAAMG,IAEV/D,EAAOA,GAAQ,IACV8H,KAAO9H,EAAK8H,MAAQ,aACzBlK,KAAKoC,KAAOA,EACZD,EAAsBnC,KAAMoC,GAC5BpC,KAAK0Z,cAAmC,IAAtBtX,EAAKsX,cACvB1Z,KAAK2Z,qBAAqBvX,EAAKuX,sBAAwBC,KACvD5Z,KAAK6Z,kBAAkBzX,EAAKyX,mBAAqB,KACjD7Z,KAAK8Z,qBAAqB1X,EAAK0X,sBAAwB,KACvD9Z,KAAK+Z,oBAAwD,QAAnC9D,EAAK7T,EAAK2X,2BAAwC,IAAP9D,EAAgBA,EAAK,IAC1FjW,KAAKga,QAAU,IAAIxB,GAAQ,CACvBE,IAAK1Y,KAAK6Z,oBACVlB,IAAK3Y,KAAK8Z,uBACVjB,OAAQ7Y,KAAK+Z,wBAEjB/Z,KAAK6G,QAAQ,MAAQzE,EAAKyE,QAAU,IAAQzE,EAAKyE,SACjD7G,KAAKsV,YAAc,SACnBtV,KAAKgG,IAAMA,EACX,MAAMiU,EAAU7X,EAAK8X,QAAUA,GAC/Bla,KAAKma,QAAU,IAAIF,EAAQG,QAC3Bpa,KAAKqa,QAAU,IAAIJ,EAAQtI,QAC3B3R,KAAKgV,cAAoC,IAArB5S,EAAKkY,YACrBta,KAAKgV,cACLhV,KAAKqD,MACZ,CACDqW,aAAaa,GACT,OAAKja,UAAUpC,QAEf8B,KAAKwa,gBAAkBD,EAChBva,MAFIA,KAAKwa,aAGnB,CACDb,qBAAqBY,GACjB,YAAUpU,IAANoU,EACOva,KAAKya,uBAChBza,KAAKya,sBAAwBF,EACtBva,KACV,CACD6Z,kBAAkBU,GACd,IAAItE,EACJ,YAAU9P,IAANoU,EACOva,KAAK0a,oBAChB1a,KAAK0a,mBAAqBH,EACF,QAAvBtE,EAAKjW,KAAKga,eAA4B,IAAP/D,GAAyBA,EAAGoD,OAAOkB,GAC5Dva,KACV,CACD+Z,oBAAoBQ,GAChB,IAAItE,EACJ,YAAU9P,IAANoU,EACOva,KAAK2a,sBAChB3a,KAAK2a,qBAAuBJ,EACJ,QAAvBtE,EAAKjW,KAAKga,eAA4B,IAAP/D,GAAyBA,EAAGsD,UAAUgB,GAC/Dva,KACV,CACD8Z,qBAAqBS,GACjB,IAAItE,EACJ,YAAU9P,IAANoU,EACOva,KAAK4a,uBAChB5a,KAAK4a,sBAAwBL,EACL,QAAvBtE,EAAKjW,KAAKga,eAA4B,IAAP/D,GAAyBA,EAAGqD,OAAOiB,GAC5Dva,KACV,CACD6G,QAAQ0T,GACJ,OAAKja,UAAUpC,QAEf8B,KAAK6a,SAAWN,EACTva,MAFIA,KAAK6a,QAGnB,CAODC,wBAES9a,KAAK+a,eACN/a,KAAKwa,eACqB,IAA1Bxa,KAAKga,QAAQlB,UAEb9Y,KAAKgb,WAEZ,CAQD3X,KAAKtD,GACD,IAAKC,KAAKsV,YAAYrL,QAAQ,QAC1B,OAAOjK,KACXA,KAAK+V,OAAS,IAAIkF,EAAOjb,KAAKgG,IAAKhG,KAAKoC,MACxC,MAAMe,EAASnD,KAAK+V,OACd1U,EAAOrB,KACbA,KAAKsV,YAAc,UACnBtV,KAAKkb,eAAgB,EAErB,MAAMC,EAAiBvb,GAAGuD,EAAQ,QAAQ,WACtC9B,EAAKyH,SACL/I,GAAMA,GAClB,IAEcqb,EAAWxb,GAAGuD,EAAQ,SAAUkC,IAClChE,EAAKgG,UACLhG,EAAKiU,YAAc,SACnBtV,KAAKiB,aAAa,QAASoE,GACvBtF,EACAA,EAAGsF,GAIHhE,EAAKyZ,sBACR,IAEL,IAAI,IAAU9a,KAAK6a,SAAU,CACzB,MAAMhU,EAAU7G,KAAK6a,SACL,IAAZhU,GACAsU,IAGJ,MAAMhF,EAAQnW,KAAKsC,cAAa,KAC5B6Y,IACAhY,EAAOK,QAEPL,EAAOtC,KAAK,QAAS,IAAI6B,MAAM,WAAW,GAC3CmE,GACC7G,KAAKoC,KAAK2G,WACVoN,EAAMlN,QAEVjJ,KAAKmV,KAAKjV,MAAK,WACXgC,aAAaiU,EAC7B,GACS,CAGD,OAFAnW,KAAKmV,KAAKjV,KAAKib,GACfnb,KAAKmV,KAAKjV,KAAKkb,GACRpb,IACV,CAOD+T,QAAQhU,GACJ,OAAOC,KAAKqD,KAAKtD,EACpB,CAMD+I,SAEI9I,KAAKqH,UAELrH,KAAKsV,YAAc,OACnBtV,KAAKiB,aAAa,QAElB,MAAMkC,EAASnD,KAAK+V,OACpB/V,KAAKmV,KAAKjV,KAAKN,GAAGuD,EAAQ,OAAQnD,KAAKqb,OAAO9Y,KAAKvC,OAAQJ,GAAGuD,EAAQ,OAAQnD,KAAKsb,OAAO/Y,KAAKvC,OAAQJ,GAAGuD,EAAQ,QAASnD,KAAKsJ,QAAQ/G,KAAKvC,OAAQJ,GAAGuD,EAAQ,QAASnD,KAAKkJ,QAAQ3G,KAAKvC,OAAQJ,GAAGI,KAAKqa,QAAS,UAAWra,KAAKub,UAAUhZ,KAAKvC,OACtP,CAMDqb,SACIrb,KAAKiB,aAAa,OACrB,CAMDqa,OAAO9e,GACH,IACIwD,KAAKqa,QAAQxI,IAAIrV,EAIpB,CAFD,MAAOiJ,GACHzF,KAAKkJ,QAAQ,cAAezD,EAC/B,CACJ,CAMD8V,UAAUvX,GAEN2D,GAAS,KACL3H,KAAKiB,aAAa,SAAU+C,EAAO,GACpChE,KAAKsC,aACX,CAMDgH,QAAQjE,GACJrF,KAAKiB,aAAa,QAASoE,EAC9B,CAODlC,OAAOsP,EAAKrQ,GACR,IAAIe,EAASnD,KAAKyZ,KAAKhH,GAQvB,OAPKtP,EAIInD,KAAKgV,eAAiB7R,EAAOkS,QAClClS,EAAO4Q,WAJP5Q,EAAS,IAAIuJ,GAAO1M,KAAMyS,EAAKrQ,GAC/BpC,KAAKyZ,KAAKhH,GAAOtP,GAKdA,CACV,CAODqY,SAASrY,GACL,MAAMsW,EAAOzd,OAAOG,KAAK6D,KAAKyZ,MAC9B,IAAK,MAAMhH,KAAOgH,EAAM,CAEpB,GADezZ,KAAKyZ,KAAKhH,GACd4C,OACP,MAEP,CACDrV,KAAKyb,QACR,CAOD3E,QAAQ9S,GACJ,MAAM2G,EAAiB3K,KAAKma,QAAQ1V,OAAOT,GAC3C,IAAK,IAAI/F,EAAI,EAAGA,EAAI0M,EAAezM,OAAQD,IACvC+B,KAAK+V,OAAOlS,MAAM8G,EAAe1M,GAAI+F,EAAOmM,QAEnD,CAMD9I,UACIrH,KAAKmV,KAAK/Y,SAAS0b,GAAeA,MAClC9X,KAAKmV,KAAKjX,OAAS,EACnB8B,KAAKqa,QAAQlH,SAChB,CAMDsI,SACIzb,KAAKkb,eAAgB,EACrBlb,KAAK+a,eAAgB,EACrB/a,KAAKkJ,QAAQ,gBACTlJ,KAAK+V,QACL/V,KAAK+V,OAAOvS,OACnB,CAMDyQ,aACI,OAAOjU,KAAKyb,QACf,CAMDvS,QAAQtG,EAAQC,GACZ7C,KAAKqH,UACLrH,KAAKga,QAAQZ,QACbpZ,KAAKsV,YAAc,SACnBtV,KAAKiB,aAAa,QAAS2B,EAAQC,GAC/B7C,KAAKwa,gBAAkBxa,KAAKkb,eAC5Blb,KAAKgb,WAEZ,CAMDA,YACI,GAAIhb,KAAK+a,eAAiB/a,KAAKkb,cAC3B,OAAOlb,KACX,MAAMqB,EAAOrB,KACb,GAAIA,KAAKga,QAAQlB,UAAY9Y,KAAKya,sBAC9Bza,KAAKga,QAAQZ,QACbpZ,KAAKiB,aAAa,oBAClBjB,KAAK+a,eAAgB,MAEpB,CACD,MAAMW,EAAQ1b,KAAKga,QAAQjB,WAC3B/Y,KAAK+a,eAAgB,EACrB,MAAM5E,EAAQnW,KAAKsC,cAAa,KACxBjB,EAAK6Z,gBAETlb,KAAKiB,aAAa,oBAAqBI,EAAK2Y,QAAQlB,UAEhDzX,EAAK6Z,eAET7Z,EAAKgC,MAAMgC,IACHA,GACAhE,EAAK0Z,eAAgB,EACrB1Z,EAAK2Z,YACLhb,KAAKiB,aAAa,kBAAmBoE,IAGrChE,EAAKsa,aACR,IACH,GACHD,GACC1b,KAAKoC,KAAK2G,WACVoN,EAAMlN,QAEVjJ,KAAKmV,KAAKjV,MAAK,WACXgC,aAAaiU,EAC7B,GACS,CACJ,CAMDwF,cACI,MAAMC,EAAU5b,KAAKga,QAAQlB,SAC7B9Y,KAAK+a,eAAgB,EACrB/a,KAAKga,QAAQZ,QACbpZ,KAAKiB,aAAa,YAAa2a,EAClC,ECjWL,MAAMC,GAAQ,CAAA,EACd,SAAS9d,GAAOiI,EAAK5D,GACE,iBAAR4D,IACP5D,EAAO4D,EACPA,OAAMG,GAGV,MAAM2V,ECHH,SAAa9V,EAAKkE,EAAO,GAAI6R,GAChC,IAAI3e,EAAM4I,EAEV+V,EAAMA,GAA4B,oBAAb3R,UAA4BA,SAC7C,MAAQpE,IACRA,EAAM+V,EAAIzR,SAAW,KAAOyR,EAAI/P,MAEjB,iBAARhG,IACH,MAAQA,EAAIxH,OAAO,KAEfwH,EADA,MAAQA,EAAIxH,OAAO,GACbud,EAAIzR,SAAWtE,EAGf+V,EAAI/P,KAAOhG,GAGpB,sBAAsBgW,KAAKhW,KAExBA,OADA,IAAuB+V,EACjBA,EAAIzR,SAAW,KAAOtE,EAGtB,WAAaA,GAI3B5I,EAAMqO,EAAMzF,IAGX5I,EAAIsM,OACD,cAAcsS,KAAK5e,EAAIkN,UACvBlN,EAAIsM,KAAO,KAEN,eAAesS,KAAK5e,EAAIkN,YAC7BlN,EAAIsM,KAAO,QAGnBtM,EAAI8M,KAAO9M,EAAI8M,MAAQ,IACvB,MACM8B,GADkC,IAA3B5O,EAAI4O,KAAK/B,QAAQ,KACV,IAAM7M,EAAI4O,KAAO,IAAM5O,EAAI4O,KAS/C,OAPA5O,EAAIwQ,GAAKxQ,EAAIkN,SAAW,MAAQ0B,EAAO,IAAM5O,EAAIsM,KAAOQ,EAExD9M,EAAI6e,KACA7e,EAAIkN,SACA,MACA0B,GACC+P,GAAOA,EAAIrS,OAAStM,EAAIsM,KAAO,GAAK,IAAMtM,EAAIsM,MAChDtM,CACX,CD7CmB8e,CAAIlW,GADnB5D,EAAOA,GAAQ,IACc8H,MAAQ,cAC/B6B,EAAS+P,EAAO/P,OAChB6B,EAAKkO,EAAOlO,GACZ1D,EAAO4R,EAAO5R,KACdiS,EAAgBN,GAAMjO,IAAO1D,KAAQ2R,GAAMjO,GAAU,KAK3D,IAAIwG,EAaJ,OAjBsBhS,EAAKga,UACvBha,EAAK,0BACL,IAAUA,EAAKia,WACfF,EAGA/H,EAAK,IAAIoF,GAAQzN,EAAQ3J,IAGpByZ,GAAMjO,KACPiO,GAAMjO,GAAM,IAAI4L,GAAQzN,EAAQ3J,IAEpCgS,EAAKyH,GAAMjO,IAEXkO,EAAO5Y,QAAUd,EAAKc,QACtBd,EAAKc,MAAQ4Y,EAAOxP,UAEjB8H,EAAGjR,OAAO2Y,EAAO5R,KAAM9H,EAClC,CAGApG,OAAOmP,OAAOpN,GAAQ,CAClByb,WACA9M,UACA0H,GAAIrW,GACJgW,QAAShW"}