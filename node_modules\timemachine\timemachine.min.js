!function(a,b){if("function"==typeof define)define(b);else if("undefined"!=typeof module&&module.exports)module.exports=b();else{var c=b(),d=this,e=d[a];c.noConflict=function(){return d[a]=e,c},d[a]=c}}("timemachine",function(){var a=Date,b={timestamp:0,tick:!1,tickStartDate:null,keepTime:!1,difference:0,config:function(b){this.timestamp=a.parse(b.dateString)||b.timestamp||this.timestamp,this.difference=b.difference||this.difference,this.keepTime=b.keepTime||this.keepTime,this.tick=b.tick||this.tick,this.tick&&(this.tickStartDate=new a),this._apply()},reset:function(){this.timestamp=0,this.tick=!1,this.tickStartDate=null,this.keepTime=!1,this.difference=0,Date=a,Date.prototype=a.prototype},_apply:function(){var b=this;Date=function(){var c;if(c=b.keepTime?new a:1===arguments.length?new a(arguments[0]):2===arguments.length?new a(arguments[0],arguments[1]):3===arguments.length?new a(arguments[0],arguments[1],arguments[2]):4===arguments.length?new a(arguments[0],arguments[1],arguments[2],arguments[3]):5===arguments.length?new a(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4]):6===arguments.length?new a(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4],arguments[5]):7===arguments.length?new a(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4],arguments[5],arguments[6]):new a(b.timestamp),0===arguments.length){var d=b._getDifference();0!==d&&(c=new a(c.getTime()+d))}return c},Date.prototype=a.prototype,Date.now=function(){return(b.keepTime?a.now():b.timestamp)+b._getDifference()},Date.OriginalDate=a,Date.UTC=a.UTC},_getDifference:function(){var b=this.difference;return this.tick&&(b+=a.now()-this.tickStartDate.getTime()),b}};return b._apply(),b});