# Installation
> `npm install --save @types/react`

# Summary
This package contains type definitions for React (http://facebook.github.io/react/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react.

### Additional Details
 * Last updated: Fri, 24 Mar 2023 16:02:37 GMT
 * Dependencies: [@types/csstype](https://npmjs.com/package/@types/csstype), [@types/prop-types](https://npmjs.com/package/@types/prop-types), [@types/scheduler](https://npmjs.com/package/@types/scheduler)
 * Global values: `React`

# Credits
These definitions were written by [<PERSON><PERSON>](https://asana.com), [AssureSign](http://www.assuresign.com), [Microsoft](https://microsoft.com), [<PERSON>](https://github.com/johnnyreilly), [<PERSON><PERSON> Benezech](https://github.com/bbenezech), [<PERSON><PERSON><PERSON>](https://github.com/p<PERSON><PERSON><PERSON><PERSON>), [<PERSON>](https://github.com/ericanderson), [<PERSON><PERSON><PERSON>](https://github.com/<PERSON><PERSON>dasNavickas), [<PERSON>](https://github.com/theruther4d), [Guilherme Hübner](https://github.com/guilhermehubner), [Ferdy Budhidharma](https://github.com/ferdaber), [<PERSON> <PERSON>kotoharisoa](https://github.com/jrakotoharisoa), [Olivier Pascal](https://github.com/pascaloliv), [Martin Hochel](https://github.com/hotell), [Frank Li](https://github.com/franklixuefei), [Jessica Franco](https://github.com/Jessidhia), [Saransh Kataria](https://github.com/saranshkataria), [Kanitkorn Sujautra](https://github.com/lukyth), [Sebastian Silbermann](https://github.com/eps1lon), [Kyle Scully](https://github.com/zieka), [Cong Zhang](https://github.com/dancerphil), [Dimitri Mitropoulos](https://github.com/dimitropoulos), [JongChan Choi](https://github.com/disjukr), [Victor Magalhães](https://github.com/vhfmag), [Dale Tan](https://github.com/hellatan), [Priyanshu Rav](https://github.com/priyanshurav), and [Dmitry Semigradsky](https://github.com/Semigradsky).
