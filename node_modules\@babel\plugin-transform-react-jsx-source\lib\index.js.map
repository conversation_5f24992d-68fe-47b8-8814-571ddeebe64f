{"version": 3, "names": ["TRACE_ID", "FILE_NAME_VAR", "createNodeFromNullish", "val", "fn", "t", "nullLiteral", "declare", "api", "assertVersion", "makeTrace", "fileNameIdentifier", "line", "column", "fileLineLiteral", "numericLiteral", "fileColumnLiteral", "c", "template", "expression", "ast", "isSourceAttr", "attr", "isJSXAttribute", "name", "visitor", "JSXOpeningElement", "path", "state", "node", "loc", "attributes", "some", "fileNameId", "scope", "generateUidIdentifier", "getProgramParent", "push", "id", "init", "stringLiteral", "filename", "jsxAttribute", "jsxIdentifier", "jsxExpressionContainer", "cloneNode", "start"], "sources": ["../src/index.ts"], "sourcesContent": ["/**\n * This adds {fileName, lineNumber, columnNumber} annotations to JSX tags.\n *\n * NOTE: lineNumber and columnNumber are both 1-based.\n *\n * == JSX Literals ==\n *\n * <sometag />\n *\n * becomes:\n *\n * var __jsxFileName = 'this/file.js';\n * <sometag __source={{fileName: __jsxFileName, lineNumber: 10, columnNumber: 1}}/>\n */\nimport { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t, template } from \"@babel/core\";\n\nconst TRACE_ID = \"__source\";\nconst FILE_NAME_VAR = \"_jsxFileName\";\n\nconst createNodeFromNullish = <T, N extends t.Node>(\n  val: T | null,\n  fn: (val: T) => N,\n): N | t.NullLiteral => (val == null ? t.nullLiteral() : fn(val));\n\ntype State = {\n  fileNameIdentifier: t.Identifier;\n};\nexport default declare<State>(api => {\n  api.assertVersion(7);\n\n  function makeTrace(\n    fileNameIdentifier: t.Identifier,\n    { line, column }: { line: number; column: number },\n  ) {\n    const fileLineLiteral = createNodeFromNullish(line, t.numericLiteral);\n    const fileColumnLiteral = createNodeFromNullish(column, c =>\n      // c + 1 to make it 1-based instead of 0-based.\n      t.numericLiteral(c + 1),\n    );\n\n    return template.expression.ast`{\n      fileName: ${fileNameIdentifier},\n      lineNumber: ${fileLineLiteral},\n      columnNumber: ${fileColumnLiteral},\n    }`;\n  }\n\n  const isSourceAttr = (attr: t.Node) =>\n    t.isJSXAttribute(attr) && attr.name.name === TRACE_ID;\n\n  return {\n    name: \"transform-react-jsx-source\",\n    visitor: {\n      JSXOpeningElement(path, state) {\n        const { node } = path;\n        if (\n          // the element was generated and doesn't have location information\n          !node.loc ||\n          // Already has __source\n          path.node.attributes.some(isSourceAttr)\n        ) {\n          return;\n        }\n\n        if (!state.fileNameIdentifier) {\n          const fileNameId = path.scope.generateUidIdentifier(FILE_NAME_VAR);\n          state.fileNameIdentifier = fileNameId;\n\n          path.scope.getProgramParent().push({\n            id: fileNameId,\n            init: t.stringLiteral(state.filename || \"\"),\n          });\n        }\n\n        node.attributes.push(\n          t.jsxAttribute(\n            t.jsxIdentifier(TRACE_ID),\n            t.jsxExpressionContainer(\n              makeTrace(t.cloneNode(state.fileNameIdentifier), node.loc.start),\n            ),\n          ),\n        );\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;;AAcA;;AACA;;AAEA,MAAMA,QAAQ,GAAG,UAAjB;AACA,MAAMC,aAAa,GAAG,cAAtB;;AAEA,MAAMC,qBAAqB,GAAG,CAC5BC,GAD4B,EAE5BC,EAF4B,KAGLD,GAAG,IAAI,IAAP,GAAcE,WAAA,CAAEC,WAAF,EAAd,GAAgCF,EAAE,CAACD,GAAD,CAH3D;;eAQe,IAAAI,0BAAA,EAAeC,GAAG,IAAI;EACnCA,GAAG,CAACC,aAAJ,CAAkB,CAAlB;;EAEA,SAASC,SAAT,CACEC,kBADF,EAEE;IAAEC,IAAF;IAAQC;EAAR,CAFF,EAGE;IACA,MAAMC,eAAe,GAAGZ,qBAAqB,CAACU,IAAD,EAAOP,WAAA,CAAEU,cAAT,CAA7C;IACA,MAAMC,iBAAiB,GAAGd,qBAAqB,CAACW,MAAD,EAASI,CAAC,IAEvDZ,WAAA,CAAEU,cAAF,CAAiBE,CAAC,GAAG,CAArB,CAF6C,CAA/C;IAKA,OAAOC,cAAA,CAASC,UAAT,CAAoBC,GAAI;AACnC,kBAAkBT,kBAAmB;AACrC,oBAAoBG,eAAgB;AACpC,sBAAsBE,iBAAkB;AACxC,MAJI;EAKD;;EAED,MAAMK,YAAY,GAAIC,IAAD,IACnBjB,WAAA,CAAEkB,cAAF,CAAiBD,IAAjB,KAA0BA,IAAI,CAACE,IAAL,CAAUA,IAAV,KAAmBxB,QAD/C;;EAGA,OAAO;IACLwB,IAAI,EAAE,4BADD;IAELC,OAAO,EAAE;MACPC,iBAAiB,CAACC,IAAD,EAAOC,KAAP,EAAc;QAC7B,MAAM;UAAEC;QAAF,IAAWF,IAAjB;;QACA,IAEE,CAACE,IAAI,CAACC,GAAN,IAEAH,IAAI,CAACE,IAAL,CAAUE,UAAV,CAAqBC,IAArB,CAA0BX,YAA1B,CAJF,EAKE;UACA;QACD;;QAED,IAAI,CAACO,KAAK,CAACjB,kBAAX,EAA+B;UAC7B,MAAMsB,UAAU,GAAGN,IAAI,CAACO,KAAL,CAAWC,qBAAX,CAAiClC,aAAjC,CAAnB;UACA2B,KAAK,CAACjB,kBAAN,GAA2BsB,UAA3B;UAEAN,IAAI,CAACO,KAAL,CAAWE,gBAAX,GAA8BC,IAA9B,CAAmC;YACjCC,EAAE,EAAEL,UAD6B;YAEjCM,IAAI,EAAElC,WAAA,CAAEmC,aAAF,CAAgBZ,KAAK,CAACa,QAAN,IAAkB,EAAlC;UAF2B,CAAnC;QAID;;QAEDZ,IAAI,CAACE,UAAL,CAAgBM,IAAhB,CACEhC,WAAA,CAAEqC,YAAF,CACErC,WAAA,CAAEsC,aAAF,CAAgB3C,QAAhB,CADF,EAEEK,WAAA,CAAEuC,sBAAF,CACElC,SAAS,CAACL,WAAA,CAAEwC,SAAF,CAAYjB,KAAK,CAACjB,kBAAlB,CAAD,EAAwCkB,IAAI,CAACC,GAAL,CAASgB,KAAjD,CADX,CAFF,CADF;MAQD;;IA9BM;EAFJ,CAAP;AAmCD,CA1Dc,C"}