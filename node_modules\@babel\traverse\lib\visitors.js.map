{"version": 3, "names": ["virtualTypes", "require", "_t", "DEPRECATED_KEYS", "DEPRECATED_ALIASES", "FLIPPED_ALIAS_KEYS", "TYPES", "__internal__deprecationWarning", "deprecationWarning", "isVirtualType", "type", "explode", "visitor", "_exploded", "nodeType", "Object", "keys", "should<PERSON><PERSON>re<PERSON>ey", "parts", "split", "length", "fns", "part", "verify", "__esModule", "ensureEntranceObjects", "ensureCallbackArrays", "wrapCheck", "types", "mergePair", "aliases", "deprecated<PERSON>ey", "depre<PERSON><PERSON><PERSON><PERSON>", "alias", "existing", "assign", "_verified", "Error", "validateVisitorMethods", "indexOf", "visitors", "<PERSON><PERSON><PERSON>", "path", "val", "concat", "fn", "TypeError", "merge", "states", "wrapper", "rootVisitor", "i", "state", "visitorType", "wrapWithStateOrWrapper", "nodeVisitor", "oldVisitor", "newVisitor", "key", "Array", "isArray", "map", "newFn", "call", "toString", "obj", "enter", "exit", "apply", "arguments", "dest", "src"], "sources": ["../src/visitors.ts"], "sourcesContent": ["import * as virtualTypes from \"./path/lib/virtual-types\";\nimport {\n  DEPRECATED_KEYS,\n  DEPRECATED_ALIASES,\n  FLIPPED_ALIAS_KEYS,\n  TYPES,\n  __internal__deprecationWarning as deprecationWarning,\n} from \"@babel/types\";\nimport type { <PERSON>de<PERSON><PERSON>, Visitor } from \"./index\";\n\ntype VIRTUAL_TYPES = keyof typeof virtualTypes;\nfunction isVirtualType(type: string): type is VIRTUAL_TYPES {\n  return type in virtualTypes;\n}\n\n/**\n * explode() will take a visitor object with all of the various shorthands\n * that we support, and validates & normalizes it into a common format, ready\n * to be used in traversal\n *\n * The various shorthands are:\n * * `Identifier() { ... }` -> `Identifier: { enter() { ... } }`\n * * `\"Identifier|NumericLiteral\": { ... }` -> `Identifier: { ... }, NumericLiteral: { ... }`\n * * Aliases in `@babel/types`: e.g. `Property: { ... }` -> `ObjectProperty: { ... }, ClassProperty: { ... }`\n *\n * Other normalizations are:\n * * Visitors of virtual types are wrapped, so that they are only visited when\n *   their dynamic check passes\n * * `enter` and `exit` functions are wrapped in arrays, to ease merging of\n *   visitors\n */\nexport function explode(visitor: Visitor) {\n  if (visitor._exploded) return visitor;\n  visitor._exploded = true;\n\n  // normalise pipes\n  for (const nodeType of Object.keys(visitor) as (keyof Visitor)[]) {\n    if (shouldIgnoreKey(nodeType)) continue;\n\n    const parts: Array<string> = nodeType.split(\"|\");\n    if (parts.length === 1) continue;\n\n    const fns = visitor[nodeType];\n    delete visitor[nodeType];\n\n    for (const part of parts) {\n      // @ts-expect-error part will be verified by `verify` later\n      visitor[part] = fns;\n    }\n  }\n\n  // verify data structure\n  verify(visitor);\n\n  // make sure there's no __esModule type since this is because we're using loose mode\n  // and it sets __esModule to be enumerable on all modules :(\n  // @ts-expect-error ESModule interop\n  delete visitor.__esModule;\n\n  // ensure visitors are objects\n  ensureEntranceObjects(visitor);\n\n  // ensure enter/exit callbacks are arrays\n  ensureCallbackArrays(visitor);\n\n  // add type wrappers\n  for (const nodeType of Object.keys(visitor)) {\n    if (shouldIgnoreKey(nodeType)) continue;\n\n    if (!isVirtualType(nodeType)) continue;\n\n    // wrap all the functions\n    const fns = visitor[nodeType];\n    for (const type of Object.keys(fns)) {\n      // @ts-expect-error manipulating visitors\n      fns[type] = wrapCheck(nodeType, fns[type]);\n    }\n\n    // clear it from the visitor\n    delete visitor[nodeType];\n\n    const types = virtualTypes[nodeType];\n    if (types !== null) {\n      for (const type of types) {\n        // merge the visitor if necessary or just put it back in\n        if (visitor[type]) {\n          mergePair(visitor[type], fns);\n        } else {\n          // @ts-expect-error Expression produces too complex union\n          visitor[type] = fns;\n        }\n      }\n    } else {\n      mergePair(visitor, fns);\n    }\n  }\n\n  // add aliases\n  for (const nodeType of Object.keys(visitor) as (keyof Visitor)[]) {\n    if (shouldIgnoreKey(nodeType)) continue;\n\n    let aliases = FLIPPED_ALIAS_KEYS[nodeType];\n\n    if (nodeType in DEPRECATED_KEYS) {\n      const deprecatedKey = DEPRECATED_KEYS[nodeType];\n      deprecationWarning(nodeType, deprecatedKey, \"Visitor \");\n      aliases = [deprecatedKey];\n    } else if (nodeType in DEPRECATED_ALIASES) {\n      const deprecatedAlias =\n        DEPRECATED_ALIASES[nodeType as keyof typeof DEPRECATED_ALIASES];\n      deprecationWarning(nodeType, deprecatedAlias, \"Visitor \");\n      aliases = FLIPPED_ALIAS_KEYS[deprecatedAlias];\n    }\n\n    if (!aliases) continue;\n\n    const fns = visitor[nodeType];\n    // clear it from the visitor\n    delete visitor[nodeType];\n\n    for (const alias of aliases) {\n      const existing = visitor[alias];\n      if (existing) {\n        mergePair(existing, fns);\n      } else {\n        // @ts-expect-error Expression produces a union type that is too complex to represent.\n        visitor[alias] = { ...fns };\n      }\n    }\n  }\n\n  for (const nodeType of Object.keys(visitor)) {\n    if (shouldIgnoreKey(nodeType)) continue;\n\n    ensureCallbackArrays(\n      // @ts-expect-error nodeType must present in visitor after previous validations\n      visitor[nodeType],\n    );\n  }\n\n  return visitor;\n}\n\nexport function verify(visitor: Visitor) {\n  if (visitor._verified) return;\n\n  if (typeof visitor === \"function\") {\n    throw new Error(\n      \"You passed `traverse()` a function when it expected a visitor object, \" +\n        \"are you sure you didn't mean `{ enter: Function }`?\",\n    );\n  }\n\n  for (const nodeType of Object.keys(visitor) as (keyof Visitor)[]) {\n    if (nodeType === \"enter\" || nodeType === \"exit\") {\n      validateVisitorMethods(nodeType, visitor[nodeType]);\n    }\n\n    if (shouldIgnoreKey(nodeType)) continue;\n\n    if (TYPES.indexOf(nodeType) < 0) {\n      throw new Error(\n        `You gave us a visitor for the node type ${nodeType} but it's not a valid type`,\n      );\n    }\n\n    const visitors = visitor[nodeType];\n    if (typeof visitors === \"object\") {\n      for (const visitorKey of Object.keys(visitors)) {\n        if (visitorKey === \"enter\" || visitorKey === \"exit\") {\n          // verify that it just contains functions\n          validateVisitorMethods(\n            `${nodeType}.${visitorKey}`,\n            visitors[visitorKey],\n          );\n        } else {\n          throw new Error(\n            \"You passed `traverse()` a visitor object with the property \" +\n              `${nodeType} that has the invalid property ${visitorKey}`,\n          );\n        }\n      }\n    }\n  }\n\n  visitor._verified = true;\n}\n\nfunction validateVisitorMethods(\n  path: string,\n  val: any,\n): asserts val is Function | Function[] {\n  const fns = [].concat(val);\n  for (const fn of fns) {\n    if (typeof fn !== \"function\") {\n      throw new TypeError(\n        `Non-function found defined in ${path} with type ${typeof fn}`,\n      );\n    }\n  }\n}\n\nexport function merge<State>(visitors: Visitor<State>[]): Visitor<State>;\nexport function merge(\n  visitors: Visitor<unknown>[],\n  states?: any[],\n  wrapper?: Function | null,\n): Visitor<unknown>;\nexport function merge(\n  visitors: any[],\n  states: any[] = [],\n  wrapper?: Function | null,\n) {\n  const rootVisitor: Visitor = {};\n\n  for (let i = 0; i < visitors.length; i++) {\n    const visitor = visitors[i];\n    const state = states[i];\n\n    explode(visitor);\n\n    for (const type of Object.keys(visitor) as (keyof Visitor)[]) {\n      let visitorType = visitor[type];\n\n      // if we have state or wrapper then overload the callbacks to take it\n      if (state || wrapper) {\n        visitorType = wrapWithStateOrWrapper(visitorType, state, wrapper);\n      }\n\n      // @ts-expect-error: Expression produces a union type that is too complex to represent.\n      const nodeVisitor = (rootVisitor[type] ||= {});\n      mergePair(nodeVisitor, visitorType);\n    }\n  }\n\n  return rootVisitor;\n}\n\nfunction wrapWithStateOrWrapper<State>(\n  oldVisitor: Visitor<State>,\n  state: State,\n  wrapper?: Function | null,\n) {\n  const newVisitor: Visitor = {};\n\n  for (const key of Object.keys(oldVisitor) as (keyof Visitor<State>)[]) {\n    let fns = oldVisitor[key];\n\n    // not an enter/exit array of callbacks\n    if (!Array.isArray(fns)) continue;\n\n    // @ts-expect-error manipulating visitors\n    fns = fns.map(function (fn) {\n      let newFn = fn;\n\n      if (state) {\n        newFn = function (path: NodePath) {\n          return fn.call(state, path, state);\n        };\n      }\n\n      if (wrapper) {\n        // @ts-expect-error Fixme: document state.key\n        newFn = wrapper(state.key, key, newFn);\n      }\n\n      // Override toString in case this function is printed, we want to print the wrapped function, same as we do in `wrapCheck`\n      if (newFn !== fn) {\n        newFn.toString = () => fn.toString();\n      }\n\n      return newFn;\n    });\n\n    // @ts-expect-error: Expression produces a union type that is too complex to represent.\n    newVisitor[key] = fns;\n  }\n\n  return newVisitor;\n}\n\nfunction ensureEntranceObjects(obj: Visitor) {\n  for (const key of Object.keys(obj) as (keyof Visitor)[]) {\n    if (shouldIgnoreKey(key)) continue;\n\n    const fns = obj[key];\n    if (typeof fns === \"function\") {\n      // @ts-expect-error: Expression produces a union type that is too complex to represent.\n      obj[key] = { enter: fns };\n    }\n  }\n}\n\nfunction ensureCallbackArrays(obj: Visitor) {\n  // @ts-expect-error normalizing enter property\n  if (obj.enter && !Array.isArray(obj.enter)) obj.enter = [obj.enter];\n  // @ts-expect-error normalizing exit property\n  if (obj.exit && !Array.isArray(obj.exit)) obj.exit = [obj.exit];\n}\n\nfunction wrapCheck(nodeType: VIRTUAL_TYPES, fn: Function) {\n  const newFn = function (this: unknown, path: NodePath) {\n    if (path[`is${nodeType}`]()) {\n      return fn.apply(this, arguments);\n    }\n  };\n  newFn.toString = () => fn.toString();\n  return newFn;\n}\n\nfunction shouldIgnoreKey(\n  key: string,\n): key is\n  | \"enter\"\n  | \"exit\"\n  | \"shouldSkip\"\n  | \"denylist\"\n  | \"noScope\"\n  | \"skipKeys\"\n  | \"blacklist\" {\n  // internal/hidden key\n  if (key[0] === \"_\") return true;\n\n  // ignore function keys\n  if (key === \"enter\" || key === \"exit\" || key === \"shouldSkip\") return true;\n\n  // ignore other options\n  if (\n    key === \"denylist\" ||\n    key === \"noScope\" ||\n    key === \"skipKeys\" ||\n    // TODO: Remove in Babel 8\n    key === \"blacklist\"\n  ) {\n    return true;\n  }\n\n  return false;\n}\n\nfunction mergePair(dest: any, src: any) {\n  for (const key of Object.keys(src)) {\n    dest[key] = [].concat(dest[key] || [], src[key]);\n  }\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,EAAA,GAAAD,OAAA;AAMsB;EALpBE,eAAe;EACfC,kBAAkB;EAClBC,kBAAkB;EAClBC,KAAK;EACLC,8BAA8B,EAAIC;AAAkB,IAAAN,EAAA;AAKtD,SAASO,aAAaA,CAACC,IAAY,EAAyB;EAC1D,OAAOA,IAAI,IAAIV,YAAY;AAC7B;AAkBO,SAASW,OAAOA,CAACC,OAAgB,EAAE;EACxC,IAAIA,OAAO,CAACC,SAAS,EAAE,OAAOD,OAAO;EACrCA,OAAO,CAACC,SAAS,GAAG,IAAI;EAGxB,KAAK,MAAMC,QAAQ,IAAIC,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,EAAuB;IAChE,IAAIK,eAAe,CAACH,QAAQ,CAAC,EAAE;IAE/B,MAAMI,KAAoB,GAAGJ,QAAQ,CAACK,KAAK,CAAC,GAAG,CAAC;IAChD,IAAID,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;IAExB,MAAMC,GAAG,GAAGT,OAAO,CAACE,QAAQ,CAAC;IAC7B,OAAOF,OAAO,CAACE,QAAQ,CAAC;IAExB,KAAK,MAAMQ,IAAI,IAAIJ,KAAK,EAAE;MAExBN,OAAO,CAACU,IAAI,CAAC,GAAGD,GAAG;IACrB;EACF;EAGAE,MAAM,CAACX,OAAO,CAAC;EAKf,OAAOA,OAAO,CAACY,UAAU;EAGzBC,qBAAqB,CAACb,OAAO,CAAC;EAG9Bc,oBAAoB,CAACd,OAAO,CAAC;EAG7B,KAAK,MAAME,QAAQ,IAAIC,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,EAAE;IAC3C,IAAIK,eAAe,CAACH,QAAQ,CAAC,EAAE;IAE/B,IAAI,CAACL,aAAa,CAACK,QAAQ,CAAC,EAAE;IAG9B,MAAMO,GAAG,GAAGT,OAAO,CAACE,QAAQ,CAAC;IAC7B,KAAK,MAAMJ,IAAI,IAAIK,MAAM,CAACC,IAAI,CAACK,GAAG,CAAC,EAAE;MAEnCA,GAAG,CAACX,IAAI,CAAC,GAAGiB,SAAS,CAACb,QAAQ,EAAEO,GAAG,CAACX,IAAI,CAAC,CAAC;IAC5C;IAGA,OAAOE,OAAO,CAACE,QAAQ,CAAC;IAExB,MAAMc,KAAK,GAAG5B,YAAY,CAACc,QAAQ,CAAC;IACpC,IAAIc,KAAK,KAAK,IAAI,EAAE;MAClB,KAAK,MAAMlB,IAAI,IAAIkB,KAAK,EAAE;QAExB,IAAIhB,OAAO,CAACF,IAAI,CAAC,EAAE;UACjBmB,SAAS,CAACjB,OAAO,CAACF,IAAI,CAAC,EAAEW,GAAG,CAAC;QAC/B,CAAC,MAAM;UAELT,OAAO,CAACF,IAAI,CAAC,GAAGW,GAAG;QACrB;MACF;IACF,CAAC,MAAM;MACLQ,SAAS,CAACjB,OAAO,EAAES,GAAG,CAAC;IACzB;EACF;EAGA,KAAK,MAAMP,QAAQ,IAAIC,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,EAAuB;IAChE,IAAIK,eAAe,CAACH,QAAQ,CAAC,EAAE;IAE/B,IAAIgB,OAAO,GAAGzB,kBAAkB,CAACS,QAAQ,CAAC;IAE1C,IAAIA,QAAQ,IAAIX,eAAe,EAAE;MAC/B,MAAM4B,aAAa,GAAG5B,eAAe,CAACW,QAAQ,CAAC;MAC/CN,kBAAkB,CAACM,QAAQ,EAAEiB,aAAa,EAAE,UAAU,CAAC;MACvDD,OAAO,GAAG,CAACC,aAAa,CAAC;IAC3B,CAAC,MAAM,IAAIjB,QAAQ,IAAIV,kBAAkB,EAAE;MACzC,MAAM4B,eAAe,GACnB5B,kBAAkB,CAACU,QAAQ,CAAoC;MACjEN,kBAAkB,CAACM,QAAQ,EAAEkB,eAAe,EAAE,UAAU,CAAC;MACzDF,OAAO,GAAGzB,kBAAkB,CAAC2B,eAAe,CAAC;IAC/C;IAEA,IAAI,CAACF,OAAO,EAAE;IAEd,MAAMT,GAAG,GAAGT,OAAO,CAACE,QAAQ,CAAC;IAE7B,OAAOF,OAAO,CAACE,QAAQ,CAAC;IAExB,KAAK,MAAMmB,KAAK,IAAIH,OAAO,EAAE;MAC3B,MAAMI,QAAQ,GAAGtB,OAAO,CAACqB,KAAK,CAAC;MAC/B,IAAIC,QAAQ,EAAE;QACZL,SAAS,CAACK,QAAQ,EAAEb,GAAG,CAAC;MAC1B,CAAC,MAAM;QAELT,OAAO,CAACqB,KAAK,CAAC,GAAAlB,MAAA,CAAAoB,MAAA,KAAQd,GAAG,CAAE;MAC7B;IACF;EACF;EAEA,KAAK,MAAMP,QAAQ,IAAIC,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,EAAE;IAC3C,IAAIK,eAAe,CAACH,QAAQ,CAAC,EAAE;IAE/BY,oBAAoB,CAElBd,OAAO,CAACE,QAAQ,CAAC,CAClB;EACH;EAEA,OAAOF,OAAO;AAChB;AAEO,SAASW,MAAMA,CAACX,OAAgB,EAAE;EACvC,IAAIA,OAAO,CAACwB,SAAS,EAAE;EAEvB,IAAI,OAAOxB,OAAO,KAAK,UAAU,EAAE;IACjC,MAAM,IAAIyB,KAAK,CACb,wEAAwE,GACtE,qDAAqD,CACxD;EACH;EAEA,KAAK,MAAMvB,QAAQ,IAAIC,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,EAAuB;IAChE,IAAIE,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,MAAM,EAAE;MAC/CwB,sBAAsB,CAACxB,QAAQ,EAAEF,OAAO,CAACE,QAAQ,CAAC,CAAC;IACrD;IAEA,IAAIG,eAAe,CAACH,QAAQ,CAAC,EAAE;IAE/B,IAAIR,KAAK,CAACiC,OAAO,CAACzB,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC/B,MAAM,IAAIuB,KAAK,CACZ,2CAA0CvB,QAAS,4BAA2B,CAChF;IACH;IAEA,MAAM0B,QAAQ,GAAG5B,OAAO,CAACE,QAAQ,CAAC;IAClC,IAAI,OAAO0B,QAAQ,KAAK,QAAQ,EAAE;MAChC,KAAK,MAAMC,UAAU,IAAI1B,MAAM,CAACC,IAAI,CAACwB,QAAQ,CAAC,EAAE;QAC9C,IAAIC,UAAU,KAAK,OAAO,IAAIA,UAAU,KAAK,MAAM,EAAE;UAEnDH,sBAAsB,CACnB,GAAExB,QAAS,IAAG2B,UAAW,EAAC,EAC3BD,QAAQ,CAACC,UAAU,CAAC,CACrB;QACH,CAAC,MAAM;UACL,MAAM,IAAIJ,KAAK,CACb,6DAA6D,GAC1D,GAAEvB,QAAS,kCAAiC2B,UAAW,EAAC,CAC5D;QACH;MACF;IACF;EACF;EAEA7B,OAAO,CAACwB,SAAS,GAAG,IAAI;AAC1B;AAEA,SAASE,sBAAsBA,CAC7BI,IAAY,EACZC,GAAQ,EAC8B;EACtC,MAAMtB,GAAG,GAAG,EAAE,CAACuB,MAAM,CAACD,GAAG,CAAC;EAC1B,KAAK,MAAME,EAAE,IAAIxB,GAAG,EAAE;IACpB,IAAI,OAAOwB,EAAE,KAAK,UAAU,EAAE;MAC5B,MAAM,IAAIC,SAAS,CAChB,iCAAgCJ,IAAK,cAAa,OAAOG,EAAG,EAAC,CAC/D;IACH;EACF;AACF;AAQO,SAASE,KAAKA,CACnBP,QAAe,EACfQ,MAAa,GAAG,EAAE,EAClBC,OAAyB,EACzB;EACA,MAAMC,WAAoB,GAAG,CAAC,CAAC;EAE/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,QAAQ,CAACpB,MAAM,EAAE+B,CAAC,EAAE,EAAE;IACxC,MAAMvC,OAAO,GAAG4B,QAAQ,CAACW,CAAC,CAAC;IAC3B,MAAMC,KAAK,GAAGJ,MAAM,CAACG,CAAC,CAAC;IAEvBxC,OAAO,CAACC,OAAO,CAAC;IAEhB,KAAK,MAAMF,IAAI,IAAIK,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,EAAuB;MAC5D,IAAIyC,WAAW,GAAGzC,OAAO,CAACF,IAAI,CAAC;MAG/B,IAAI0C,KAAK,IAAIH,OAAO,EAAE;QACpBI,WAAW,GAAGC,sBAAsB,CAACD,WAAW,EAAED,KAAK,EAAEH,OAAO,CAAC;MACnE;MAGA,MAAMM,WAAW,GAAIL,WAAW,CAACxC,IAAI,CAAC,KAAjBwC,WAAW,CAACxC,IAAI,CAAC,GAAK,CAAC,CAAC,CAAC;MAC9CmB,SAAS,CAAC0B,WAAW,EAAEF,WAAW,CAAC;IACrC;EACF;EAEA,OAAOH,WAAW;AACpB;AAEA,SAASI,sBAAsBA,CAC7BE,UAA0B,EAC1BJ,KAAY,EACZH,OAAyB,EACzB;EACA,MAAMQ,UAAmB,GAAG,CAAC,CAAC;EAE9B,KAAK,MAAMC,GAAG,IAAI3C,MAAM,CAACC,IAAI,CAACwC,UAAU,CAAC,EAA8B;IACrE,IAAInC,GAAG,GAAGmC,UAAU,CAACE,GAAG,CAAC;IAGzB,IAAI,CAACC,KAAK,CAACC,OAAO,CAACvC,GAAG,CAAC,EAAE;IAGzBA,GAAG,GAAGA,GAAG,CAACwC,GAAG,CAAC,UAAUhB,EAAE,EAAE;MAC1B,IAAIiB,KAAK,GAAGjB,EAAE;MAEd,IAAIO,KAAK,EAAE;QACTU,KAAK,GAAG,SAAAA,CAAUpB,IAAc,EAAE;UAChC,OAAOG,EAAE,CAACkB,IAAI,CAACX,KAAK,EAAEV,IAAI,EAAEU,KAAK,CAAC;QACpC,CAAC;MACH;MAEA,IAAIH,OAAO,EAAE;QAEXa,KAAK,GAAGb,OAAO,CAACG,KAAK,CAACM,GAAG,EAAEA,GAAG,EAAEI,KAAK,CAAC;MACxC;MAGA,IAAIA,KAAK,KAAKjB,EAAE,EAAE;QAChBiB,KAAK,CAACE,QAAQ,GAAG,MAAMnB,EAAE,CAACmB,QAAQ,EAAE;MACtC;MAEA,OAAOF,KAAK;IACd,CAAC,CAAC;IAGFL,UAAU,CAACC,GAAG,CAAC,GAAGrC,GAAG;EACvB;EAEA,OAAOoC,UAAU;AACnB;AAEA,SAAShC,qBAAqBA,CAACwC,GAAY,EAAE;EAC3C,KAAK,MAAMP,GAAG,IAAI3C,MAAM,CAACC,IAAI,CAACiD,GAAG,CAAC,EAAuB;IACvD,IAAIhD,eAAe,CAACyC,GAAG,CAAC,EAAE;IAE1B,MAAMrC,GAAG,GAAG4C,GAAG,CAACP,GAAG,CAAC;IACpB,IAAI,OAAOrC,GAAG,KAAK,UAAU,EAAE;MAE7B4C,GAAG,CAACP,GAAG,CAAC,GAAG;QAAEQ,KAAK,EAAE7C;MAAI,CAAC;IAC3B;EACF;AACF;AAEA,SAASK,oBAAoBA,CAACuC,GAAY,EAAE;EAE1C,IAAIA,GAAG,CAACC,KAAK,IAAI,CAACP,KAAK,CAACC,OAAO,CAACK,GAAG,CAACC,KAAK,CAAC,EAAED,GAAG,CAACC,KAAK,GAAG,CAACD,GAAG,CAACC,KAAK,CAAC;EAEnE,IAAID,GAAG,CAACE,IAAI,IAAI,CAACR,KAAK,CAACC,OAAO,CAACK,GAAG,CAACE,IAAI,CAAC,EAAEF,GAAG,CAACE,IAAI,GAAG,CAACF,GAAG,CAACE,IAAI,CAAC;AACjE;AAEA,SAASxC,SAASA,CAACb,QAAuB,EAAE+B,EAAY,EAAE;EACxD,MAAMiB,KAAK,GAAG,SAAAA,CAAyBpB,IAAc,EAAE;IACrD,IAAIA,IAAI,CAAE,KAAI5B,QAAS,EAAC,CAAC,EAAE,EAAE;MAC3B,OAAO+B,EAAE,CAACuB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAClC;EACF,CAAC;EACDP,KAAK,CAACE,QAAQ,GAAG,MAAMnB,EAAE,CAACmB,QAAQ,EAAE;EACpC,OAAOF,KAAK;AACd;AAEA,SAAS7C,eAAeA,CACtByC,GAAW,EAQG;EAEd,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,OAAO,IAAI;EAG/B,IAAIA,GAAG,KAAK,OAAO,IAAIA,GAAG,KAAK,MAAM,IAAIA,GAAG,KAAK,YAAY,EAAE,OAAO,IAAI;EAG1E,IACEA,GAAG,KAAK,UAAU,IAClBA,GAAG,KAAK,SAAS,IACjBA,GAAG,KAAK,UAAU,IAElBA,GAAG,KAAK,WAAW,EACnB;IACA,OAAO,IAAI;EACb;EAEA,OAAO,KAAK;AACd;AAEA,SAAS7B,SAASA,CAACyC,IAAS,EAAEC,GAAQ,EAAE;EACtC,KAAK,MAAMb,GAAG,IAAI3C,MAAM,CAACC,IAAI,CAACuD,GAAG,CAAC,EAAE;IAClCD,IAAI,CAACZ,GAAG,CAAC,GAAG,EAAE,CAACd,MAAM,CAAC0B,IAAI,CAACZ,GAAG,CAAC,IAAI,EAAE,EAAEa,GAAG,CAACb,GAAG,CAAC,CAAC;EAClD;AACF"}