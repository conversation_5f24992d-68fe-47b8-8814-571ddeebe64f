/*!
 * Socket.IO v4.6.1
 * (c) 2014-2023 <PERSON>
 * Released under the MIT License.
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).io=e()}(this,(function(){"use strict";function t(t,e){return e.forEach((function(e){e&&"string"!=typeof e&&!Array.isArray(e)&&Object.keys(e).forEach((function(n){if("default"!==n&&!(n in t)){var i=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,i.get?i:{enumerable:!0,get:function(){return e[n]}})}}))})),Object.freeze(t)}function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(t)}function n(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function i(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function r(t,e,n){return e&&i(t.prototype,e),n&&i(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function s(){return s=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},s.apply(this,arguments)}function o(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&u(t,e)}function a(t){return a=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},a(t)}function u(t,e){return u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},u(t,e)}function c(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}function h(t,e,n){return h=c()?Reflect.construct.bind():function(t,e,n){var i=[null];i.push.apply(i,e);var r=new(Function.bind.apply(t,i));return n&&u(r,n.prototype),r},h.apply(null,arguments)}function f(t){var e="function"==typeof Map?new Map:void 0;return f=function(t){if(null===t||(n=t,-1===Function.toString.call(n).indexOf("[native code]")))return t;var n;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==e){if(e.has(t))return e.get(t);e.set(t,i)}function i(){return h(t,arguments,a(this).constructor)}return i.prototype=Object.create(t.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),u(i,t)},f(t)}function l(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function p(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return l(t)}function d(t){var e=c();return function(){var n,i=a(t);if(e){var r=a(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return p(this,n)}}function y(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=a(t)););return t}function v(){return v="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,n){var i=y(t,e);if(i){var r=Object.getOwnPropertyDescriptor(i,e);return r.get?r.get.call(arguments.length<3?t:n):r.value}},v.apply(this,arguments)}function g(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function _(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return g(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?g(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,o=!0,a=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return o=t.done,t},e:function(t){a=!0,s=t},f:function(){try{o||null==n.return||n.return()}finally{if(a)throw s}}}}var m=Object.create(null);m.open="0",m.close="1",m.ping="2",m.pong="3",m.message="4",m.upgrade="5",m.noop="6";var b=Object.create(null);Object.keys(m).forEach((function(t){b[m[t]]=t}));for(var k={type:"error",data:"parser error"},w="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===Object.prototype.toString.call(Blob),O="function"==typeof ArrayBuffer,E=function(t,e,n){var i,r=t.type,s=t.data;return w&&s instanceof Blob?e?n(s):C(s,n):O&&(s instanceof ArrayBuffer||(i=s,"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(i):i&&i.buffer instanceof ArrayBuffer))?e?n(s):C(new Blob([s]),n):n(m[r]+(s||""))},C=function(t,e){var n=new FileReader;return n.onload=function(){var t=n.result.split(",")[1];e("b"+t)},n.readAsDataURL(t)},T="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",R="undefined"==typeof Uint8Array?[]:new Uint8Array(256),A=0;A<T.length;A++)R[T.charCodeAt(A)]=A;var S="function"==typeof ArrayBuffer,L=function(t,e){if("string"!=typeof t)return{type:"message",data:B(t,e)};var n=t.charAt(0);return"b"===n?{type:"message",data:x(t.substring(1),e)}:b[n]?t.length>1?{type:b[n],data:t.substring(1)}:{type:b[n]}:k},x=function(t,e){if(S){var n=function(t){var e,n,i,r,s,o=.75*t.length,a=t.length,u=0;"="===t[t.length-1]&&(o--,"="===t[t.length-2]&&o--);var c=new ArrayBuffer(o),h=new Uint8Array(c);for(e=0;e<a;e+=4)n=R[t.charCodeAt(e)],i=R[t.charCodeAt(e+1)],r=R[t.charCodeAt(e+2)],s=R[t.charCodeAt(e+3)],h[u++]=n<<2|i>>4,h[u++]=(15&i)<<4|r>>2,h[u++]=(3&r)<<6|63&s;return c}(t);return B(n,e)}return{base64:!0,data:t}},B=function(t,e){return"blob"===e&&t instanceof ArrayBuffer?new Blob([t]):t},U=String.fromCharCode(30);function P(t){if(t)return function(t){for(var e in P.prototype)t[e]=P.prototype[e];return t}(t)}P.prototype.on=P.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+t]=this._callbacks["$"+t]||[]).push(e),this},P.prototype.once=function(t,e){function n(){this.off(t,n),e.apply(this,arguments)}return n.fn=e,this.on(t,n),this},P.prototype.off=P.prototype.removeListener=P.prototype.removeAllListeners=P.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var n,i=this._callbacks["$"+t];if(!i)return this;if(1==arguments.length)return delete this._callbacks["$"+t],this;for(var r=0;r<i.length;r++)if((n=i[r])===e||n.fn===e){i.splice(r,1);break}return 0===i.length&&delete this._callbacks["$"+t],this},P.prototype.emit=function(t){this._callbacks=this._callbacks||{};for(var e=new Array(arguments.length-1),n=this._callbacks["$"+t],i=1;i<arguments.length;i++)e[i-1]=arguments[i];if(n){i=0;for(var r=(n=n.slice(0)).length;i<r;++i)n[i].apply(this,e)}return this},P.prototype.emitReserved=P.prototype.emit,P.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks["$"+t]||[]},P.prototype.hasListeners=function(t){return!!this.listeners(t).length};var j="undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")();function q(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];return n.reduce((function(e,n){return t.hasOwnProperty(n)&&(e[n]=t[n]),e}),{})}var N=j.setTimeout,M=j.clearTimeout;function D(t,e){e.useNativeTimers?(t.setTimeoutFn=N.bind(j),t.clearTimeoutFn=M.bind(j)):(t.setTimeoutFn=j.setTimeout.bind(j),t.clearTimeoutFn=j.clearTimeout.bind(j))}var I,F=function(t){o(i,t);var e=d(i);function i(t,r,s){var o;return n(this,i),(o=e.call(this,t)).description=r,o.context=s,o.type="TransportError",o}return r(i)}(f(Error)),H=function(t){o(i,t);var e=d(i);function i(t){var r;return n(this,i),(r=e.call(this)).writable=!1,D(l(r),t),r.opts=t,r.query=t.query,r.socket=t.socket,r}return r(i,[{key:"onError",value:function(t,e,n){return v(a(i.prototype),"emitReserved",this).call(this,"error",new F(t,e,n)),this}},{key:"open",value:function(){return this.readyState="opening",this.doOpen(),this}},{key:"close",value:function(){return"opening"!==this.readyState&&"open"!==this.readyState||(this.doClose(),this.onClose()),this}},{key:"send",value:function(t){"open"===this.readyState&&this.write(t)}},{key:"onOpen",value:function(){this.readyState="open",this.writable=!0,v(a(i.prototype),"emitReserved",this).call(this,"open")}},{key:"onData",value:function(t){var e=L(t,this.socket.binaryType);this.onPacket(e)}},{key:"onPacket",value:function(t){v(a(i.prototype),"emitReserved",this).call(this,"packet",t)}},{key:"onClose",value:function(t){this.readyState="closed",v(a(i.prototype),"emitReserved",this).call(this,"close",t)}},{key:"pause",value:function(t){}}]),i}(P),$="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_".split(""),z={},W=0,V=0;function Q(t){var e="";do{e=$[t%64]+e,t=Math.floor(t/64)}while(t>0);return e}function X(){var t=Q(+new Date);return t!==I?(W=0,I=t):t+"."+Q(W++)}for(;V<64;V++)z[$[V]]=V;function K(t){var e="";for(var n in t)t.hasOwnProperty(n)&&(e.length&&(e+="&"),e+=encodeURIComponent(n)+"="+encodeURIComponent(t[n]));return e}function J(t){for(var e={},n=t.split("&"),i=0,r=n.length;i<r;i++){var s=n[i].split("=");e[decodeURIComponent(s[0])]=decodeURIComponent(s[1])}return e}var Y=!1;try{Y="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(t){}var G=Y;function Z(t){var e=t.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!e||G))return new XMLHttpRequest}catch(t){}if(!e)try{return new(j[["Active"].concat("Object").join("X")])("Microsoft.XMLHTTP")}catch(t){}}function tt(){}var et=null!=new Z({xdomain:!1}).responseType,nt=function(t){o(i,t);var e=d(i);function i(t){var r;if(n(this,i),(r=e.call(this,t)).polling=!1,"undefined"!=typeof location){var s="https:"===location.protocol,o=location.port;o||(o=s?"443":"80"),r.xd="undefined"!=typeof location&&t.hostname!==location.hostname||o!==t.port,r.xs=t.secure!==s}var a=t&&t.forceBase64;return r.supportsBinary=et&&!a,r}return r(i,[{key:"name",get:function(){return"polling"}},{key:"doOpen",value:function(){this.poll()}},{key:"pause",value:function(t){var e=this;this.readyState="pausing";var n=function(){e.readyState="paused",t()};if(this.polling||!this.writable){var i=0;this.polling&&(i++,this.once("pollComplete",(function(){--i||n()}))),this.writable||(i++,this.once("drain",(function(){--i||n()})))}else n()}},{key:"poll",value:function(){this.polling=!0,this.doPoll(),this.emitReserved("poll")}},{key:"onData",value:function(t){var e=this;(function(t,e){for(var n=t.split(U),i=[],r=0;r<n.length;r++){var s=L(n[r],e);if(i.push(s),"error"===s.type)break}return i})(t,this.socket.binaryType).forEach((function(t){if("opening"===e.readyState&&"open"===t.type&&e.onOpen(),"close"===t.type)return e.onClose({description:"transport closed by the server"}),!1;e.onPacket(t)})),"closed"!==this.readyState&&(this.polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState&&this.poll())}},{key:"doClose",value:function(){var t=this,e=function(){t.write([{type:"close"}])};"open"===this.readyState?e():this.once("open",e)}},{key:"write",value:function(t){var e=this;this.writable=!1,function(t,e){var n=t.length,i=new Array(n),r=0;t.forEach((function(t,s){E(t,!1,(function(t){i[s]=t,++r===n&&e(i.join(U))}))}))}(t,(function(t){e.doWrite(t,(function(){e.writable=!0,e.emitReserved("drain")}))}))}},{key:"uri",value:function(){var t=this.query||{},e=this.opts.secure?"https":"http",n="";!1!==this.opts.timestampRequests&&(t[this.opts.timestampParam]=X()),this.supportsBinary||t.sid||(t.b64=1),this.opts.port&&("https"===e&&443!==Number(this.opts.port)||"http"===e&&80!==Number(this.opts.port))&&(n=":"+this.opts.port);var i=K(t);return e+"://"+(-1!==this.opts.hostname.indexOf(":")?"["+this.opts.hostname+"]":this.opts.hostname)+n+this.opts.path+(i.length?"?"+i:"")}},{key:"request",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return s(t,{xd:this.xd,xs:this.xs},this.opts),new it(this.uri(),t)}},{key:"doWrite",value:function(t,e){var n=this,i=this.request({method:"POST",data:t});i.on("success",e),i.on("error",(function(t,e){n.onError("xhr post error",t,e)}))}},{key:"doPoll",value:function(){var t=this,e=this.request();e.on("data",this.onData.bind(this)),e.on("error",(function(e,n){t.onError("xhr poll error",e,n)})),this.pollXhr=e}}]),i}(H),it=function(t){o(i,t);var e=d(i);function i(t,r){var s;return n(this,i),D(l(s=e.call(this)),r),s.opts=r,s.method=r.method||"GET",s.uri=t,s.async=!1!==r.async,s.data=void 0!==r.data?r.data:null,s.create(),s}return r(i,[{key:"create",value:function(){var t=this,e=q(this.opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");e.xdomain=!!this.opts.xd,e.xscheme=!!this.opts.xs;var n=this.xhr=new Z(e);try{n.open(this.method,this.uri,this.async);try{if(this.opts.extraHeaders)for(var r in n.setDisableHeaderCheck&&n.setDisableHeaderCheck(!0),this.opts.extraHeaders)this.opts.extraHeaders.hasOwnProperty(r)&&n.setRequestHeader(r,this.opts.extraHeaders[r])}catch(t){}if("POST"===this.method)try{n.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(t){}try{n.setRequestHeader("Accept","*/*")}catch(t){}"withCredentials"in n&&(n.withCredentials=this.opts.withCredentials),this.opts.requestTimeout&&(n.timeout=this.opts.requestTimeout),n.onreadystatechange=function(){4===n.readyState&&(200===n.status||1223===n.status?t.onLoad():t.setTimeoutFn((function(){t.onError("number"==typeof n.status?n.status:0)}),0))},n.send(this.data)}catch(e){return void this.setTimeoutFn((function(){t.onError(e)}),0)}"undefined"!=typeof document&&(this.index=i.requestsCount++,i.requests[this.index]=this)}},{key:"onError",value:function(t){this.emitReserved("error",t,this.xhr),this.cleanup(!0)}},{key:"cleanup",value:function(t){if(void 0!==this.xhr&&null!==this.xhr){if(this.xhr.onreadystatechange=tt,t)try{this.xhr.abort()}catch(t){}"undefined"!=typeof document&&delete i.requests[this.index],this.xhr=null}}},{key:"onLoad",value:function(){var t=this.xhr.responseText;null!==t&&(this.emitReserved("data",t),this.emitReserved("success"),this.cleanup())}},{key:"abort",value:function(){this.cleanup()}}]),i}(P);if(it.requestsCount=0,it.requests={},"undefined"!=typeof document)if("function"==typeof attachEvent)attachEvent("onunload",rt);else if("function"==typeof addEventListener){addEventListener("onpagehide"in j?"pagehide":"unload",rt,!1)}function rt(){for(var t in it.requests)it.requests.hasOwnProperty(t)&&it.requests[t].abort()}var st="function"==typeof Promise&&"function"==typeof Promise.resolve?function(t){return Promise.resolve().then(t)}:function(t,e){return e(t,0)},ot=j.WebSocket||j.MozWebSocket,at="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase(),ut=function(t){o(i,t);var e=d(i);function i(t){var r;return n(this,i),(r=e.call(this,t)).supportsBinary=!t.forceBase64,r}return r(i,[{key:"name",get:function(){return"websocket"}},{key:"doOpen",value:function(){if(this.check()){var t=this.uri(),e=this.opts.protocols,n=at?{}:q(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(n.headers=this.opts.extraHeaders);try{this.ws=at?new ot(t,e,n):e?new ot(t,e):new ot(t)}catch(t){return this.emitReserved("error",t)}this.ws.binaryType=this.socket.binaryType||"arraybuffer",this.addEventListeners()}}},{key:"addEventListeners",value:function(){var t=this;this.ws.onopen=function(){t.opts.autoUnref&&t.ws._socket.unref(),t.onOpen()},this.ws.onclose=function(e){return t.onClose({description:"websocket connection closed",context:e})},this.ws.onmessage=function(e){return t.onData(e.data)},this.ws.onerror=function(e){return t.onError("websocket error",e)}}},{key:"write",value:function(t){var e=this;this.writable=!1;for(var n=function(n){var i=t[n],r=n===t.length-1;E(i,e.supportsBinary,(function(t){try{e.ws.send(t)}catch(t){}r&&st((function(){e.writable=!0,e.emitReserved("drain")}),e.setTimeoutFn)}))},i=0;i<t.length;i++)n(i)}},{key:"doClose",value:function(){void 0!==this.ws&&(this.ws.close(),this.ws=null)}},{key:"uri",value:function(){var t=this.query||{},e=this.opts.secure?"wss":"ws",n="";this.opts.port&&("wss"===e&&443!==Number(this.opts.port)||"ws"===e&&80!==Number(this.opts.port))&&(n=":"+this.opts.port),this.opts.timestampRequests&&(t[this.opts.timestampParam]=X()),this.supportsBinary||(t.b64=1);var i=K(t);return e+"://"+(-1!==this.opts.hostname.indexOf(":")?"["+this.opts.hostname+"]":this.opts.hostname)+n+this.opts.path+(i.length?"?"+i:"")}},{key:"check",value:function(){return!!ot}}]),i}(H),ct={websocket:ut,polling:nt},ht=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,ft=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function lt(t){var e=t,n=t.indexOf("["),i=t.indexOf("]");-1!=n&&-1!=i&&(t=t.substring(0,n)+t.substring(n,i).replace(/:/g,";")+t.substring(i,t.length));for(var r,s,o=ht.exec(t||""),a={},u=14;u--;)a[ft[u]]=o[u]||"";return-1!=n&&-1!=i&&(a.source=e,a.host=a.host.substring(1,a.host.length-1).replace(/;/g,":"),a.authority=a.authority.replace("[","").replace("]","").replace(/;/g,":"),a.ipv6uri=!0),a.pathNames=function(t,e){var n=/\/{2,9}/g,i=e.replace(n,"/").split("/");"/"!=e.slice(0,1)&&0!==e.length||i.splice(0,1);"/"==e.slice(-1)&&i.splice(i.length-1,1);return i}(0,a.path),a.queryKey=(r=a.query,s={},r.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,(function(t,e,n){e&&(s[e]=n)})),s),a}var pt=function(t){o(a,t);var i=d(a);function a(t){var r,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n(this,a),(r=i.call(this)).writeBuffer=[],t&&"object"===e(t)&&(o=t,t=null),t?(t=lt(t),o.hostname=t.host,o.secure="https"===t.protocol||"wss"===t.protocol,o.port=t.port,t.query&&(o.query=t.query)):o.host&&(o.hostname=lt(o.host).host),D(l(r),o),r.secure=null!=o.secure?o.secure:"undefined"!=typeof location&&"https:"===location.protocol,o.hostname&&!o.port&&(o.port=r.secure?"443":"80"),r.hostname=o.hostname||("undefined"!=typeof location?location.hostname:"localhost"),r.port=o.port||("undefined"!=typeof location&&location.port?location.port:r.secure?"443":"80"),r.transports=o.transports||["polling","websocket"],r.writeBuffer=[],r.prevBufferLen=0,r.opts=s({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!0},o),r.opts.path=r.opts.path.replace(/\/$/,"")+(r.opts.addTrailingSlash?"/":""),"string"==typeof r.opts.query&&(r.opts.query=J(r.opts.query)),r.id=null,r.upgrades=null,r.pingInterval=null,r.pingTimeout=null,r.pingTimeoutTimer=null,"function"==typeof addEventListener&&(r.opts.closeOnBeforeunload&&(r.beforeunloadEventListener=function(){r.transport&&(r.transport.removeAllListeners(),r.transport.close())},addEventListener("beforeunload",r.beforeunloadEventListener,!1)),"localhost"!==r.hostname&&(r.offlineEventListener=function(){r.onClose("transport close",{description:"network connection lost"})},addEventListener("offline",r.offlineEventListener,!1))),r.open(),r}return r(a,[{key:"createTransport",value:function(t){var e=s({},this.opts.query);e.EIO=4,e.transport=t,this.id&&(e.sid=this.id);var n=s({},this.opts.transportOptions[t],this.opts,{query:e,socket:this,hostname:this.hostname,secure:this.secure,port:this.port});return new ct[t](n)}},{key:"open",value:function(){var t,e=this;if(this.opts.rememberUpgrade&&a.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket"))t="websocket";else{if(0===this.transports.length)return void this.setTimeoutFn((function(){e.emitReserved("error","No transports available")}),0);t=this.transports[0]}this.readyState="opening";try{t=this.createTransport(t)}catch(t){return this.transports.shift(),void this.open()}t.open(),this.setTransport(t)}},{key:"setTransport",value:function(t){var e=this;this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this.onDrain.bind(this)).on("packet",this.onPacket.bind(this)).on("error",this.onError.bind(this)).on("close",(function(t){return e.onClose("transport close",t)}))}},{key:"probe",value:function(t){var e=this,n=this.createTransport(t),i=!1;a.priorWebsocketSuccess=!1;var r=function(){i||(n.send([{type:"ping",data:"probe"}]),n.once("packet",(function(t){if(!i)if("pong"===t.type&&"probe"===t.data){if(e.upgrading=!0,e.emitReserved("upgrading",n),!n)return;a.priorWebsocketSuccess="websocket"===n.name,e.transport.pause((function(){i||"closed"!==e.readyState&&(f(),e.setTransport(n),n.send([{type:"upgrade"}]),e.emitReserved("upgrade",n),n=null,e.upgrading=!1,e.flush())}))}else{var r=new Error("probe error");r.transport=n.name,e.emitReserved("upgradeError",r)}})))};function s(){i||(i=!0,f(),n.close(),n=null)}var o=function(t){var i=new Error("probe error: "+t);i.transport=n.name,s(),e.emitReserved("upgradeError",i)};function u(){o("transport closed")}function c(){o("socket closed")}function h(t){n&&t.name!==n.name&&s()}var f=function(){n.removeListener("open",r),n.removeListener("error",o),n.removeListener("close",u),e.off("close",c),e.off("upgrading",h)};n.once("open",r),n.once("error",o),n.once("close",u),this.once("close",c),this.once("upgrading",h),n.open()}},{key:"onOpen",value:function(){if(this.readyState="open",a.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush(),"open"===this.readyState&&this.opts.upgrade)for(var t=0,e=this.upgrades.length;t<e;t++)this.probe(this.upgrades[t])}},{key:"onPacket",value:function(t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this.resetPingTimeout(),this.sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong");break;case"error":var e=new Error("server error");e.code=t.data,this.onError(e);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data)}}},{key:"onHandshake",value:function(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this.upgrades=this.filterUpgrades(t.upgrades),this.pingInterval=t.pingInterval,this.pingTimeout=t.pingTimeout,this.maxPayload=t.maxPayload,this.onOpen(),"closed"!==this.readyState&&this.resetPingTimeout()}},{key:"resetPingTimeout",value:function(){var t=this;this.clearTimeoutFn(this.pingTimeoutTimer),this.pingTimeoutTimer=this.setTimeoutFn((function(){t.onClose("ping timeout")}),this.pingInterval+this.pingTimeout),this.opts.autoUnref&&this.pingTimeoutTimer.unref()}},{key:"onDrain",value:function(){this.writeBuffer.splice(0,this.prevBufferLen),this.prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}},{key:"flush",value:function(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){var t=this.getWritablePackets();this.transport.send(t),this.prevBufferLen=t.length,this.emitReserved("flush")}}},{key:"getWritablePackets",value:function(){if(!(this.maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;for(var t,e=1,n=0;n<this.writeBuffer.length;n++){var i=this.writeBuffer[n].data;if(i&&(e+="string"==typeof(t=i)?function(t){for(var e=0,n=0,i=0,r=t.length;i<r;i++)(e=t.charCodeAt(i))<128?n+=1:e<2048?n+=2:e<55296||e>=57344?n+=3:(i++,n+=4);return n}(t):Math.ceil(1.33*(t.byteLength||t.size))),n>0&&e>this.maxPayload)return this.writeBuffer.slice(0,n);e+=2}return this.writeBuffer}},{key:"write",value:function(t,e,n){return this.sendPacket("message",t,e,n),this}},{key:"send",value:function(t,e,n){return this.sendPacket("message",t,e,n),this}},{key:"sendPacket",value:function(t,e,n,i){if("function"==typeof e&&(i=e,e=void 0),"function"==typeof n&&(i=n,n=null),"closing"!==this.readyState&&"closed"!==this.readyState){(n=n||{}).compress=!1!==n.compress;var r={type:t,data:e,options:n};this.emitReserved("packetCreate",r),this.writeBuffer.push(r),i&&this.once("flush",i),this.flush()}}},{key:"close",value:function(){var t=this,e=function(){t.onClose("forced close"),t.transport.close()},n=function n(){t.off("upgrade",n),t.off("upgradeError",n),e()},i=function(){t.once("upgrade",n),t.once("upgradeError",n)};return"opening"!==this.readyState&&"open"!==this.readyState||(this.readyState="closing",this.writeBuffer.length?this.once("drain",(function(){t.upgrading?i():e()})):this.upgrading?i():e()),this}},{key:"onError",value:function(t){a.priorWebsocketSuccess=!1,this.emitReserved("error",t),this.onClose("transport error",t)}},{key:"onClose",value:function(t,e){"opening"!==this.readyState&&"open"!==this.readyState&&"closing"!==this.readyState||(this.clearTimeoutFn(this.pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),"function"==typeof removeEventListener&&(removeEventListener("beforeunload",this.beforeunloadEventListener,!1),removeEventListener("offline",this.offlineEventListener,!1)),this.readyState="closed",this.id=null,this.emitReserved("close",t,e),this.writeBuffer=[],this.prevBufferLen=0)}},{key:"filterUpgrades",value:function(t){for(var e=[],n=0,i=t.length;n<i;n++)~this.transports.indexOf(t[n])&&e.push(t[n]);return e}}]),a}(P);pt.protocol=4,pt.protocol;var dt={},yt={};function vt(t,e,n){for(var i=0,r=0,s=n.length;r<s;r++)(i=n.charCodeAt(r))<128?t.setUint8(e++,i):i<2048?(t.setUint8(e++,192|i>>6),t.setUint8(e++,128|63&i)):i<55296||i>=57344?(t.setUint8(e++,224|i>>12),t.setUint8(e++,128|i>>6&63),t.setUint8(e++,128|63&i)):(r++,i=65536+((1023&i)<<10|1023&n.charCodeAt(r)),t.setUint8(e++,240|i>>18),t.setUint8(e++,128|i>>12&63),t.setUint8(e++,128|i>>6&63),t.setUint8(e++,128|63&i))}function gt(t,n,i){var r=e(i),s=0,o=0,a=0,u=0,c=0,h=0;if("string"===r){if(c=function(t){for(var e=0,n=0,i=0,r=t.length;i<r;i++)(e=t.charCodeAt(i))<128?n+=1:e<2048?n+=2:e<55296||e>=57344?n+=3:(i++,n+=4);return n}(i),c<32)t.push(160|c),h=1;else if(c<256)t.push(217,c),h=2;else if(c<65536)t.push(218,c>>8,c),h=3;else{if(!(c<4294967296))throw new Error("String too long");t.push(219,c>>24,c>>16,c>>8,c),h=5}return n.push({_str:i,_length:c,_offset:t.length}),h+c}if("number"===r)return Math.floor(i)===i&&isFinite(i)?i>=0?i<128?(t.push(i),1):i<256?(t.push(204,i),2):i<65536?(t.push(205,i>>8,i),3):i<4294967296?(t.push(206,i>>24,i>>16,i>>8,i),5):(a=i/Math.pow(2,32)>>0,u=i>>>0,t.push(207,a>>24,a>>16,a>>8,a,u>>24,u>>16,u>>8,u),9):i>=-32?(t.push(i),1):i>=-128?(t.push(208,i),2):i>=-32768?(t.push(209,i>>8,i),3):i>=-2147483648?(t.push(210,i>>24,i>>16,i>>8,i),5):(a=Math.floor(i/Math.pow(2,32)),u=i>>>0,t.push(211,a>>24,a>>16,a>>8,a,u>>24,u>>16,u>>8,u),9):(t.push(203),n.push({_float:i,_length:8,_offset:t.length}),9);if("object"===r){if(null===i)return t.push(192),1;if(Array.isArray(i)){if((c=i.length)<16)t.push(144|c),h=1;else if(c<65536)t.push(220,c>>8,c),h=3;else{if(!(c<4294967296))throw new Error("Array too large");t.push(221,c>>24,c>>16,c>>8,c),h=5}for(s=0;s<c;s++)h+=gt(t,n,i[s]);return h}if(i instanceof Date){var f=i.getTime();return a=Math.floor(f/Math.pow(2,32)),u=f>>>0,t.push(215,0,a>>24,a>>16,a>>8,a,u>>24,u>>16,u>>8,u),10}if(i instanceof ArrayBuffer){if((c=i.byteLength)<256)t.push(196,c),h=2;else if(c<65536)t.push(197,c>>8,c),h=3;else{if(!(c<4294967296))throw new Error("Buffer too large");t.push(198,c>>24,c>>16,c>>8,c),h=5}return n.push({_bin:i,_length:c,_offset:t.length}),h+c}if("function"==typeof i.toJSON)return gt(t,n,i.toJSON());var l=[],p="",d=Object.keys(i);for(s=0,o=d.length;s<o;s++)"function"!=typeof i[p=d[s]]&&l.push(p);if((c=l.length)<16)t.push(128|c),h=1;else if(c<65536)t.push(222,c>>8,c),h=3;else{if(!(c<4294967296))throw new Error("Object too large");t.push(223,c>>24,c>>16,c>>8,c),h=5}for(s=0;s<c;s++)h+=gt(t,n,p=l[s]),h+=gt(t,n,i[p]);return h}if("boolean"===r)return t.push(i?195:194),1;if("undefined"===r)return t.push(212,0,0),3;throw new Error("Could not encode")}var _t=function(t){var e=[],n=[],i=gt(e,n,t),r=new ArrayBuffer(i),s=new DataView(r),o=0,a=0,u=-1;n.length>0&&(u=n[0]._offset);for(var c,h=0,f=0,l=0,p=e.length;l<p;l++)if(s.setUint8(a+l,e[l]),l+1===u){if(h=(c=n[o])._length,f=a+u,c._bin)for(var d=new Uint8Array(c._bin),y=0;y<h;y++)s.setUint8(f+y,d[y]);else c._str?vt(s,f,c._str):void 0!==c._float&&s.setFloat64(f,c._float);a+=h,n[++o]&&(u=n[o]._offset)}return r};function mt(t){if(this._offset=0,t instanceof ArrayBuffer)this._buffer=t,this._view=new DataView(this._buffer);else{if(!ArrayBuffer.isView(t))throw new Error("Invalid argument");this._buffer=t.buffer,this._view=new DataView(this._buffer,t.byteOffset,t.byteLength)}}mt.prototype._array=function(t){for(var e=new Array(t),n=0;n<t;n++)e[n]=this._parse();return e},mt.prototype._map=function(t){for(var e={},n=0;n<t;n++)e[this._parse()]=this._parse();return e},mt.prototype._str=function(t){var e=function(t,e,n){for(var i="",r=0,s=e,o=e+n;s<o;s++){var a=t.getUint8(s);if(0!=(128&a))if(192!=(224&a))if(224!=(240&a)){if(240!=(248&a))throw new Error("Invalid byte "+a.toString(16));(r=(7&a)<<18|(63&t.getUint8(++s))<<12|(63&t.getUint8(++s))<<6|(63&t.getUint8(++s))<<0)>=65536?(r-=65536,i+=String.fromCharCode(55296+(r>>>10),56320+(1023&r))):i+=String.fromCharCode(r)}else i+=String.fromCharCode((15&a)<<12|(63&t.getUint8(++s))<<6|(63&t.getUint8(++s))<<0);else i+=String.fromCharCode((31&a)<<6|63&t.getUint8(++s));else i+=String.fromCharCode(a)}return i}(this._view,this._offset,t);return this._offset+=t,e},mt.prototype._bin=function(t){var e=this._buffer.slice(this._offset,this._offset+t);return this._offset+=t,e},mt.prototype._parse=function(){var t,e=this._view.getUint8(this._offset++),n=0,i=0,r=0,s=0;if(e<192)return e<128?e:e<144?this._map(15&e):e<160?this._array(15&e):this._str(31&e);if(e>223)return-1*(255-e+1);switch(e){case 192:return null;case 194:return!1;case 195:return!0;case 196:return n=this._view.getUint8(this._offset),this._offset+=1,this._bin(n);case 197:return n=this._view.getUint16(this._offset),this._offset+=2,this._bin(n);case 198:return n=this._view.getUint32(this._offset),this._offset+=4,this._bin(n);case 199:return n=this._view.getUint8(this._offset),i=this._view.getInt8(this._offset+1),this._offset+=2,[i,this._bin(n)];case 200:return n=this._view.getUint16(this._offset),i=this._view.getInt8(this._offset+2),this._offset+=3,[i,this._bin(n)];case 201:return n=this._view.getUint32(this._offset),i=this._view.getInt8(this._offset+4),this._offset+=5,[i,this._bin(n)];case 202:return t=this._view.getFloat32(this._offset),this._offset+=4,t;case 203:return t=this._view.getFloat64(this._offset),this._offset+=8,t;case 204:return t=this._view.getUint8(this._offset),this._offset+=1,t;case 205:return t=this._view.getUint16(this._offset),this._offset+=2,t;case 206:return t=this._view.getUint32(this._offset),this._offset+=4,t;case 207:return r=this._view.getUint32(this._offset)*Math.pow(2,32),s=this._view.getUint32(this._offset+4),this._offset+=8,r+s;case 208:return t=this._view.getInt8(this._offset),this._offset+=1,t;case 209:return t=this._view.getInt16(this._offset),this._offset+=2,t;case 210:return t=this._view.getInt32(this._offset),this._offset+=4,t;case 211:return r=this._view.getInt32(this._offset)*Math.pow(2,32),s=this._view.getUint32(this._offset+4),this._offset+=8,r+s;case 212:return i=this._view.getInt8(this._offset),this._offset+=1,0===i?void(this._offset+=1):[i,this._bin(1)];case 213:return i=this._view.getInt8(this._offset),this._offset+=1,[i,this._bin(2)];case 214:return i=this._view.getInt8(this._offset),this._offset+=1,[i,this._bin(4)];case 215:return i=this._view.getInt8(this._offset),this._offset+=1,0===i?(r=this._view.getInt32(this._offset)*Math.pow(2,32),s=this._view.getUint32(this._offset+4),this._offset+=8,new Date(r+s)):[i,this._bin(8)];case 216:return i=this._view.getInt8(this._offset),this._offset+=1,[i,this._bin(16)];case 217:return n=this._view.getUint8(this._offset),this._offset+=1,this._str(n);case 218:return n=this._view.getUint16(this._offset),this._offset+=2,this._str(n);case 219:return n=this._view.getUint32(this._offset),this._offset+=4,this._str(n);case 220:return n=this._view.getUint16(this._offset),this._offset+=2,this._array(n);case 221:return n=this._view.getUint32(this._offset),this._offset+=4,this._array(n);case 222:return n=this._view.getUint16(this._offset),this._offset+=2,this._map(n);case 223:return n=this._view.getUint32(this._offset),this._offset+=4,this._map(n)}throw new Error("Could not parse")};var bt=function(t){var e=new mt(t),n=e._parse();if(e._offset!==t.byteLength)throw new Error(t.byteLength-e._offset+" trailing bytes");return n};yt.encode=_t,yt.decode=bt;var kt,wt={exports:{}};!function(t){function e(t){if(t)return function(t){for(var n in e.prototype)t[n]=e.prototype[n];return t}(t)}t.exports=e,e.prototype.on=e.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+t]=this._callbacks["$"+t]||[]).push(e),this},e.prototype.once=function(t,e){function n(){this.off(t,n),e.apply(this,arguments)}return n.fn=e,this.on(t,n),this},e.prototype.off=e.prototype.removeListener=e.prototype.removeAllListeners=e.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var n,i=this._callbacks["$"+t];if(!i)return this;if(1==arguments.length)return delete this._callbacks["$"+t],this;for(var r=0;r<i.length;r++)if((n=i[r])===e||n.fn===e){i.splice(r,1);break}return 0===i.length&&delete this._callbacks["$"+t],this},e.prototype.emit=function(t){this._callbacks=this._callbacks||{};for(var e=new Array(arguments.length-1),n=this._callbacks["$"+t],i=1;i<arguments.length;i++)e[i-1]=arguments[i];if(n){i=0;for(var r=(n=n.slice(0)).length;i<r;++i)n[i].apply(this,e)}return this},e.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks["$"+t]||[]},e.prototype.hasListeners=function(t){return!!this.listeners(t).length}}(wt);var Ot=yt,Et=wt.exports,Ct=dt.protocol=5,Tt=kt=dt.PacketType={CONNECT:0,DISCONNECT:1,EVENT:2,ACK:3,CONNECT_ERROR:4},Rt=Number.isInteger||function(t){return"number"==typeof t&&isFinite(t)&&Math.floor(t)===t},At=function(t){return"string"==typeof t},St=function(t){return"[object Object]"===Object.prototype.toString.call(t)};function Lt(){}function xt(){}Lt.prototype.encode=function(t){return[Ot.encode(t)]},Et(xt.prototype),xt.prototype.add=function(t){var e=Ot.decode(t);this.checkPacket(e),this.emit("decoded",e)},xt.prototype.checkPacket=function(t){if(!(Rt(t.type)&&t.type>=Tt.CONNECT&&t.type<=Tt.CONNECT_ERROR))throw new Error("invalid packet type");if(!At(t.nsp))throw new Error("invalid namespace");if(!function(t){switch(t.type){case Tt.CONNECT:return void 0===t.data||St(t.data);case Tt.DISCONNECT:return void 0===t.data;case Tt.CONNECT_ERROR:return At(t.data)||St(t.data);default:return Array.isArray(t.data)}}(t))throw new Error("invalid payload");if(!(void 0===t.id||Rt(t.id)))throw new Error("invalid packet id")},xt.prototype.destroy=function(){};var Bt=dt.Encoder=Lt,Ut=dt.Decoder=xt,Pt=t({__proto__:null,default:dt,protocol:Ct,get PacketType(){return kt},Encoder:Bt,Decoder:Ut},[dt]);function jt(t,e,n){return t.on(e,n),function(){t.off(e,n)}}var qt=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1}),Nt=function(t){o(i,t);var e=d(i);function i(t,r,o){var a;return n(this,i),(a=e.call(this)).connected=!1,a.recovered=!1,a.receiveBuffer=[],a.sendBuffer=[],a._queue=[],a._queueSeq=0,a.ids=0,a.acks={},a.flags={},a.io=t,a.nsp=r,o&&o.auth&&(a.auth=o.auth),a._opts=s({},o),a.io._autoConnect&&a.open(),a}return r(i,[{key:"disconnected",get:function(){return!this.connected}},{key:"subEvents",value:function(){if(!this.subs){var t=this.io;this.subs=[jt(t,"open",this.onopen.bind(this)),jt(t,"packet",this.onpacket.bind(this)),jt(t,"error",this.onerror.bind(this)),jt(t,"close",this.onclose.bind(this))]}}},{key:"active",get:function(){return!!this.subs}},{key:"connect",value:function(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}},{key:"open",value:function(){return this.connect()}},{key:"send",value:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return e.unshift("message"),this.emit.apply(this,e),this}},{key:"emit",value:function(t){if(qt.hasOwnProperty(t))throw new Error('"'+t.toString()+'" is a reserved event name');for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];if(n.unshift(t),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(n),this;var r={type:kt.EVENT,data:n,options:{}};if(r.options.compress=!1!==this.flags.compress,"function"==typeof n[n.length-1]){var s=this.ids++,o=n.pop();this._registerAckCallback(s,o),r.id=s}var a=this.io.engine&&this.io.engine.transport&&this.io.engine.transport.writable,u=this.flags.volatile&&(!a||!this.connected);return u||(this.connected?(this.notifyOutgoingListeners(r),this.packet(r)):this.sendBuffer.push(r)),this.flags={},this}},{key:"_registerAckCallback",value:function(t,e){var n,i=this,r=null!==(n=this.flags.timeout)&&void 0!==n?n:this._opts.ackTimeout;if(void 0!==r){var s=this.io.setTimeoutFn((function(){delete i.acks[t];for(var n=0;n<i.sendBuffer.length;n++)i.sendBuffer[n].id===t&&i.sendBuffer.splice(n,1);e.call(i,new Error("operation has timed out"))}),r);this.acks[t]=function(){i.io.clearTimeoutFn(s);for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];e.apply(i,[null].concat(n))}}else this.acks[t]=e}},{key:"emitWithAck",value:function(t){for(var e=this,n=arguments.length,i=new Array(n>1?n-1:0),r=1;r<n;r++)i[r-1]=arguments[r];var s=void 0!==this.flags.timeout||void 0!==this._opts.ackTimeout;return new Promise((function(n,r){i.push((function(t,e){return s?t?r(t):n(e):n(t)})),e.emit.apply(e,[t].concat(i))}))}},{key:"_addToQueue",value:function(t){var e,n=this;"function"==typeof t[t.length-1]&&(e=t.pop());var i={id:this._queueSeq++,tryCount:0,pending:!1,args:t,flags:s({fromQueue:!0},this.flags)};t.push((function(t){if(i===n._queue[0]){var r=null!==t;if(r)i.tryCount>n._opts.retries&&(n._queue.shift(),e&&e(t));else if(n._queue.shift(),e){for(var s=arguments.length,o=new Array(s>1?s-1:0),a=1;a<s;a++)o[a-1]=arguments[a];e.apply(void 0,[null].concat(o))}return i.pending=!1,n._drainQueue()}})),this._queue.push(i),this._drainQueue()}},{key:"_drainQueue",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.connected&&0!==this._queue.length){var e=this._queue[0];e.pending&&!t||(e.pending=!0,e.tryCount++,this.flags=e.flags,this.emit.apply(this,e.args))}}},{key:"packet",value:function(t){t.nsp=this.nsp,this.io._packet(t)}},{key:"onopen",value:function(){var t=this;"function"==typeof this.auth?this.auth((function(e){t._sendConnectPacket(e)})):this._sendConnectPacket(this.auth)}},{key:"_sendConnectPacket",value:function(t){this.packet({type:kt.CONNECT,data:this._pid?s({pid:this._pid,offset:this._lastOffset},t):t})}},{key:"onerror",value:function(t){this.connected||this.emitReserved("connect_error",t)}},{key:"onclose",value:function(t,e){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,e)}},{key:"onpacket",value:function(t){if(t.nsp===this.nsp)switch(t.type){case kt.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case kt.EVENT:case kt.BINARY_EVENT:this.onevent(t);break;case kt.ACK:case kt.BINARY_ACK:this.onack(t);break;case kt.DISCONNECT:this.ondisconnect();break;case kt.CONNECT_ERROR:this.destroy();var e=new Error(t.data.message);e.data=t.data.data,this.emitReserved("connect_error",e)}}},{key:"onevent",value:function(t){var e=t.data||[];null!=t.id&&e.push(this.ack(t.id)),this.connected?this.emitEvent(e):this.receiveBuffer.push(Object.freeze(e))}},{key:"emitEvent",value:function(t){if(this._anyListeners&&this._anyListeners.length){var e,n=_(this._anyListeners.slice());try{for(n.s();!(e=n.n()).done;){e.value.apply(this,t)}}catch(t){n.e(t)}finally{n.f()}}v(a(i.prototype),"emit",this).apply(this,t),this._pid&&t.length&&"string"==typeof t[t.length-1]&&(this._lastOffset=t[t.length-1])}},{key:"ack",value:function(t){var e=this,n=!1;return function(){if(!n){n=!0;for(var i=arguments.length,r=new Array(i),s=0;s<i;s++)r[s]=arguments[s];e.packet({type:kt.ACK,id:t,data:r})}}}},{key:"onack",value:function(t){var e=this.acks[t.id];"function"==typeof e&&(e.apply(this,t.data),delete this.acks[t.id])}},{key:"onconnect",value:function(t,e){this.id=t,this.recovered=e&&this._pid===e,this._pid=e,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}},{key:"emitBuffered",value:function(){var t=this;this.receiveBuffer.forEach((function(e){return t.emitEvent(e)})),this.receiveBuffer=[],this.sendBuffer.forEach((function(e){t.notifyOutgoingListeners(e),t.packet(e)})),this.sendBuffer=[]}},{key:"ondisconnect",value:function(){this.destroy(),this.onclose("io server disconnect")}},{key:"destroy",value:function(){this.subs&&(this.subs.forEach((function(t){return t()})),this.subs=void 0),this.io._destroy(this)}},{key:"disconnect",value:function(){return this.connected&&this.packet({type:kt.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}},{key:"close",value:function(){return this.disconnect()}},{key:"compress",value:function(t){return this.flags.compress=t,this}},{key:"volatile",get:function(){return this.flags.volatile=!0,this}},{key:"timeout",value:function(t){return this.flags.timeout=t,this}},{key:"onAny",value:function(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(t),this}},{key:"prependAny",value:function(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(t),this}},{key:"offAny",value:function(t){if(!this._anyListeners)return this;if(t){for(var e=this._anyListeners,n=0;n<e.length;n++)if(t===e[n])return e.splice(n,1),this}else this._anyListeners=[];return this}},{key:"listenersAny",value:function(){return this._anyListeners||[]}},{key:"onAnyOutgoing",value:function(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(t),this}},{key:"prependAnyOutgoing",value:function(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(t),this}},{key:"offAnyOutgoing",value:function(t){if(!this._anyOutgoingListeners)return this;if(t){for(var e=this._anyOutgoingListeners,n=0;n<e.length;n++)if(t===e[n])return e.splice(n,1),this}else this._anyOutgoingListeners=[];return this}},{key:"listenersAnyOutgoing",value:function(){return this._anyOutgoingListeners||[]}},{key:"notifyOutgoingListeners",value:function(t){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){var e,n=_(this._anyOutgoingListeners.slice());try{for(n.s();!(e=n.n()).done;){e.value.apply(this,t.data)}}catch(t){n.e(t)}finally{n.f()}}}}]),i}(P);function Mt(t){t=t||{},this.ms=t.min||100,this.max=t.max||1e4,this.factor=t.factor||2,this.jitter=t.jitter>0&&t.jitter<=1?t.jitter:0,this.attempts=0}Mt.prototype.duration=function(){var t=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var e=Math.random(),n=Math.floor(e*this.jitter*t);t=0==(1&Math.floor(10*e))?t-n:t+n}return 0|Math.min(t,this.max)},Mt.prototype.reset=function(){this.attempts=0},Mt.prototype.setMin=function(t){this.ms=t},Mt.prototype.setMax=function(t){this.max=t},Mt.prototype.setJitter=function(t){this.jitter=t};var Dt=function(t){o(s,t);var i=d(s);function s(t,r){var o,a;n(this,s),(o=i.call(this)).nsps={},o.subs=[],t&&"object"===e(t)&&(r=t,t=void 0),(r=r||{}).path=r.path||"/socket.io",o.opts=r,D(l(o),r),o.reconnection(!1!==r.reconnection),o.reconnectionAttempts(r.reconnectionAttempts||1/0),o.reconnectionDelay(r.reconnectionDelay||1e3),o.reconnectionDelayMax(r.reconnectionDelayMax||5e3),o.randomizationFactor(null!==(a=r.randomizationFactor)&&void 0!==a?a:.5),o.backoff=new Mt({min:o.reconnectionDelay(),max:o.reconnectionDelayMax(),jitter:o.randomizationFactor()}),o.timeout(null==r.timeout?2e4:r.timeout),o._readyState="closed",o.uri=t;var u=r.parser||Pt;return o.encoder=new u.Encoder,o.decoder=new u.Decoder,o._autoConnect=!1!==r.autoConnect,o._autoConnect&&o.open(),o}return r(s,[{key:"reconnection",value:function(t){return arguments.length?(this._reconnection=!!t,this):this._reconnection}},{key:"reconnectionAttempts",value:function(t){return void 0===t?this._reconnectionAttempts:(this._reconnectionAttempts=t,this)}},{key:"reconnectionDelay",value:function(t){var e;return void 0===t?this._reconnectionDelay:(this._reconnectionDelay=t,null===(e=this.backoff)||void 0===e||e.setMin(t),this)}},{key:"randomizationFactor",value:function(t){var e;return void 0===t?this._randomizationFactor:(this._randomizationFactor=t,null===(e=this.backoff)||void 0===e||e.setJitter(t),this)}},{key:"reconnectionDelayMax",value:function(t){var e;return void 0===t?this._reconnectionDelayMax:(this._reconnectionDelayMax=t,null===(e=this.backoff)||void 0===e||e.setMax(t),this)}},{key:"timeout",value:function(t){return arguments.length?(this._timeout=t,this):this._timeout}},{key:"maybeReconnectOnOpen",value:function(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}},{key:"open",value:function(t){var e=this;if(~this._readyState.indexOf("open"))return this;this.engine=new pt(this.uri,this.opts);var n=this.engine,i=this;this._readyState="opening",this.skipReconnect=!1;var r=jt(n,"open",(function(){i.onopen(),t&&t()})),s=jt(n,"error",(function(n){i.cleanup(),i._readyState="closed",e.emitReserved("error",n),t?t(n):i.maybeReconnectOnOpen()}));if(!1!==this._timeout){var o=this._timeout;0===o&&r();var a=this.setTimeoutFn((function(){r(),n.close(),n.emit("error",new Error("timeout"))}),o);this.opts.autoUnref&&a.unref(),this.subs.push((function(){clearTimeout(a)}))}return this.subs.push(r),this.subs.push(s),this}},{key:"connect",value:function(t){return this.open(t)}},{key:"onopen",value:function(){this.cleanup(),this._readyState="open",this.emitReserved("open");var t=this.engine;this.subs.push(jt(t,"ping",this.onping.bind(this)),jt(t,"data",this.ondata.bind(this)),jt(t,"error",this.onerror.bind(this)),jt(t,"close",this.onclose.bind(this)),jt(this.decoder,"decoded",this.ondecoded.bind(this)))}},{key:"onping",value:function(){this.emitReserved("ping")}},{key:"ondata",value:function(t){try{this.decoder.add(t)}catch(t){this.onclose("parse error",t)}}},{key:"ondecoded",value:function(t){var e=this;st((function(){e.emitReserved("packet",t)}),this.setTimeoutFn)}},{key:"onerror",value:function(t){this.emitReserved("error",t)}},{key:"socket",value:function(t,e){var n=this.nsps[t];return n?this._autoConnect&&!n.active&&n.connect():(n=new Nt(this,t,e),this.nsps[t]=n),n}},{key:"_destroy",value:function(t){for(var e=0,n=Object.keys(this.nsps);e<n.length;e++){var i=n[e];if(this.nsps[i].active)return}this._close()}},{key:"_packet",value:function(t){for(var e=this.encoder.encode(t),n=0;n<e.length;n++)this.engine.write(e[n],t.options)}},{key:"cleanup",value:function(){this.subs.forEach((function(t){return t()})),this.subs.length=0,this.decoder.destroy()}},{key:"_close",value:function(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close"),this.engine&&this.engine.close()}},{key:"disconnect",value:function(){return this._close()}},{key:"onclose",value:function(t,e){this.cleanup(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",t,e),this._reconnection&&!this.skipReconnect&&this.reconnect()}},{key:"reconnect",value:function(){var t=this;if(this._reconnecting||this.skipReconnect)return this;var e=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{var n=this.backoff.duration();this._reconnecting=!0;var i=this.setTimeoutFn((function(){e.skipReconnect||(t.emitReserved("reconnect_attempt",e.backoff.attempts),e.skipReconnect||e.open((function(n){n?(e._reconnecting=!1,e.reconnect(),t.emitReserved("reconnect_error",n)):e.onreconnect()})))}),n);this.opts.autoUnref&&i.unref(),this.subs.push((function(){clearTimeout(i)}))}}},{key:"onreconnect",value:function(){var t=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",t)}}]),s}(P),It={};function Ft(t,n){"object"===e(t)&&(n=t,t=void 0);var i,r=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2?arguments[2]:void 0,i=t;n=n||"undefined"!=typeof location&&location,null==t&&(t=n.protocol+"//"+n.host),"string"==typeof t&&("/"===t.charAt(0)&&(t="/"===t.charAt(1)?n.protocol+t:n.host+t),/^(https?|wss?):\/\//.test(t)||(t=void 0!==n?n.protocol+"//"+t:"https://"+t),i=lt(t)),i.port||(/^(http|ws)$/.test(i.protocol)?i.port="80":/^(http|ws)s$/.test(i.protocol)&&(i.port="443")),i.path=i.path||"/";var r=-1!==i.host.indexOf(":")?"["+i.host+"]":i.host;return i.id=i.protocol+"://"+r+":"+i.port+e,i.href=i.protocol+"://"+r+(n&&n.port===i.port?"":":"+i.port),i}(t,(n=n||{}).path||"/socket.io"),s=r.source,o=r.id,a=r.path,u=It[o]&&a in It[o].nsps;return n.forceNew||n["force new connection"]||!1===n.multiplex||u?i=new Dt(s,n):(It[o]||(It[o]=new Dt(s,n)),i=It[o]),r.query&&!n.query&&(n.query=r.queryKey),i.socket(r.path,n)}return s(Ft,{Manager:Dt,Socket:Nt,io:Ft,connect:Ft}),Ft}));
//# sourceMappingURL=socket.io.msgpack.min.js.map
