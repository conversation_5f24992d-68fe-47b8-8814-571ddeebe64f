{"version": 3, "names": ["_gensync", "data", "require", "_config", "_transformation", "_rewriteStackTrace", "transformFromAstRunner", "gens<PERSON>", "ast", "code", "opts", "config", "loadConfig", "Error", "run", "transformFromAst", "optsOrCallback", "maybe<PERSON><PERSON><PERSON>", "callback", "undefined", "beginHiddenCallStack", "sync", "errback", "exports", "transformFromAstSync", "args", "transformFromAstAsync", "async"], "sources": ["../src/transform-ast.ts"], "sourcesContent": ["import gensync, { type Hand<PERSON> } from \"gensync\";\n\nimport loadConfig from \"./config\";\nimport type { InputOptions, ResolvedConfig } from \"./config\";\nimport { run } from \"./transformation\";\nimport type * as t from \"@babel/types\";\n\nimport { beginHiddenCallStack } from \"./errors/rewrite-stack-trace\";\n\nimport type { FileResult, FileResultCallback } from \"./transformation\";\ntype AstRoot = t.File | t.Program;\n\ntype TransformFromAst = {\n  (ast: AstRoot, code: string, callback: FileResultCallback): void;\n  (\n    ast: AstRoot,\n    code: string,\n    opts: InputOptions | undefined | null,\n    callback: FileResultCallback,\n  ): void;\n  (ast: AstRoot, code: string, opts?: InputOptions | null): FileResult | null;\n};\n\nconst transformFromAstRunner = gensync(function* (\n  ast: AstRoot,\n  code: string,\n  opts: InputOptions | undefined | null,\n): Handler<FileResult | null> {\n  const config: ResolvedConfig | null = yield* loadConfig(opts);\n  if (config === null) return null;\n\n  if (!ast) throw new Error(\"No AST given\");\n\n  return yield* run(config, code, ast);\n});\n\nexport const transformFromAst: TransformFromAst = function transformFromAst(\n  ast,\n  code,\n  optsOrCallback?: InputOptions | null | undefined | FileResultCallback,\n  maybeCallback?: FileResultCallback,\n) {\n  let opts: InputOptions | undefined | null;\n  let callback: FileResultCallback | undefined;\n  if (typeof optsOrCallback === \"function\") {\n    callback = optsOrCallback;\n    opts = undefined;\n  } else {\n    opts = optsOrCallback;\n    callback = maybeCallback;\n  }\n\n  if (callback === undefined) {\n    if (process.env.BABEL_8_BREAKING) {\n      throw new Error(\n        \"Starting from Babel 8.0.0, the 'transformFromAst' function expects a callback. If you need to call it synchronously, please use 'transformFromAstSync'.\",\n      );\n    } else {\n      // console.warn(\n      //   \"Starting from Babel 8.0.0, the 'transformFromAst' function will expect a callback. If you need to call it synchronously, please use 'transformFromAstSync'.\",\n      // );\n      return beginHiddenCallStack(transformFromAstRunner.sync)(ast, code, opts);\n    }\n  }\n\n  beginHiddenCallStack(transformFromAstRunner.errback)(\n    ast,\n    code,\n    opts,\n    callback,\n  );\n};\n\nexport function transformFromAstSync(\n  ...args: Parameters<typeof transformFromAstRunner.sync>\n) {\n  return beginHiddenCallStack(transformFromAstRunner.sync)(...args);\n}\n\nexport function transformFromAstAsync(\n  ...args: Parameters<typeof transformFromAstRunner.async>\n) {\n  return beginHiddenCallStack(transformFromAstRunner.async)(...args);\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,SAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,QAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAE,OAAA,GAAAD,OAAA;AAEA,IAAAE,eAAA,GAAAF,OAAA;AAGA,IAAAG,kBAAA,GAAAH,OAAA;AAgBA,MAAMI,sBAAsB,GAAGC,UAAO,CAAC,WACrCC,GAAY,EACZC,IAAY,EACZC,IAAqC,EACT;EAC5B,MAAMC,MAA6B,GAAG,OAAO,IAAAC,eAAU,EAACF,IAAI,CAAC;EAC7D,IAAIC,MAAM,KAAK,IAAI,EAAE,OAAO,IAAI;EAEhC,IAAI,CAACH,GAAG,EAAE,MAAM,IAAIK,KAAK,CAAC,cAAc,CAAC;EAEzC,OAAO,OAAO,IAAAC,mBAAG,EAACH,MAAM,EAAEF,IAAI,EAAED,GAAG,CAAC;AACtC,CAAC,CAAC;AAEK,MAAMO,gBAAkC,GAAG,SAASA,gBAAgBA,CACzEP,GAAG,EACHC,IAAI,EACJO,cAAqE,EACrEC,aAAkC,EAClC;EACA,IAAIP,IAAqC;EACzC,IAAIQ,QAAwC;EAC5C,IAAI,OAAOF,cAAc,KAAK,UAAU,EAAE;IACxCE,QAAQ,GAAGF,cAAc;IACzBN,IAAI,GAAGS,SAAS;EAClB,CAAC,MAAM;IACLT,IAAI,GAAGM,cAAc;IACrBE,QAAQ,GAAGD,aAAa;EAC1B;EAEA,IAAIC,QAAQ,KAAKC,SAAS,EAAE;IAKnB;MAIL,OAAO,IAAAC,uCAAoB,EAACd,sBAAsB,CAACe,IAAI,CAAC,CAACb,GAAG,EAAEC,IAAI,EAAEC,IAAI,CAAC;IAC3E;EACF;EAEA,IAAAU,uCAAoB,EAACd,sBAAsB,CAACgB,OAAO,CAAC,CAClDd,GAAG,EACHC,IAAI,EACJC,IAAI,EACJQ,QAAQ,CACT;AACH,CAAC;AAACK,OAAA,CAAAR,gBAAA,GAAAA,gBAAA;AAEK,SAASS,oBAAoBA,CAClC,GAAGC,IAAoD,EACvD;EACA,OAAO,IAAAL,uCAAoB,EAACd,sBAAsB,CAACe,IAAI,CAAC,CAAC,GAAGI,IAAI,CAAC;AACnE;AAEO,SAASC,qBAAqBA,CACnC,GAAGD,IAAqD,EACxD;EACA,OAAO,IAAAL,uCAAoB,EAACd,sBAAsB,CAACqB,KAAK,CAAC,CAAC,GAAGF,IAAI,CAAC;AACpE;AAAC"}