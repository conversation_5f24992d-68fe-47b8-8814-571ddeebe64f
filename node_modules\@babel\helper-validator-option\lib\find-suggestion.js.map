{"version": 3, "names": ["min", "Math", "<PERSON><PERSON><PERSON><PERSON>", "a", "b", "t", "u", "i", "j", "m", "length", "n", "findSuggestion", "str", "arr", "distances", "map", "el", "indexOf"], "sources": ["../src/find-suggestion.ts"], "sourcesContent": ["const { min } = Math;\n\n// a minimal leven distance implementation\n// balanced maintainability with code size\n// It is not blazingly fast but should be okay for Babel user case\n// where it will be run for at most tens of time on strings\n// that have less than 20 ASCII characters\n\n// https://rosettacode.org/wiki/Levenshtein_distance#ES5\nfunction levenshtein(a: string, b: string): number {\n  let t = [],\n    u: number[] = [],\n    i,\n    j;\n  const m = a.length,\n    n = b.length;\n  if (!m) {\n    return n;\n  }\n  if (!n) {\n    return m;\n  }\n  for (j = 0; j <= n; j++) {\n    t[j] = j;\n  }\n  for (i = 1; i <= m; i++) {\n    for (u = [i], j = 1; j <= n; j++) {\n      u[j] =\n        a[i - 1] === b[j - 1] ? t[j - 1] : min(t[j - 1], t[j], u[j - 1]) + 1;\n    }\n    t = u;\n  }\n  return u[n];\n}\n\n/**\n * Given a string `str` and an array of candidates `arr`,\n * return the first of elements in candidates that has minimal\n * <PERSON><PERSON>htein distance with `str`.\n * @export\n * @param {string} str\n * @param {string[]} arr\n * @returns {string}\n */\nexport function findSuggestion(str: string, arr: readonly string[]): string {\n  const distances = arr.map<number>(el => levenshtein(el, str));\n  return arr[distances.indexOf(min(...distances))];\n}\n"], "mappings": ";;;;;;AAAA,MAAM;EAAEA;AAAI,CAAC,GAAGC,IAAI;AASpB,SAASC,WAAW,CAACC,CAAS,EAAEC,CAAS,EAAU;EACjD,IAAIC,CAAC,GAAG,EAAE;IACRC,CAAW,GAAG,EAAE;IAChBC,CAAC;IACDC,CAAC;EACH,MAAMC,CAAC,GAAGN,CAAC,CAACO,MAAM;IAChBC,CAAC,GAAGP,CAAC,CAACM,MAAM;EACd,IAAI,CAACD,CAAC,EAAE;IACN,OAAOE,CAAC;EACV;EACA,IAAI,CAACA,CAAC,EAAE;IACN,OAAOF,CAAC;EACV;EACA,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIG,CAAC,EAAEH,CAAC,EAAE,EAAE;IACvBH,CAAC,CAACG,CAAC,CAAC,GAAGA,CAAC;EACV;EACA,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIE,CAAC,EAAEF,CAAC,EAAE,EAAE;IACvB,KAAKD,CAAC,GAAG,CAACC,CAAC,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIG,CAAC,EAAEH,CAAC,EAAE,EAAE;MAChCF,CAAC,CAACE,CAAC,CAAC,GACFL,CAAC,CAACI,CAAC,GAAG,CAAC,CAAC,KAAKH,CAAC,CAACI,CAAC,GAAG,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,GAAG,CAAC,CAAC,GAAGR,GAAG,CAACK,CAAC,CAACG,CAAC,GAAG,CAAC,CAAC,EAAEH,CAAC,CAACG,CAAC,CAAC,EAAEF,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IACxE;IACAH,CAAC,GAAGC,CAAC;EACP;EACA,OAAOA,CAAC,CAACK,CAAC,CAAC;AACb;AAWO,SAASC,cAAc,CAACC,GAAW,EAAEC,GAAsB,EAAU;EAC1E,MAAMC,SAAS,GAAGD,GAAG,CAACE,GAAG,CAASC,EAAE,IAAIf,WAAW,CAACe,EAAE,EAAEJ,GAAG,CAAC,CAAC;EAC7D,OAAOC,GAAG,CAACC,SAAS,CAACG,OAAO,CAAClB,GAAG,CAAC,GAAGe,SAAS,CAAC,CAAC,CAAC;AAClD"}