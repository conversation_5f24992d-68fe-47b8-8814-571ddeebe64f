{"version": 3, "names": ["smart", "createTemplateBuilder", "formatters", "statement", "statements", "expression", "program", "Object", "assign", "bind", "undefined", "ast"], "sources": ["../src/index.ts"], "sourcesContent": ["import * as formatters from \"./formatters\";\nimport createTemplateBuilder from \"./builder\";\n\nexport const smart = createTemplateBuilder(formatters.smart);\nexport const statement = createTemplateBuilder(formatters.statement);\nexport const statements = createTemplateBuilder(formatters.statements);\nexport const expression = createTemplateBuilder(formatters.expression);\nexport const program = createTemplateBuilder(formatters.program);\n\ntype DefaultTemplateBuilder = typeof smart & {\n  smart: typeof smart;\n  statement: typeof statement;\n  statements: typeof statements;\n  expression: typeof expression;\n  program: typeof program;\n};\n\nexport default Object.assign(smart.bind(undefined) as DefaultTemplateBuilder, {\n  smart,\n  statement,\n  statements,\n  expression,\n  program,\n  ast: smart.ast,\n});\n"], "mappings": ";;;;;;AAAA;AACA;AAEO,MAAMA,KAAK,GAAG,IAAAC,gBAAqB,EAACC,UAAU,CAACF,KAAK,CAAC;AAAC;AACtD,MAAMG,SAAS,GAAG,IAAAF,gBAAqB,EAACC,UAAU,CAACC,SAAS,CAAC;AAAC;AAC9D,MAAMC,UAAU,GAAG,IAAAH,gBAAqB,EAACC,UAAU,CAACE,UAAU,CAAC;AAAC;AAChE,MAAMC,UAAU,GAAG,IAAAJ,gBAAqB,EAACC,UAAU,CAACG,UAAU,CAAC;AAAC;AAChE,MAAMC,OAAO,GAAG,IAAAL,gBAAqB,EAACC,UAAU,CAACI,OAAO,CAAC;AAAC;AAAA,eAUlDC,MAAM,CAACC,MAAM,CAACR,KAAK,CAACS,IAAI,CAACC,SAAS,CAAC,EAA4B;EAC5EV,KAAK;EACLG,SAAS;EACTC,UAAU;EACVC,UAAU;EACVC,OAAO;EACPK,GAAG,EAAEX,KAAK,CAACW;AACb,CAAC,CAAC;AAAA"}