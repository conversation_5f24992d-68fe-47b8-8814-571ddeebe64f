{"version": 3, "file": "popper-lite.js", "sources": ["../../src/dom-utils/getWindow.js", "../../src/dom-utils/instanceOf.js", "../../src/utils/math.js", "../../src/utils/userAgent.js", "../../src/dom-utils/isLayoutViewport.js", "../../src/dom-utils/getBoundingClientRect.js", "../../src/dom-utils/getWindowScroll.js", "../../src/dom-utils/getHTMLElementScroll.js", "../../src/dom-utils/getNodeScroll.js", "../../src/dom-utils/getNodeName.js", "../../src/dom-utils/getDocumentElement.js", "../../src/dom-utils/getWindowScrollBarX.js", "../../src/dom-utils/getComputedStyle.js", "../../src/dom-utils/isScrollParent.js", "../../src/dom-utils/getCompositeRect.js", "../../src/dom-utils/getLayoutRect.js", "../../src/dom-utils/getParentNode.js", "../../src/dom-utils/getScrollParent.js", "../../src/dom-utils/listScrollParents.js", "../../src/dom-utils/isTableElement.js", "../../src/dom-utils/getOffsetParent.js", "../../src/enums.js", "../../src/utils/orderModifiers.js", "../../src/utils/debounce.js", "../../src/utils/format.js", "../../src/utils/validateModifiers.js", "../../src/utils/uniqueBy.js", "../../src/utils/getBasePlacement.js", "../../src/utils/mergeByName.js", "../../src/dom-utils/getViewportRect.js", "../../src/dom-utils/getDocumentRect.js", "../../src/dom-utils/contains.js", "../../src/utils/rectToClientRect.js", "../../src/dom-utils/getClippingRect.js", "../../src/utils/getVariation.js", "../../src/utils/getMainAxisFromPlacement.js", "../../src/utils/computeOffsets.js", "../../src/utils/getFreshSideObject.js", "../../src/utils/mergePaddingObject.js", "../../src/utils/expandToHashMap.js", "../../src/utils/detectOverflow.js", "../../src/createPopper.js", "../../src/modifiers/eventListeners.js", "../../src/modifiers/popperOffsets.js", "../../src/modifiers/computeStyles.js", "../../src/modifiers/applyStyles.js", "../../src/popper-lite.js"], "sourcesContent": ["// @flow\nimport type { Window } from '../types';\ndeclare function getWindow(node: Node | Window): Window;\n\nexport default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    const ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}\n", "// @flow\nimport getWindow from './getWindow';\n\ndeclare function isElement(node: mixed): boolean %checks(node instanceof\n  Element);\nfunction isElement(node) {\n  const OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\ndeclare function isHTMLElement(node: mixed): boolean %checks(node instanceof\n  HTMLElement);\nfunction isHTMLElement(node) {\n  const OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\ndeclare function isShadowRoot(node: mixed): boolean %checks(node instanceof\n  ShadowRoot);\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  const OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };\n", "// @flow\nexport const max = Math.max;\nexport const min = Math.min;\nexport const round = Math.round;\n", "// @flow\ntype Navigator = Navigator & { userAgentData?: NavigatorUAData };\n\ninterface NavigatorUAData {\n  brands: Array<{ brand: string, version: string }>;\n  mobile: boolean;\n  platform: string;\n}\n\nexport default function getUAString(): string {\n  const uaData = (navigator: Navigator).userAgentData;\n\n  if (uaData?.brands && Array.isArray(uaData.brands)) {\n    return uaData.brands\n      .map((item) => `${item.brand}/${item.version}`)\n      .join(' ');\n  }\n\n  return navigator.userAgent;\n}\n", "// @flow\nimport getUAString from '../utils/userAgent';\n\nexport default function isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}\n", "// @flow\nimport type { ClientRectObject, VirtualElement } from '../types';\nimport { isElement, isHTMLElement } from './instanceOf';\nimport { round } from '../utils/math';\nimport getWindow from './getWindow';\nimport isLayoutViewport from './isLayoutViewport';\n\nexport default function getBoundingClientRect(\n  element: Element | VirtualElement,\n  includeScale: boolean = false,\n  isFixedStrategy: boolean = false\n): ClientRectObject {\n  const clientRect = element.getBoundingClientRect();\n  let scaleX = 1;\n  let scaleY = 1;\n\n  if (includeScale && isHTMLElement(element)) {\n    scaleX =\n      (element: HTMLElement).offsetWidth > 0\n        ? round(clientRect.width) / (element: HTMLElement).offsetWidth || 1\n        : 1;\n    scaleY =\n      (element: HTMLElement).offsetHeight > 0\n        ? round(clientRect.height) / (element: HTMLElement).offsetHeight || 1\n        : 1;\n  }\n\n  const { visualViewport } = isElement(element) ? getWindow(element) : window;\n  const addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n\n  const x =\n    (clientRect.left +\n      (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) /\n    scaleX;\n  const y =\n    (clientRect.top +\n      (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) /\n    scaleY;\n  const width = clientRect.width / scaleX;\n  const height = clientRect.height / scaleY;\n\n  return {\n    width,\n    height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x,\n    y,\n  };\n}\n", "// @flow\nimport getWindow from './getWindow';\nimport type { Window } from '../types';\n\nexport default function getWindowScroll(node: Node | Window) {\n  const win = getWindow(node);\n  const scrollLeft = win.pageXOffset;\n  const scrollTop = win.pageYOffset;\n\n  return {\n    scrollLeft,\n    scrollTop,\n  };\n}\n", "// @flow\n\nexport default function getHTMLElementScroll(element: HTMLElement) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop,\n  };\n}\n", "// @flow\nimport getWindowScroll from './getWindowScroll';\nimport getWindow from './getWindow';\nimport { isHTMLElement } from './instanceOf';\nimport getHTMLElementScroll from './getHTMLElementScroll';\nimport type { Window } from '../types';\n\nexport default function getNodeScroll(node: Node | Window) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}\n", "// @flow\nimport type { Window } from '../types';\n\nexport default function getNodeName(element: ?Node | Window): ?string {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}\n", "// @flow\nimport { isElement } from './instanceOf';\nimport type { Window } from '../types';\n\nexport default function getDocumentElement(\n  element: Element | Window\n): HTMLElement {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return (\n    (isElement(element)\n      ? element.ownerDocument\n      : // $FlowFixMe[prop-missing]\n        element.document) || window.document\n  ).documentElement;\n}\n", "// @flow\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getDocumentElement from './getDocumentElement';\nimport getWindowScroll from './getWindowScroll';\n\nexport default function getWindowScrollBarX(element: Element): number {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return (\n    getBoundingClientRect(getDocumentElement(element)).left +\n    getWindowScroll(element).scrollLeft\n  );\n}\n", "// @flow\nimport getWindow from './getWindow';\n\nexport default function getComputedStyle(\n  element: Element\n): CSSStyleDeclaration {\n  return getWindow(element).getComputedStyle(element);\n}\n", "// @flow\nimport getComputedStyle from './getComputedStyle';\n\nexport default function isScrollParent(element: HTMLElement): boolean {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  const { overflow, overflowX, overflowY } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}\n", "// @flow\nimport type { Rect, VirtualElement, Window } from '../types';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getNodeScroll from './getNodeScroll';\nimport getNodeName from './getNodeName';\nimport { isHTMLElement } from './instanceOf';\nimport getWindowScrollBarX from './getWindowScrollBarX';\nimport getDocumentElement from './getDocumentElement';\nimport isScrollParent from './isScrollParent';\nimport { round } from '../utils/math';\n\nfunction isElementScaled(element: HTMLElement) {\n  const rect = element.getBoundingClientRect();\n  const scaleX = round(rect.width) / element.offsetWidth || 1;\n  const scaleY = round(rect.height) / element.offsetHeight || 1;\n\n  return scaleX !== 1 || scaleY !== 1;\n}\n\n// Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\nexport default function getCompositeRect(\n  elementOrVirtualElement: Element | VirtualElement,\n  offsetParent: Element | Window,\n  isFixed: boolean = false\n): Rect {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const offsetParentIsScaled =\n    isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const rect = getBoundingClientRect(\n    elementOrVirtualElement,\n    offsetParentIsScaled,\n    isFixed\n  );\n\n  let scroll = { scrollLeft: 0, scrollTop: 0 };\n  let offsets = { x: 0, y: 0 };\n\n  if (isOffsetParentAnElement || (!isOffsetParentAnElement && !isFixed)) {\n    if (\n      getNodeName(offsetParent) !== 'body' ||\n      // https://github.com/popperjs/popper-core/issues/1078\n      isScrollParent(documentElement)\n    ) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height,\n  };\n}\n", "// @flow\nimport type { Rect } from '../types';\nimport getBoundingClientRect from './getBoundingClientRect';\n\n// Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\nexport default function getLayoutRect(element: HTMLElement): Rect {\n  const clientRect = getBoundingClientRect(element);\n\n  // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n  let width = element.offsetWidth;\n  let height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width,\n    height,\n  };\n}\n", "// @flow\nimport getNodeName from './getNodeName';\nimport getDocumentElement from './getDocumentElement';\nimport { isShadowRoot } from './instanceOf';\n\nexport default function getParentNode(element: Node | ShadowRoot): Node {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (\n    // this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || // DOM Element detected\n    (isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n  );\n}\n", "// @flow\nimport getParentNode from './getParentNode';\nimport isScrollParent from './isScrollParent';\nimport getNodeName from './getNodeName';\nimport { isHTMLElement } from './instanceOf';\n\nexport default function getScrollParent(node: Node): HTMLElement {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}\n", "// @flow\nimport getScrollParent from './getScrollParent';\nimport getParentNode from './getParentNode';\nimport getWindow from './getWindow';\nimport type { Window, VisualViewport } from '../types';\nimport isScrollParent from './isScrollParent';\n\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\nexport default function listScrollParents(\n  element: Node,\n  list: Array<Element | Window> = []\n): Array<Element | Window | VisualViewport> {\n  const scrollParent = getScrollParent(element);\n  const isBody = scrollParent === element.ownerDocument?.body;\n  const win = getWindow(scrollParent);\n  const target = isBody\n    ? [win].concat(\n        win.visualViewport || [],\n        isScrollParent(scrollParent) ? scrollParent : []\n      )\n    : scrollParent;\n  const updatedList = list.concat(target);\n\n  return isBody\n    ? updatedList\n    : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n      updatedList.concat(listScrollParents(getParentNode(target)));\n}\n", "// @flow\nimport getNodeName from './getNodeName';\n\nexport default function isTableElement(element: Element): boolean {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}\n", "// @flow\nimport getWindow from './getWindow';\nimport getNodeName from './getNodeName';\nimport getComputedStyle from './getComputedStyle';\nimport { isHTMLElement, isShadowRoot } from './instanceOf';\nimport isTableElement from './isTableElement';\nimport getParentNode from './getParentNode';\nimport getUAString from '../utils/userAgent';\n\nfunction getTrueOffsetParent(element: Element): ?Element {\n  if (\n    !isHTMLElement(element) ||\n    // https://github.com/popperjs/popper-core/issues/837\n    getComputedStyle(element).position === 'fixed'\n  ) {\n    return null;\n  }\n\n  return element.offsetParent;\n}\n\n// `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\nfunction getContainingBlock(element: Element) {\n  const isFirefox = /firefox/i.test(getUAString());\n  const isIE = /Trident/i.test(getUAString());\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    const elementCss = getComputedStyle(element);\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  let currentNode = getParentNode(element);\n\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while (\n    isHTMLElement(currentNode) &&\n    ['html', 'body'].indexOf(getNodeName(currentNode)) < 0\n  ) {\n    const css = getComputedStyle(currentNode);\n\n    // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n    if (\n      css.transform !== 'none' ||\n      css.perspective !== 'none' ||\n      css.contain === 'paint' ||\n      ['transform', 'perspective'].indexOf(css.willChange) !== -1 ||\n      (isFirefox && css.willChange === 'filter') ||\n      (isFirefox && css.filter && css.filter !== 'none')\n    ) {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nexport default function getOffsetParent(element: Element) {\n  const window = getWindow(element);\n\n  let offsetParent = getTrueOffsetParent(element);\n\n  while (\n    offsetParent &&\n    isTableElement(offsetParent) &&\n    getComputedStyle(offsetParent).position === 'static'\n  ) {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (\n    offsetParent &&\n    (getNodeName(offsetParent) === 'html' ||\n      (getNodeName(offsetParent) === 'body' &&\n        getComputedStyle(offsetParent).position === 'static'))\n  ) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}\n", "// @flow\nexport const top: 'top' = 'top';\nexport const bottom: 'bottom' = 'bottom';\nexport const right: 'right' = 'right';\nexport const left: 'left' = 'left';\nexport const auto: 'auto' = 'auto';\nexport type BasePlacement =\n  | typeof top\n  | typeof bottom\n  | typeof right\n  | typeof left;\nexport const basePlacements: Array<BasePlacement> = [top, bottom, right, left];\n\nexport const start: 'start' = 'start';\nexport const end: 'end' = 'end';\nexport type Variation = typeof start | typeof end;\n\nexport const clippingParents: 'clippingParents' = 'clippingParents';\nexport const viewport: 'viewport' = 'viewport';\nexport type Boundary = Element | Array<Element> | typeof clippingParents;\nexport type RootBoundary = typeof viewport | 'document';\n\nexport const popper: 'popper' = 'popper';\nexport const reference: 'reference' = 'reference';\nexport type Context = typeof popper | typeof reference;\n\nexport type VariationPlacement =\n  | 'top-start'\n  | 'top-end'\n  | 'bottom-start'\n  | 'bottom-end'\n  | 'right-start'\n  | 'right-end'\n  | 'left-start'\n  | 'left-end';\nexport type AutoPlacement = 'auto' | 'auto-start' | 'auto-end';\nexport type ComputedPlacement = VariationPlacement | BasePlacement;\nexport type Placement = AutoPlacement | BasePlacement | VariationPlacement;\n\nexport const variationPlacements: Array<VariationPlacement> = basePlacements.reduce(\n  (acc: Array<VariationPlacement>, placement: BasePlacement) =>\n    acc.concat([(`${placement}-${start}`: any), (`${placement}-${end}`: any)]),\n  []\n);\nexport const placements: Array<Placement> = [...basePlacements, auto].reduce(\n  (\n    acc: Array<Placement>,\n    placement: BasePlacement | typeof auto\n  ): Array<Placement> =>\n    acc.concat([\n      placement,\n      (`${placement}-${start}`: any),\n      (`${placement}-${end}`: any),\n    ]),\n  []\n);\n\n// modifiers that need to read the DOM\nexport const beforeRead: 'beforeRead' = 'beforeRead';\nexport const read: 'read' = 'read';\nexport const afterRead: 'afterRead' = 'afterRead';\n// pure-logic modifiers\nexport const beforeMain: 'beforeMain' = 'beforeMain';\nexport const main: 'main' = 'main';\nexport const afterMain: 'afterMain' = 'afterMain';\n// modifier with the purpose to write to the DOM (or write into a framework state)\nexport const beforeWrite: 'beforeWrite' = 'beforeWrite';\nexport const write: 'write' = 'write';\nexport const afterWrite: 'afterWrite' = 'afterWrite';\nexport const modifierPhases: Array<ModifierPhases> = [\n  beforeRead,\n  read,\n  afterRead,\n  beforeMain,\n  main,\n  afterMain,\n  beforeWrite,\n  write,\n  afterWrite,\n];\n\nexport type ModifierPhases =\n  | typeof beforeRead\n  | typeof read\n  | typeof afterRead\n  | typeof beforeMain\n  | typeof main\n  | typeof afterMain\n  | typeof beforeWrite\n  | typeof write\n  | typeof afterWrite;\n", "// @flow\nimport type { Modifier } from '../types';\nimport { modifierPhases } from '../enums';\n\n// source: https://stackoverflow.com/questions/49875255\nfunction order(modifiers) {\n  const map = new Map();\n  const visited = new Set();\n  const result = [];\n\n  modifiers.forEach(modifier => {\n    map.set(modifier.name, modifier);\n  });\n\n  // On visiting object, check for its dependencies and visit them recursively\n  function sort(modifier: Modifier<any, any>) {\n    visited.add(modifier.name);\n\n    const requires = [\n      ...(modifier.requires || []),\n      ...(modifier.requiresIfExists || []),\n    ];\n\n    requires.forEach(dep => {\n      if (!visited.has(dep)) {\n        const depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n\n    result.push(modifier);\n  }\n\n  modifiers.forEach(modifier => {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n\n  return result;\n}\n\nexport default function orderModifiers(\n  modifiers: Array<Modifier<any, any>>\n): Array<Modifier<any, any>> {\n  // order based on dependencies\n  const orderedModifiers = order(modifiers);\n\n  // order based on phase\n  return modifierPhases.reduce((acc, phase) => {\n    return acc.concat(\n      orderedModifiers.filter(modifier => modifier.phase === phase)\n    );\n  }, []);\n}\n", "// @flow\n\nexport default function debounce<T>(fn: Function): () => Promise<T> {\n  let pending;\n  return () => {\n    if (!pending) {\n      pending = new Promise<T>(resolve => {\n        Promise.resolve().then(() => {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}\n", "// @flow\n\nexport default function format(str: string, ...args: Array<string>) {\n  return [...args].reduce((p, c) => p.replace(/%s/, c), str);\n}\n", "// @flow\nimport format from './format';\nimport { modifierPhases } from '../enums';\n\nconst INVALID_MODIFIER_ERROR =\n  'Popper: modifier \"%s\" provided an invalid %s property, expected %s but got %s';\nconst MISSING_DEPENDENCY_ERROR =\n  'Popper: modifier \"%s\" requires \"%s\", but \"%s\" modifier is not available';\nconst VALID_PROPERTIES = [\n  'name',\n  'enabled',\n  'phase',\n  'fn',\n  'effect',\n  'requires',\n  'options',\n];\n\nexport default function validateModifiers(modifiers: Array<any>): void {\n  modifiers.forEach((modifier) => {\n    [...Object.keys(modifier), ...VALID_PROPERTIES]\n      // IE11-compatible replacement for `new Set(iterable)`\n      .filter((value, index, self) => self.indexOf(value) === index)\n      .forEach((key) => {\n        switch (key) {\n          case 'name':\n            if (typeof modifier.name !== 'string') {\n              console.error(\n                format(\n                  INVALID_MODIFIER_ERROR,\n                  String(modifier.name),\n                  '\"name\"',\n                  '\"string\"',\n                  `\"${String(modifier.name)}\"`\n                )\n              );\n            }\n            break;\n          case 'enabled':\n            if (typeof modifier.enabled !== 'boolean') {\n              console.error(\n                format(\n                  INVALID_MODIFIER_ERROR,\n                  modifier.name,\n                  '\"enabled\"',\n                  '\"boolean\"',\n                  `\"${String(modifier.enabled)}\"`\n                )\n              );\n            }\n            break;\n          case 'phase':\n            if (modifierPhases.indexOf(modifier.phase) < 0) {\n              console.error(\n                format(\n                  INVALID_MODIFIER_ERROR,\n                  modifier.name,\n                  '\"phase\"',\n                  `either ${modifierPhases.join(', ')}`,\n                  `\"${String(modifier.phase)}\"`\n                )\n              );\n            }\n            break;\n          case 'fn':\n            if (typeof modifier.fn !== 'function') {\n              console.error(\n                format(\n                  INVALID_MODIFIER_ERROR,\n                  modifier.name,\n                  '\"fn\"',\n                  '\"function\"',\n                  `\"${String(modifier.fn)}\"`\n                )\n              );\n            }\n            break;\n          case 'effect':\n            if (\n              modifier.effect != null &&\n              typeof modifier.effect !== 'function'\n            ) {\n              console.error(\n                format(\n                  INVALID_MODIFIER_ERROR,\n                  modifier.name,\n                  '\"effect\"',\n                  '\"function\"',\n                  `\"${String(modifier.fn)}\"`\n                )\n              );\n            }\n            break;\n          case 'requires':\n            if (\n              modifier.requires != null &&\n              !Array.isArray(modifier.requires)\n            ) {\n              console.error(\n                format(\n                  INVALID_MODIFIER_ERROR,\n                  modifier.name,\n                  '\"requires\"',\n                  '\"array\"',\n                  `\"${String(modifier.requires)}\"`\n                )\n              );\n            }\n            break;\n          case 'requiresIfExists':\n            if (!Array.isArray(modifier.requiresIfExists)) {\n              console.error(\n                format(\n                  INVALID_MODIFIER_ERROR,\n                  modifier.name,\n                  '\"requiresIfExists\"',\n                  '\"array\"',\n                  `\"${String(modifier.requiresIfExists)}\"`\n                )\n              );\n            }\n            break;\n          case 'options':\n          case 'data':\n            break;\n          default:\n            console.error(\n              `PopperJS: an invalid property has been provided to the \"${\n                modifier.name\n              }\" modifier, valid properties are ${VALID_PROPERTIES.map(\n                (s) => `\"${s}\"`\n              ).join(', ')}; but \"${key}\" was provided.`\n            );\n        }\n\n        modifier.requires &&\n          modifier.requires.forEach((requirement) => {\n            if (modifiers.find((mod) => mod.name === requirement) == null) {\n              console.error(\n                format(\n                  MISSING_DEPENDENCY_ERROR,\n                  String(modifier.name),\n                  requirement,\n                  requirement\n                )\n              );\n            }\n          });\n      });\n  });\n}\n", "// @flow\n\nexport default function uniqueBy<T>(arr: Array<T>, fn: T => any): Array<T> {\n  const identifiers = new Set();\n\n  return arr.filter(item => {\n    const identifier = fn(item);\n\n    if (!identifiers.has(identifier)) {\n      identifiers.add(identifier);\n      return true;\n    }\n  });\n}\n", "// @flow\nimport { type BasePlacement, type Placement, auto } from '../enums';\n\nexport default function getBasePlacement(\n  placement: Placement | typeof auto\n): BasePlacement {\n  return (placement.split('-')[0]: any);\n}\n", "// @flow\nimport type { Modifier } from '../types';\n\nexport default function mergeByName(\n  modifiers: Array<$Shape<Modifier<any, any>>>\n): Array<$Shape<Modifier<any, any>>> {\n  const merged = modifiers.reduce((merged, current) => {\n    const existing = merged[current.name];\n    merged[current.name] = existing\n      ? {\n          ...existing,\n          ...current,\n          options: { ...existing.options, ...current.options },\n          data: { ...existing.data, ...current.data },\n        }\n      : current;\n    return merged;\n  }, {});\n\n  // IE11 does not support Object.values\n  return Object.keys(merged).map(key => merged[key]);\n}\n", "// @flow\nimport getWindow from './getWindow';\nimport getDocumentElement from './getDocumentElement';\nimport getWindowScrollBarX from './getWindowScrollBarX';\nimport isLayoutViewport from './isLayoutViewport';\nimport type { PositioningStrategy } from '../types';\n\nexport default function getViewportRect(\n  element: Element,\n  strategy: PositioningStrategy\n) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n\n    const layoutViewport = isLayoutViewport();\n\n    if (layoutViewport || (!layoutViewport && strategy === 'fixed')) {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width,\n    height,\n    x: x + getWindowScrollBarX(element),\n    y,\n  };\n}\n", "// @flow\nimport type { Rect } from '../types';\nimport getDocumentElement from './getDocumentElement';\nimport getComputedStyle from './getComputedStyle';\nimport getWindowScrollBarX from './getWindowScrollBarX';\nimport getWindowScroll from './getWindowScroll';\nimport { max } from '../utils/math';\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\nexport default function getDocumentRect(element: HTMLElement): Rect {\n  const html = getDocumentElement(element);\n  const winScroll = getWindowScroll(element);\n  const body = element.ownerDocument?.body;\n\n  const width = max(\n    html.scrollWidth,\n    html.clientWidth,\n    body ? body.scrollWidth : 0,\n    body ? body.clientWidth : 0\n  );\n  const height = max(\n    html.scrollHeight,\n    html.clientHeight,\n    body ? body.scrollHeight : 0,\n    body ? body.clientHeight : 0\n  );\n\n  let x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return { width, height, x, y };\n}\n", "// @flow\nimport { isShadowRoot } from './instanceOf';\n\nexport default function contains(parent: Element, child: Element) {\n  const rootNode = child.getRootNode && child.getRootNode();\n\n  // First, attempt with faster native method\n  if (parent.contains(child)) {\n    return true;\n  }\n  // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n    let next = child;\n    do {\n      if (next && parent.isSameNode(next)) {\n        return true;\n      }\n      // $FlowFixMe[prop-missing]: need a better way to handle this...\n      next = next.parentNode || next.host;\n    } while (next);\n  }\n\n  // Give up, the result is false\n  return false;\n}\n", "// @flow\nimport type { Rect, ClientRectObject } from '../types';\n\nexport default function rectToClientRect(rect: Rect): ClientRectObject {\n  return {\n    ...rect,\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height,\n  };\n}\n", "// @flow\nimport type { ClientRectObject, PositioningStrategy } from '../types';\nimport type { Boundary, RootBoundary } from '../enums';\nimport { viewport } from '../enums';\nimport getViewportRect from './getViewportRect';\nimport getDocumentRect from './getDocumentRect';\nimport listScrollParents from './listScrollParents';\nimport getOffsetParent from './getOffsetParent';\nimport getDocumentElement from './getDocumentElement';\nimport getComputedStyle from './getComputedStyle';\nimport { isElement, isHTMLElement } from './instanceOf';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getParentNode from './getParentNode';\nimport contains from './contains';\nimport getNodeName from './getNodeName';\nimport rectToClientRect from '../utils/rectToClientRect';\nimport { max, min } from '../utils/math';\n\nfunction getInnerBoundingClientRect(\n  element: Element,\n  strategy: PositioningStrategy\n) {\n  const rect = getBoundingClientRect(element, false, strategy === 'fixed');\n\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n\n  return rect;\n}\n\nfunction getClientRectFromMixedType(\n  element: Element,\n  clippingParent: Element | RootBoundary,\n  strategy: PositioningStrategy\n): ClientRectObject {\n  return clippingParent === viewport\n    ? rectToClientRect(getViewportRect(element, strategy))\n    : isElement(clippingParent)\n    ? getInnerBoundingClientRect(clippingParent, strategy)\n    : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n}\n\n// A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\nfunction getClippingParents(element: Element): Array<Element> {\n  const clippingParents = listScrollParents(getParentNode(element));\n  const canEscapeClipping =\n    ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  const clipperElement =\n    canEscapeClipping && isHTMLElement(element)\n      ? getOffsetParent(element)\n      : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  }\n\n  // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n  return clippingParents.filter(\n    (clippingParent) =>\n      isElement(clippingParent) &&\n      contains(clippingParent, clipperElement) &&\n      getNodeName(clippingParent) !== 'body'\n  );\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping parents\nexport default function getClippingRect(\n  element: Element,\n  boundary: Boundary,\n  rootBoundary: RootBoundary,\n  strategy: PositioningStrategy\n): ClientRectObject {\n  const mainClippingParents =\n    boundary === 'clippingParents'\n      ? getClippingParents(element)\n      : [].concat(boundary);\n  const clippingParents = [...mainClippingParents, rootBoundary];\n  const firstClippingParent = clippingParents[0];\n\n  const clippingRect = clippingParents.reduce((accRect, clippingParent) => {\n    const rect = getClientRectFromMixedType(element, clippingParent, strategy);\n\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n\n  return clippingRect;\n}\n", "// @flow\nimport { type Variation, type Placement } from '../enums';\n\nexport default function getVariation(placement: Placement): ?Variation {\n  return (placement.split('-')[1]: any);\n}\n", "// @flow\nimport type { Placement } from '../enums';\n\nexport default function getMainAxisFromPlacement(\n  placement: Placement\n): 'x' | 'y' {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}\n", "// @flow\nimport getBasePlacement from './getBasePlacement';\nimport getVariation from './getVariation';\nimport getMainAxisFromPlacement from './getMainAxisFromPlacement';\nimport type {\n  Rect,\n  PositioningStrategy,\n  Offsets,\n  ClientRectObject,\n} from '../types';\nimport { top, right, bottom, left, start, end, type Placement } from '../enums';\n\nexport default function computeOffsets({\n  reference,\n  element,\n  placement,\n}: {\n  reference: Rect | ClientRectObject,\n  element: Rect | ClientRectObject,\n  strategy: PositioningStrategy,\n  placement?: Placement,\n}): Offsets {\n  const basePlacement = placement ? getBasePlacement(placement) : null;\n  const variation = placement ? getVariation(placement) : null;\n  const commonX = reference.x + reference.width / 2 - element.width / 2;\n  const commonY = reference.y + reference.height / 2 - element.height / 2;\n\n  let offsets;\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height,\n      };\n      break;\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height,\n      };\n      break;\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY,\n      };\n      break;\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY,\n      };\n      break;\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y,\n      };\n  }\n\n  const mainAxis = basePlacement\n    ? getMainAxisFromPlacement(basePlacement)\n    : null;\n\n  if (mainAxis != null) {\n    const len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] =\n          offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n      case end:\n        offsets[mainAxis] =\n          offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n      default:\n    }\n  }\n\n  return offsets;\n}\n", "// @flow\nimport type { SideObject } from '../types';\n\nexport default function getFreshSideObject(): SideObject {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n  };\n}\n", "// @flow\nimport type { SideObject } from '../types';\nimport getFreshSideObject from './getFreshSideObject';\n\nexport default function mergePaddingObject(\n  paddingObject: $Shape<SideObject>\n): SideObject {\n  return {\n    ...getFreshSideObject(),\n    ...paddingObject,\n  };\n}\n", "// @flow\n\nexport default function expandToHashMap<\n  T: number | string | boolean,\n  K: string\n>(value: T, keys: Array<K>): { [key: string]: T } {\n  return keys.reduce((hashMap, key) => {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}\n", "// @flow\nimport type { State, SideObject, Padding, PositioningStrategy } from '../types';\nimport type { Placement, Boundary, RootBoundary, Context } from '../enums';\nimport getClippingRect from '../dom-utils/getClippingRect';\nimport getDocumentElement from '../dom-utils/getDocumentElement';\nimport getBoundingClientRect from '../dom-utils/getBoundingClientRect';\nimport computeOffsets from './computeOffsets';\nimport rectToClientRect from './rectToClientRect';\nimport {\n  clippingParents,\n  reference,\n  popper,\n  bottom,\n  top,\n  right,\n  basePlacements,\n  viewport,\n} from '../enums';\nimport { isElement } from '../dom-utils/instanceOf';\nimport mergePaddingObject from './mergePaddingObject';\nimport expandToHashMap from './expandToHashMap';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  placement: Placement,\n  strategy: PositioningStrategy,\n  boundary: Boundary,\n  rootBoundary: RootBoundary,\n  elementContext: Context,\n  altBoundary: boolean,\n  padding: Padding,\n};\n\nexport default function detectOverflow(\n  state: State,\n  options: $Shape<Options> = {}\n): SideObject {\n  const {\n    placement = state.placement,\n    strategy = state.strategy,\n    boundary = clippingParents,\n    rootBoundary = viewport,\n    elementContext = popper,\n    altBoundary = false,\n    padding = 0,\n  } = options;\n\n  const paddingObject = mergePaddingObject(\n    typeof padding !== 'number'\n      ? padding\n      : expandToHashMap(padding, basePlacements)\n  );\n\n  const altContext = elementContext === popper ? reference : popper;\n\n  const popperRect = state.rects.popper;\n  const element = state.elements[altBoundary ? altContext : elementContext];\n\n  const clippingClientRect = getClippingRect(\n    isElement(element)\n      ? element\n      : element.contextElement || getDocumentElement(state.elements.popper),\n    boundary,\n    rootBoundary,\n    strategy\n  );\n\n  const referenceClientRect = getBoundingClientRect(state.elements.reference);\n\n  const popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement,\n  });\n\n  const popperClientRect = rectToClientRect({\n    ...popperRect,\n    ...popperOffsets,\n  });\n\n  const elementClientRect =\n    elementContext === popper ? popperClientRect : referenceClientRect;\n\n  // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n  const overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom:\n      elementClientRect.bottom -\n      clippingClientRect.bottom +\n      paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right:\n      elementClientRect.right - clippingClientRect.right + paddingObject.right,\n  };\n\n  const offsetData = state.modifiersData.offset;\n\n  // Offsets can be applied only to the popper element\n  if (elementContext === popper && offsetData) {\n    const offset = offsetData[placement];\n\n    Object.keys(overflowOffsets).forEach((key) => {\n      const multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      const axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}\n", "// @flow\nimport type {\n  State,\n  OptionsGeneric,\n  Modifier,\n  Instance,\n  VirtualElement,\n} from './types';\nimport getCompositeRect from './dom-utils/getCompositeRect';\nimport getLayoutRect from './dom-utils/getLayoutRect';\nimport listScrollParents from './dom-utils/listScrollParents';\nimport getOffsetParent from './dom-utils/getOffsetParent';\nimport getComputedStyle from './dom-utils/getComputedStyle';\nimport orderModifiers from './utils/orderModifiers';\nimport debounce from './utils/debounce';\nimport validateModifiers from './utils/validateModifiers';\nimport uniqueBy from './utils/uniqueBy';\nimport getBasePlacement from './utils/getBasePlacement';\nimport mergeByName from './utils/mergeByName';\nimport detectOverflow from './utils/detectOverflow';\nimport { isElement } from './dom-utils/instanceOf';\nimport { auto } from './enums';\n\nconst INVALID_ELEMENT_ERROR =\n  'Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.';\nconst INFINITE_LOOP_ERROR =\n  'Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.';\n\nconst DEFAULT_OPTIONS: OptionsGeneric<any> = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute',\n};\n\ntype PopperGeneratorArgs = {\n  defaultModifiers?: Array<Modifier<any, any>>,\n  defaultOptions?: $Shape<OptionsGeneric<any>>,\n};\n\nfunction areValidElements(...args: Array<any>): boolean {\n  return !args.some(\n    (element) =>\n      !(element && typeof element.getBoundingClientRect === 'function')\n  );\n}\n\nexport function popperGenerator(generatorOptions: PopperGeneratorArgs = {}) {\n  const {\n    defaultModifiers = [],\n    defaultOptions = DEFAULT_OPTIONS,\n  } = generatorOptions;\n\n  return function createPopper<TModifier: $Shape<Modifier<any, any>>>(\n    reference: Element | VirtualElement,\n    popper: HTMLElement,\n    options: $Shape<OptionsGeneric<TModifier>> = defaultOptions\n  ): Instance {\n    let state: $Shape<State> = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: { ...DEFAULT_OPTIONS, ...defaultOptions },\n      modifiersData: {},\n      elements: {\n        reference,\n        popper,\n      },\n      attributes: {},\n      styles: {},\n    };\n\n    let effectCleanupFns: Array<() => void> = [];\n    let isDestroyed = false;\n\n    const instance = {\n      state,\n      setOptions(setOptionsAction) {\n        const options =\n          typeof setOptionsAction === 'function'\n            ? setOptionsAction(state.options)\n            : setOptionsAction;\n\n        cleanupModifierEffects();\n\n        state.options = {\n          // $FlowFixMe[exponential-spread]\n          ...defaultOptions,\n          ...state.options,\n          ...options,\n        };\n\n        state.scrollParents = {\n          reference: isElement(reference)\n            ? listScrollParents(reference)\n            : reference.contextElement\n            ? listScrollParents(reference.contextElement)\n            : [],\n          popper: listScrollParents(popper),\n        };\n\n        // Orders the modifiers based on their dependencies and `phase`\n        // properties\n        const orderedModifiers = orderModifiers(\n          mergeByName([...defaultModifiers, ...state.options.modifiers])\n        );\n\n        // Strip out disabled modifiers\n        state.orderedModifiers = orderedModifiers.filter((m) => m.enabled);\n\n        // Validate the provided modifiers so that the consumer will get warned\n        // if one of the modifiers is invalid for any reason\n        if (__DEV__) {\n          const modifiers = uniqueBy(\n            [...orderedModifiers, ...state.options.modifiers],\n            ({ name }) => name\n          );\n\n          validateModifiers(modifiers);\n\n          if (getBasePlacement(state.options.placement) === auto) {\n            const flipModifier = state.orderedModifiers.find(\n              ({ name }) => name === 'flip'\n            );\n\n            if (!flipModifier) {\n              console.error(\n                [\n                  'Popper: \"auto\" placements require the \"flip\" modifier be',\n                  'present and enabled to work.',\n                ].join(' ')\n              );\n            }\n          }\n\n          const {\n            marginTop,\n            marginRight,\n            marginBottom,\n            marginLeft,\n          } = getComputedStyle(popper);\n\n          // We no longer take into account `margins` on the popper, and it can\n          // cause bugs with positioning, so we'll warn the consumer\n          if (\n            [marginTop, marginRight, marginBottom, marginLeft].some((margin) =>\n              parseFloat(margin)\n            )\n          ) {\n            console.warn(\n              [\n                'Popper: CSS \"margin\" styles cannot be used to apply padding',\n                'between the popper and its reference element or boundary.',\n                'To replicate margin, use the `offset` modifier, as well as',\n                'the `padding` option in the `preventOverflow` and `flip`',\n                'modifiers.',\n              ].join(' ')\n            );\n          }\n        }\n\n        runModifierEffects();\n\n        return instance.update();\n      },\n\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        const { reference, popper } = state.elements;\n\n        // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n        if (!areValidElements(reference, popper)) {\n          if (__DEV__) {\n            console.error(INVALID_ELEMENT_ERROR);\n          }\n          return;\n        }\n\n        // Store the reference and popper rects to be read by modifiers\n        state.rects = {\n          reference: getCompositeRect(\n            reference,\n            getOffsetParent(popper),\n            state.options.strategy === 'fixed'\n          ),\n          popper: getLayoutRect(popper),\n        };\n\n        // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n        state.reset = false;\n\n        state.placement = state.options.placement;\n\n        // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n        state.orderedModifiers.forEach(\n          (modifier) =>\n            (state.modifiersData[modifier.name] = {\n              ...modifier.data,\n            })\n        );\n\n        let __debug_loops__ = 0;\n        for (let index = 0; index < state.orderedModifiers.length; index++) {\n          if (__DEV__) {\n            __debug_loops__ += 1;\n            if (__debug_loops__ > 100) {\n              console.error(INFINITE_LOOP_ERROR);\n              break;\n            }\n          }\n\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          const { fn, options = {}, name } = state.orderedModifiers[index];\n\n          if (typeof fn === 'function') {\n            state = fn({ state, options, name, instance }) || state;\n          }\n        }\n      },\n\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce<$Shape<State>>(\n        () =>\n          new Promise<$Shape<State>>((resolve) => {\n            instance.forceUpdate();\n            resolve(state);\n          })\n      ),\n\n      destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      },\n    };\n\n    if (!areValidElements(reference, popper)) {\n      if (__DEV__) {\n        console.error(INVALID_ELEMENT_ERROR);\n      }\n      return instance;\n    }\n\n    instance.setOptions(options).then((state) => {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    });\n\n    // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(({ name, options = {}, effect }) => {\n        if (typeof effect === 'function') {\n          const cleanupFn = effect({ state, name, instance, options });\n          const noopFn = () => {};\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach((fn) => fn());\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\n\nexport const createPopper = popperGenerator();\n\n// eslint-disable-next-line import/no-unused-modules\nexport { detectOverflow };\n", "// @flow\nimport type { ModifierArguments, Modifier } from '../types';\nimport getWindow from '../dom-utils/getWindow';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  scroll: boolean,\n  resize: boolean,\n};\n\nconst passive = { passive: true };\n\nfunction effect({ state, instance, options }: ModifierArguments<Options>) {\n  const { scroll = true, resize = true } = options;\n\n  const window = getWindow(state.elements.popper);\n  const scrollParents = [\n    ...state.scrollParents.reference,\n    ...state.scrollParents.popper,\n  ];\n\n  if (scroll) {\n    scrollParents.forEach(scrollParent => {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return () => {\n    if (scroll) {\n      scrollParents.forEach(scrollParent => {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type EventListenersModifier = Modifier<'eventListeners', Options>;\nexport default ({\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: () => {},\n  effect,\n  data: {},\n}: EventListenersModifier);\n", "// @flow\nimport type { ModifierArguments, Modifier } from '../types';\nimport computeOffsets from '../utils/computeOffsets';\n\nfunction popperOffsets({ state, name }: ModifierArguments<{||}>) {\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement,\n  });\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type PopperOffsetsModifier = Modifier<'popperOffsets', {||}>;\nexport default ({\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {},\n}: PopperOffsetsModifier);\n", "// @flow\nimport type {\n  PositioningStrategy,\n  Offsets,\n  Modifier,\n  ModifierArguments,\n  Rect,\n  Window,\n} from '../types';\nimport {\n  type BasePlacement,\n  type Variation,\n  top,\n  left,\n  right,\n  bottom,\n  end,\n} from '../enums';\nimport getOffsetParent from '../dom-utils/getOffsetParent';\nimport getWindow from '../dom-utils/getWindow';\nimport getDocumentElement from '../dom-utils/getDocumentElement';\nimport getComputedStyle from '../dom-utils/getComputedStyle';\nimport getBasePlacement from '../utils/getBasePlacement';\nimport getVariation from '../utils/getVariation';\nimport { round } from '../utils/math';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type RoundOffsets = (\n  offsets: $Shape<{ x: number, y: number, centerOffset: number }>\n) => Offsets;\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  gpuAcceleration: boolean,\n  adaptive: boolean,\n  roundOffsets?: boolean | RoundOffsets,\n};\n\nconst unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto',\n};\n\n// Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\nfunction roundOffsetsByDPR({ x, y }, win: Window): Offsets {\n  const dpr = win.devicePixelRatio || 1;\n\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0,\n  };\n}\n\nexport function mapToStyles({\n  popper,\n  popperRect,\n  placement,\n  variation,\n  offsets,\n  position,\n  gpuAcceleration,\n  adaptive,\n  roundOffsets,\n  isFixed,\n}: {\n  popper: HTMLElement,\n  popperRect: Rect,\n  placement: BasePlacement,\n  variation: ?Variation,\n  offsets: $Shape<{ x: number, y: number, centerOffset: number }>,\n  position: PositioningStrategy,\n  gpuAcceleration: boolean,\n  adaptive: boolean,\n  roundOffsets: boolean | RoundOffsets,\n  isFixed: boolean,\n}) {\n  let { x = 0, y = 0 } = offsets;\n\n  ({ x, y } =\n    typeof roundOffsets === 'function'\n      ? roundOffsets({ x, y })\n      : { x, y });\n\n  const hasX = offsets.hasOwnProperty('x');\n  const hasY = offsets.hasOwnProperty('y');\n\n  let sideX: string = left;\n  let sideY: string = top;\n\n  const win: Window = window;\n\n  if (adaptive) {\n    let offsetParent = getOffsetParent(popper);\n    let heightProp = 'clientHeight';\n    let widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (\n        getComputedStyle(offsetParent).position !== 'static' &&\n        position === 'absolute'\n      ) {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    }\n\n    // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n    offsetParent = (offsetParent: Element);\n\n    if (\n      placement === top ||\n      ((placement === left || placement === right) && variation === end)\n    ) {\n      sideY = bottom;\n      const offsetY =\n        isFixed && offsetParent === win && win.visualViewport\n          ? win.visualViewport.height\n          : // $FlowFixMe[prop-missing]\n            offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (\n      placement === left ||\n      ((placement === top || placement === bottom) && variation === end)\n    ) {\n      sideX = right;\n      const offsetX =\n        isFixed && offsetParent === win && win.visualViewport\n          ? win.visualViewport.width\n          : // $FlowFixMe[prop-missing]\n            offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  const commonStyles = {\n    position,\n    ...(adaptive && unsetSides),\n  };\n\n  ({ x, y } =\n    roundOffsets === true\n      ? roundOffsetsByDPR({ x, y }, getWindow(popper))\n      : { x, y });\n\n  if (gpuAcceleration) {\n    return {\n      ...commonStyles,\n      [sideY]: hasY ? '0' : '',\n      [sideX]: hasX ? '0' : '',\n      // Layer acceleration can disable subpixel rendering which causes slightly\n      // blurry text on low PPI displays, so we want to use 2D transforms\n      // instead\n      transform:\n        (win.devicePixelRatio || 1) <= 1\n          ? `translate(${x}px, ${y}px)`\n          : `translate3d(${x}px, ${y}px, 0)`,\n    };\n  }\n\n  return {\n    ...commonStyles,\n    [sideY]: hasY ? `${y}px` : '',\n    [sideX]: hasX ? `${x}px` : '',\n    transform: '',\n  };\n}\n\nfunction computeStyles({ state, options }: ModifierArguments<Options>) {\n  const {\n    gpuAcceleration = true,\n    adaptive = true,\n    // defaults to use builtin `roundOffsetsByDPR`\n    roundOffsets = true,\n  } = options;\n\n  if (__DEV__) {\n    const transitionProperty =\n      getComputedStyle(state.elements.popper).transitionProperty || '';\n\n    if (\n      adaptive &&\n      ['transform', 'top', 'right', 'bottom', 'left'].some(\n        (property) => transitionProperty.indexOf(property) >= 0\n      )\n    ) {\n      console.warn(\n        [\n          'Popper: Detected CSS transitions on at least one of the following',\n          'CSS properties: \"transform\", \"top\", \"right\", \"bottom\", \"left\".',\n          '\\n\\n',\n          'Disable the \"computeStyles\" modifier\\'s `adaptive` option to allow',\n          'for smooth transitions, or remove these properties from the CSS',\n          'transition declaration on the popper element if only transitioning',\n          'opacity or background-color for example.',\n          '\\n\\n',\n          'We recommend using the popper element as a wrapper around an inner',\n          'element that can have any CSS property transitioned for animations.',\n        ].join(' ')\n      );\n    }\n  }\n\n  const commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed',\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = {\n      ...state.styles.popper,\n      ...mapToStyles({\n        ...commonStyles,\n        offsets: state.modifiersData.popperOffsets,\n        position: state.options.strategy,\n        adaptive,\n        roundOffsets,\n      }),\n    };\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = {\n      ...state.styles.arrow,\n      ...mapToStyles({\n        ...commonStyles,\n        offsets: state.modifiersData.arrow,\n        position: 'absolute',\n        adaptive: false,\n        roundOffsets,\n      }),\n    };\n  }\n\n  state.attributes.popper = {\n    ...state.attributes.popper,\n    'data-popper-placement': state.placement,\n  };\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type ComputeStylesModifier = Modifier<'computeStyles', Options>;\nexport default ({\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {},\n}: ComputeStylesModifier);\n", "// @flow\nimport type { Modifier, ModifierArguments } from '../types';\nimport getNodeName from '../dom-utils/getNodeName';\nimport { isHTMLElement } from '../dom-utils/instanceOf';\n\n// This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles({ state }: ModifierArguments<{||}>) {\n  Object.keys(state.elements).forEach((name) => {\n    const style = state.styles[name] || {};\n\n    const attributes = state.attributes[name] || {};\n    const element = state.elements[name];\n\n    // arrow is optional + virtual elements\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    }\n\n    // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n    Object.assign(element.style, style);\n\n    Object.keys(attributes).forEach((name) => {\n      const value = attributes[name];\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect({ state }: ModifierArguments<{||}>) {\n  const initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0',\n    },\n    arrow: {\n      position: 'absolute',\n    },\n    reference: {},\n  };\n\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return () => {\n    Object.keys(state.elements).forEach((name) => {\n      const element = state.elements[name];\n      const attributes = state.attributes[name] || {};\n\n      const styleProperties = Object.keys(\n        state.styles.hasOwnProperty(name)\n          ? state.styles[name]\n          : initialStyles[name]\n      );\n\n      // Set all values to an empty string to unset them\n      const style = styleProperties.reduce((style, property) => {\n        style[property] = '';\n        return style;\n      }, {});\n\n      // arrow is optional + virtual elements\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n\n      Object.keys(attributes).forEach((attribute) => {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type ApplyStylesModifier = Modifier<'applyStyles', {||}>;\nexport default ({\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect,\n  requires: ['computeStyles'],\n}: ApplyStylesModifier);\n", "// @flow\nimport { popperGenerator, detectOverflow } from './createPopper';\n\nimport eventListeners from './modifiers/eventListeners';\nimport popperOffsets from './modifiers/popperOffsets';\nimport computeStyles from './modifiers/computeStyles';\nimport applyStyles from './modifiers/applyStyles';\n\nexport type * from './types';\n\nconst defaultModifiers = [\n  eventListeners,\n  popperOffsets,\n  computeStyles,\n  applyStyles,\n];\n\nconst createPopper = popperGenerator({ defaultModifiers });\n\n// eslint-disable-next-line import/no-unused-modules\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };\n"], "names": ["getWindow", "node", "window", "toString", "ownerDocument", "defaultView", "isElement", "OwnElement", "Element", "isHTMLElement", "HTMLElement", "isShadowRoot", "ShadowRoot", "max", "Math", "min", "round", "getUAString", "uaData", "navigator", "userAgentData", "brands", "Array", "isArray", "map", "item", "brand", "version", "join", "userAgent", "isLayoutViewport", "test", "getBoundingClientRect", "element", "includeScale", "isFixedStrategy", "clientRect", "scaleX", "scaleY", "offsetWidth", "width", "offsetHeight", "height", "visualViewport", "addVisualOffsets", "x", "left", "offsetLeft", "y", "top", "offsetTop", "right", "bottom", "getWindowScroll", "win", "scrollLeft", "pageXOffset", "scrollTop", "pageYOffset", "getHTMLElementScroll", "getNodeScroll", "getNodeName", "nodeName", "toLowerCase", "getDocumentElement", "document", "documentElement", "getWindowScrollBarX", "getComputedStyle", "isScrollParent", "overflow", "overflowX", "overflowY", "isElementScaled", "rect", "getCompositeRect", "elementOrVirtualElement", "offsetParent", "isFixed", "isOffsetParentAnElement", "offsetParentIsScaled", "scroll", "offsets", "clientLeft", "clientTop", "getLayoutRect", "abs", "getParentNode", "assignedSlot", "parentNode", "host", "getScrollParent", "indexOf", "body", "listScrollParents", "list", "scrollParent", "isBody", "target", "concat", "updatedList", "isTableElement", "getTrueOffsetParent", "position", "getContainingBlock", "isFirefox", "isIE", "elementCss", "currentNode", "css", "transform", "perspective", "contain", "<PERSON><PERSON><PERSON><PERSON>", "filter", "getOffsetParent", "auto", "basePlacements", "start", "end", "clippingParents", "viewport", "popper", "reference", "beforeRead", "read", "afterRead", "<PERSON><PERSON><PERSON>", "main", "<PERSON><PERSON><PERSON>", "beforeWrite", "write", "afterWrite", "modifierPhases", "order", "modifiers", "Map", "visited", "Set", "result", "for<PERSON>ach", "modifier", "set", "name", "sort", "add", "requires", "requiresIfExists", "dep", "has", "depModifier", "get", "push", "orderModifiers", "orderedModifiers", "reduce", "acc", "phase", "debounce", "fn", "pending", "Promise", "resolve", "then", "undefined", "format", "str", "args", "p", "c", "replace", "INVALID_MODIFIER_ERROR", "MISSING_DEPENDENCY_ERROR", "VALID_PROPERTIES", "validateModifiers", "Object", "keys", "value", "index", "self", "key", "console", "error", "String", "enabled", "effect", "s", "requirement", "find", "mod", "uniqueBy", "arr", "identifiers", "identifier", "getBasePlacement", "placement", "split", "mergeByName", "merged", "current", "existing", "options", "data", "getViewportRect", "strategy", "html", "clientWidth", "clientHeight", "layoutViewport", "getDocumentRect", "winScroll", "scrollWidth", "scrollHeight", "direction", "contains", "parent", "child", "rootNode", "getRootNode", "next", "isSameNode", "rectToClientRect", "getInnerBoundingClientRect", "getClientRectFromMixedType", "clippingParent", "getClippingParents", "canEscapeClipping", "clipperElement", "getClippingRect", "boundary", "rootBoundary", "mainClippingParents", "firstClippingParent", "clippingRect", "accRect", "getVariation", "getMainAxisFromPlacement", "computeOffsets", "basePlacement", "variation", "commonX", "commonY", "mainAxis", "len", "getFreshSideObject", "mergePaddingObject", "paddingObject", "expandToHashMap", "hashMap", "detectOverflow", "state", "elementContext", "altBoundary", "padding", "altContext", "popperRect", "rects", "elements", "clippingClientRect", "contextElement", "referenceClientRect", "popperOffsets", "popperClientRect", "elementClientRect", "overflowOffsets", "offsetData", "modifiersData", "offset", "multiply", "axis", "INVALID_ELEMENT_ERROR", "INFINITE_LOOP_ERROR", "DEFAULT_OPTIONS", "areValidElements", "some", "popperGenerator", "generatorOptions", "defaultModifiers", "defaultOptions", "createPopper", "attributes", "styles", "effectCleanupFns", "isDestroyed", "instance", "setOptions", "setOptionsAction", "cleanupModifierEffects", "scrollParents", "m", "flipModifier", "marginTop", "marginRight", "marginBottom", "marginLeft", "margin", "parseFloat", "warn", "runModifierEffects", "update", "forceUpdate", "reset", "__debug_loops__", "length", "destroy", "onFirstUpdate", "cleanupFn", "noopFn", "passive", "resize", "addEventListener", "removeEventListener", "unsetSides", "roundOffsetsByDPR", "dpr", "devicePixelRatio", "mapToStyles", "gpuAcceleration", "adaptive", "roundOffsets", "hasX", "hasOwnProperty", "hasY", "sideX", "sideY", "heightProp", "widthProp", "offsetY", "offsetX", "commonStyles", "computeStyles", "transitionProperty", "property", "arrow", "applyStyles", "style", "assign", "removeAttribute", "setAttribute", "initialStyles", "styleProperties", "attribute", "eventListeners"], "mappings": ";;;;;;;;AAIe,SAASA,SAAT,CAAmBC,IAAnB,EAAyB;AACtC,MAAIA,IAAI,IAAI,IAAZ,EAAkB;AAChB,WAAOC,MAAP;AACD;;AAED,MAAID,IAAI,CAACE,QAAL,OAAoB,iBAAxB,EAA2C;AACzC,QAAMC,aAAa,GAAGH,IAAI,CAACG,aAA3B;AACA,WAAOA,aAAa,GAAGA,aAAa,CAACC,WAAd,IAA6BH,MAAhC,GAAyCA,MAA7D;AACD;;AAED,SAAOD,IAAP;AACD;;ACVD,SAASK,SAAT,CAAmBL,IAAnB,EAAyB;AACvB,MAAMM,UAAU,GAAGP,SAAS,CAACC,IAAD,CAAT,CAAgBO,OAAnC;AACA,SAAOP,IAAI,YAAYM,UAAhB,IAA8BN,IAAI,YAAYO,OAArD;AACD;;AAID,SAASC,aAAT,CAAuBR,IAAvB,EAA6B;AAC3B,MAAMM,UAAU,GAAGP,SAAS,CAACC,IAAD,CAAT,CAAgBS,WAAnC;AACA,SAAOT,IAAI,YAAYM,UAAhB,IAA8BN,IAAI,YAAYS,WAArD;AACD;;AAID,SAASC,YAAT,CAAsBV,IAAtB,EAA4B;AAC1B;AACA,MAAI,OAAOW,UAAP,KAAsB,WAA1B,EAAuC;AACrC,WAAO,KAAP;AACD;;AACD,MAAML,UAAU,GAAGP,SAAS,CAACC,IAAD,CAAT,CAAgBW,UAAnC;AACA,SAAOX,IAAI,YAAYM,UAAhB,IAA8BN,IAAI,YAAYW,UAArD;AACD;;ACzBM,IAAMC,GAAG,GAAGC,IAAI,CAACD,GAAjB;AACA,IAAME,GAAG,GAAGD,IAAI,CAACC,GAAjB;AACA,IAAMC,KAAK,GAAGF,IAAI,CAACE,KAAnB;;ACMQ,SAASC,WAAT,GAA+B;AAC5C,MAAMC,MAAM,GAAIC,SAAD,CAAuBC,aAAtC;;AAEA,MAAIF,MAAM,QAAN,IAAAA,MAAM,CAAEG,MAAR,IAAkBC,KAAK,CAACC,OAAN,CAAcL,MAAM,CAACG,MAArB,CAAtB,EAAoD;AAClD,WAAOH,MAAM,CAACG,MAAP,CACJG,GADI,CACA,UAACC,IAAD;AAAA,aAAaA,IAAI,CAACC,KAAlB,SAA2BD,IAAI,CAACE,OAAhC;AAAA,KADA,EAEJC,IAFI,CAEC,GAFD,CAAP;AAGD;;AAED,SAAOT,SAAS,CAACU,SAAjB;AACD;;AChBc,SAASC,gBAAT,GAA4B;AACzC,SAAO,CAAC,iCAAiCC,IAAjC,CAAsCd,WAAW,EAAjD,CAAR;AACD;;ACEc,SAASe,qBAAT,CACbC,OADa,EAEbC,YAFa,EAGbC,eAHa,EAIK;AAAA,MAFlBD,YAEkB;AAFlBA,IAAAA,YAEkB,GAFM,KAEN;AAAA;;AAAA,MADlBC,eACkB;AADlBA,IAAAA,eACkB,GADS,KACT;AAAA;;AAClB,MAAMC,UAAU,GAAGH,OAAO,CAACD,qBAAR,EAAnB;AACA,MAAIK,MAAM,GAAG,CAAb;AACA,MAAIC,MAAM,GAAG,CAAb;;AAEA,MAAIJ,YAAY,IAAIzB,aAAa,CAACwB,OAAD,CAAjC,EAA4C;AAC1CI,IAAAA,MAAM,GACHJ,OAAD,CAAuBM,WAAvB,GAAqC,CAArC,GACIvB,KAAK,CAACoB,UAAU,CAACI,KAAZ,CAAL,GAA2BP,OAAD,CAAuBM,WAAjD,IAAgE,CADpE,GAEI,CAHN;AAIAD,IAAAA,MAAM,GACHL,OAAD,CAAuBQ,YAAvB,GAAsC,CAAtC,GACIzB,KAAK,CAACoB,UAAU,CAACM,MAAZ,CAAL,GAA4BT,OAAD,CAAuBQ,YAAlD,IAAkE,CADtE,GAEI,CAHN;AAID;;AAdiB,aAgBSnC,SAAS,CAAC2B,OAAD,CAAT,GAAqBjC,SAAS,CAACiC,OAAD,CAA9B,GAA0C/B,MAhBnD;AAAA,MAgBVyC,cAhBU,QAgBVA,cAhBU;;AAiBlB,MAAMC,gBAAgB,GAAG,CAACd,gBAAgB,EAAjB,IAAuBK,eAAhD;AAEA,MAAMU,CAAC,GACL,CAACT,UAAU,CAACU,IAAX,IACEF,gBAAgB,IAAID,cAApB,GAAqCA,cAAc,CAACI,UAApD,GAAiE,CADnE,CAAD,IAEAV,MAHF;AAIA,MAAMW,CAAC,GACL,CAACZ,UAAU,CAACa,GAAX,IACEL,gBAAgB,IAAID,cAApB,GAAqCA,cAAc,CAACO,SAApD,GAAgE,CADlE,CAAD,IAEAZ,MAHF;AAIA,MAAME,KAAK,GAAGJ,UAAU,CAACI,KAAX,GAAmBH,MAAjC;AACA,MAAMK,MAAM,GAAGN,UAAU,CAACM,MAAX,GAAoBJ,MAAnC;AAEA,SAAO;AACLE,IAAAA,KAAK,EAALA,KADK;AAELE,IAAAA,MAAM,EAANA,MAFK;AAGLO,IAAAA,GAAG,EAAED,CAHA;AAILG,IAAAA,KAAK,EAAEN,CAAC,GAAGL,KAJN;AAKLY,IAAAA,MAAM,EAAEJ,CAAC,GAAGN,MALP;AAMLI,IAAAA,IAAI,EAAED,CAND;AAOLA,IAAAA,CAAC,EAADA,CAPK;AAQLG,IAAAA,CAAC,EAADA;AARK,GAAP;AAUD;;AC/Cc,SAASK,eAAT,CAAyBpD,IAAzB,EAA8C;AAC3D,MAAMqD,GAAG,GAAGtD,SAAS,CAACC,IAAD,CAArB;AACA,MAAMsD,UAAU,GAAGD,GAAG,CAACE,WAAvB;AACA,MAAMC,SAAS,GAAGH,GAAG,CAACI,WAAtB;AAEA,SAAO;AACLH,IAAAA,UAAU,EAAVA,UADK;AAELE,IAAAA,SAAS,EAATA;AAFK,GAAP;AAID;;ACXc,SAASE,oBAAT,CAA8B1B,OAA9B,EAAoD;AACjE,SAAO;AACLsB,IAAAA,UAAU,EAAEtB,OAAO,CAACsB,UADf;AAELE,IAAAA,SAAS,EAAExB,OAAO,CAACwB;AAFd,GAAP;AAID;;ACAc,SAASG,aAAT,CAAuB3D,IAAvB,EAA4C;AACzD,MAAIA,IAAI,KAAKD,SAAS,CAACC,IAAD,CAAlB,IAA4B,CAACQ,aAAa,CAACR,IAAD,CAA9C,EAAsD;AACpD,WAAOoD,eAAe,CAACpD,IAAD,CAAtB;AACD,GAFD,MAEO;AACL,WAAO0D,oBAAoB,CAAC1D,IAAD,CAA3B;AACD;AACF;;ACVc,SAAS4D,WAAT,CAAqB5B,OAArB,EAAuD;AACpE,SAAOA,OAAO,GAAG,CAACA,OAAO,CAAC6B,QAAR,IAAoB,EAArB,EAAyBC,WAAzB,EAAH,GAA4C,IAA1D;AACD;;ACDc,SAASC,kBAAT,CACb/B,OADa,EAEA;AACb;AACA,SAAO,CACL,CAAC3B,SAAS,CAAC2B,OAAD,CAAT,GACGA,OAAO,CAAC7B,aADX;AAGG6B,EAAAA,OAAO,CAACgC,QAHZ,KAGyB/D,MAAM,CAAC+D,QAJ3B,EAKLC,eALF;AAMD;;ACTc,SAASC,mBAAT,CAA6BlC,OAA7B,EAAuD;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SACED,qBAAqB,CAACgC,kBAAkB,CAAC/B,OAAD,CAAnB,CAArB,CAAmDa,IAAnD,GACAO,eAAe,CAACpB,OAAD,CAAf,CAAyBsB,UAF3B;AAID;;ACdc,SAASa,gBAAT,CACbnC,OADa,EAEQ;AACrB,SAAOjC,SAAS,CAACiC,OAAD,CAAT,CAAmBmC,gBAAnB,CAAoCnC,OAApC,CAAP;AACD;;ACJc,SAASoC,cAAT,CAAwBpC,OAAxB,EAAuD;AACpE;AADoE,0BAEzBmC,gBAAgB,CAACnC,OAAD,CAFS;AAAA,MAE5DqC,QAF4D,qBAE5DA,QAF4D;AAAA,MAElDC,SAFkD,qBAElDA,SAFkD;AAAA,MAEvCC,SAFuC,qBAEvCA,SAFuC;;AAGpE,SAAO,6BAA6BzC,IAA7B,CAAkCuC,QAAQ,GAAGE,SAAX,GAAuBD,SAAzD,CAAP;AACD;;ACID,SAASE,eAAT,CAAyBxC,OAAzB,EAA+C;AAC7C,MAAMyC,IAAI,GAAGzC,OAAO,CAACD,qBAAR,EAAb;AACA,MAAMK,MAAM,GAAGrB,KAAK,CAAC0D,IAAI,CAAClC,KAAN,CAAL,GAAoBP,OAAO,CAACM,WAA5B,IAA2C,CAA1D;AACA,MAAMD,MAAM,GAAGtB,KAAK,CAAC0D,IAAI,CAAChC,MAAN,CAAL,GAAqBT,OAAO,CAACQ,YAA7B,IAA6C,CAA5D;AAEA,SAAOJ,MAAM,KAAK,CAAX,IAAgBC,MAAM,KAAK,CAAlC;AACD;AAGD;;;AACe,SAASqC,gBAAT,CACbC,uBADa,EAEbC,YAFa,EAGbC,OAHa,EAIP;AAAA,MADNA,OACM;AADNA,IAAAA,OACM,GADa,KACb;AAAA;;AACN,MAAMC,uBAAuB,GAAGtE,aAAa,CAACoE,YAAD,CAA7C;AACA,MAAMG,oBAAoB,GACxBvE,aAAa,CAACoE,YAAD,CAAb,IAA+BJ,eAAe,CAACI,YAAD,CADhD;AAEA,MAAMX,eAAe,GAAGF,kBAAkB,CAACa,YAAD,CAA1C;AACA,MAAMH,IAAI,GAAG1C,qBAAqB,CAChC4C,uBADgC,EAEhCI,oBAFgC,EAGhCF,OAHgC,CAAlC;AAMA,MAAIG,MAAM,GAAG;AAAE1B,IAAAA,UAAU,EAAE,CAAd;AAAiBE,IAAAA,SAAS,EAAE;AAA5B,GAAb;AACA,MAAIyB,OAAO,GAAG;AAAErC,IAAAA,CAAC,EAAE,CAAL;AAAQG,IAAAA,CAAC,EAAE;AAAX,GAAd;;AAEA,MAAI+B,uBAAuB,IAAK,CAACA,uBAAD,IAA4B,CAACD,OAA7D,EAAuE;AACrE,QACEjB,WAAW,CAACgB,YAAD,CAAX,KAA8B,MAA9B;AAEAR,IAAAA,cAAc,CAACH,eAAD,CAHhB,EAIE;AACAe,MAAAA,MAAM,GAAGrB,aAAa,CAACiB,YAAD,CAAtB;AACD;;AAED,QAAIpE,aAAa,CAACoE,YAAD,CAAjB,EAAiC;AAC/BK,MAAAA,OAAO,GAAGlD,qBAAqB,CAAC6C,YAAD,EAAe,IAAf,CAA/B;AACAK,MAAAA,OAAO,CAACrC,CAAR,IAAagC,YAAY,CAACM,UAA1B;AACAD,MAAAA,OAAO,CAAClC,CAAR,IAAa6B,YAAY,CAACO,SAA1B;AACD,KAJD,MAIO,IAAIlB,eAAJ,EAAqB;AAC1BgB,MAAAA,OAAO,CAACrC,CAAR,GAAYsB,mBAAmB,CAACD,eAAD,CAA/B;AACD;AACF;;AAED,SAAO;AACLrB,IAAAA,CAAC,EAAE6B,IAAI,CAAC5B,IAAL,GAAYmC,MAAM,CAAC1B,UAAnB,GAAgC2B,OAAO,CAACrC,CADtC;AAELG,IAAAA,CAAC,EAAE0B,IAAI,CAACzB,GAAL,GAAWgC,MAAM,CAACxB,SAAlB,GAA8ByB,OAAO,CAAClC,CAFpC;AAGLR,IAAAA,KAAK,EAAEkC,IAAI,CAAClC,KAHP;AAILE,IAAAA,MAAM,EAAEgC,IAAI,CAAChC;AAJR,GAAP;AAMD;;AC1DD;;AACe,SAAS2C,aAAT,CAAuBpD,OAAvB,EAAmD;AAChE,MAAMG,UAAU,GAAGJ,qBAAqB,CAACC,OAAD,CAAxC,CADgE;AAIhE;;AACA,MAAIO,KAAK,GAAGP,OAAO,CAACM,WAApB;AACA,MAAIG,MAAM,GAAGT,OAAO,CAACQ,YAArB;;AAEA,MAAI3B,IAAI,CAACwE,GAAL,CAASlD,UAAU,CAACI,KAAX,GAAmBA,KAA5B,KAAsC,CAA1C,EAA6C;AAC3CA,IAAAA,KAAK,GAAGJ,UAAU,CAACI,KAAnB;AACD;;AAED,MAAI1B,IAAI,CAACwE,GAAL,CAASlD,UAAU,CAACM,MAAX,GAAoBA,MAA7B,KAAwC,CAA5C,EAA+C;AAC7CA,IAAAA,MAAM,GAAGN,UAAU,CAACM,MAApB;AACD;;AAED,SAAO;AACLG,IAAAA,CAAC,EAAEZ,OAAO,CAACc,UADN;AAELC,IAAAA,CAAC,EAAEf,OAAO,CAACiB,SAFN;AAGLV,IAAAA,KAAK,EAALA,KAHK;AAILE,IAAAA,MAAM,EAANA;AAJK,GAAP;AAMD;;ACvBc,SAAS6C,aAAT,CAAuBtD,OAAvB,EAAyD;AACtE,MAAI4B,WAAW,CAAC5B,OAAD,CAAX,KAAyB,MAA7B,EAAqC;AACnC,WAAOA,OAAP;AACD;;AAED;AAEE;AACA;AACAA,IAAAA,OAAO,CAACuD,YAAR;AACAvD,IAAAA,OAAO,CAACwD,UADR;AAEC9E,IAAAA,YAAY,CAACsB,OAAD,CAAZ,GAAwBA,OAAO,CAACyD,IAAhC,GAAuC,IAFxC;AAGA;AACA1B,IAAAA,kBAAkB,CAAC/B,OAAD,CARpB;;AAAA;AAUD;;ACdc,SAAS0D,eAAT,CAAyB1F,IAAzB,EAAkD;AAC/D,MAAI,CAAC,MAAD,EAAS,MAAT,EAAiB,WAAjB,EAA8B2F,OAA9B,CAAsC/B,WAAW,CAAC5D,IAAD,CAAjD,KAA4D,CAAhE,EAAmE;AACjE;AACA,WAAOA,IAAI,CAACG,aAAL,CAAmByF,IAA1B;AACD;;AAED,MAAIpF,aAAa,CAACR,IAAD,CAAb,IAAuBoE,cAAc,CAACpE,IAAD,CAAzC,EAAiD;AAC/C,WAAOA,IAAP;AACD;;AAED,SAAO0F,eAAe,CAACJ,aAAa,CAACtF,IAAD,CAAd,CAAtB;AACD;;ACVD;AACA;AACA;AACA;AACA;AACA;;AACe,SAAS6F,iBAAT,CACb7D,OADa,EAEb8D,IAFa,EAG6B;AAAA;;AAAA,MAD1CA,IAC0C;AAD1CA,IAAAA,IAC0C,GADV,EACU;AAAA;;AAC1C,MAAMC,YAAY,GAAGL,eAAe,CAAC1D,OAAD,CAApC;AACA,MAAMgE,MAAM,GAAGD,YAAY,+BAAK/D,OAAO,CAAC7B,aAAb,qBAAK,sBAAuByF,IAA5B,CAA3B;AACA,MAAMvC,GAAG,GAAGtD,SAAS,CAACgG,YAAD,CAArB;AACA,MAAME,MAAM,GAAGD,MAAM,GACjB,CAAC3C,GAAD,EAAM6C,MAAN,CACE7C,GAAG,CAACX,cAAJ,IAAsB,EADxB,EAEE0B,cAAc,CAAC2B,YAAD,CAAd,GAA+BA,YAA/B,GAA8C,EAFhD,CADiB,GAKjBA,YALJ;AAMA,MAAMI,WAAW,GAAGL,IAAI,CAACI,MAAL,CAAYD,MAAZ,CAApB;AAEA,SAAOD,MAAM,GACTG,WADS;AAGTA,EAAAA,WAAW,CAACD,MAAZ,CAAmBL,iBAAiB,CAACP,aAAa,CAACW,MAAD,CAAd,CAApC,CAHJ;AAID;;AC7Bc,SAASG,cAAT,CAAwBpE,OAAxB,EAAmD;AAChE,SAAO,CAAC,OAAD,EAAU,IAAV,EAAgB,IAAhB,EAAsB2D,OAAtB,CAA8B/B,WAAW,CAAC5B,OAAD,CAAzC,KAAuD,CAA9D;AACD;;ACID,SAASqE,mBAAT,CAA6BrE,OAA7B,EAAyD;AACvD,MACE,CAACxB,aAAa,CAACwB,OAAD,CAAd;AAEAmC,EAAAA,gBAAgB,CAACnC,OAAD,CAAhB,CAA0BsE,QAA1B,KAAuC,OAHzC,EAIE;AACA,WAAO,IAAP;AACD;;AAED,SAAOtE,OAAO,CAAC4C,YAAf;AACD;AAGD;;;AACA,SAAS2B,kBAAT,CAA4BvE,OAA5B,EAA8C;AAC5C,MAAMwE,SAAS,GAAG,WAAW1E,IAAX,CAAgBd,WAAW,EAA3B,CAAlB;AACA,MAAMyF,IAAI,GAAG,WAAW3E,IAAX,CAAgBd,WAAW,EAA3B,CAAb;;AAEA,MAAIyF,IAAI,IAAIjG,aAAa,CAACwB,OAAD,CAAzB,EAAoC;AAClC;AACA,QAAM0E,UAAU,GAAGvC,gBAAgB,CAACnC,OAAD,CAAnC;;AACA,QAAI0E,UAAU,CAACJ,QAAX,KAAwB,OAA5B,EAAqC;AACnC,aAAO,IAAP;AACD;AACF;;AAED,MAAIK,WAAW,GAAGrB,aAAa,CAACtD,OAAD,CAA/B;;AAEA,MAAItB,YAAY,CAACiG,WAAD,CAAhB,EAA+B;AAC7BA,IAAAA,WAAW,GAAGA,WAAW,CAAClB,IAA1B;AACD;;AAED,SACEjF,aAAa,CAACmG,WAAD,CAAb,IACA,CAAC,MAAD,EAAS,MAAT,EAAiBhB,OAAjB,CAAyB/B,WAAW,CAAC+C,WAAD,CAApC,IAAqD,CAFvD,EAGE;AACA,QAAMC,GAAG,GAAGzC,gBAAgB,CAACwC,WAAD,CAA5B,CADA;AAIA;AACA;;AACA,QACEC,GAAG,CAACC,SAAJ,KAAkB,MAAlB,IACAD,GAAG,CAACE,WAAJ,KAAoB,MADpB,IAEAF,GAAG,CAACG,OAAJ,KAAgB,OAFhB,IAGA,CAAC,WAAD,EAAc,aAAd,EAA6BpB,OAA7B,CAAqCiB,GAAG,CAACI,UAAzC,MAAyD,CAAC,CAH1D,IAICR,SAAS,IAAII,GAAG,CAACI,UAAJ,KAAmB,QAJjC,IAKCR,SAAS,IAAII,GAAG,CAACK,MAAjB,IAA2BL,GAAG,CAACK,MAAJ,KAAe,MAN7C,EAOE;AACA,aAAON,WAAP;AACD,KATD,MASO;AACLA,MAAAA,WAAW,GAAGA,WAAW,CAACnB,UAA1B;AACD;AACF;;AAED,SAAO,IAAP;AACD;AAGD;;;AACe,SAAS0B,eAAT,CAAyBlF,OAAzB,EAA2C;AACxD,MAAM/B,MAAM,GAAGF,SAAS,CAACiC,OAAD,CAAxB;AAEA,MAAI4C,YAAY,GAAGyB,mBAAmB,CAACrE,OAAD,CAAtC;;AAEA,SACE4C,YAAY,IACZwB,cAAc,CAACxB,YAAD,CADd,IAEAT,gBAAgB,CAACS,YAAD,CAAhB,CAA+B0B,QAA/B,KAA4C,QAH9C,EAIE;AACA1B,IAAAA,YAAY,GAAGyB,mBAAmB,CAACzB,YAAD,CAAlC;AACD;;AAED,MACEA,YAAY,KACXhB,WAAW,CAACgB,YAAD,CAAX,KAA8B,MAA9B,IACEhB,WAAW,CAACgB,YAAD,CAAX,KAA8B,MAA9B,IACCT,gBAAgB,CAACS,YAAD,CAAhB,CAA+B0B,QAA/B,KAA4C,QAHpC,CADd,EAKE;AACA,WAAOrG,MAAP;AACD;;AAED,SAAO2E,YAAY,IAAI2B,kBAAkB,CAACvE,OAAD,CAAlC,IAA+C/B,MAAtD;AACD;;AC3FM,IAAM+C,GAAU,GAAG,KAAnB;AACA,IAAMG,MAAgB,GAAG,QAAzB;AACA,IAAMD,KAAc,GAAG,OAAvB;AACA,IAAML,IAAY,GAAG,MAArB;AACA,IAAMsE,IAAY,GAAG,MAArB;AAMA,IAAMC,cAAoC,GAAG,CAACpE,GAAD,EAAMG,MAAN,EAAcD,KAAd,EAAqBL,IAArB,CAA7C;AAEA,IAAMwE,KAAc,GAAG,OAAvB;AACA,IAAMC,GAAU,GAAG,KAAnB;AAGA,IAAMC,eAAkC,GAAG,iBAA3C;AACA,IAAMC,QAAoB,GAAG,UAA7B;AAIA,IAAMC,MAAgB,GAAG,QAAzB;AACA,IAAMC,SAAsB,GAAG,WAA/B;;AAmCA,IAAMC,UAAwB,GAAG,YAAjC;AACA,IAAMC,IAAY,GAAG,MAArB;AACA,IAAMC,SAAsB,GAAG,WAA/B;;AAEA,IAAMC,UAAwB,GAAG,YAAjC;AACA,IAAMC,IAAY,GAAG,MAArB;AACA,IAAMC,SAAsB,GAAG,WAA/B;;AAEA,IAAMC,WAA0B,GAAG,aAAnC;AACA,IAAMC,KAAc,GAAG,OAAvB;AACA,IAAMC,UAAwB,GAAG,YAAjC;AACA,IAAMC,cAAqC,GAAG,CACnDT,UADmD,EAEnDC,IAFmD,EAGnDC,SAHmD,EAInDC,UAJmD,EAKnDC,IALmD,EAMnDC,SANmD,EAOnDC,WAPmD,EAQnDC,KARmD,EASnDC,UATmD,CAA9C;;AChEP,SAASE,KAAT,CAAeC,SAAf,EAA0B;AACxB,MAAM/G,GAAG,GAAG,IAAIgH,GAAJ,EAAZ;AACA,MAAMC,OAAO,GAAG,IAAIC,GAAJ,EAAhB;AACA,MAAMC,MAAM,GAAG,EAAf;AAEAJ,EAAAA,SAAS,CAACK,OAAV,CAAkB,UAAAC,QAAQ,EAAI;AAC5BrH,IAAAA,GAAG,CAACsH,GAAJ,CAAQD,QAAQ,CAACE,IAAjB,EAAuBF,QAAvB;AACD,GAFD,EALwB;;AAUxB,WAASG,IAAT,CAAcH,QAAd,EAA4C;AAC1CJ,IAAAA,OAAO,CAACQ,GAAR,CAAYJ,QAAQ,CAACE,IAArB;AAEA,QAAMG,QAAQ,aACRL,QAAQ,CAACK,QAAT,IAAqB,EADb,EAERL,QAAQ,CAACM,gBAAT,IAA6B,EAFrB,CAAd;AAKAD,IAAAA,QAAQ,CAACN,OAAT,CAAiB,UAAAQ,GAAG,EAAI;AACtB,UAAI,CAACX,OAAO,CAACY,GAAR,CAAYD,GAAZ,CAAL,EAAuB;AACrB,YAAME,WAAW,GAAG9H,GAAG,CAAC+H,GAAJ,CAAQH,GAAR,CAApB;;AAEA,YAAIE,WAAJ,EAAiB;AACfN,UAAAA,IAAI,CAACM,WAAD,CAAJ;AACD;AACF;AACF,KARD;AAUAX,IAAAA,MAAM,CAACa,IAAP,CAAYX,QAAZ;AACD;;AAEDN,EAAAA,SAAS,CAACK,OAAV,CAAkB,UAAAC,QAAQ,EAAI;AAC5B,QAAI,CAACJ,OAAO,CAACY,GAAR,CAAYR,QAAQ,CAACE,IAArB,CAAL,EAAiC;AAC/B;AACAC,MAAAA,IAAI,CAACH,QAAD,CAAJ;AACD;AACF,GALD;AAOA,SAAOF,MAAP;AACD;;AAEc,SAASc,cAAT,CACblB,SADa,EAEc;AAC3B;AACA,MAAMmB,gBAAgB,GAAGpB,KAAK,CAACC,SAAD,CAA9B,CAF2B;;AAK3B,SAAOF,cAAc,CAACsB,MAAf,CAAsB,UAACC,GAAD,EAAMC,KAAN,EAAgB;AAC3C,WAAOD,GAAG,CAACzD,MAAJ,CACLuD,gBAAgB,CAACxC,MAAjB,CAAwB,UAAA2B,QAAQ;AAAA,aAAIA,QAAQ,CAACgB,KAAT,KAAmBA,KAAvB;AAAA,KAAhC,CADK,CAAP;AAGD,GAJM,EAIJ,EAJI,CAAP;AAKD;;ACxDc,SAASC,QAAT,CAAqBC,EAArB,EAAqD;AAClE,MAAIC,OAAJ;AACA,SAAO,YAAM;AACX,QAAI,CAACA,OAAL,EAAc;AACZA,MAAAA,OAAO,GAAG,IAAIC,OAAJ,CAAe,UAAAC,OAAO,EAAI;AAClCD,QAAAA,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,YAAM;AAC3BH,UAAAA,OAAO,GAAGI,SAAV;AACAF,UAAAA,OAAO,CAACH,EAAE,EAAH,CAAP;AACD,SAHD;AAID,OALS,CAAV;AAMD;;AAED,WAAOC,OAAP;AACD,GAXD;AAYD;;ACdc,SAASK,MAAT,CAAgBC,GAAhB,EAAqD;AAAA,oCAArBC,IAAqB;AAArBA,IAAAA,IAAqB;AAAA;;AAClE,SAAO,UAAIA,IAAJ,EAAUZ,MAAV,CAAiB,UAACa,CAAD,EAAIC,CAAJ;AAAA,WAAUD,CAAC,CAACE,OAAF,CAAU,IAAV,EAAgBD,CAAhB,CAAV;AAAA,GAAjB,EAA+CH,GAA/C,CAAP;AACD;;ACAD,IAAMK,sBAAsB,GAC1B,+EADF;AAEA,IAAMC,wBAAwB,GAC5B,yEADF;AAEA,IAAMC,gBAAgB,GAAG,CACvB,MADuB,EAEvB,SAFuB,EAGvB,OAHuB,EAIvB,IAJuB,EAKvB,QALuB,EAMvB,UANuB,EAOvB,SAPuB,CAAzB;AAUe,SAASC,iBAAT,CAA2BvC,SAA3B,EAAwD;AACrEA,EAAAA,SAAS,CAACK,OAAV,CAAkB,UAACC,QAAD,EAAc;AAC9B,cAAIkC,MAAM,CAACC,IAAP,CAAYnC,QAAZ,CAAJ,EAA8BgC,gBAA9B;AAAA,KAEG3D,MAFH,CAEU,UAAC+D,KAAD,EAAQC,KAAR,EAAeC,IAAf;AAAA,aAAwBA,IAAI,CAACvF,OAAL,CAAaqF,KAAb,MAAwBC,KAAhD;AAAA,KAFV,EAGGtC,OAHH,CAGW,UAACwC,GAAD,EAAS;AAChB,cAAQA,GAAR;AACE,aAAK,MAAL;AACE,cAAI,OAAOvC,QAAQ,CAACE,IAAhB,KAAyB,QAA7B,EAAuC;AACrCsC,YAAAA,OAAO,CAACC,KAAR,CACEjB,MAAM,CACJM,sBADI,EAEJY,MAAM,CAAC1C,QAAQ,CAACE,IAAV,CAFF,EAGJ,QAHI,EAIJ,UAJI,SAKAwC,MAAM,CAAC1C,QAAQ,CAACE,IAAV,CALN,QADR;AASD;;AACD;;AACF,aAAK,SAAL;AACE,cAAI,OAAOF,QAAQ,CAAC2C,OAAhB,KAA4B,SAAhC,EAA2C;AACzCH,YAAAA,OAAO,CAACC,KAAR,CACEjB,MAAM,CACJM,sBADI,EAEJ9B,QAAQ,CAACE,IAFL,EAGJ,WAHI,EAIJ,WAJI,SAKAwC,MAAM,CAAC1C,QAAQ,CAAC2C,OAAV,CALN,QADR;AASD;;AACD;;AACF,aAAK,OAAL;AACE,cAAInD,cAAc,CAACzC,OAAf,CAAuBiD,QAAQ,CAACgB,KAAhC,IAAyC,CAA7C,EAAgD;AAC9CwB,YAAAA,OAAO,CAACC,KAAR,CACEjB,MAAM,CACJM,sBADI,EAEJ9B,QAAQ,CAACE,IAFL,EAGJ,SAHI,cAIMV,cAAc,CAACzG,IAAf,CAAoB,IAApB,CAJN,SAKA2J,MAAM,CAAC1C,QAAQ,CAACgB,KAAV,CALN,QADR;AASD;;AACD;;AACF,aAAK,IAAL;AACE,cAAI,OAAOhB,QAAQ,CAACkB,EAAhB,KAAuB,UAA3B,EAAuC;AACrCsB,YAAAA,OAAO,CAACC,KAAR,CACEjB,MAAM,CACJM,sBADI,EAEJ9B,QAAQ,CAACE,IAFL,EAGJ,MAHI,EAIJ,YAJI,SAKAwC,MAAM,CAAC1C,QAAQ,CAACkB,EAAV,CALN,QADR;AASD;;AACD;;AACF,aAAK,QAAL;AACE,cACElB,QAAQ,CAAC4C,MAAT,IAAmB,IAAnB,IACA,OAAO5C,QAAQ,CAAC4C,MAAhB,KAA2B,UAF7B,EAGE;AACAJ,YAAAA,OAAO,CAACC,KAAR,CACEjB,MAAM,CACJM,sBADI,EAEJ9B,QAAQ,CAACE,IAFL,EAGJ,UAHI,EAIJ,YAJI,SAKAwC,MAAM,CAAC1C,QAAQ,CAACkB,EAAV,CALN,QADR;AASD;;AACD;;AACF,aAAK,UAAL;AACE,cACElB,QAAQ,CAACK,QAAT,IAAqB,IAArB,IACA,CAAC5H,KAAK,CAACC,OAAN,CAAcsH,QAAQ,CAACK,QAAvB,CAFH,EAGE;AACAmC,YAAAA,OAAO,CAACC,KAAR,CACEjB,MAAM,CACJM,sBADI,EAEJ9B,QAAQ,CAACE,IAFL,EAGJ,YAHI,EAIJ,SAJI,SAKAwC,MAAM,CAAC1C,QAAQ,CAACK,QAAV,CALN,QADR;AASD;;AACD;;AACF,aAAK,kBAAL;AACE,cAAI,CAAC5H,KAAK,CAACC,OAAN,CAAcsH,QAAQ,CAACM,gBAAvB,CAAL,EAA+C;AAC7CkC,YAAAA,OAAO,CAACC,KAAR,CACEjB,MAAM,CACJM,sBADI,EAEJ9B,QAAQ,CAACE,IAFL,EAGJ,oBAHI,EAIJ,SAJI,SAKAwC,MAAM,CAAC1C,QAAQ,CAACM,gBAAV,CALN,QADR;AASD;;AACD;;AACF,aAAK,SAAL;AACA,aAAK,MAAL;AACE;;AACF;AACEkC,UAAAA,OAAO,CAACC,KAAR,+DAEIzC,QAAQ,CAACE,IAFb,0CAGsC8B,gBAAgB,CAACrJ,GAAjB,CAClC,UAACkK,CAAD;AAAA,0BAAWA,CAAX;AAAA,WADkC,EAElC9J,IAFkC,CAE7B,IAF6B,CAHtC,gBAKwBwJ,GALxB;AAtGJ;;AA+GAvC,MAAAA,QAAQ,CAACK,QAAT,IACEL,QAAQ,CAACK,QAAT,CAAkBN,OAAlB,CAA0B,UAAC+C,WAAD,EAAiB;AACzC,YAAIpD,SAAS,CAACqD,IAAV,CAAe,UAACC,GAAD;AAAA,iBAASA,GAAG,CAAC9C,IAAJ,KAAa4C,WAAtB;AAAA,SAAf,KAAqD,IAAzD,EAA+D;AAC7DN,UAAAA,OAAO,CAACC,KAAR,CACEjB,MAAM,CACJO,wBADI,EAEJW,MAAM,CAAC1C,QAAQ,CAACE,IAAV,CAFF,EAGJ4C,WAHI,EAIJA,WAJI,CADR;AAQD;AACF,OAXD,CADF;AAaD,KAhIH;AAiID,GAlID;AAmID;;ACpJc,SAASG,QAAT,CAAqBC,GAArB,EAAoChC,EAApC,EAA4D;AACzE,MAAMiC,WAAW,GAAG,IAAItD,GAAJ,EAApB;AAEA,SAAOqD,GAAG,CAAC7E,MAAJ,CAAW,UAAAzF,IAAI,EAAI;AACxB,QAAMwK,UAAU,GAAGlC,EAAE,CAACtI,IAAD,CAArB;;AAEA,QAAI,CAACuK,WAAW,CAAC3C,GAAZ,CAAgB4C,UAAhB,CAAL,EAAkC;AAChCD,MAAAA,WAAW,CAAC/C,GAAZ,CAAgBgD,UAAhB;AACA,aAAO,IAAP;AACD;AACF,GAPM,CAAP;AAQD;;ACVc,SAASC,gBAAT,CACbC,SADa,EAEE;AACf,SAAQA,SAAS,CAACC,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAR;AACD;;ACJc,SAASC,WAAT,CACb9D,SADa,EAEsB;AACnC,MAAM+D,MAAM,GAAG/D,SAAS,CAACoB,MAAV,CAAiB,UAAC2C,MAAD,EAASC,OAAT,EAAqB;AACnD,QAAMC,QAAQ,GAAGF,MAAM,CAACC,OAAO,CAACxD,IAAT,CAAvB;AACAuD,IAAAA,MAAM,CAACC,OAAO,CAACxD,IAAT,CAAN,GAAuByD,QAAQ,qBAEtBA,QAFsB,EAGtBD,OAHsB;AAIzBE,MAAAA,OAAO,oBAAOD,QAAQ,CAACC,OAAhB,EAA4BF,OAAO,CAACE,OAApC,CAJkB;AAKzBC,MAAAA,IAAI,oBAAOF,QAAQ,CAACE,IAAhB,EAAyBH,OAAO,CAACG,IAAjC;AALqB,SAO3BH,OAPJ;AAQA,WAAOD,MAAP;AACD,GAXc,EAWZ,EAXY,CAAf,CADmC;;AAenC,SAAOvB,MAAM,CAACC,IAAP,CAAYsB,MAAZ,EAAoB9K,GAApB,CAAwB,UAAA4J,GAAG;AAAA,WAAIkB,MAAM,CAAClB,GAAD,CAAV;AAAA,GAA3B,CAAP;AACD;;ACdc,SAASuB,eAAT,CACb1K,OADa,EAEb2K,QAFa,EAGb;AACA,MAAMtJ,GAAG,GAAGtD,SAAS,CAACiC,OAAD,CAArB;AACA,MAAM4K,IAAI,GAAG7I,kBAAkB,CAAC/B,OAAD,CAA/B;AACA,MAAMU,cAAc,GAAGW,GAAG,CAACX,cAA3B;AAEA,MAAIH,KAAK,GAAGqK,IAAI,CAACC,WAAjB;AACA,MAAIpK,MAAM,GAAGmK,IAAI,CAACE,YAAlB;AACA,MAAIlK,CAAC,GAAG,CAAR;AACA,MAAIG,CAAC,GAAG,CAAR;;AAEA,MAAIL,cAAJ,EAAoB;AAClBH,IAAAA,KAAK,GAAGG,cAAc,CAACH,KAAvB;AACAE,IAAAA,MAAM,GAAGC,cAAc,CAACD,MAAxB;AAEA,QAAMsK,cAAc,GAAGlL,gBAAgB,EAAvC;;AAEA,QAAIkL,cAAc,IAAK,CAACA,cAAD,IAAmBJ,QAAQ,KAAK,OAAvD,EAAiE;AAC/D/J,MAAAA,CAAC,GAAGF,cAAc,CAACI,UAAnB;AACAC,MAAAA,CAAC,GAAGL,cAAc,CAACO,SAAnB;AACD;AACF;;AAED,SAAO;AACLV,IAAAA,KAAK,EAALA,KADK;AAELE,IAAAA,MAAM,EAANA,MAFK;AAGLG,IAAAA,CAAC,EAAEA,CAAC,GAAGsB,mBAAmB,CAAClC,OAAD,CAHrB;AAILe,IAAAA,CAAC,EAADA;AAJK,GAAP;AAMD;;AC7BD;;AACe,SAASiK,eAAT,CAAyBhL,OAAzB,EAAqD;AAAA;;AAClE,MAAM4K,IAAI,GAAG7I,kBAAkB,CAAC/B,OAAD,CAA/B;AACA,MAAMiL,SAAS,GAAG7J,eAAe,CAACpB,OAAD,CAAjC;AACA,MAAM4D,IAAI,4BAAG5D,OAAO,CAAC7B,aAAX,qBAAG,sBAAuByF,IAApC;AAEA,MAAMrD,KAAK,GAAG3B,GAAG,CACfgM,IAAI,CAACM,WADU,EAEfN,IAAI,CAACC,WAFU,EAGfjH,IAAI,GAAGA,IAAI,CAACsH,WAAR,GAAsB,CAHX,EAIftH,IAAI,GAAGA,IAAI,CAACiH,WAAR,GAAsB,CAJX,CAAjB;AAMA,MAAMpK,MAAM,GAAG7B,GAAG,CAChBgM,IAAI,CAACO,YADW,EAEhBP,IAAI,CAACE,YAFW,EAGhBlH,IAAI,GAAGA,IAAI,CAACuH,YAAR,GAAuB,CAHX,EAIhBvH,IAAI,GAAGA,IAAI,CAACkH,YAAR,GAAuB,CAJX,CAAlB;AAOA,MAAIlK,CAAC,GAAG,CAACqK,SAAS,CAAC3J,UAAX,GAAwBY,mBAAmB,CAAClC,OAAD,CAAnD;AACA,MAAMe,CAAC,GAAG,CAACkK,SAAS,CAACzJ,SAArB;;AAEA,MAAIW,gBAAgB,CAACyB,IAAI,IAAIgH,IAAT,CAAhB,CAA+BQ,SAA/B,KAA6C,KAAjD,EAAwD;AACtDxK,IAAAA,CAAC,IAAIhC,GAAG,CAACgM,IAAI,CAACC,WAAN,EAAmBjH,IAAI,GAAGA,IAAI,CAACiH,WAAR,GAAsB,CAA7C,CAAH,GAAqDtK,KAA1D;AACD;;AAED,SAAO;AAAEA,IAAAA,KAAK,EAALA,KAAF;AAASE,IAAAA,MAAM,EAANA,MAAT;AAAiBG,IAAAA,CAAC,EAADA,CAAjB;AAAoBG,IAAAA,CAAC,EAADA;AAApB,GAAP;AACD;;ACjCc,SAASsK,QAAT,CAAkBC,MAAlB,EAAmCC,KAAnC,EAAmD;AAChE,MAAMC,QAAQ,GAAGD,KAAK,CAACE,WAAN,IAAqBF,KAAK,CAACE,WAAN,EAAtC,CADgE;;AAIhE,MAAIH,MAAM,CAACD,QAAP,CAAgBE,KAAhB,CAAJ,EAA4B;AAC1B,WAAO,IAAP;AACD,GAFD;AAAA,OAIK,IAAIC,QAAQ,IAAI9M,YAAY,CAAC8M,QAAD,CAA5B,EAAwC;AAC3C,UAAIE,IAAI,GAAGH,KAAX;;AACA,SAAG;AACD,YAAIG,IAAI,IAAIJ,MAAM,CAACK,UAAP,CAAkBD,IAAlB,CAAZ,EAAqC;AACnC,iBAAO,IAAP;AACD,SAHA;;;AAKDA,QAAAA,IAAI,GAAGA,IAAI,CAAClI,UAAL,IAAmBkI,IAAI,CAACjI,IAA/B;AACD,OAND,QAMSiI,IANT;AAOD,KAjB+D;;;AAoBhE,SAAO,KAAP;AACD;;ACrBc,SAASE,gBAAT,CAA0BnJ,IAA1B,EAAwD;AACrE,2BACKA,IADL;AAEE5B,IAAAA,IAAI,EAAE4B,IAAI,CAAC7B,CAFb;AAGEI,IAAAA,GAAG,EAAEyB,IAAI,CAAC1B,CAHZ;AAIEG,IAAAA,KAAK,EAAEuB,IAAI,CAAC7B,CAAL,GAAS6B,IAAI,CAAClC,KAJvB;AAKEY,IAAAA,MAAM,EAAEsB,IAAI,CAAC1B,CAAL,GAAS0B,IAAI,CAAChC;AALxB;AAOD;;ACOD,SAASoL,0BAAT,CACE7L,OADF,EAEE2K,QAFF,EAGE;AACA,MAAMlI,IAAI,GAAG1C,qBAAqB,CAACC,OAAD,EAAU,KAAV,EAAiB2K,QAAQ,KAAK,OAA9B,CAAlC;AAEAlI,EAAAA,IAAI,CAACzB,GAAL,GAAWyB,IAAI,CAACzB,GAAL,GAAWhB,OAAO,CAACmD,SAA9B;AACAV,EAAAA,IAAI,CAAC5B,IAAL,GAAY4B,IAAI,CAAC5B,IAAL,GAAYb,OAAO,CAACkD,UAAhC;AACAT,EAAAA,IAAI,CAACtB,MAAL,GAAcsB,IAAI,CAACzB,GAAL,GAAWhB,OAAO,CAAC8K,YAAjC;AACArI,EAAAA,IAAI,CAACvB,KAAL,GAAauB,IAAI,CAAC5B,IAAL,GAAYb,OAAO,CAAC6K,WAAjC;AACApI,EAAAA,IAAI,CAAClC,KAAL,GAAaP,OAAO,CAAC6K,WAArB;AACApI,EAAAA,IAAI,CAAChC,MAAL,GAAcT,OAAO,CAAC8K,YAAtB;AACArI,EAAAA,IAAI,CAAC7B,CAAL,GAAS6B,IAAI,CAAC5B,IAAd;AACA4B,EAAAA,IAAI,CAAC1B,CAAL,GAAS0B,IAAI,CAACzB,GAAd;AAEA,SAAOyB,IAAP;AACD;;AAED,SAASqJ,0BAAT,CACE9L,OADF,EAEE+L,cAFF,EAGEpB,QAHF,EAIoB;AAClB,SAAOoB,cAAc,KAAKvG,QAAnB,GACHoG,gBAAgB,CAAClB,eAAe,CAAC1K,OAAD,EAAU2K,QAAV,CAAhB,CADb,GAEHtM,SAAS,CAAC0N,cAAD,CAAT,GACAF,0BAA0B,CAACE,cAAD,EAAiBpB,QAAjB,CAD1B,GAEAiB,gBAAgB,CAACZ,eAAe,CAACjJ,kBAAkB,CAAC/B,OAAD,CAAnB,CAAhB,CAJpB;AAKD;AAGD;AACA;;;AACA,SAASgM,kBAAT,CAA4BhM,OAA5B,EAA8D;AAC5D,MAAMuF,eAAe,GAAG1B,iBAAiB,CAACP,aAAa,CAACtD,OAAD,CAAd,CAAzC;AACA,MAAMiM,iBAAiB,GACrB,CAAC,UAAD,EAAa,OAAb,EAAsBtI,OAAtB,CAA8BxB,gBAAgB,CAACnC,OAAD,CAAhB,CAA0BsE,QAAxD,KAAqE,CADvE;AAEA,MAAM4H,cAAc,GAClBD,iBAAiB,IAAIzN,aAAa,CAACwB,OAAD,CAAlC,GACIkF,eAAe,CAAClF,OAAD,CADnB,GAEIA,OAHN;;AAKA,MAAI,CAAC3B,SAAS,CAAC6N,cAAD,CAAd,EAAgC;AAC9B,WAAO,EAAP;AACD,GAX2D;;;AAc5D,SAAO3G,eAAe,CAACN,MAAhB,CACL,UAAC8G,cAAD;AAAA,WACE1N,SAAS,CAAC0N,cAAD,CAAT,IACAV,QAAQ,CAACU,cAAD,EAAiBG,cAAjB,CADR,IAEAtK,WAAW,CAACmK,cAAD,CAAX,KAAgC,MAHlC;AAAA,GADK,CAAP;AAMD;AAGD;;;AACe,SAASI,eAAT,CACbnM,OADa,EAEboM,QAFa,EAGbC,YAHa,EAIb1B,QAJa,EAKK;AAClB,MAAM2B,mBAAmB,GACvBF,QAAQ,KAAK,iBAAb,GACIJ,kBAAkB,CAAChM,OAAD,CADtB,GAEI,GAAGkE,MAAH,CAAUkI,QAAV,CAHN;AAIA,MAAM7G,eAAe,aAAO+G,mBAAP,GAA4BD,YAA5B,EAArB;AACA,MAAME,mBAAmB,GAAGhH,eAAe,CAAC,CAAD,CAA3C;AAEA,MAAMiH,YAAY,GAAGjH,eAAe,CAACmC,MAAhB,CAAuB,UAAC+E,OAAD,EAAUV,cAAV,EAA6B;AACvE,QAAMtJ,IAAI,GAAGqJ,0BAA0B,CAAC9L,OAAD,EAAU+L,cAAV,EAA0BpB,QAA1B,CAAvC;AAEA8B,IAAAA,OAAO,CAACzL,GAAR,GAAcpC,GAAG,CAAC6D,IAAI,CAACzB,GAAN,EAAWyL,OAAO,CAACzL,GAAnB,CAAjB;AACAyL,IAAAA,OAAO,CAACvL,KAAR,GAAgBpC,GAAG,CAAC2D,IAAI,CAACvB,KAAN,EAAauL,OAAO,CAACvL,KAArB,CAAnB;AACAuL,IAAAA,OAAO,CAACtL,MAAR,GAAiBrC,GAAG,CAAC2D,IAAI,CAACtB,MAAN,EAAcsL,OAAO,CAACtL,MAAtB,CAApB;AACAsL,IAAAA,OAAO,CAAC5L,IAAR,GAAejC,GAAG,CAAC6D,IAAI,CAAC5B,IAAN,EAAY4L,OAAO,CAAC5L,IAApB,CAAlB;AAEA,WAAO4L,OAAP;AACD,GAToB,EASlBX,0BAA0B,CAAC9L,OAAD,EAAUuM,mBAAV,EAA+B5B,QAA/B,CATR,CAArB;AAWA6B,EAAAA,YAAY,CAACjM,KAAb,GAAqBiM,YAAY,CAACtL,KAAb,GAAqBsL,YAAY,CAAC3L,IAAvD;AACA2L,EAAAA,YAAY,CAAC/L,MAAb,GAAsB+L,YAAY,CAACrL,MAAb,GAAsBqL,YAAY,CAACxL,GAAzD;AACAwL,EAAAA,YAAY,CAAC5L,CAAb,GAAiB4L,YAAY,CAAC3L,IAA9B;AACA2L,EAAAA,YAAY,CAACzL,CAAb,GAAiByL,YAAY,CAACxL,GAA9B;AAEA,SAAOwL,YAAP;AACD;;ACtGc,SAASE,YAAT,CAAsBxC,SAAtB,EAAwD;AACrE,SAAQA,SAAS,CAACC,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAR;AACD;;ACFc,SAASwC,wBAAT,CACbzC,SADa,EAEF;AACX,SAAO,CAAC,KAAD,EAAQ,QAAR,EAAkBvG,OAAlB,CAA0BuG,SAA1B,KAAwC,CAAxC,GAA4C,GAA5C,GAAkD,GAAzD;AACD;;ACKc,SAAS0C,cAAT,OASH;AAAA,MARVlH,SAQU,QARVA,SAQU;AAAA,MAPV1F,OAOU,QAPVA,OAOU;AAAA,MANVkK,SAMU,QANVA,SAMU;AACV,MAAM2C,aAAa,GAAG3C,SAAS,GAAGD,gBAAgB,CAACC,SAAD,CAAnB,GAAiC,IAAhE;AACA,MAAM4C,SAAS,GAAG5C,SAAS,GAAGwC,YAAY,CAACxC,SAAD,CAAf,GAA6B,IAAxD;AACA,MAAM6C,OAAO,GAAGrH,SAAS,CAAC9E,CAAV,GAAc8E,SAAS,CAACnF,KAAV,GAAkB,CAAhC,GAAoCP,OAAO,CAACO,KAAR,GAAgB,CAApE;AACA,MAAMyM,OAAO,GAAGtH,SAAS,CAAC3E,CAAV,GAAc2E,SAAS,CAACjF,MAAV,GAAmB,CAAjC,GAAqCT,OAAO,CAACS,MAAR,GAAiB,CAAtE;AAEA,MAAIwC,OAAJ;;AACA,UAAQ4J,aAAR;AACE,SAAK7L,GAAL;AACEiC,MAAAA,OAAO,GAAG;AACRrC,QAAAA,CAAC,EAAEmM,OADK;AAERhM,QAAAA,CAAC,EAAE2E,SAAS,CAAC3E,CAAV,GAAcf,OAAO,CAACS;AAFjB,OAAV;AAIA;;AACF,SAAKU,MAAL;AACE8B,MAAAA,OAAO,GAAG;AACRrC,QAAAA,CAAC,EAAEmM,OADK;AAERhM,QAAAA,CAAC,EAAE2E,SAAS,CAAC3E,CAAV,GAAc2E,SAAS,CAACjF;AAFnB,OAAV;AAIA;;AACF,SAAKS,KAAL;AACE+B,MAAAA,OAAO,GAAG;AACRrC,QAAAA,CAAC,EAAE8E,SAAS,CAAC9E,CAAV,GAAc8E,SAAS,CAACnF,KADnB;AAERQ,QAAAA,CAAC,EAAEiM;AAFK,OAAV;AAIA;;AACF,SAAKnM,IAAL;AACEoC,MAAAA,OAAO,GAAG;AACRrC,QAAAA,CAAC,EAAE8E,SAAS,CAAC9E,CAAV,GAAcZ,OAAO,CAACO,KADjB;AAERQ,QAAAA,CAAC,EAAEiM;AAFK,OAAV;AAIA;;AACF;AACE/J,MAAAA,OAAO,GAAG;AACRrC,QAAAA,CAAC,EAAE8E,SAAS,CAAC9E,CADL;AAERG,QAAAA,CAAC,EAAE2E,SAAS,CAAC3E;AAFL,OAAV;AA1BJ;;AAgCA,MAAMkM,QAAQ,GAAGJ,aAAa,GAC1BF,wBAAwB,CAACE,aAAD,CADE,GAE1B,IAFJ;;AAIA,MAAII,QAAQ,IAAI,IAAhB,EAAsB;AACpB,QAAMC,GAAG,GAAGD,QAAQ,KAAK,GAAb,GAAmB,QAAnB,GAA8B,OAA1C;;AAEA,YAAQH,SAAR;AACE,WAAKzH,KAAL;AACEpC,QAAAA,OAAO,CAACgK,QAAD,CAAP,GACEhK,OAAO,CAACgK,QAAD,CAAP,IAAqBvH,SAAS,CAACwH,GAAD,CAAT,GAAiB,CAAjB,GAAqBlN,OAAO,CAACkN,GAAD,CAAP,GAAe,CAAzD,CADF;AAEA;;AACF,WAAK5H,GAAL;AACErC,QAAAA,OAAO,CAACgK,QAAD,CAAP,GACEhK,OAAO,CAACgK,QAAD,CAAP,IAAqBvH,SAAS,CAACwH,GAAD,CAAT,GAAiB,CAAjB,GAAqBlN,OAAO,CAACkN,GAAD,CAAP,GAAe,CAAzD,CADF;AAEA;AARJ;AAWD;;AAED,SAAOjK,OAAP;AACD;;AC9Ec,SAASkK,kBAAT,GAA0C;AACvD,SAAO;AACLnM,IAAAA,GAAG,EAAE,CADA;AAELE,IAAAA,KAAK,EAAE,CAFF;AAGLC,IAAAA,MAAM,EAAE,CAHH;AAILN,IAAAA,IAAI,EAAE;AAJD,GAAP;AAMD;;ACNc,SAASuM,kBAAT,CACbC,aADa,EAED;AACZ,2BACKF,kBAAkB,EADvB,EAEKE,aAFL;AAID;;ACTc,SAASC,eAAT,CAGbtE,KAHa,EAGHD,IAHG,EAGmC;AAChD,SAAOA,IAAI,CAACrB,MAAL,CAAY,UAAC6F,OAAD,EAAUpE,GAAV,EAAkB;AACnCoE,IAAAA,OAAO,CAACpE,GAAD,CAAP,GAAeH,KAAf;AACA,WAAOuE,OAAP;AACD,GAHM,EAGJ,EAHI,CAAP;AAID;;ACuBc,SAASC,cAAT,CACbC,KADa,EAEbjD,OAFa,EAGD;AAAA,MADZA,OACY;AADZA,IAAAA,OACY,GADe,EACf;AAAA;;AAAA,iBASRA,OATQ;AAAA,oCAEVN,SAFU;AAAA,MAEVA,SAFU,mCAEEuD,KAAK,CAACvD,SAFR;AAAA,mCAGVS,QAHU;AAAA,MAGVA,QAHU,kCAGC8C,KAAK,CAAC9C,QAHP;AAAA,mCAIVyB,QAJU;AAAA,MAIVA,QAJU,kCAIC7G,eAJD;AAAA,uCAKV8G,YALU;AAAA,MAKVA,YALU,sCAKK7G,QALL;AAAA,uCAMVkI,cANU;AAAA,MAMVA,cANU,sCAMOjI,MANP;AAAA,sCAOVkI,WAPU;AAAA,MAOVA,WAPU,qCAOI,KAPJ;AAAA,kCAQVC,OARU;AAAA,MAQVA,OARU,iCAQA,CARA;AAWZ,MAAMP,aAAa,GAAGD,kBAAkB,CACtC,OAAOQ,OAAP,KAAmB,QAAnB,GACIA,OADJ,GAEIN,eAAe,CAACM,OAAD,EAAUxI,cAAV,CAHmB,CAAxC;AAMA,MAAMyI,UAAU,GAAGH,cAAc,KAAKjI,MAAnB,GAA4BC,SAA5B,GAAwCD,MAA3D;AAEA,MAAMqI,UAAU,GAAGL,KAAK,CAACM,KAAN,CAAYtI,MAA/B;AACA,MAAMzF,OAAO,GAAGyN,KAAK,CAACO,QAAN,CAAeL,WAAW,GAAGE,UAAH,GAAgBH,cAA1C,CAAhB;AAEA,MAAMO,kBAAkB,GAAG9B,eAAe,CACxC9N,SAAS,CAAC2B,OAAD,CAAT,GACIA,OADJ,GAEIA,OAAO,CAACkO,cAAR,IAA0BnM,kBAAkB,CAAC0L,KAAK,CAACO,QAAN,CAAevI,MAAhB,CAHR,EAIxC2G,QAJwC,EAKxCC,YALwC,EAMxC1B,QANwC,CAA1C;AASA,MAAMwD,mBAAmB,GAAGpO,qBAAqB,CAAC0N,KAAK,CAACO,QAAN,CAAetI,SAAhB,CAAjD;AAEA,MAAM0I,aAAa,GAAGxB,cAAc,CAAC;AACnClH,IAAAA,SAAS,EAAEyI,mBADwB;AAEnCnO,IAAAA,OAAO,EAAE8N,UAF0B;AAGnCnD,IAAAA,QAAQ,EAAE,UAHyB;AAInCT,IAAAA,SAAS,EAATA;AAJmC,GAAD,CAApC;AAOA,MAAMmE,gBAAgB,GAAGzC,gBAAgB,mBACpCkC,UADoC,EAEpCM,aAFoC,EAAzC;AAKA,MAAME,iBAAiB,GACrBZ,cAAc,KAAKjI,MAAnB,GAA4B4I,gBAA5B,GAA+CF,mBADjD,CA7CY;AAiDZ;;AACA,MAAMI,eAAe,GAAG;AACtBvN,IAAAA,GAAG,EAAEiN,kBAAkB,CAACjN,GAAnB,GAAyBsN,iBAAiB,CAACtN,GAA3C,GAAiDqM,aAAa,CAACrM,GAD9C;AAEtBG,IAAAA,MAAM,EACJmN,iBAAiB,CAACnN,MAAlB,GACA8M,kBAAkB,CAAC9M,MADnB,GAEAkM,aAAa,CAAClM,MALM;AAMtBN,IAAAA,IAAI,EAAEoN,kBAAkB,CAACpN,IAAnB,GAA0ByN,iBAAiB,CAACzN,IAA5C,GAAmDwM,aAAa,CAACxM,IANjD;AAOtBK,IAAAA,KAAK,EACHoN,iBAAiB,CAACpN,KAAlB,GAA0B+M,kBAAkB,CAAC/M,KAA7C,GAAqDmM,aAAa,CAACnM;AAR/C,GAAxB;AAWA,MAAMsN,UAAU,GAAGf,KAAK,CAACgB,aAAN,CAAoBC,MAAvC,CA7DY;;AAgEZ,MAAIhB,cAAc,KAAKjI,MAAnB,IAA6B+I,UAAjC,EAA6C;AAC3C,QAAME,MAAM,GAAGF,UAAU,CAACtE,SAAD,CAAzB;AAEApB,IAAAA,MAAM,CAACC,IAAP,CAAYwF,eAAZ,EAA6B5H,OAA7B,CAAqC,UAACwC,GAAD,EAAS;AAC5C,UAAMwF,QAAQ,GAAG,CAACzN,KAAD,EAAQC,MAAR,EAAgBwC,OAAhB,CAAwBwF,GAAxB,KAAgC,CAAhC,GAAoC,CAApC,GAAwC,CAAC,CAA1D;AACA,UAAMyF,IAAI,GAAG,CAAC5N,GAAD,EAAMG,MAAN,EAAcwC,OAAd,CAAsBwF,GAAtB,KAA8B,CAA9B,GAAkC,GAAlC,GAAwC,GAArD;AACAoF,MAAAA,eAAe,CAACpF,GAAD,CAAf,IAAwBuF,MAAM,CAACE,IAAD,CAAN,GAAeD,QAAvC;AACD,KAJD;AAKD;;AAED,SAAOJ,eAAP;AACD;;ACxFD,IAAMM,qBAAqB,GACzB,8GADF;AAEA,IAAMC,mBAAmB,GACvB,+HADF;AAGA,IAAMC,eAAoC,GAAG;AAC3C7E,EAAAA,SAAS,EAAE,QADgC;AAE3C5D,EAAAA,SAAS,EAAE,EAFgC;AAG3CqE,EAAAA,QAAQ,EAAE;AAHiC,CAA7C;;AAWA,SAASqE,gBAAT,GAAwD;AAAA,oCAA3B1G,IAA2B;AAA3BA,IAAAA,IAA2B;AAAA;;AACtD,SAAO,CAACA,IAAI,CAAC2G,IAAL,CACN,UAACjP,OAAD;AAAA,WACE,EAAEA,OAAO,IAAI,OAAOA,OAAO,CAACD,qBAAf,KAAyC,UAAtD,CADF;AAAA,GADM,CAAR;AAID;;AAEM,SAASmP,eAAT,CAAyBC,gBAAzB,EAAqE;AAAA,MAA5CA,gBAA4C;AAA5CA,IAAAA,gBAA4C,GAAJ,EAAI;AAAA;;AAAA,0BAItEA,gBAJsE;AAAA,gDAExEC,gBAFwE;AAAA,MAExEA,gBAFwE,sCAErD,EAFqD;AAAA,iDAGxEC,cAHwE;AAAA,MAGxEA,cAHwE,uCAGvDN,eAHuD;AAM1E,SAAO,SAASO,YAAT,CACL5J,SADK,EAELD,MAFK,EAGL+E,OAHK,EAIK;AAAA,QADVA,OACU;AADVA,MAAAA,OACU,GADmC6E,cACnC;AAAA;;AACV,QAAI5B,KAAoB,GAAG;AACzBvD,MAAAA,SAAS,EAAE,QADc;AAEzBzC,MAAAA,gBAAgB,EAAE,EAFO;AAGzB+C,MAAAA,OAAO,oBAAOuE,eAAP,EAA2BM,cAA3B,CAHkB;AAIzBZ,MAAAA,aAAa,EAAE,EAJU;AAKzBT,MAAAA,QAAQ,EAAE;AACRtI,QAAAA,SAAS,EAATA,SADQ;AAERD,QAAAA,MAAM,EAANA;AAFQ,OALe;AASzB8J,MAAAA,UAAU,EAAE,EATa;AAUzBC,MAAAA,MAAM,EAAE;AAViB,KAA3B;AAaA,QAAIC,gBAAmC,GAAG,EAA1C;AACA,QAAIC,WAAW,GAAG,KAAlB;AAEA,QAAMC,QAAQ,GAAG;AACflC,MAAAA,KAAK,EAALA,KADe;AAEfmC,MAAAA,UAFe,sBAEJC,gBAFI,EAEc;AAC3B,YAAMrF,OAAO,GACX,OAAOqF,gBAAP,KAA4B,UAA5B,GACIA,gBAAgB,CAACpC,KAAK,CAACjD,OAAP,CADpB,GAEIqF,gBAHN;AAKAC,QAAAA,sBAAsB;AAEtBrC,QAAAA,KAAK,CAACjD,OAAN,qBAEK6E,cAFL,EAGK5B,KAAK,CAACjD,OAHX,EAIKA,OAJL;AAOAiD,QAAAA,KAAK,CAACsC,aAAN,GAAsB;AACpBrK,UAAAA,SAAS,EAAErH,SAAS,CAACqH,SAAD,CAAT,GACP7B,iBAAiB,CAAC6B,SAAD,CADV,GAEPA,SAAS,CAACwI,cAAV,GACArK,iBAAiB,CAAC6B,SAAS,CAACwI,cAAX,CADjB,GAEA,EALgB;AAMpBzI,UAAAA,MAAM,EAAE5B,iBAAiB,CAAC4B,MAAD;AANL,SAAtB,CAf2B;AAyB3B;;AACA,YAAMgC,gBAAgB,GAAGD,cAAc,CACrC4C,WAAW,WAAKgF,gBAAL,EAA0B3B,KAAK,CAACjD,OAAN,CAAclE,SAAxC,EAD0B,CAAvC,CA1B2B;;AA+B3BmH,QAAAA,KAAK,CAAChG,gBAAN,GAAyBA,gBAAgB,CAACxC,MAAjB,CAAwB,UAAC+K,CAAD;AAAA,iBAAOA,CAAC,CAACzG,OAAT;AAAA,SAAxB,CAAzB,CA/B2B;AAkC3B;;AACA,mDAAa;AACX,cAAMjD,SAAS,GAAGuD,QAAQ,WACpBpC,gBADoB,EACCgG,KAAK,CAACjD,OAAN,CAAclE,SADf,GAExB;AAAA,gBAAGQ,IAAH,QAAGA,IAAH;AAAA,mBAAcA,IAAd;AAAA,WAFwB,CAA1B;AAKA+B,UAAAA,iBAAiB,CAACvC,SAAD,CAAjB;;AAEA,cAAI2D,gBAAgB,CAACwD,KAAK,CAACjD,OAAN,CAAcN,SAAf,CAAhB,KAA8C/E,IAAlD,EAAwD;AACtD,gBAAM8K,YAAY,GAAGxC,KAAK,CAAChG,gBAAN,CAAuBkC,IAAvB,CACnB;AAAA,kBAAG7C,IAAH,SAAGA,IAAH;AAAA,qBAAcA,IAAI,KAAK,MAAvB;AAAA,aADmB,CAArB;;AAIA,gBAAI,CAACmJ,YAAL,EAAmB;AACjB7G,cAAAA,OAAO,CAACC,KAAR,CACE,CACE,0DADF,EAEE,8BAFF,EAGE1J,IAHF,CAGO,GAHP,CADF;AAMD;AACF;;AArBU,kCA4BPwC,gBAAgB,CAACsD,MAAD,CA5BT;AAAA,cAwBTyK,SAxBS,qBAwBTA,SAxBS;AAAA,cAyBTC,WAzBS,qBAyBTA,WAzBS;AAAA,cA0BTC,YA1BS,qBA0BTA,YA1BS;AAAA,cA2BTC,UA3BS,qBA2BTA,UA3BS;AA+BX;;;AACA,cACE,CAACH,SAAD,EAAYC,WAAZ,EAAyBC,YAAzB,EAAuCC,UAAvC,EAAmDpB,IAAnD,CAAwD,UAACqB,MAAD;AAAA,mBACtDC,UAAU,CAACD,MAAD,CAD4C;AAAA,WAAxD,CADF,EAIE;AACAlH,YAAAA,OAAO,CAACoH,IAAR,CACE,CACE,6DADF,EAEE,2DAFF,EAGE,4DAHF,EAIE,0DAJF,EAKE,YALF,EAME7Q,IANF,CAMO,GANP,CADF;AASD;AACF;;AAED8Q,QAAAA,kBAAkB;AAElB,eAAOd,QAAQ,CAACe,MAAT,EAAP;AACD,OAzFc;AA2Ff;AACA;AACA;AACA;AACA;AACAC,MAAAA,WAhGe,yBAgGD;AACZ,YAAIjB,WAAJ,EAAiB;AACf;AACD;;AAHW,8BAKkBjC,KAAK,CAACO,QALxB;AAAA,YAKJtI,SALI,mBAKJA,SALI;AAAA,YAKOD,MALP,mBAKOA,MALP;AAQZ;;AACA,YAAI,CAACuJ,gBAAgB,CAACtJ,SAAD,EAAYD,MAAZ,CAArB,EAA0C;AACxC,qDAAa;AACX2D,YAAAA,OAAO,CAACC,KAAR,CAAcwF,qBAAd;AACD;;AACD;AACD,SAdW;;;AAiBZpB,QAAAA,KAAK,CAACM,KAAN,GAAc;AACZrI,UAAAA,SAAS,EAAEhD,gBAAgB,CACzBgD,SADyB,EAEzBR,eAAe,CAACO,MAAD,CAFU,EAGzBgI,KAAK,CAACjD,OAAN,CAAcG,QAAd,KAA2B,OAHF,CADf;AAMZlF,UAAAA,MAAM,EAAErC,aAAa,CAACqC,MAAD;AANT,SAAd,CAjBY;AA2BZ;AACA;AACA;AACA;;AACAgI,QAAAA,KAAK,CAACmD,KAAN,GAAc,KAAd;AAEAnD,QAAAA,KAAK,CAACvD,SAAN,GAAkBuD,KAAK,CAACjD,OAAN,CAAcN,SAAhC,CAjCY;AAoCZ;AACA;AACA;;AACAuD,QAAAA,KAAK,CAAChG,gBAAN,CAAuBd,OAAvB,CACE,UAACC,QAAD;AAAA,iBACG6G,KAAK,CAACgB,aAAN,CAAoB7H,QAAQ,CAACE,IAA7B,sBACIF,QAAQ,CAAC6D,IADb,CADH;AAAA,SADF;AAOA,YAAIoG,eAAe,GAAG,CAAtB;;AACA,aAAK,IAAI5H,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGwE,KAAK,CAAChG,gBAAN,CAAuBqJ,MAAnD,EAA2D7H,KAAK,EAAhE,EAAoE;AAClE,qDAAa;AACX4H,YAAAA,eAAe,IAAI,CAAnB;;AACA,gBAAIA,eAAe,GAAG,GAAtB,EAA2B;AACzBzH,cAAAA,OAAO,CAACC,KAAR,CAAcyF,mBAAd;AACA;AACD;AACF;;AAED,cAAIrB,KAAK,CAACmD,KAAN,KAAgB,IAApB,EAA0B;AACxBnD,YAAAA,KAAK,CAACmD,KAAN,GAAc,KAAd;AACA3H,YAAAA,KAAK,GAAG,CAAC,CAAT;AACA;AACD;;AAbiE,sCAe/BwE,KAAK,CAAChG,gBAAN,CAAuBwB,KAAvB,CAf+B;AAAA,cAe1DnB,EAf0D,yBAe1DA,EAf0D;AAAA,6DAetD0C,OAfsD;AAAA,cAetDA,QAfsD,uCAe5C,EAf4C;AAAA,cAexC1D,IAfwC,yBAexCA,IAfwC;;AAiBlE,cAAI,OAAOgB,EAAP,KAAc,UAAlB,EAA8B;AAC5B2F,YAAAA,KAAK,GAAG3F,EAAE,CAAC;AAAE2F,cAAAA,KAAK,EAALA,KAAF;AAASjD,cAAAA,OAAO,EAAPA,QAAT;AAAkB1D,cAAAA,IAAI,EAAJA,IAAlB;AAAwB6I,cAAAA,QAAQ,EAARA;AAAxB,aAAD,CAAF,IAA0ClC,KAAlD;AACD;AACF;AACF,OApKc;AAsKf;AACA;AACAiD,MAAAA,MAAM,EAAE7I,QAAQ,CACd;AAAA,eACE,IAAIG,OAAJ,CAA2B,UAACC,OAAD,EAAa;AACtC0H,UAAAA,QAAQ,CAACgB,WAAT;AACA1I,UAAAA,OAAO,CAACwF,KAAD,CAAP;AACD,SAHD,CADF;AAAA,OADc,CAxKD;AAgLfsD,MAAAA,OAhLe,qBAgLL;AACRjB,QAAAA,sBAAsB;AACtBJ,QAAAA,WAAW,GAAG,IAAd;AACD;AAnLc,KAAjB;;AAsLA,QAAI,CAACV,gBAAgB,CAACtJ,SAAD,EAAYD,MAAZ,CAArB,EAA0C;AACxC,iDAAa;AACX2D,QAAAA,OAAO,CAACC,KAAR,CAAcwF,qBAAd;AACD;;AACD,aAAOc,QAAP;AACD;;AAEDA,IAAAA,QAAQ,CAACC,UAAT,CAAoBpF,OAApB,EAA6BtC,IAA7B,CAAkC,UAACuF,KAAD,EAAW;AAC3C,UAAI,CAACiC,WAAD,IAAgBlF,OAAO,CAACwG,aAA5B,EAA2C;AACzCxG,QAAAA,OAAO,CAACwG,aAAR,CAAsBvD,KAAtB;AACD;AACF,KAJD,EA9MU;AAqNV;AACA;AACA;AACA;;AACA,aAASgD,kBAAT,GAA8B;AAC5BhD,MAAAA,KAAK,CAAChG,gBAAN,CAAuBd,OAAvB,CAA+B,iBAAoC;AAAA,YAAjCG,IAAiC,SAAjCA,IAAiC;AAAA,kCAA3B0D,OAA2B;AAAA,YAA3BA,OAA2B,8BAAjB,EAAiB;AAAA,YAAbhB,MAAa,SAAbA,MAAa;;AACjE,YAAI,OAAOA,MAAP,KAAkB,UAAtB,EAAkC;AAChC,cAAMyH,SAAS,GAAGzH,MAAM,CAAC;AAAEiE,YAAAA,KAAK,EAALA,KAAF;AAAS3G,YAAAA,IAAI,EAAJA,IAAT;AAAe6I,YAAAA,QAAQ,EAARA,QAAf;AAAyBnF,YAAAA,OAAO,EAAPA;AAAzB,WAAD,CAAxB;;AACA,cAAM0G,MAAM,GAAG,SAATA,MAAS,GAAM,EAArB;;AACAzB,UAAAA,gBAAgB,CAAClI,IAAjB,CAAsB0J,SAAS,IAAIC,MAAnC;AACD;AACF,OAND;AAOD;;AAED,aAASpB,sBAAT,GAAkC;AAChCL,MAAAA,gBAAgB,CAAC9I,OAAjB,CAAyB,UAACmB,EAAD;AAAA,eAAQA,EAAE,EAAV;AAAA,OAAzB;AACA2H,MAAAA,gBAAgB,GAAG,EAAnB;AACD;;AAED,WAAOE,QAAP;AACD,GA7OD;AA8OD;;ACxRD,IAAMwB,OAAO,GAAG;AAAEA,EAAAA,OAAO,EAAE;AAAX,CAAhB;;AAEA,SAAS3H,QAAT,OAA0E;AAAA,MAAxDiE,KAAwD,QAAxDA,KAAwD;AAAA,MAAjDkC,QAAiD,QAAjDA,QAAiD;AAAA,MAAvCnF,OAAuC,QAAvCA,OAAuC;AAAA,wBAC/BA,OAD+B,CAChExH,MADgE;AAAA,MAChEA,MADgE,gCACvD,IADuD;AAAA,wBAC/BwH,OAD+B,CACjD4G,MADiD;AAAA,MACjDA,MADiD,gCACxC,IADwC;AAGxE,MAAMnT,MAAM,GAAGF,SAAS,CAAC0P,KAAK,CAACO,QAAN,CAAevI,MAAhB,CAAxB;AACA,MAAMsK,aAAa,aACdtC,KAAK,CAACsC,aAAN,CAAoBrK,SADN,EAEd+H,KAAK,CAACsC,aAAN,CAAoBtK,MAFN,CAAnB;;AAKA,MAAIzC,MAAJ,EAAY;AACV+M,IAAAA,aAAa,CAACpJ,OAAd,CAAsB,UAAA5C,YAAY,EAAI;AACpCA,MAAAA,YAAY,CAACsN,gBAAb,CAA8B,QAA9B,EAAwC1B,QAAQ,CAACe,MAAjD,EAAyDS,OAAzD;AACD,KAFD;AAGD;;AAED,MAAIC,MAAJ,EAAY;AACVnT,IAAAA,MAAM,CAACoT,gBAAP,CAAwB,QAAxB,EAAkC1B,QAAQ,CAACe,MAA3C,EAAmDS,OAAnD;AACD;;AAED,SAAO,YAAM;AACX,QAAInO,MAAJ,EAAY;AACV+M,MAAAA,aAAa,CAACpJ,OAAd,CAAsB,UAAA5C,YAAY,EAAI;AACpCA,QAAAA,YAAY,CAACuN,mBAAb,CAAiC,QAAjC,EAA2C3B,QAAQ,CAACe,MAApD,EAA4DS,OAA5D;AACD,OAFD;AAGD;;AAED,QAAIC,MAAJ,EAAY;AACVnT,MAAAA,MAAM,CAACqT,mBAAP,CAA2B,QAA3B,EAAqC3B,QAAQ,CAACe,MAA9C,EAAsDS,OAAtD;AACD;AACF,GAVD;AAWD;;;AAID,qBAAgB;AACdrK,EAAAA,IAAI,EAAE,gBADQ;AAEdyC,EAAAA,OAAO,EAAE,IAFK;AAGd3B,EAAAA,KAAK,EAAE,OAHO;AAIdE,EAAAA,EAAE,EAAE,cAAM,EAJI;AAKd0B,EAAAA,MAAM,EAANA,QALc;AAMdiB,EAAAA,IAAI,EAAE;AANQ,CAAhB;;AC1CA,SAAS2D,aAAT,OAAiE;AAAA,MAAxCX,KAAwC,QAAxCA,KAAwC;AAAA,MAAjC3G,IAAiC,QAAjCA,IAAiC;AAC/D;AACA;AACA;AACA;AACA2G,EAAAA,KAAK,CAACgB,aAAN,CAAoB3H,IAApB,IAA4B8F,cAAc,CAAC;AACzClH,IAAAA,SAAS,EAAE+H,KAAK,CAACM,KAAN,CAAYrI,SADkB;AAEzC1F,IAAAA,OAAO,EAAEyN,KAAK,CAACM,KAAN,CAAYtI,MAFoB;AAGzCkF,IAAAA,QAAQ,EAAE,UAH+B;AAIzCT,IAAAA,SAAS,EAAEuD,KAAK,CAACvD;AAJwB,GAAD,CAA1C;AAMD;;;AAID,sBAAgB;AACdpD,EAAAA,IAAI,EAAE,eADQ;AAEdyC,EAAAA,OAAO,EAAE,IAFK;AAGd3B,EAAAA,KAAK,EAAE,MAHO;AAIdE,EAAAA,EAAE,EAAEsG,aAJU;AAKd3D,EAAAA,IAAI,EAAE;AALQ,CAAhB;;ACmBA,IAAM8G,UAAU,GAAG;AACjBvQ,EAAAA,GAAG,EAAE,MADY;AAEjBE,EAAAA,KAAK,EAAE,MAFU;AAGjBC,EAAAA,MAAM,EAAE,MAHS;AAIjBN,EAAAA,IAAI,EAAE;AAJW,CAAnB;AAQA;AACA;;AACA,SAAS2Q,iBAAT,OAAqCnQ,GAArC,EAA2D;AAAA,MAA9BT,CAA8B,QAA9BA,CAA8B;AAAA,MAA3BG,CAA2B,QAA3BA,CAA2B;AACzD,MAAM0Q,GAAG,GAAGpQ,GAAG,CAACqQ,gBAAJ,IAAwB,CAApC;AAEA,SAAO;AACL9Q,IAAAA,CAAC,EAAE7B,KAAK,CAAC6B,CAAC,GAAG6Q,GAAL,CAAL,GAAiBA,GAAjB,IAAwB,CADtB;AAEL1Q,IAAAA,CAAC,EAAEhC,KAAK,CAACgC,CAAC,GAAG0Q,GAAL,CAAL,GAAiBA,GAAjB,IAAwB;AAFtB,GAAP;AAID;;AAEM,SAASE,WAAT,QAsBJ;AAAA;;AAAA,MArBDlM,MAqBC,SArBDA,MAqBC;AAAA,MApBDqI,UAoBC,SApBDA,UAoBC;AAAA,MAnBD5D,SAmBC,SAnBDA,SAmBC;AAAA,MAlBD4C,SAkBC,SAlBDA,SAkBC;AAAA,MAjBD7J,OAiBC,SAjBDA,OAiBC;AAAA,MAhBDqB,QAgBC,SAhBDA,QAgBC;AAAA,MAfDsN,eAeC,SAfDA,eAeC;AAAA,MAdDC,QAcC,SAdDA,QAcC;AAAA,MAbDC,YAaC,SAbDA,YAaC;AAAA,MAZDjP,OAYC,SAZDA,OAYC;AAAA,mBACsBI,OADtB,CACKrC,CADL;AAAA,MACKA,CADL,2BACS,CADT;AAAA,mBACsBqC,OADtB,CACYlC,CADZ;AAAA,MACYA,CADZ,2BACgB,CADhB;;AAAA,cAIC,OAAO+Q,YAAP,KAAwB,UAAxB,GACIA,YAAY,CAAC;AAAElR,IAAAA,CAAC,EAADA,CAAF;AAAKG,IAAAA,CAAC,EAADA;AAAL,GAAD,CADhB,GAEI;AAAEH,IAAAA,CAAC,EAADA,CAAF;AAAKG,IAAAA,CAAC,EAADA;AAAL,GANL;;AAGEH,EAAAA,CAHF,SAGEA,CAHF;AAGKG,EAAAA,CAHL,SAGKA,CAHL;AAQD,MAAMgR,IAAI,GAAG9O,OAAO,CAAC+O,cAAR,CAAuB,GAAvB,CAAb;AACA,MAAMC,IAAI,GAAGhP,OAAO,CAAC+O,cAAR,CAAuB,GAAvB,CAAb;AAEA,MAAIE,KAAa,GAAGrR,IAApB;AACA,MAAIsR,KAAa,GAAGnR,GAApB;AAEA,MAAMK,GAAW,GAAGpD,MAApB;;AAEA,MAAI4T,QAAJ,EAAc;AACZ,QAAIjP,YAAY,GAAGsC,eAAe,CAACO,MAAD,CAAlC;AACA,QAAI2M,UAAU,GAAG,cAAjB;AACA,QAAIC,SAAS,GAAG,aAAhB;;AAEA,QAAIzP,YAAY,KAAK7E,SAAS,CAAC0H,MAAD,CAA9B,EAAwC;AACtC7C,MAAAA,YAAY,GAAGb,kBAAkB,CAAC0D,MAAD,CAAjC;;AAEA,UACEtD,gBAAgB,CAACS,YAAD,CAAhB,CAA+B0B,QAA/B,KAA4C,QAA5C,IACAA,QAAQ,KAAK,UAFf,EAGE;AACA8N,QAAAA,UAAU,GAAG,cAAb;AACAC,QAAAA,SAAS,GAAG,aAAZ;AACD;AACF,KAfW;;;AAkBZzP,IAAAA,YAAY,GAAIA,YAAhB;;AAEA,QACEsH,SAAS,KAAKlJ,GAAd,IACC,CAACkJ,SAAS,KAAKrJ,IAAd,IAAsBqJ,SAAS,KAAKhJ,KAArC,KAA+C4L,SAAS,KAAKxH,GAFhE,EAGE;AACA6M,MAAAA,KAAK,GAAGhR,MAAR;AACA,UAAMmR,OAAO,GACXzP,OAAO,IAAID,YAAY,KAAKvB,GAA5B,IAAmCA,GAAG,CAACX,cAAvC,GACIW,GAAG,CAACX,cAAJ,CAAmBD,MADvB;AAGImC,MAAAA,YAAY,CAACwP,UAAD,CAJlB;AAKArR,MAAAA,CAAC,IAAIuR,OAAO,GAAGxE,UAAU,CAACrN,MAA1B;AACAM,MAAAA,CAAC,IAAI6Q,eAAe,GAAG,CAAH,GAAO,CAAC,CAA5B;AACD;;AAED,QACE1H,SAAS,KAAKrJ,IAAd,IACC,CAACqJ,SAAS,KAAKlJ,GAAd,IAAqBkJ,SAAS,KAAK/I,MAApC,KAA+C2L,SAAS,KAAKxH,GAFhE,EAGE;AACA4M,MAAAA,KAAK,GAAGhR,KAAR;AACA,UAAMqR,OAAO,GACX1P,OAAO,IAAID,YAAY,KAAKvB,GAA5B,IAAmCA,GAAG,CAACX,cAAvC,GACIW,GAAG,CAACX,cAAJ,CAAmBH,KADvB;AAGIqC,MAAAA,YAAY,CAACyP,SAAD,CAJlB;AAKAzR,MAAAA,CAAC,IAAI2R,OAAO,GAAGzE,UAAU,CAACvN,KAA1B;AACAK,MAAAA,CAAC,IAAIgR,eAAe,GAAG,CAAH,GAAO,CAAC,CAA5B;AACD;AACF;;AAED,MAAMY,YAAY;AAChBlO,IAAAA,QAAQ,EAARA;AADgB,KAEZuN,QAAQ,IAAIN,UAFA,CAAlB;;AAjEC,cAuECO,YAAY,KAAK,IAAjB,GACIN,iBAAiB,CAAC;AAAE5Q,IAAAA,CAAC,EAADA,CAAF;AAAKG,IAAAA,CAAC,EAADA;AAAL,GAAD,EAAWhD,SAAS,CAAC0H,MAAD,CAApB,CADrB,GAEI;AAAE7E,IAAAA,CAAC,EAADA,CAAF;AAAKG,IAAAA,CAAC,EAADA;AAAL,GAzEL;;AAsEEH,EAAAA,CAtEF,SAsEEA,CAtEF;AAsEKG,EAAAA,CAtEL,SAsEKA,CAtEL;;AA2ED,MAAI6Q,eAAJ,EAAqB;AAAA;;AACnB,6BACKY,YADL,uCAEGL,KAFH,IAEWF,IAAI,GAAG,GAAH,GAAS,EAFxB,iBAGGC,KAHH,IAGWH,IAAI,GAAG,GAAH,GAAS,EAHxB,iBAOElN,SAPF,GAQI,CAACxD,GAAG,CAACqQ,gBAAJ,IAAwB,CAAzB,KAA+B,CAA/B,kBACiB9Q,CADjB,YACyBG,CADzB,4BAEmBH,CAFnB,YAE2BG,CAF3B,WARJ;AAYD;;AAED,2BACKyR,YADL,yCAEGL,KAFH,IAEWF,IAAI,GAAMlR,CAAN,UAAc,EAF7B,kBAGGmR,KAHH,IAGWH,IAAI,GAAMnR,CAAN,UAAc,EAH7B,kBAIEiE,SAJF,GAIa,EAJb;AAMD;;AAED,SAAS4N,aAAT,QAAuE;AAAA,MAA9ChF,KAA8C,SAA9CA,KAA8C;AAAA,MAAvCjD,OAAuC,SAAvCA,OAAuC;AAAA,8BAMjEA,OANiE,CAEnEoH,eAFmE;AAAA,MAEnEA,eAFmE,sCAEjD,IAFiD;AAAA,0BAMjEpH,OANiE,CAGnEqH,QAHmE;AAAA,MAGnEA,QAHmE,kCAGxD,IAHwD;AAAA,8BAMjErH,OANiE,CAKnEsH,YALmE;AAAA,MAKnEA,YALmE,sCAKpD,IALoD;;AAQrE,6CAAa;AACX,QAAMY,kBAAkB,GACtBvQ,gBAAgB,CAACsL,KAAK,CAACO,QAAN,CAAevI,MAAhB,CAAhB,CAAwCiN,kBAAxC,IAA8D,EADhE;;AAGA,QACEb,QAAQ,IACR,CAAC,WAAD,EAAc,KAAd,EAAqB,OAArB,EAA8B,QAA9B,EAAwC,MAAxC,EAAgD5C,IAAhD,CACE,UAAC0D,QAAD;AAAA,aAAcD,kBAAkB,CAAC/O,OAAnB,CAA2BgP,QAA3B,KAAwC,CAAtD;AAAA,KADF,CAFF,EAKE;AACAvJ,MAAAA,OAAO,CAACoH,IAAR,CACE,CACE,mEADF,EAEE,gEAFF,EAGE,MAHF,EAIE,oEAJF,EAKE,iEALF,EAME,oEANF,EAOE,0CAPF,EAQE,MARF,EASE,oEATF,EAUE,qEAVF,EAWE7Q,IAXF,CAWO,GAXP,CADF;AAcD;AACF;;AAED,MAAM6S,YAAY,GAAG;AACnBtI,IAAAA,SAAS,EAAED,gBAAgB,CAACwD,KAAK,CAACvD,SAAP,CADR;AAEnB4C,IAAAA,SAAS,EAAEJ,YAAY,CAACe,KAAK,CAACvD,SAAP,CAFJ;AAGnBzE,IAAAA,MAAM,EAAEgI,KAAK,CAACO,QAAN,CAAevI,MAHJ;AAInBqI,IAAAA,UAAU,EAAEL,KAAK,CAACM,KAAN,CAAYtI,MAJL;AAKnBmM,IAAAA,eAAe,EAAfA,eALmB;AAMnB/O,IAAAA,OAAO,EAAE4K,KAAK,CAACjD,OAAN,CAAcG,QAAd,KAA2B;AANjB,GAArB;;AASA,MAAI8C,KAAK,CAACgB,aAAN,CAAoBL,aAApB,IAAqC,IAAzC,EAA+C;AAC7CX,IAAAA,KAAK,CAAC+B,MAAN,CAAa/J,MAAb,qBACKgI,KAAK,CAAC+B,MAAN,CAAa/J,MADlB,EAEKkM,WAAW,mBACTa,YADS;AAEZvP,MAAAA,OAAO,EAAEwK,KAAK,CAACgB,aAAN,CAAoBL,aAFjB;AAGZ9J,MAAAA,QAAQ,EAAEmJ,KAAK,CAACjD,OAAN,CAAcG,QAHZ;AAIZkH,MAAAA,QAAQ,EAARA,QAJY;AAKZC,MAAAA,YAAY,EAAZA;AALY,OAFhB;AAUD;;AAED,MAAIrE,KAAK,CAACgB,aAAN,CAAoBmE,KAApB,IAA6B,IAAjC,EAAuC;AACrCnF,IAAAA,KAAK,CAAC+B,MAAN,CAAaoD,KAAb,qBACKnF,KAAK,CAAC+B,MAAN,CAAaoD,KADlB,EAEKjB,WAAW,mBACTa,YADS;AAEZvP,MAAAA,OAAO,EAAEwK,KAAK,CAACgB,aAAN,CAAoBmE,KAFjB;AAGZtO,MAAAA,QAAQ,EAAE,UAHE;AAIZuN,MAAAA,QAAQ,EAAE,KAJE;AAKZC,MAAAA,YAAY,EAAZA;AALY,OAFhB;AAUD;;AAEDrE,EAAAA,KAAK,CAAC8B,UAAN,CAAiB9J,MAAjB,qBACKgI,KAAK,CAAC8B,UAAN,CAAiB9J,MADtB;AAEE,6BAAyBgI,KAAK,CAACvD;AAFjC;AAID;;;AAID,sBAAgB;AACdpD,EAAAA,IAAI,EAAE,eADQ;AAEdyC,EAAAA,OAAO,EAAE,IAFK;AAGd3B,EAAAA,KAAK,EAAE,aAHO;AAIdE,EAAAA,EAAE,EAAE2K,aAJU;AAKdhI,EAAAA,IAAI,EAAE;AALQ,CAAhB;;ACzPA;;AAEA,SAASoI,WAAT,OAAyD;AAAA,MAAlCpF,KAAkC,QAAlCA,KAAkC;AACvD3E,EAAAA,MAAM,CAACC,IAAP,CAAY0E,KAAK,CAACO,QAAlB,EAA4BrH,OAA5B,CAAoC,UAACG,IAAD,EAAU;AAC5C,QAAMgM,KAAK,GAAGrF,KAAK,CAAC+B,MAAN,CAAa1I,IAAb,KAAsB,EAApC;AAEA,QAAMyI,UAAU,GAAG9B,KAAK,CAAC8B,UAAN,CAAiBzI,IAAjB,KAA0B,EAA7C;AACA,QAAM9G,OAAO,GAAGyN,KAAK,CAACO,QAAN,CAAelH,IAAf,CAAhB,CAJ4C;;AAO5C,QAAI,CAACtI,aAAa,CAACwB,OAAD,CAAd,IAA2B,CAAC4B,WAAW,CAAC5B,OAAD,CAA3C,EAAsD;AACpD;AACD,KAT2C;AAY5C;AACA;;;AACA8I,IAAAA,MAAM,CAACiK,MAAP,CAAc/S,OAAO,CAAC8S,KAAtB,EAA6BA,KAA7B;AAEAhK,IAAAA,MAAM,CAACC,IAAP,CAAYwG,UAAZ,EAAwB5I,OAAxB,CAAgC,UAACG,IAAD,EAAU;AACxC,UAAMkC,KAAK,GAAGuG,UAAU,CAACzI,IAAD,CAAxB;;AACA,UAAIkC,KAAK,KAAK,KAAd,EAAqB;AACnBhJ,QAAAA,OAAO,CAACgT,eAAR,CAAwBlM,IAAxB;AACD,OAFD,MAEO;AACL9G,QAAAA,OAAO,CAACiT,YAAR,CAAqBnM,IAArB,EAA2BkC,KAAK,KAAK,IAAV,GAAiB,EAAjB,GAAsBA,KAAjD;AACD;AACF,KAPD;AAQD,GAxBD;AAyBD;;AAED,SAASQ,MAAT,QAAoD;AAAA,MAAlCiE,KAAkC,SAAlCA,KAAkC;AAClD,MAAMyF,aAAa,GAAG;AACpBzN,IAAAA,MAAM,EAAE;AACNnB,MAAAA,QAAQ,EAAEmJ,KAAK,CAACjD,OAAN,CAAcG,QADlB;AAEN9J,MAAAA,IAAI,EAAE,GAFA;AAGNG,MAAAA,GAAG,EAAE,GAHC;AAINsP,MAAAA,MAAM,EAAE;AAJF,KADY;AAOpBsC,IAAAA,KAAK,EAAE;AACLtO,MAAAA,QAAQ,EAAE;AADL,KAPa;AAUpBoB,IAAAA,SAAS,EAAE;AAVS,GAAtB;AAaAoD,EAAAA,MAAM,CAACiK,MAAP,CAActF,KAAK,CAACO,QAAN,CAAevI,MAAf,CAAsBqN,KAApC,EAA2CI,aAAa,CAACzN,MAAzD;AACAgI,EAAAA,KAAK,CAAC+B,MAAN,GAAe0D,aAAf;;AAEA,MAAIzF,KAAK,CAACO,QAAN,CAAe4E,KAAnB,EAA0B;AACxB9J,IAAAA,MAAM,CAACiK,MAAP,CAActF,KAAK,CAACO,QAAN,CAAe4E,KAAf,CAAqBE,KAAnC,EAA0CI,aAAa,CAACN,KAAxD;AACD;;AAED,SAAO,YAAM;AACX9J,IAAAA,MAAM,CAACC,IAAP,CAAY0E,KAAK,CAACO,QAAlB,EAA4BrH,OAA5B,CAAoC,UAACG,IAAD,EAAU;AAC5C,UAAM9G,OAAO,GAAGyN,KAAK,CAACO,QAAN,CAAelH,IAAf,CAAhB;AACA,UAAMyI,UAAU,GAAG9B,KAAK,CAAC8B,UAAN,CAAiBzI,IAAjB,KAA0B,EAA7C;AAEA,UAAMqM,eAAe,GAAGrK,MAAM,CAACC,IAAP,CACtB0E,KAAK,CAAC+B,MAAN,CAAawC,cAAb,CAA4BlL,IAA5B,IACI2G,KAAK,CAAC+B,MAAN,CAAa1I,IAAb,CADJ,GAEIoM,aAAa,CAACpM,IAAD,CAHK,CAAxB,CAJ4C;;AAW5C,UAAMgM,KAAK,GAAGK,eAAe,CAACzL,MAAhB,CAAuB,UAACoL,KAAD,EAAQH,QAAR,EAAqB;AACxDG,QAAAA,KAAK,CAACH,QAAD,CAAL,GAAkB,EAAlB;AACA,eAAOG,KAAP;AACD,OAHa,EAGX,EAHW,CAAd,CAX4C;;AAiB5C,UAAI,CAACtU,aAAa,CAACwB,OAAD,CAAd,IAA2B,CAAC4B,WAAW,CAAC5B,OAAD,CAA3C,EAAsD;AACpD;AACD;;AAED8I,MAAAA,MAAM,CAACiK,MAAP,CAAc/S,OAAO,CAAC8S,KAAtB,EAA6BA,KAA7B;AAEAhK,MAAAA,MAAM,CAACC,IAAP,CAAYwG,UAAZ,EAAwB5I,OAAxB,CAAgC,UAACyM,SAAD,EAAe;AAC7CpT,QAAAA,OAAO,CAACgT,eAAR,CAAwBI,SAAxB;AACD,OAFD;AAGD,KA1BD;AA2BD,GA5BD;AA6BD;;;AAID,oBAAgB;AACdtM,EAAAA,IAAI,EAAE,aADQ;AAEdyC,EAAAA,OAAO,EAAE,IAFK;AAGd3B,EAAAA,KAAK,EAAE,OAHO;AAIdE,EAAAA,EAAE,EAAE+K,WAJU;AAKdrJ,EAAAA,MAAM,EAANA,MALc;AAMdvC,EAAAA,QAAQ,EAAE,CAAC,eAAD;AANI,CAAhB;;IChFMmI,gBAAgB,GAAG,CACvBiE,cADuB,EAEvBjF,eAFuB,EAGvBqE,eAHuB,EAIvBI,aAJuB;IAOnBvD,YAAY,gBAAGJ,eAAe,CAAC;AAAEE,EAAAA,gBAAgB,EAAhBA;AAAF,CAAD;;;;;"}