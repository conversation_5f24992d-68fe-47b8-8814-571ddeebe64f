(window.webpackJsonp=window.webpackJsonp||[]).push([[5],{"/KDZ":function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var r=n("q1tI");class s extends r.PureComponent{constructor(e){super(e),this._handleChange=()=>{this.forceUpdate()},this.state={query:window.matchMedia(this.props.rule)}}componentDidMount(){this._subscribe(this.state.query)}componentDidUpdate(e,t){this.state.query!==t.query&&(this._unsubscribe(t.query),this._subscribe(this.state.query))}componentWillUnmount(){this._unsubscribe(this.state.query)}render(){return this.props.children(this.state.query.matches)}static getDerivedStateFromProps(e,t){return e.rule!==t.query.media?{query:window.matchMedia(e.rule)}:null}_subscribe(e){e.addListener(this._handleChange)}_unsubscribe(e){e.removeListener(this._handleChange)}}},"6uNr":function(e,t,n){e.exports={menuWrap:"menuWrap-g78rwseC",isMeasuring:"isMeasuring-g78rwseC",scrollWrap:"scrollWrap-g78rwseC",momentumBased:"momentumBased-g78rwseC",menuBox:"menuBox-g78rwseC",isHidden:"isHidden-g78rwseC"}},DTHj:function(e,t,n){"use strict";n.d(t,"a",(function(){return b})),n.d(t,"b",(function(){return g}));var r=n("q1tI"),s=n.n(r),i=n("TSYQ"),o=n.n(i),a=n("Eyy1"),l=n("Hr11"),u=n("XAms"),c=n("+EG+"),h=n("tWVy"),d=n("jAh7"),p=n("lxNp"),m=n("dxYz"),f=n("PN6A"),_=n("6uNr");const b=_;class g extends s.a.PureComponent{constructor(e){super(e),this._containerRef=null,this._scrollWrapRef=null,this._raf=null,this._manager=new d.a,this._hotkeys=null,this._scroll=0,this._handleContainerRef=e=>{this._containerRef=e,this.props.reference&&("function"==typeof this.props.reference&&this.props.reference(e),"object"==typeof this.props.reference&&(this.props.reference.current=e))},this._handleScrollWrapRef=e=>{this._scrollWrapRef=e,"function"==typeof this.props.scrollWrapReference&&this.props.scrollWrapReference(e),"object"==typeof this.props.scrollWrapReference&&(this.props.scrollWrapReference.current=e)},this._handleMeasure=e=>{if(this.state.isMeasureValid)return;const{position:t}=this.props,n=Object(a.ensureNotNull)(this._containerRef);let r=n.getBoundingClientRect();const s=document.documentElement.clientHeight,i=document.documentElement.clientWidth;let o=s-0;const u=r.height>o;if(u){Object(a.ensureNotNull)(this._scrollWrapRef).style.overflowY="scroll",r=n.getBoundingClientRect()}const{width:c,height:h}=r,d="function"==typeof t?t(c,h):t,p=i-(d.overrideWidth||c)-0,m=Object(l.clamp)(d.x,0,Math.max(0,p)),f=s-(d.overrideHeight||h)-0;let _=Object(l.clamp)(d.y,0,Math.max(0,f));d.forbidCorrectYCoord&&_<d.y&&(o-=d.y-_,_=d.y),this.setState({appearingMenuHeight:d.overrideHeight||(u?o:void 0),appearingMenuWidth:d.overrideWidth,appearingPosition:{x:m,y:_},isMeasureValid:!0},()=>{this._restoreScrollPosition(),e&&e()})},this._restoreScrollPosition=()=>{const e=document.activeElement,t=Object(a.ensureNotNull)(this._containerRef);if(null!==e&&t.contains(e))try{e.scrollIntoView()}catch(e){}else Object(a.ensureNotNull)(this._scrollWrapRef).scrollTop=this._scroll},this._resize=()=>{null===this._raf&&(this._raf=requestAnimationFrame(()=>{
this.setState({appearingMenuHeight:void 0,appearingMenuWidth:void 0,appearingPosition:void 0,isMeasureValid:void 0}),this._raf=null}))},this._handleGlobalClose=()=>{this.props.onClose()},this._handleSlot=e=>{this._manager.setContainer(e)},this._handleScroll=()=>{this._scroll=Object(a.ensureNotNull)(this._scrollWrapRef).scrollTop},this.state={}}componentDidMount(){this._handleMeasure(this.props.onOpen);const{customCloseDelegate:e=h.a}=this.props;e.subscribe(this,this._handleGlobalClose),window.addEventListener("resize",this._resize);const t=null!==this.context;this._hotkeys||t||(this._hotkeys=p.createGroup({desc:"Popup menu"}),this._hotkeys.add({desc:"Close",hotkey:27,handler:()=>this._handleGlobalClose()}))}componentDidUpdate(){this._handleMeasure()}componentWillUnmount(){const{customCloseDelegate:e=h.a}=this.props;e.unsubscribe(this,this._handleGlobalClose),window.removeEventListener("resize",this._resize),this._hotkeys&&(this._hotkeys.destroy(),this._hotkeys=null),null!==this._raf&&(cancelAnimationFrame(this._raf),this._raf=null)}render(){const{id:e,role:t,"aria-labelledby":n,"aria-activedescendant":r,children:i,minWidth:a,theme:l=_,className:h,maxHeight:d,onMouseOver:p,onMouseOut:b,onKeyDown:g,onFocus:v,onBlur:C}=this.props,{appearingMenuHeight:M,appearingMenuWidth:w,appearingPosition:W,isMeasureValid:x}=this.state;return s.a.createElement(f.a.Provider,{value:this},s.a.createElement(m.b,null,s.a.createElement(c.b.Provider,{value:this._manager},s.a.createElement("div",{id:e,role:t,"aria-labelledby":n,"aria-activedescendant":r,className:o()(h,l.menuWrap,!x&&l.isMeasuring),style:{height:M,left:W&&W.x,minWidth:a,position:"fixed",top:W&&W.y,width:w},"data-name":this.props["data-name"],ref:this._handleContainerRef,onScrollCapture:this.props.onScroll,onContextMenu:u.b,tabIndex:this.props.tabIndex,onMouseOver:p,onMouseOut:b,onKeyDown:g,onFocus:v,onBlur:C},s.a.createElement("div",{className:o()(l.scrollWrap,!this.props.noMomentumBasedScroll&&l.momentumBased),style:{overflowY:void 0!==M?"scroll":"auto",maxHeight:d},onScrollCapture:this._handleScroll,ref:this._handleScrollWrapRef},s.a.createElement(y,{className:l.menuBox},i)))),s.a.createElement(c.a,{reference:this._handleSlot})))}update(){this._resize()}}function y(e){const t=Object(a.ensureNotNull)(Object(r.useContext)(m.a)),n=s.a.useRef(null);return s.a.createElement("div",{ref:n,className:e.className,onMouseOver:function(e){if(!(null!==t.current&&e.target instanceof Node&&(r=e.target,null===(s=n.current)||void 0===s?void 0:s.contains(r))))return;var r,s;t.isSubmenuNode(e.target)||t.setCurrent(null)},"data-name":"menu-inner"},e.children)}g.contextType=m.a},PN6A:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var r=n("q1tI");const s=n.n(r).a.createContext(null)},X0gx:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n("q1tI"),s=n.n(r),i=n("tWVy");const o=s.a.createContext(i.a)},dxYz:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return o}));var r=n("q1tI"),s=n.n(r);const i=s.a.createContext(null)
;function o(e){const[t,n]=Object(r.useState)(null),o=Object(r.useRef)(null),a=Object(r.useRef)(new Map);return Object(r.useEffect)(()=>()=>{null!==o.current&&clearTimeout(o.current)},[]),s.a.createElement(i.Provider,{value:{current:t,setCurrent:function(e){null!==o.current&&(clearTimeout(o.current),o.current=null);null===t?n(e):o.current=setTimeout(()=>{o.current=null,n(e)},100)},registerSubmenu:function(e,t){return a.current.set(e,t),()=>{a.current.delete(e)}},isSubmenuNode:function(e){return Array.from(a.current.values()).some(t=>t(e))}}},e.children)}}}]);