<!DOCTYPE html><html lang="hu" dir="ltr"><head><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=Edge"><script>window===window.parent&&(location.href="about:blank")</script><script defer="defer" crossorigin="anonymous" src="bundles/runtime.dfe30490f666a6b5d4fe.js"></script><script defer="defer" crossorigin="anonymous" src="bundles/vendors.6f5e0d8d267cb3e63ee0.js"></script><script defer="defer" crossorigin="anonymous" src="bundles/library.8b0dd8732414a2bcbcb1.js"></script><link type="text/css" href="bundles/library.579e6e3fd95b660ad833.css" rel="stylesheet"></head><body class="chart-page unselectable on-widget"><div class="loading-indicator" id="loading-indicator"></div><script>var JSServer={},__initialEnabledFeaturesets=["charting_library"]</script><script>!function(){window.urlParams=function(){function n(n){return decodeURIComponent(n.replace(t," ")).replace(/<\/?[^>]+(>|$)/g,"")}for(var e,t=/\+/g,r=/([^&=]+)=?([^&]*)/g,i=function(){var n=location.href,e=n.indexOf("#");if(0<=e)return n.substring(e+1);throw"Unexpected use of this page"}(),o={};e=r.exec(i);)o[n(e[1])]=n(e[2]);var s,a=window.parent[o.uid],l=["datafeed","customFormatters","brokerFactory","save_load_adapter"];for(s in a)-1===l.indexOf(s)&&(o[s]=JSON.stringify(a[s]));return o}(),window.locale=urlParams.locale,window.language=urlParams.locale,window.addCustomCSSFile=function(n){var e=document.createElement("link");e.setAttribute("type","text/css"),e.setAttribute("rel","stylesheet"),e.setAttribute("href",n),document.body.appendChild(e)},urlParams.customCSS&&window.addCustomCSSFile(urlParams.customCSS);var n={};if("string"==typeof urlParams.loading_screen)try{n=JSON.parse(urlParams.loading_screen)}catch(n){}var e=document.getElementById("loading-indicator"),n=(n.backgroundColor&&(e.style="background-color: "+n.backgroundColor),!function(){"use strict";var n,e,t;n="\n/* Thanks to google guys for the original <paper-spinner> =)\n * https://github.com/PolymerElements/paper-spinner */\n.tv-spinner {\n  display: none;\n  position: absolute;\n  width: 1em;\n  height: 1em;\n  top: calc(50% - 0.5em);\n  left: calc(50% - 0.5em);\n  margin: 0 auto;\n  color: #2962FF;\n  animation: tv-spinner__container-rotate 0.9s linear infinite;\n  will-change: transform;\n  /* The spinner does not have any contents that would have to be\n\t * flipped if the direction changes. Always use ltr so that the\n\t * style works out correctly in both cases. */\n  direction: ltr;\n}\n.tv-spinner--size_mini {\n  font-size: 16px;\n}\n.tv-spinner--size_medium {\n  font-size: 32px;\n}\n.tv-spinner--size_large {\n  font-size: 56px;\n}\n.tv-spinner--size_mini .tv-spinner__width_element:after {\n  border-width: 2px;\n}\n.tv-spinner--size_medium .tv-spinner__width_element:after {\n  border-width: 3px;\n}\n.tv-spinner--size_large .tv-spinner__width_element:after {\n  border-width: 4px;\n}\n.tv-spinner--shown {\n  display: block;\n}\n.tv-spinner__spinner-layer {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  white-space: nowrap;\n  color: currentColor;\n  transform: rotate(90deg);\n  /**\n\t\t * Patch the gap that appear between the two adjacent div.circle-clipper while the\n\t\t * spinner is rotating (appears on Chrome 50, Safari 9.1.1, and Edge).\n\t\t */\n}\n.tv-spinner__spinner-layer::after {\n  content: '';\n  position: absolute;\n  box-sizing: border-box;\n  top: 0;\n  border-width: 0.07em;\n  border-radius: 50%;\n  left: 45%;\n  width: 10%;\n  border-top-style: solid;\n}\n.tv-spinner__background {\n  display: inline-block;\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n}\n.tv-spinner__background::after {\n  content: '';\n  position: absolute;\n  box-sizing: border-box;\n  top: 0;\n  left: 0;\n  border-radius: 50%;\n  bottom: 0;\n  width: 100%;\n  border-color: rgba(135, 151, 165, 0.2);\n  border-style: solid;\n}\n.tv-spinner__circle-clipper {\n  display: inline-block;\n  position: relative;\n  width: 50%;\n  height: 100%;\n  overflow: hidden;\n}\n.tv-spinner__circle-clipper::after {\n  content: '';\n  position: absolute;\n  box-sizing: border-box;\n  top: 0;\n  border-radius: 50%;\n  bottom: 0;\n  width: 200%;\n  border-style: solid;\n  border-bottom-color: transparent;\n  animation-duration: 1.4s;\n  animation-timing-function: cubic-bezier(0.36, 0, 0.37, 0.99);\n  animation-iteration-count: 1;\n  will-change: transform;\n}\n.tv-spinner__circle-clipper--left::after {\n  left: 0;\n  border-right-color: transparent;\n  transform: rotate(0deg);\n  animation-name: tv-spinner__left-spin;\n}\n.tv-spinner__circle-clipper--right::after {\n  left: -100%;\n  border-left-color: transparent;\n  transform: rotate(-124deg);\n  animation-name: tv-spinner__right-spin;\n}\n@keyframes tv-spinner__container-rotate {\n  100% {\n    transform: rotate(360deg);\n  }\n}\n@keyframes tv-spinner__left-spin {\n  0% {\n    transform: rotate(130deg);\n  }\n  to {\n    transform: rotate(0deg);\n  }\n}\n@keyframes tv-spinner__right-spin {\n  0% {\n    transform: rotate(-130deg);\n  }\n  to {\n    transform: rotate(-124deg);\n  }\n}\n",r=(r=void 0===r?{}:r).insertAt,"undefined"!=typeof document&&(e=document.head||document.getElementsByTagName("head")[0],(t=document.createElement("style")).type="text/css","top"===r&&e.firstChild?e.insertBefore(t,e.firstChild):e.appendChild(t),t.styleSheet?t.styleSheet.cssText=n:t.appendChild(document.createTextNode(n)));var s,r,a=new WeakMap,i=((r=s=s||{})[r.Element=1]="Element",r[r.Document=9]="Document",function(){e=document.documentElement,(n=a?a.get(e):n)||((n=e.ownerDocument.createRange()).selectNodeContents(e),a&&a.set(e,n));var n,e,t=n.createContextualFragment('\n\t\t<div class="tv-spinner" role="progressbar">\n\t\t\t<div class="tv-spinner__spinner-layer">\n\t\t\t\t<div class="tv-spinner__background tv-spinner__width_element"></div>\n\t\t\t\t<div class="tv-spinner__circle-clipper tv-spinner__width_element tv-spinner__circle-clipper--left"></div>\x3c!--\n\t\t\t\t--\x3e<div class="tv-spinner__circle-clipper tv-spinner__width_element tv-spinner__circle-clipper--right"></div>\n\t\t\t</div>\n\t\t</div>\n\t');if("firstElementChild"in t)r=t.firstElementChild;else for(var r=null,i=0;i<t.childNodes.length;i++){var o=t.childNodes[i];if(o.nodeType===s.Element){r=o;break}}return null!==r&&t.removeChild(r),r}());function o(n){this._shown=!1,this._el=i.cloneNode(!0),this.setSize(n||"large")}o.prototype.spin=function(n){return this._el.classList.add("tv-spinner--shown"),void 0===this._container&&(void 0!==(this._container=n)&&n.appendChild(this._el)),this._shown=!0,this},o.prototype.stop=function(n){return n&&void 0!==this._container&&this._container.removeChild(this._el),this._el.classList.remove("tv-spinner--shown"),this._shown=!1,this},o.prototype.setStyle=function(t){var r=this;return Object.keys(t).forEach(function(n){var e=t[n];void 0!==e&&r._el.style.setProperty(n,e)}),this},o.prototype.setSize=function(n){return this._el.className="tv-spinner "+(void 0!==n?"tv-spinner--size_"+n:"")+" "+(this._shown?"tv-spinner--shown":""),this},o.prototype.getEl=function(){return this._el},o.prototype.destroy=function(){this.stop(),delete this._el,delete this._container},window.Spinner=o}(),n.foregroundColor||"auto"),n=new Spinner("large").setStyle({color:n,zIndex:String(2e9)});n.getEl().classList.add("spinner"),n.spin(e)}()</script><script>var _tv_languages=_tv_languages||{};_tv_languages.hu_HU={"Learn more":"Tudj meg többet",Cancel:"Törlés",Price:"Ár",Currency:"Valuta",Open:"Nyitó",Comment:"Komment",Interval:"Időköz",Time:"Idő",Dividends:"Osztalék",Search:"Keresés",Date:"Dátum",Source:"Forrás",Description:"Leírás",Settings:"Beállítások",Background:"Háttér",Border:"Keret",Apply:"Alkalmaz",Symbol:"Szimbólum",Exchange:"Tőzsde",Timezone:"Időzóna","Bar's Style":"Bár Stílusa",Bars:"Bárok",Candles:"Gyertyák","Hollow Candles":"Áttetsző Gyertyák",Line:"Vonal",Area:"Terület",Currencies:"Devizák",Grid:"Rács",Add:"Hozzáad",Show:"Mutat",Trading:"Kereskedés",Sector:"Szektor",Industry:"Iparág",More:"Több",Prices:"Árak",Rectangle:"Téglalap","Parallel Channel":"Párhuzamos Csatorna",Triangle:"Háromszög","Gann Box":"Gann Doboz","Gann Fan":"Gann Legyező","Gann Square":"Gann Négyszög",Earnings:"Bevétel","Volume Profile":"Volumen Profil",Error:"Hiba",Remove:"Eltávolítás",January:"Január",February:"Február",March:"Március",April:"Április",May:"Május",June:"Június",July:"Július",August:"Augusztus",September:"Szeptember",October:"Október",Contracts:"Szerződések",Change:"Változás",Indicators:"Indikátorok",Type:"Típus","Invalid Symbol":"Érvénytelen Szimbólum","New Zealand":"Új-Zéland",Singapore:"Szingapúr",Ok:"Oké",day:"nap",hour:"óra","Click to set a point":"Klikkelj a pont megadásához",Baseline:"Alapvonal",Reverse:"Fordított","Period Ending":"Időszak Vége",Estimate:"Becsült",Yes:"Igen",No:"Nem",Compare:"Összehasonlít",Close:"Zárás",Save:"Mentés",month:"months",Hide:"Elrejt",m_interval_short:"m",h_interval_short:"h",D_interval_short:"N",W_interval_short:"W",M_interval_short:"M",Rename:"Átnevezés","Remove from favorites":"Eltávolít kedvencek közül","Add to favorites":"Hozzáadás kedvencekhez","Time Interval":"Idő Intervallum",Copy:"Másolás",Drawings:"Rajzok","Price format is invalid.":"Érvénytelen árformátum.",Quantity:"Mennyiség",Risk:"Kockázat",Right:"Jobb",Left:"Bal","Price Line":"Árvonal",Bogota:"Bogotá","Mexico City":"Mexikóváros","Sao Paulo":"São Paulo",Almaty:"Almati",Ashkhabad:"Asgábád",Dubai:"Dubaj",Kolkata:"Kalkutta",Seoul:"Szöul",Shanghai:"Sanghaj",Taipei:"Tajpej",Tehran:"Teherán",Tokyo:"Tokió",Athens:"Athén",Istanbul:"Isztambul",Moscow:"Moszkva",Paris:"Párizs",Warsaw:"Varsó",Zurich:"Zürich","Chatham Islands":"Chatham-szigetek",Coordinates:"Koordináták","Events & Alerts":"Események & Riasztások",Events:"Események",Inputs:"Inputok",Properties:"Tulajdonságok",Scales:"Skálák","Source Code":"Forráskód",Style:"Stílus","Timezone/Sessions":"Időzóna/Munkamenet",Visibility:"Láthatóság","Color bars based on previous close":"Bárszínek az előző záró alapján",Borders:"Határok",Wick:"Kanóc","Show real prices on price scale (instead of Heikin-Ashi price)":"Valódi árak mutatása az ártáblázaton (a Heikin-Ashi árak helyett)","ATR length":"ATR Hossz",Range:"Tartomány","Accumulation/Distribution_study":"Akkumuláció/Disztribúció","Accumulative Swing Index_study":"Akkumulatív Swing Index","Advance/Decline_study":"Advance/Decline","Arnaud Legoux Moving Average_study":"Arnaud Legoux Mozgóátlag",Aroon_study:"Aroon",ASI_study:"ASI","Average Directional Index_study":"Átlagos Irányított Index","Average True Range_study":"Átlagos Valós Tartomány","Awesome Oscillator_study":"Awesome Oszcillátor","Balance of Power_study":"Erőegyensúly","Bollinger Bands %B_study":"Bollinger Szalagok %B","Bollinger Bands Width_study":"Bollinger Szalag Szélesség","Bollinger Bands_study":"Bollinger Szalagok","Chaikin Money Flow_study":"Chaikin Pénzáramlás","Chaikin Oscillator_study":"Chaikin Oszcillátor","Chande Kroll Stop_study":"Chande Kroll Stop","Chande Momentum Oscillator_study":"Chande Momentum Oszcillátor","Chop Zone_study":"Oldalazó Zóna","Choppiness Index_study":"Szaggatottság Index","Commodity Channel Index_study":"Árucsatorna Index","Connors RSI_study":"Connors RSI","Coppock Curve_study":"Coppock Görbe","Correlation Coefficient_study":"Korrelációs Koefficiens",CRSI_study:"CRSI","Detrended Price Oscillator_study":"Trendmentes Ár Oszcillátor","Directional Movement_study":"Irányított Mozgás","Donchian Channels_study":"Donchian Csatornák","Double EMA_study":"Dupla EMA","Ease Of Movement_study":"Mozgás Könnyedség","Elder's Force Index_study":"Nemes Erő Index","EMA Cross_study":"EMA Cross",Envelopes_study:"Envelopes","Fisher Transform_study":"Fisher Transzformáció","Fixed Range_study":"Fixed Range","Guppy Multiple Moving Average_study":"Guppy Multiple Moving Average","Historical Volatility_study":"Histórikus Volatilitás","Hull Moving Average_study":"Hull Mozgóátlag","Ichimoku Cloud_study":"Ichimoku Felhő","Keltner Channels_study":"Keltner Csatornák","Klinger Oscillator_study":"Klinger Oszcillátor","Know Sure Thing_study":"Biztosra Tudd Dolog","Least Squares Moving Average_study":"Least Squares Mozgóátlag","Linear Regression Curve_study":"Linear Regression Curve","MA Cross_study":"MA Kereszt","MA with EMA Cross_study":"MA with EMA Cross","MA/EMA Cross_study":"MA/EMA Cross",MACD_study:"MACD","Mass Index_study":"Tömeg Index","McGinley Dynamic_study":"McGinley Dinamika",Momentum_study:"Momentum","Money Flow_study":"Pénzáramlás","Moving Average Channel_study":"Moving Average Channel","Moving Average Exponential_study":"Mozgóátlag Exponenciális","Moving Average Weighted_study":"Mozgóátlag Súlyozott","Moving Average_study":"Mozgóátlag","Net Volume_study":"Nettó Volumen","On Balance Volume_study":"Egyensúly Volumen","Parabolic SAR_study":"Parabolikus SAR","Pivot Points Standard_study":"Pivotális Pontok Standard","Price Channel_study":"Price Channel","Price Oscillator_study":"Price Oszcillátor","Price Volume Trend_study":"Árvolumen Trend","Rate Of Change_study":"Változás Üteme","Relative Strength Index_study":"Relatív Erő Index","Relative Vigor Index_study":"Relatív Életerő Index","Relative Volatility Index_study":"Relative Volatility Index","Session Volume_study":"Session Volume","Session Volume HD_study":"Session Volume","SMI Ergodic Indicator/Oscillator_study":"SMI Ergodic Indicator/Oscillator","Smoothed Moving Average_study":"Simított Mozgóátlag","Stochastic RSI_study":"Sztochasztikus RSI",Stochastic_study:"Sztochasztikus","Triple EMA_study":"Triple EMA",TRIX_study:"TRIX","True Strength Indicator_study":"True Strength Indikátor","Ultimate Oscillator_study":"Végső Oszcillátor","Visible Range_study":"Visible Range","Volume Oscillator_study":"Volumen Oszcillátor",Volume_study:"Volumen","Vortex Indicator_study":"Vortex Indikátor",VWAP_study:"VWAP",VWMA_study:"VWMA","Williams %R_study":"Williams %R","Williams Alligator_study":"Williams Alligátor","Williams Fractal_study":"Williams Fraktál","Zig Zag_study":"Cikk Cakk","Plots Background_study":"Plots Background",SuperTrend_study:"SuperTrend","Average Price_study":"Average Price","Typical Price_study":"Typical Price","Median Price_study":"Median Price","Money Flow Index_study":"Money Flow Index","Moving Average Double_study":"Moving Average Double","Moving Average Triple_study":"Moving Average Triple","Moving Average Adaptive_study":"Moving Average Adaptive","Moving Average Hamming_study":"Moving Average Hamming","Moving Average Modified_study":"Moving Average Modified","Moving Average Multiple_study":"Moving Average Multiple","Linear Regression Slope_study":"Linear Regression Slope","Standard Error_study":"Standard Error","Standard Error Bands_study":"Standard Error Bands","Correlation - Log_study":"Correlation - Log","Standard Deviation_study":"Standard Deviation","Chaikin Volatility_study":"Chaikin Volatility","Volatility Close-to-Close_study":"Volatility Close-to-Close","Volatility Zero Trend Close-to-Close_study":"Volatility Zero Trend Close-to-Close","Volatility O-H-L-C_study":"Volatility O-H-L-C","Volatility Index_study":"Volatility Index","Trend Strength Index_study":"Trend Strength Index","Majority Rule_study":"Majority Rule",Length_input:"Hossz",Plot_input:"Plot",Zero_input:"Zero",Signal_input:"Signal",Long_input:"Long",Short_input:"Short",UpperLimit_input:"UpperLimit",LowerLimit_input:"LowerLimit",Offset_input:"Offset",length_input:"hossz",mult_input:"mult",short_input:"short",long_input:"long",Limit_input:"Limit",Move_input:"Move",Value_input:"Value",Method_input:"Method","Accumulation/Distribution_input":"Akkumuláció/Disztribúció",ADR_B_input:"ADR_B","Equality Line_input":"Equality Line","Window Size_input":"Ablakméret",Sigma_input:"Sigma","Aroon Up_input":"Aroon Up","Aroon Down_input":"Aroon Down",Upper_input:"Upper",Lower_input:"Lower",Deviation_input:"Deviation","Levels Format_input":"Levels Format","Labels Position_input":"Labels Position","0 Level Color_input":"0 Level Color","0.236 Level Color_input":"0.236 Level Color","0.382 Level Color_input":"0.382 Level Color","0.5 Level Color_input":"0.5 Level Color","0.618 Level Color_input":"0.618 Level Color","0.65 Level Color_input":"0.65 Level Color","0.786 Level Color_input":"0.786 Level Color","1 Level Color_input":"1 Level Color","1.272 Level Color_input":"1.272 Level Color","1.414 Level Color_input":"1.414 Level Color","1.618 Level Color_input":"1.618 Level Color","1.65 Level Color_input":"1.65 Level Color","2.618 Level Color_input":"2.618 Level Color","2.65 Level Color_input":"2.65 Level Color","3.618 Level Color_input":"3.618 Level Color","3.65 Level Color_input":"3.65 Level Color","4.236 Level Color_input":"4.236 Level Color","-0.236 Level Color_input":"-0.236 Level Color","-0.382 Level Color_input":"-0.382 Level Color","-0.618 Level Color_input":"-0.618 Level Color","-0.65 Level Color_input":"-0.65 Level Color",ADX_input:"ADX","ADX Smoothing_input":"ADX Smoothing","DI Length_input":"DI Length",Smoothing_input:"Smoothing",ATR_input:"ATR",Growing_input:"Growing",Falling_input:"Falling","Color 0_input":"Color 0","Color 1_input":"Color 1",Source_input:"Source",StdDev_input:"StdDev",Basis_input:"Basis",Median_input:"Median","Bollinger Bands %B_input":"Bollinger Bands %B",Overbought_input:"Overbought",Oversold_input:"Oversold","Bollinger Bands Width_input":"Bollinger Bands Width","RSI Length_input":"RSI Length","UpDown Length_input":"UpDown Length","ROC Length_input":"ROC Hossz",MF_input:"MF",resolution_input:"resolution","Fast Length_input":"Fast Length","Slow Length_input":"Slow Length","Chaikin Oscillator_input":"Chaikin Oscillator",P_input:"P",X_input:"X",Q_input:"Q",p_input:"p",x_input:"x",q_input:"q",Price_input:"Price","Chande MO_input":"Chande MO","Zero Line_input":"Zero Line","Color 2_input":"Color 2","Color 3_input":"Color 3","Color 4_input":"Color 4","Color 5_input":"Color 5","Color 6_input":"Color 6","Color 7_input":"Color 7","Color 8_input":"Color 8",CHOP_input:"CHOP","Upper Band_input":"Upper Band","Lower Band_input":"Lower Band",CCI_input:"CCI","WMA Length_input":"WMA hosszúság","Long RoC Length_input":"Long RoC Length","Short RoC Length_input":"Short RoC Length",sym_input:"sym",Symbol_input:"Symbol",Correlation_input:"Correlation",Period_input:"Period",Centered_input:"Centered","Detrended Price Oscillator_input":"Detrended Price Oscillator",isCentered_input:"isCentered",DPO_input:"DPO","ADX smoothing_input":"ADX smoothing","+DI_input":"+DI","-DI_input":"-DI",DEMA_input:"DEMA",Divisor_input:"Divisor",EOM_input:"EOM","Elder's Force Index_input":"Elder's Force Index",Percent_input:"Percent",Exponential_input:"Exponential",Average_input:"Average","Upper Percentage_input":"Upper Percentage","Lower Percentage_input":"Lower Percentage",Fisher_input:"Fisher",Trigger_input:"Trigger",Level_input:"Level","Trader EMA 1 length_input":"Trader EMA 1 length","Trader EMA 2 length_input":"Trader EMA 2 length","Trader EMA 3 length_input":"Trader EMA 3 length","Trader EMA 4 length_input":"Trader EMA 4 length","Trader EMA 5 length_input":"Trader EMA 5 length","Trader EMA 6 length_input":"Trader EMA 6 length","Investor EMA 1 length_input":"Investor EMA 1 length","Investor EMA 2 length_input":"Investor EMA 2 length","Investor EMA 3 length_input":"Investor EMA 3 length","Investor EMA 4 length_input":"Investor EMA 4 length","Investor EMA 5 length_input":"Investor EMA 5 length","Investor EMA 6 length_input":"Investor EMA 6 length",HV_input:"HV","Hull MA_input":"Hull MA","Conversion Line Periods_input":"Conversion Line Periods","Base Line Periods_input":"Base Line Periods","Lagging Span 2 Periods_input":"Lagging Span 2 Periods",Displacement_input:"Displacement","Conversion Line_input":"Conversion Line","Base Line_input":"Base Line","Lagging Span_input":"Lagging Span","Lead 1_input":"Lead 1","Lead 2_input":"Lead 2","yay Color 0_input":"yay Color 0","yay Color 1_input":"yay Color 1",Multiplier_input:"Multiplier","Bands style_input":"Bands style",Middle_input:"Middle",useTrueRange_input:"useTrueRange",ROCLen1_input:"ROCLen1",ROCLen2_input:"ROCLen2",ROCLen3_input:"ROCLen3",ROCLen4_input:"ROCLen4",SMALen1_input:"SMALen1",SMALen2_input:"SMALen2",SMALen3_input:"SMALen3",SMALen4_input:"SMALen4",SigLen_input:"SigLen",KST_input:"KST",Sig_input:"Sig",roclen1_input:"roclen1",roclen2_input:"roclen2",roclen3_input:"roclen3",roclen4_input:"roclen4",smalen1_input:"smalen1",smalen2_input:"smalen2",smalen3_input:"smalen3",smalen4_input:"smalen4",siglen_input:"siglen","Upper Deviation_input":"Upper Deviation","Lower Deviation_input":"Lower Deviation","Use Upper Deviation_input":"Use Upper Deviation","Use Lower Deviation_input":"Use Lower Deviation",Count_input:"Count",Crosses_input:"Crosses",MOM_input:"MOM",MA_input:"MA","Length EMA_input":"EMA Hossz","Length MA_input":"MA Hossz","Fast length_input":"Fast length","Slow length_input":"Slow length","Signal smoothing_input":"Signal smoothing","Simple ma(oscillator)_input":"Simple ma(oscillator)","Simple ma(signal line)_input":"Simple ma(signal line)",Histogram_input:"Histogram",MACD_input:"MACD",fastLength_input:"fastLength",slowLength_input:"slowLength",signalLength_input:"signalLength",NV_input:"NV",OnBalanceVolume_input:"OnBalanceVolume",Start_input:"Kezdés",Increment_input:"Increment","Max value_input":"Max value",ParabolicSAR_input:"ParabolicSAR",start_input:"start",increment_input:"increment",maximum_input:"maximum","Short length_input":"Short length","Long length_input":"Long length",OSC_input:"OSC",shortlen_input:"shortlen",longlen_input:"longlen",PVT_input:"PVT",ROC_input:"ROC",RSI_input:"RSI",RVGI_input:"RVGI",RVI_input:"RVI","Long period_input":"Long period","Short period_input":"Short period","Signal line period_input":"Signal line period",SMI_input:"SMI","SMI Ergodic Oscillator_input":"SMI Ergodic Oscillator",Indicator_input:"Indicator",Oscillator_input:"Oscillator",K_input:"K",D_input:"D",smoothK_input:"smoothK",smoothD_input:"smoothD","%K_input":"%K","%D_input":"%D","Stochastic Length_input":"Stochastic Length","RSI Source_input":"RSI Source",lengthRSI_input:"lengthRSI",lengthStoch_input:"lengthStoch",TRIX_input:"TRIX",TEMA_input:"TEMA","Long Length_input":"Long Length","Short Length_input":"Short Length","Signal Length_input":"Signal Length",Length1_input:"Length1",Length2_input:"Length2",Length3_input:"Hossz3",length7_input:"length7",length14_input:"length14",length28_input:"length28",UO_input:"UO",VWMA_input:"VWMA",len_input:"len","VI +_input":"VI +","VI -_input":"VI -","%R_input":"%R","Jaw Length_input":"Jaw Length","Teeth Length_input":"Teeth Length","Lips Length_input":"Lips Length",Jaw_input:"Jaw",Teeth_input:"Teeth",Lips_input:"Lips","Down fractals_input":"Down fractals","Up fractals_input":"Up fractals",Periods_input:"Periods",Shapes_input:"Shapes","show MA_input":"show MA","MA Length_input":"MA hosszúság","Color based on previous close_input":"Szín az előző záróár alapján","Rows Layout_input":"Rows Layout","Row Size_input":"Row Size",Volume_input:"Volume","Value Area volume_input":"Value Area volume","Extend POC Right_input":"Extend POC Right","Value Area Volume_input":"Value Area Volume",Placement_input:"Placement",POC_input:"POC","Developing Poc_input":"Developing Poc","Up Volume_input":"Up Volume","Down Volume_input":"Down Volume","Value Area_input":"Value Area","Histogram Box_input":"Histogram Box","Value Area Up_input":"Value Area Up","Value Area Down_input":"Value Area Down","Number Of Rows_input":"Number Of Rows","Ticks Per Row_input":"Ticks Per Row","Up/Down_input":"Up/Down",Total_input:"Total","Deviation (%)_input":"Deviation (%)",Depth_input:"Depth","Extend to last bar_input":"Extend to last bar",Simple_input:"Simple",Weighted_input:"Weighted","Wilder's Smoothing_input":"Wilder's Smoothing","1st Period_input":"1st Period","2nd Period_input":"2nd Period","3rd Period_input":"3rd Period","4th Period_input":"4th Period","5th Period_input":"5th Period","6th Period_input":"6th Period","Rate of Change Lookback_input":"Rate of Change Lookback","Instrument 1_input":"Instrument 1","Instrument 2_input":"Instrument 2","Rolling Period_input":"Rolling Period","Standard Errors_input":"Standard Errors","Averaging Periods_input":"Averaging Periods","Days Per Year_input":"Days Per Year","Market Closed Percentage_input":"Market Closed Percentage","ATR Mult_input":"ATR Mult",VWAP_input:"VWAP","Anchor Period_input":"Anchor Period",Session_input:"Session",Week_input:"Week",Month_input:"Month",Year_input:"Year",Decade_input:"Decade",Century_input:"Century","Go to":"Ugrás ide:",Commission:"Jutalék","Symbol Info":"Szimbólum Infó",Dot_hotkey:"Dot","Load Chart Layout":"Chart Elrendezés Betöltése",minutes_interval:"perc",hours_interval:"óra",days_interval:"nap",weeks_interval:"hét",months_interval:"hónap","Save chart image":"Kép mentés","Toggle Auto Scale":"Váltás Automata Méretezés","Toggle Log Scale":"Váltás Log Skála","Toggle Percentage":"Váltás Százalék",log_scale:"log",auto_scale:"auto","Toggle Maximize Chart":"Maximális Chat Kiterjesztése",adj_adjustments:"adj","Date Range":"Időintervallum",Session:"Munkamenet","Hide Drawings Toolbar":"Rajz Eszköztár Elrejtése","Show Drawings Toolbar":"Rajzok Eszköztár Mutatása",Icon:"Ikon","Remove Indicators":"Indikátorok Eltávolítása","Compare or Add Symbol":"Összehasonlítás vagy Szimbólum Hozzáadása","Fullscreen mode":"Teljes Képernyő Mód","Open Interval Dialog":"Időközi Pábeszéd Dialógus",Ticks_interval_group_name:"Ticks",Seconds_interval_group_name:"Seconds",Minutes_interval_group_name:"Minutes",Hours_interval_group_name:"Hours",Days_interval_group_name:"Days",Weeks_interval_group_name:"Weeks",Months_interval_group_name:"Months",Ranges_interval_group_name:"Ranges",Crosshair:"Szálkereszt","Open chart in popup":"Chart megnyitása felugró ablakban","Chart Properties":"Chart Tulajdonságok","Make a Copy":"Másolat Készítése","Rename Chart Layout":"Chart Elrendezés Átnevezése","Study Template '{templateName}' already exists. Do you really want to replace it?":"{templateName} névvel már létezik tanulmánysablon. Biztos, hogy cserélni akarod?",Templates:"Sablonok","Undo {hint}":"{hint} Visszavonása","Redo {hint}":"{hint} Újra","Add Alert":"Riasztás Hozzáadása","Edit {title} Alert":"{title} Riasztás Szerkesztése",Trade:"Kereskedés","Apply Manual Risk/Reward":"Manuális Kockázat/Nyereség Alkalmazása","Apply Manual Decision Point":"Manuális Döntési Pont Alkalmazása","Analyze Trade Setup":"Kereskedési Felállás Elemzése","Apply Elliott Wave":"Elliot Hullám Alkalmazása","Apply Elliott Wave Intermediate":"Közbülső Elliot Hullám Alkalmazása","Apply Elliott Wave Major":"Fő Elliot Hullám Alkalmazása","Apply Elliott Wave Minor":"Kis Elliot Hullám Alkalmazása","Apply WPT Up Wave":"WPT Fel Hullám Alkalmazása","Up Wave 1 or A":"Hullám 1 vagy A Fel","Up Wave 2 or B":"Hullám 2 vagy B Fel","Up Wave C":"Hullám C Fel","Up Wave 3":"Hullám 3 Fel","Up Wave 4":"Hullám 4 Fel","Up Wave 5":"Hullám 5 Fel","Apply WPT Down Wave":"WPT Le Hullám Alkalmazása","Down Wave 1 or A":"Hullám 1 vagy A Le","Down Wave 2 or B":"Hullám 2 vagy B Le","Down Wave C":"Hullám C Le","Down Wave 3":"Hullám 3 Le","Down Wave 4":"Hullám 4 Le","Down Wave 5":"Hullám 5 Le","Bring to Front":"Előrehozás","Send to Back":"Visszaküldés","Bring Forward":"Előterjesztés","Send Backward":"Hátrébb Küldés","Visual Order":"Vizuális Elrendezés","Apply Default":"Alapértelmezett Beállítás","Save As":"Mentés Másként",Clone:"Klón",Template:"Sablon",Unlock:"Feloldás",Lock:"Zárás","Show Earnings":"Nyereség Mutatása","Show Dividends":"Osztalékok Mutatása","Show Splits":"Felosztások Mutatása","Hide Events on Chart":"Események Elrejtése a Chartról",Absolute:"Teljes","By TradingView":"TradingView Által","{symbol} financials by TradingView":"{symbol} TradingView pénzügyek","More features on tradingview.com":"Még több funkció a tradingview.com-on",Eraser:"Radír","Trend Line":"Trendvonal","Horizontal Line":"Vízszintes Vonal","Vertical Line":"Függőleges Vonal",Arrow:"Nyíl",Ray:"Sugár",Pitchfork:"Villa","Schiff Pitchfork":"Schiff Villa","Trend-Based Fib Extension":"Trendalapú Fib Kiterjesztés","Fib Speed Resistance Fan":"Fib Speed Ellenállás Fan","Fib Time Zone":"Fib Időzóna","Fib Circles":"Fib Körök","Fib Speed Resistance Arcs":"Fib Speed Ellenállás Ívek",Ellipse:"Ellipszis",Polyline:"Sokszögvonal",Arc:"Ív",Text_tool:"Text","Anchored Text":"Horgony Szöveg",Balloon:"Ballon","Price Label":"Árcímke",Brush:"Ecset",Forecast:"Előrejelzés","Reset Chart":"Chart Visszaállítása","Time Zone":"Időzóna","Stay in Drawing Mode":"Rajzmódban Marad","Lock All Drawing Tools":"Rajzeszközök Zárolása","Hide All Drawing Tools":"Minden Rajzeszköz Elrejtése","Symbol Last Price Label":"Symbol Last Value Label","Session Breaks":"Munkamenet Szünetek",Warning:"Figyelmeztetés","Zoom Out":"Kicsinyítés","Zoom In":"Nagyítás","Source code":"Forráskód","Could not get Pine source code.":"Nem kapható Pine forráskód.","Font Size":"Betűméret",Cross:"Kereszt",Dot:"Pont","Show Hidden Tools":"Elrejtett Eszközök Mutatása",Color:"Szín","Template name":"Sablon neve","Paste %s":"%s Beillesztése","Lock Scale":"Méret Zárolása",Percent_scale_menu:"Percent","Indexed to 100_scale_menu":"Indexed to 100",Logarithmic_scale_menu:"Logarithmic",Regular_scale_menu:"Regular","No Overlapping Labels_scale_menu":"No Overlapping Labels","Invert Scale_scale_menu":"Invert Scale",Labels:"Címkék","Vert Grid Lines":"Függőleges Rácsvonalak","Horz Grid Lines":"Vízszintes Rácsvonalak",Watermark:"Vízjel","Top Margin":"Felső Margó","Navigation Buttons":"Navigációs Gombok","Bottom Margin":"Alsó Margó","Right Margin":"Jobb Margó",bars_unit:"bars",Seconds:"Másodpercek",Minutes:"Percek",Hours:"Órák",Days:"Napok",Weeks:"Hetek",Months:"Hónapok","OHLC Values":"OHCL Értékek","Indicator Titles":"Indikátor Címkék","Indicator Arguments":"Indikátor Argumentumok","Indicator Values":"Indikátor Értékek","Show Price":"Ár Mutatása",Extend:"Hosszabítás","Color Bars Based on Previous Close":"Bárszínek az előző záró alapján","Extend Lines Left":"Vonalak Hosszabbítása Balra","Show Middle Point":"Középpont Mutatása","Show Price Range":"Ártartomány Mutatás","Show Bars Range":"Bártartomány Mutatás","Show Date/Time Range":"Dátum/Időintervallum Mutatás","Show Distance":"Távolság Mutatás","Show Angle":"Szög Mutatás","Always Show Stats":"Mindig Mutasd Statisztikát","Text Wrap":"Szöveg Csomagolás",Text:"Szöveg",Mirrored:"Tükrözött",Flipped:"Flippelt","HL Bars":"HL Oszlopok","OC Bars":"OC Oszlopok","Line - Close":"Vonal - Záró","Line - Open":"Line - Nyitó","Line - High":"Vonal - Max","Line - Low":"Vonal - Min","Line - HL/2":"Vonal - HL/2",Degree:"Fokozat","Use one color":"Egyetlen szín használata",Levels:"Szintek","Levels Line":"Szintvonal","Extend Right":"Jobb Hosszabbítás","Extend Left":"Bal Hosszabítás",Percents:"Százalékok",Top:"Felső",Bottom:"Alsó","Full Circles":"Teljes Körök","Price Levels":"Árszintek","Time Levels":"Időszintek:","Left Labels":"Bal Címkék","Right Labels":"Jobb Címkék","Top Labels":"Top Címkék","Bottom Labels":"Alsó Címkék","Price/Bar Ratio":"Ár/Oszlop Arány",Fans:"Rajongók",Arcs:"Ívek",Angles:"Szögek","Extend top":"Top Kiterjesztés","Extend bottom":"Padló Kiterjesztés","Extend left":"Bal Hosszabítás","Extend right":"Jobb Hosszabbítás",Label:"Címke","Label background":"Címke Háttér",Transparency:"Átláthatóság","Avg HL in minticks":"Átl HL a minticks-ben",Variance:"Eltérés","#1 (price)_linetool point":"#1 (price)","#1 (price, bar)_linetool point":"#1 (price, bar)","#{count} (price, bar)_linetool point":"#{count} (price, bar)",Channel:"Csatorna",Median:"Medián","Extend Lines":"Vonalak Hosszabítása",Original:"Eredeti","Modified Schiff":"Módosított Schiff",Inside:"Belső","Label Background":"Címke Háttér","Stop color":"Stop Szín","Target color":"Cél Szín:","Entry price":"Belépési ár","Account size":"Egyenleg",Cash:"Készpénz","#1 (vertical position %, bar)_linetool point":"#1 (vertical position %, bar)",Angle:"Szög","#1 (bar)_linetool point":"#1 (bar)",Precision:"Pontosság",High:"Max",Low:"Min","(H + L)/2":"(M + A)/2","(H + L + C)/3":"(M + A + Z)/3","(O + H + L + C)/4":"(Ny + M + A + Z)/4",Simple:"Egyszerű","With Markers":"Jelölésekkel",Step:"Lépés",Default:"Alapértelmezett",Base:"Bázis",Up:"Fel",Down:"Le","#{count} (bar)_linetool point":"#{count} (bar)","Override Min Tick":"Min. Tick Felülírása","Initial capital":"Indulótőke","Base currency":"Alapdeviza","Order size":"Megbízás mérete",Pyramiding:"Pyramid használata","orders_Pyramiding: count orders":"orders","Verify Price for Limit Orders":"Limitáras Megbízások Árának Ellenőrzése","ticks_slippage ... ticks":"ticks",Slippage:"Csúszás","% of equity":"% a saját tőkéből",Offset:"Eltolás","Main chart symbol_input":"Main chart symbol","Another symbol_input":"Another symbol",close:"záró","Above Bar":"Felső oszlop","Below Bar":"Alsó oszlop","Width (% of the Box)":"Szélesség (a Box %-a)","Show Values":"Értékek Mutatása","Text Color":"Szöveg Szín","Trades on Chart":"Ügyletek a Charton","Signal Labels":"Jel Címkék","Show Labels":"Címkék Mutatása","Symbol Type":"Szimbólum Típusa",Surprise:"Meglepetés","Save New Chart Layout":"Új Chart Elrendezés Mentése","Enter a new chart layout name":"Add meg az új chart elrendezés nevét","Copy Chart Layout":"Chart Elrendezés Másolása","{title} copy_ex: AAPL chart copy":"{title} copy","Do you really want to delete Study Template '{name}' ?":"Biztos, hogy törölni akarod ezt a tanulmánysablont: {name}?",crypto:"kripto","Cancel Order":"Megbízás Törlése","Close Position":"Záró Pozíció","Reverse Position":"Fordított Pozíció",Confirmation:"Megerősítés","Script name":"Szkript név","Visible on Mouse Over":"Az Egér Föléhúzásakor Látható","Always Visible":"Mindig Látható","Always Invisible":"Mindig Láthatatlan","{count} bars":"{count} oszlop",Split:"Felosztás",Mar:"Már",Apr:"Ápr",May_short:"Május",Jun:"Jún",Jul:"Júl",Sep:"Szep",Oct:"Okt","Fraction part is invalid.":"Érvénytelen törtrész.","Second fraction part is invalid.":"A második törtrész érvénytelen.",d_dates:"n",h_dates:"ó",m_dates:"hó",s_dates:"s","Restore Size":"Méret Visszaállítása",Supermillennium:"Szuperévezred",Millennium:"Évezred",Submillennium:"Szubévezred","Grand Supercycle":"Nagy Szuperciklus",Supercycle:"Szuperciklus",Cycle:"Ciklus",Primary:"Elsődleges",Intermediate:"Közbülső",Minor_wave:"Kicsi",Minute_wave:"Perc",Minuette:"Menüett",Subminuette:"Szubminüett",Micro:"Mikro",Submicro:"Szubmikro","Left Shoulder":"Bal Váll","Right Shoulder":"Jobb Váll",Head:"Fej","XABCD Pattern":"XABCD Minta","ABCD Pattern":"ABCD Minta","Arrow Mark Down":"Nyíl Lefelé","Arrow Mark Left":"Nyíl Balra","Arrow Mark Right":"Nyíl Jobbra","Arrow Mark Up":"Nyíl Felfelé","Bars Pattern":"Bár Minta","Double Curve":"Dupla Görbe",Curve:"Görbe",Callout:"Kiemelő","Cyclic Lines":"Ciklikus Vonalak","Cypher Pattern":"Rejtjel Minta","Date and Price Range":"Dátum és Árfolyamtartomány","Elliott Correction Wave (ABC)":"Elliot Korrekciós Hullám (ABC)","Elliott Double Combo Wave (WXY)":"Elliott Dupla Kombinációs Hullám (WXY)","Elliott Impulse Wave (12345)":"Elliott Impulzushullám (12345)","Elliott Triangle Wave (ABCDE)":"Elliott Háromszög Hullám (ABCDE)","Elliott Triple Combo Wave (WXYXZ)":"Elliott Tripla Kombinációs Hullám (WXYXZ)","Fib Channel":"Fib Csatorna","Fib Spiral":"Fib Spirál","Fib Wedge":"Fib Ék","Flag Mark":"Zászló Jel","Flat Top/Bottom":"Lapos Felső/Alsó","Horizontal Ray":"Vízszintes Sugár","Inside Pitchfork":"Belső Villa",Note:"Megjegyzés","Anchored Note":"Horgony Megjegyzés","Price Range":"Ártartomány",Projection:"Vetület","Regression Trend":"Regresszió Trend","Long Position":"Long Pozíció","Short Position":"Short Pozíció","Rotated Rectangle":"Elforgatott Téglalap","Modified Schiff Pitchfork":"Módosított Schiff Villa","Sine Line":"Szinuszvonal","Three Drives Pattern":"Három Hajtás Minta","Time Cycles":"Ciklusidők","Trend Angle":"Trendszög","Trend-Based Fib Time":"Trendalapú Fib Idő","Triangle Pattern":"Háromszög Minta","Ghost Feed":"Ghost Hírfolyam",T_interval_short:"T",s_interval_short:"s",R_interval_short:"R",tick:"ticks",week:"weeks",second:"seconds",minute:"perc",range:"ranges",O_in_legend:"Ny",H_in_legend:"Max",L_in_legend:"Min",C_in_legend:"Z",HL2_in_legend:"HL2",HLC3_in_legend:"HLC3",OHLC4_in_legend:"OHLC4","loading...":"töltés...",Circle:"Kör",Square:"Négyzet","charts by TradingView":"TradingView chartok","powered by TradingView":"támogatta a TradingView","Change interval":"Intervallum Váltás","Not applicable":"Nem alkalmazható","Do you really want to delete Chart Layout '{name}' ?":"Biztos, hogy törölni akarod ezt a chart elrendezést: {name}?","Confirm Remove Study Tree":"Tanulmányfa Eltávolításának Jóváhagyása","Do you really want to delete study and all of it's children?":"Biztos, hogy törölni akarod a tanulmányt?",Closed:"Záró","Change symbol":"Szimbólum módosítása","Symbol Name":"Szimbólum Neve","Symbol Description":"Szimbólum Leírás","Point Value":"Pontérték","Listed Exchange":"Listázott Tőzsde","Pip Size":"Pip Méret","just now":"épp most","in %s_time_range":"%s múlva","%s ago_time_range":"ennyivel korábban: %s","%d minute":"%d perc","an hour":"egy óra","%d hour":"%d óra","a day":"egy nap","%d day":"%d nap","a month":"egy hónap","%d month":"%d hónap","a year":"egy év","%d year":"%d years",Sunday:"Vasárnap",Monday:"Hétfő",Tuesday:"Kedd",Wednesday:"Szerda",Thursday:"Csütörtök",Friday:"Péntek",Saturday:"Szombat",Sun:"Vas",Mon:"Hét",Tue:"Ke",Wed:"Szer",Thu:"Cs",Fri:"Pén",Sat:"Szom",Su_day_of_week:"V",Mo_day_of_week:"H",Tu_day_of_week:"K",We_day_of_week:"Sze",Th_day_of_week:"Cs",Fr_day_of_week:"P",Sa_day_of_week:"Szo",Light_colorThemeName:"Light",Dark_colorThemeName:"Dark","Save Theme As":"Téma Mentése Mint","Theme name":"Téma neve","Do you really want to delete Color Theme '{name}' ?":"Biztos, hogy törölni akarod ezt a színtémát: {name}?",Normal:"Normális","Line With Breaks":"Vonal Törésekkel","Step Line":"Lépcső",Histogram:"Hisztogram",Cross_chart_type:"Kereszt","Area With Breaks":"Terület Törésekkel",Columns:"Oszlopok",Circles:"Körök","No indicators matched your criteria.":"Egyetlen indikátor se felel meg a kritériumoknak.","Visual order":"Vizuális Elrendezés","Stop syncing":"Szinkronizálás leállítása","Sync to all charts":"Szinkronizálás minden charta","{symbolsCount} symbol_symbols_and_drawings_count":"{symbolsCount} symbols","with {drawingsCount} drawing_symbols_and_drawings_count":"with {drawingsCount} drawings","{drawingsCount} drawing":"{drawingsCount} drawings","No drawings yet":"Nincs még rajz","Add Symbol":"Szimbólum Hozzáadása","Confirm Inputs":"Inputok Megerősítése","Reset Settings":"Alapbeállítások Visszaállítása","Save As Default":"Mentés Alapértelmezettként",Defaults:"Alapértelmezettek","Apply Defaults":"Alapértelmezett Alkalmazása","Add Custom Color_Color Picker":"Add Custom Color","Opacity_Color Picker":"Opacity","Add_Color Picker":"Add","recently used_emoji_group":"recently used","smiles & people_emoji_group":"smiles & people","animals & nature_emoji_group":"animals & nature","food & drink_emoji_group":"food & drink",activity_emoji_group:"activity","travel & places_emoji_group":"travel & places",objects_emoji_group:"objects",symbols_emoji_group:"symbols",flags_emoji_group:"flags"}</script></body></html>