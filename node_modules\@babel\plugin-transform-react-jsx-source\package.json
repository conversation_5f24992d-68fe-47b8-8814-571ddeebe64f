{"name": "@babel/plugin-transform-react-jsx-source", "version": "7.19.6", "description": "Add a __source prop to all JSX Elements", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-react-jsx-source"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx-source", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.19.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.19.6", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-jsx": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}