{"version": 3, "file": "gen-mapping.umd.js", "sources": ["../../src/gen-mapping.ts"], "sourcesContent": ["import { SetArray, put } from '@jridgewell/set-array';\nimport { encode } from '@jridgewell/sourcemap-codec';\n\nimport type { SourceMapSegment } from './sourcemap-segment';\nimport type { DecodedSourceMap, EncodedSourceMap, Pos, Mapping } from './types';\n\nexport type { DecodedSourceMap, EncodedSourceMap, Mapping };\n\nexport type Options = {\n  file?: string | null;\n  sourceRoot?: string | null;\n};\n\n/**\n * A low-level API to associate a generated position with an original source position. Line and\n * column here are 0-based, unlike `addMapping`.\n */\nexport let addSegment: {\n  (\n    map: GenMapping,\n    genLine: number,\n    genColumn: number,\n    source?: null,\n    sourceLine?: null,\n    sourceColumn?: null,\n    name?: null,\n  ): void;\n  (\n    map: GenMapping,\n    genLine: number,\n    genColumn: number,\n    source: string,\n    sourceLine: number,\n    sourceColumn: number,\n    name?: null,\n  ): void;\n  (\n    map: GenMapping,\n    genLine: number,\n    genColumn: number,\n    source: string,\n    sourceLine: number,\n    sourceColumn: number,\n    name: string,\n  ): void;\n};\n\n/**\n * A high-level API to associate a generated position with an original source position. Line is\n * 1-based, but column is 0-based, due to legacy behavior in `source-map` library.\n */\nexport let addMapping: {\n  (\n    map: GenMapping,\n    mapping: {\n      generated: Pos;\n      source?: null;\n      original?: null;\n      name?: null;\n    },\n  ): void;\n  (\n    map: GenMapping,\n    mapping: {\n      generated: Pos;\n      source: string;\n      original: Pos;\n      name?: null;\n    },\n  ): void;\n  (\n    map: GenMapping,\n    mapping: {\n      generated: Pos;\n      source: string;\n      original: Pos;\n      name: string;\n    },\n  ): void;\n};\n\n/**\n * Adds/removes the content of the source file to the source map.\n */\nexport let setSourceContent: (map: GenMapping, source: string, content: string | null) => void;\n\n/**\n * Returns a sourcemap object (with decoded mappings) suitable for passing to a library that expects\n * a sourcemap, or to JSON.stringify.\n */\nexport let decodedMap: (map: GenMapping) => DecodedSourceMap;\n\n/**\n * Returns a sourcemap object (with encoded mappings) suitable for passing to a library that expects\n * a sourcemap, or to JSON.stringify.\n */\nexport let encodedMap: (map: GenMapping) => EncodedSourceMap;\n\n/**\n * Returns an array of high-level mapping objects for every recorded segment, which could then be\n * passed to the `source-map` library.\n */\nexport let allMappings: (map: GenMapping) => Mapping[];\n\n/**\n * Provides the state to generate a sourcemap.\n */\nexport class GenMapping {\n  private _names = new SetArray();\n  private _sources = new SetArray();\n  private _sourcesContent: (string | null)[] = [];\n  private _mappings: SourceMapSegment[][] = [];\n  declare file: string | null | undefined;\n  declare sourceRoot: string | null | undefined;\n\n  constructor({ file, sourceRoot }: Options = {}) {\n    this.file = file;\n    this.sourceRoot = sourceRoot;\n  }\n\n  static {\n    addSegment = (map, genLine, genColumn, source, sourceLine, sourceColumn, name) => {\n      const {\n        _mappings: mappings,\n        _sources: sources,\n        _sourcesContent: sourcesContent,\n        _names: names,\n      } = map;\n\n      const line = getLine(mappings, genLine);\n      if (source == null) {\n        const seg: SourceMapSegment = [genColumn];\n        const index = getColumnIndex(line, genColumn, seg);\n        return insert(line, index, seg);\n      }\n\n      // Sigh, TypeScript can't figure out sourceLine and sourceColumn aren't nullish if source\n      // isn't nullish.\n      assert<number>(sourceLine);\n      assert<number>(sourceColumn);\n      const sourcesIndex = put(sources, source);\n      const seg: SourceMapSegment = name\n        ? [genColumn, sourcesIndex, sourceLine, sourceColumn, put(names, name)]\n        : [genColumn, sourcesIndex, sourceLine, sourceColumn];\n\n      const index = getColumnIndex(line, genColumn, seg);\n      if (sourcesIndex === sourcesContent.length) sourcesContent[sourcesIndex] = null;\n      insert(line, index, seg);\n    };\n\n    addMapping = (map, mapping) => {\n      const { generated, source, original, name } = mapping;\n      return (addSegment as any)(\n        map,\n        generated.line - 1,\n        generated.column,\n        source,\n        original == null ? undefined : original.line - 1,\n        original?.column,\n        name,\n      );\n    };\n\n    setSourceContent = (map, source, content) => {\n      const { _sources: sources, _sourcesContent: sourcesContent } = map;\n      sourcesContent[put(sources, source)] = content;\n    };\n\n    decodedMap = (map) => {\n      const {\n        file,\n        sourceRoot,\n        _mappings: mappings,\n        _sources: sources,\n        _sourcesContent: sourcesContent,\n        _names: names,\n      } = map;\n\n      return {\n        version: 3,\n        file,\n        names: names.array,\n        sourceRoot: sourceRoot || undefined,\n        sources: sources.array,\n        sourcesContent,\n        mappings,\n      };\n    };\n\n    encodedMap = (map) => {\n      const decoded = decodedMap(map);\n      return {\n        ...decoded,\n        mappings: encode(decoded.mappings as SourceMapSegment[][]),\n      };\n    };\n\n    allMappings = (map) => {\n      const out: Mapping[] = [];\n      const { _mappings: mappings, _sources: sources, _names: names } = map;\n\n      for (let i = 0; i < mappings.length; i++) {\n        const line = mappings[i];\n        for (let j = 0; j < line.length; j++) {\n          const seg = line[j];\n\n          const generated = { line: i + 1, column: seg[0] };\n          let source: string | undefined = undefined;\n          let original: Pos | undefined = undefined;\n          let name: string | undefined = undefined;\n\n          if (seg.length !== 1) {\n            source = sources.array[seg[1]];\n            original = { line: seg[2] + 1, column: seg[3] };\n\n            if (seg.length === 5) name = names.array[seg[4]];\n          }\n\n          out.push({ generated, source, original, name } as Mapping);\n        }\n      }\n\n      return out;\n    };\n  }\n}\n\nfunction assert<T>(_val: unknown): asserts _val is T {\n  // noop.\n}\n\nfunction getLine(mappings: SourceMapSegment[][], index: number): SourceMapSegment[] {\n  for (let i = mappings.length; i <= index; i++) {\n    mappings[i] = [];\n  }\n  return mappings[index];\n}\n\nfunction getColumnIndex(line: SourceMapSegment[], column: number, seg: SourceMapSegment): number {\n  let index = line.length;\n  for (let i = index - 1; i >= 0; i--, index--) {\n    const current = line[i];\n    const col = current[0];\n    if (col > column) continue;\n    if (col < column) break;\n\n    const cmp = compare(current, seg);\n    if (cmp === 0) return index;\n    if (cmp < 0) break;\n  }\n  return index;\n}\n\nfunction compare(a: SourceMapSegment, b: SourceMapSegment): number {\n  let cmp = compareNum(a.length, b.length);\n  if (cmp !== 0) return cmp;\n\n  // We've already checked genColumn\n  if (a.length === 1) return 0;\n\n  cmp = compareNum(a[1], b[1]!);\n  if (cmp !== 0) return cmp;\n  cmp = compareNum(a[2], b[2]!);\n  if (cmp !== 0) return cmp;\n  cmp = compareNum(a[3], b[3]!);\n  if (cmp !== 0) return cmp;\n\n  if (a.length === 4) return 0;\n  return compareNum(a[4], b[4]!);\n}\n\nfunction compareNum(a: number, b: number): number {\n  return a - b;\n}\n\nfunction insert<T>(array: T[], index: number, value: T) {\n  if (index === -1) return;\n  for (let i = array.length; i > index; i--) {\n    array[i] = array[i - 1];\n  }\n  array[index] = value;\n}\n"], "names": ["addSegment", "addMapping", "setSourceContent", "decodedMap", "encodedMap", "allMappings", "SetArray", "put", "encode"], "mappings": ";;;;;;IAaA;;;IAGG;AACQA,gCA4BT;IAEF;;;IAGG;AACQC,gCA4BT;IAEF;;IAEG;AACQC,sCAAoF;IAE/F;;;IAGG;AACQC,gCAAkD;IAE7D;;;IAGG;AACQC,gCAAkD;IAE7D;;;IAGG;AACQC,iCAA4C;IAEvD;;IAEG;UACU,UAAU,CAAA;IAQrB,IAAA,WAAA,CAAY,EAAE,IAAI,EAAE,UAAU,KAAc,EAAE,EAAA;IAPtC,QAAA,IAAA,CAAA,MAAM,GAAG,IAAIC,iBAAQ,EAAE,CAAC;IACxB,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAIA,iBAAQ,EAAE,CAAC;YAC1B,IAAe,CAAA,eAAA,GAAsB,EAAE,CAAC;YACxC,IAAS,CAAA,SAAA,GAAyB,EAAE,CAAC;IAK3C,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;SAC9B;IA2GF,CAAA;IAzGC,CAAA,MAAA;IACE,IAAAN,kBAAU,GAAG,CAAC,GAAG,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,KAAI;IAC/E,QAAA,MAAM,EACJ,SAAS,EAAE,QAAQ,EACnB,QAAQ,EAAE,OAAO,EACjB,eAAe,EAAE,cAAc,EAC/B,MAAM,EAAE,KAAK,GACd,GAAG,GAAG,CAAC;YAER,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACxC,IAAI,MAAM,IAAI,IAAI,EAAE;IAClB,YAAA,MAAM,GAAG,GAAqB,CAAC,SAAS,CAAC,CAAC;gBAC1C,MAAM,KAAK,GAAG,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;gBACnD,OAAO,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;IACjC,SAAA;YAMD,MAAM,YAAY,GAAGO,YAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAC1C,MAAM,GAAG,GAAqB,IAAI;IAChC,cAAE,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAEA,YAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;kBACrE,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;YAExD,MAAM,KAAK,GAAG,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;IACnD,QAAA,IAAI,YAAY,KAAK,cAAc,CAAC,MAAM;IAAE,YAAA,cAAc,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC;IAChF,QAAA,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;IAC3B,KAAC,CAAC;IAEF,IAAAN,kBAAU,GAAG,CAAC,GAAG,EAAE,OAAO,KAAI;YAC5B,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;IACtD,QAAA,OAAQD,kBAAkB,CACxB,GAAG,EACH,SAAS,CAAC,IAAI,GAAG,CAAC,EAClB,SAAS,CAAC,MAAM,EAChB,MAAM,EACN,QAAQ,IAAI,IAAI,GAAG,SAAS,GAAG,QAAQ,CAAC,IAAI,GAAG,CAAC,EAChD,QAAQ,KAAR,IAAA,IAAA,QAAQ,uBAAR,QAAQ,CAAE,MAAM,EAChB,IAAI,CACL,CAAC;IACJ,KAAC,CAAC;QAEFE,wBAAgB,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,KAAI;YAC1C,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,eAAe,EAAE,cAAc,EAAE,GAAG,GAAG,CAAC;YACnE,cAAc,CAACK,YAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC;IACjD,KAAC,CAAC;IAEF,IAAAJ,kBAAU,GAAG,CAAC,GAAG,KAAI;YACnB,MAAM,EACJ,IAAI,EACJ,UAAU,EACV,SAAS,EAAE,QAAQ,EACnB,QAAQ,EAAE,OAAO,EACjB,eAAe,EAAE,cAAc,EAC/B,MAAM,EAAE,KAAK,GACd,GAAG,GAAG,CAAC;YAER,OAAO;IACL,YAAA,OAAO,EAAE,CAAC;gBACV,IAAI;gBACJ,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,UAAU,EAAE,UAAU,IAAI,SAAS;gBACnC,OAAO,EAAE,OAAO,CAAC,KAAK;gBACtB,cAAc;gBACd,QAAQ;aACT,CAAC;IACJ,KAAC,CAAC;IAEF,IAAAC,kBAAU,GAAG,CAAC,GAAG,KAAI;IACnB,QAAA,MAAM,OAAO,GAAGD,kBAAU,CAAC,GAAG,CAAC,CAAC;YAChC,OACK,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,OAAO,CACV,EAAA,EAAA,QAAQ,EAAEK,qBAAM,CAAC,OAAO,CAAC,QAAgC,CAAC,EAC1D,CAAA,CAAA;IACJ,KAAC,CAAC;IAEF,IAAAH,mBAAW,GAAG,CAAC,GAAG,KAAI;YACpB,MAAM,GAAG,GAAc,EAAE,CAAC;IAC1B,QAAA,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC;IAEtE,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACxC,YAAA,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACzB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACpC,gBAAA,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAEpB,gBAAA,MAAM,SAAS,GAAG,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;oBAClD,IAAI,MAAM,GAAuB,SAAS,CAAC;oBAC3C,IAAI,QAAQ,GAAoB,SAAS,CAAC;oBAC1C,IAAI,IAAI,GAAuB,SAAS,CAAC;IAEzC,gBAAA,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;wBACpB,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,oBAAA,QAAQ,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;IAEhD,oBAAA,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC;4BAAE,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD,iBAAA;IAED,gBAAA,GAAG,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAa,CAAC,CAAC;IAC5D,aAAA;IACF,SAAA;IAED,QAAA,OAAO,GAAG,CAAC;IACb,KAAC,CAAC;IACJ,CAAC,GAAA,CAAA;IAOH,SAAS,OAAO,CAAC,QAA8B,EAAE,KAAa,EAAA;IAC5D,IAAA,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE;IAC7C,QAAA,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;IAClB,KAAA;IACD,IAAA,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC;IAED,SAAS,cAAc,CAAC,IAAwB,EAAE,MAAc,EAAE,GAAqB,EAAA;IACrF,IAAA,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;IACxB,IAAA,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE;IAC5C,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IACxB,QAAA,MAAM,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YACvB,IAAI,GAAG,GAAG,MAAM;gBAAE,SAAS;YAC3B,IAAI,GAAG,GAAG,MAAM;gBAAE,MAAM;YAExB,MAAM,GAAG,GAAG,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YAClC,IAAI,GAAG,KAAK,CAAC;IAAE,YAAA,OAAO,KAAK,CAAC;YAC5B,IAAI,GAAG,GAAG,CAAC;gBAAE,MAAM;IACpB,KAAA;IACD,IAAA,OAAO,KAAK,CAAC;IACf,CAAC;IAED,SAAS,OAAO,CAAC,CAAmB,EAAE,CAAmB,EAAA;IACvD,IAAA,IAAI,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,GAAG,KAAK,CAAC;IAAE,QAAA,OAAO,GAAG,CAAC;;IAG1B,IAAA,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC;IAAE,QAAA,OAAO,CAAC,CAAC;IAE7B,IAAA,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;QAC9B,IAAI,GAAG,KAAK,CAAC;IAAE,QAAA,OAAO,GAAG,CAAC;IAC1B,IAAA,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;QAC9B,IAAI,GAAG,KAAK,CAAC;IAAE,QAAA,OAAO,GAAG,CAAC;IAC1B,IAAA,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;QAC9B,IAAI,GAAG,KAAK,CAAC;IAAE,QAAA,OAAO,GAAG,CAAC;IAE1B,IAAA,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC;IAAE,QAAA,OAAO,CAAC,CAAC;IAC7B,IAAA,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;IACjC,CAAC;IAED,SAAS,UAAU,CAAC,CAAS,EAAE,CAAS,EAAA;QACtC,OAAO,CAAC,GAAG,CAAC,CAAC;IACf,CAAC;IAED,SAAS,MAAM,CAAI,KAAU,EAAE,KAAa,EAAE,KAAQ,EAAA;QACpD,IAAI,KAAK,KAAK,CAAC,CAAC;YAAE,OAAO;IACzB,IAAA,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YACzC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACzB,KAAA;IACD,IAAA,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;IACvB;;;;;;;;;;"}