(window.webpackJsonp=window.webpackJsonp||[]).push([["chart-widget-gui"],{"+xKI":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 7 7" width="7" height="7"><path fill="currentColor" d="M3.5 7L7 4H4V0H3V4H0L3.5 7Z"/></svg>'},"/NcV":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14" height="14"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M5.5 1.5l5 5.5-5 5.5"/></svg>'},"0jws":function(e,t,s){e.exports={blockHidden:"blockHidden-xPfK7aM7","pane-button":"pane-button-xPfK7aM7"}},"1INk":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 7 7" width="7" height="7"><path fill="currentColor" d="M3.5 0L0 3h3v4h1V3h3L3.5 0z"/></svg>'},"1Wf8":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M3.5 2.8a.7.7 0 0 0-.7.7V6H1.2V3.5a2.3 2.3 0 0 1 2.3-2.3h11a2.3 2.3 0 0 1 2.3 2.3V6h-1.6V3.5a.7.7 0 0 0-.7-.7h-11z" class="bracket-up"/><path fill="currentColor" d="M3.5 15.2a.7.7 0 0 1-.7-.7V12H1.2v2.5a2.3 2.3 0 0 0 2.3 2.3h11a2.3 2.3 0 0 0 2.3-2.3V12h-1.6v2.5a.7.7 0 0 1-.7.7h-11z" class="bracket-down"/></svg>'},"2CEX":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 22" width="24" height="22" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M6 6.5A2.5 2.5 0 0 1 8.5 4H10v1H8.5C7.67 5 7 5.67 7 6.5v1.15a3.5 3.5 0 0 1-1.93 3.13l-.45.22.45.22A3.5 3.5 0 0 1 7 14.35v1.15c0 .83.67 1.5 1.5 1.5H10v1H8.5A2.5 2.5 0 0 1 6 15.5v-1.15a2.5 2.5 0 0 0-1.38-2.23l-1.34-.67a.5.5 0 0 1 0-.9l1.34-.67A2.5 2.5 0 0 0 6 7.65V6.5zM15.5 5H14V4h1.5A2.5 2.5 0 0 1 18 6.5v1.15c0 .94.54 1.8 1.38 2.23l1.34.67a.5.5 0 0 1 0 .9l-1.34.67A2.5 2.5 0 0 0 18 14.35v1.15a2.5 2.5 0 0 1-2.5 2.5H14v-1h1.5c.83 0 1.5-.67 1.5-1.5v-1.15a3.5 3.5 0 0 1 1.93-3.13l.45-.22-.45-.22A3.5 3.5 0 0 1 17 7.65V6.5c0-.83-.67-1.5-1.5-1.5z"/></svg>'},"4nwx":function(e,t,s){"use strict";s.r(t),s.d(t,"monthsFullNames",(function(){return l})),s.d(t,"monthsShortNames",(function(){return n})),s.d(t,"weekDaysFullNames",(function(){return a})),s.d(t,"weekDaysShortNames",(function(){return r})),s.d(t,"weekDaysMiniNames",(function(){return d}));var i=s("YFKU"),o=s("99ZO");const l={[o.Months.JANUARY]:Object(i.t)("January"),[o.Months.FEBRUARY]:Object(i.t)("February"),[o.Months.MARCH]:Object(i.t)("March"),[o.Months.APRIL]:Object(i.t)("April"),[o.Months.MAY]:Object(i.t)("May"),[o.Months.JUNE]:Object(i.t)("June"),[o.Months.JULY]:Object(i.t)("July"),[o.Months.AUGUST]:Object(i.t)("August"),[o.Months.SEPTEMBER]:Object(i.t)("September"),[o.Months.OCTOBER]:Object(i.t)("October"),[o.Months.NOVEMBER]:Object(i.t)("November"),[o.Months.DECEMBER]:Object(i.t)("December")},n={[o.Months.JANUARY]:Object(i.t)("Jan"),[o.Months.FEBRUARY]:Object(i.t)("Feb"),[o.Months.MARCH]:Object(i.t)("Mar"),[o.Months.APRIL]:Object(i.t)("Apr"),[o.Months.MAY]:Object(i.t)("May",{context:"short"}),[o.Months.JUNE]:Object(i.t)("Jun"),[o.Months.JULY]:Object(i.t)("Jul"),
[o.Months.AUGUST]:Object(i.t)("Aug"),[o.Months.SEPTEMBER]:Object(i.t)("Sep"),[o.Months.OCTOBER]:Object(i.t)("Oct"),[o.Months.NOVEMBER]:Object(i.t)("Nov"),[o.Months.DECEMBER]:Object(i.t)("Dec")},a={[o.WeekDays.SUNDAY]:Object(i.t)("Sunday"),[o.WeekDays.MONDAY]:Object(i.t)("Monday"),[o.WeekDays.TUESDAY]:Object(i.t)("Tuesday"),[o.WeekDays.WEDNESDAY]:Object(i.t)("Wednesday"),[o.WeekDays.THURSDAY]:Object(i.t)("Thursday"),[o.WeekDays.FRIDAY]:Object(i.t)("Friday"),[o.WeekDays.SATURDAY]:Object(i.t)("Saturday")},r={[o.WeekDays.SUNDAY]:Object(i.t)("Sun"),[o.WeekDays.MONDAY]:Object(i.t)("Mon"),[o.WeekDays.TUESDAY]:Object(i.t)("Tue"),[o.WeekDays.WEDNESDAY]:Object(i.t)("Wed"),[o.WeekDays.THURSDAY]:Object(i.t)("Thu"),[o.WeekDays.FRIDAY]:Object(i.t)("Fri"),[o.WeekDays.SATURDAY]:Object(i.t)("Sat")},d={[o.WeekDays.SUNDAY]:Object(i.t)("Su",{context:"day_of_week"}),[o.WeekDays.MONDAY]:Object(i.t)("Mo",{context:"day_of_week"}),[o.WeekDays.TUESDAY]:Object(i.t)("Tu",{context:"day_of_week"}),[o.WeekDays.WEDNESDAY]:Object(i.t)("We",{context:"day_of_week"}),[o.WeekDays.THURSDAY]:Object(i.t)("Th",{context:"day_of_week"}),[o.WeekDays.FRIDAY]:Object(i.t)("Fr",{context:"day_of_week"}),[o.WeekDays.SATURDAY]:Object(i.t)("Sa",{context:"day_of_week"})}},"5Alx":function(e,t,s){e.exports={marginlegendhoriz:"4px",legend:"legend-2KhwsEwE",item:"item-2KhwsEwE",withAction:"withAction-2KhwsEwE",selected:"selected-2KhwsEwE",last:"last-2KhwsEwE",text:"text-2KhwsEwE",noWrapWrapper:"noWrapWrapper-2KhwsEwE",noWrap:"noWrap-2KhwsEwE",series:"series-2KhwsEwE",valuesAdditionalWrapper:"valuesAdditionalWrapper-2KhwsEwE",valueItem:"valueItem-2KhwsEwE",valueTitle:"valueTitle-2KhwsEwE",valueValue:"valueValue-2KhwsEwE",valuesWrapper:"valuesWrapper-2KhwsEwE",directionColumn:"directionColumn-2KhwsEwE",titleWrapper:"titleWrapper-2KhwsEwE",button:"button-2KhwsEwE",statusesWrapper:"statusesWrapper-2KhwsEwE",buttonsWrapper:"buttonsWrapper-2KhwsEwE",buttons:"buttons-2KhwsEwE",noActions:"noActions-2KhwsEwE",title:"title-2KhwsEwE",title2nd:"title2nd-2KhwsEwE",disabled:"disabled-2KhwsEwE",disabledOnInterval:"disabledOnInterval-2KhwsEwE",withCustomTextColor:"withCustomTextColor-2KhwsEwE",study:"study-2KhwsEwE",title1st:"title1st-2KhwsEwE",hideValues:"hideValues-2KhwsEwE",has5Buttons:"has5Buttons-2KhwsEwE",stayInHoveredMode:"stayInHoveredMode-2KhwsEwE",withTail:"withTail-2KhwsEwE",loading:"loading-2KhwsEwE",loader:"loader-2KhwsEwE",withDot:"withDot-2KhwsEwE",title3rd:"title3rd-2KhwsEwE",title4th:"title4th-2KhwsEwE",miniHidden2Title:"miniHidden2Title-2KhwsEwE",microHidden2Title:"microHidden2Title-2KhwsEwE",hidden3Title:"hidden3Title-2KhwsEwE",flagged:"flagged-2KhwsEwE",medium:"medium-2KhwsEwE",minimized:"minimized-2KhwsEwE",micro:"micro-2KhwsEwE",onlyOneButtonCanBeStick:"onlyOneButtonCanBeStick-2KhwsEwE",touchMode:"touchMode-2KhwsEwE",buttonIcon:"buttonIcon-2KhwsEwE",flag:"flag-2KhwsEwE",invisibleHover:"invisibleHover-2KhwsEwE",eye:"eye-2KhwsEwE",hiddenLoading:"hiddenLoading-2KhwsEwE","eye-animation":"eye-animation-2KhwsEwE",markerContainer:"markerContainer-2KhwsEwE",
flagWrapper:"flagWrapper-2KhwsEwE",sourcesWrapper:"sourcesWrapper-2KhwsEwE",newCollapser:"newCollapser-2KhwsEwE",sources:"sources-2KhwsEwE",toggler:"toggler-2KhwsEwE pane-button-xPfK7aM7",counter:"counter-2KhwsEwE",iconArrow:"iconArrow-2KhwsEwE",objectTree:"objectTree-2KhwsEwE",onlyOneSourceShown:"onlyOneSourceShown-2KhwsEwE",closed:"closed-2KhwsEwE",objectsTreeCanBeShown:"objectsTreeCanBeShown-2KhwsEwE"}},"61S9":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 22" width="30" height="24" fill="none"><g fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" class="normal-eye"><path d="M18 7.91C16.7 6.5 14.7 5 12 5S7.3 6.49 6 7.91C6 7.91 4 10 4 11s2 3.09 2 3.09C7.3 15.5 9.3 17 12 17s4.7-1.49 6-2.91c0 0 2-2.09 2-3.09s-2-3.09-2-3.09zm-11.26 5.5C7.94 14.74 9.7 16 12 16s4.05-1.26 5.25-2.59c0 0 1.75-1.91 1.75-2.41 0-.5-1.75-2.41-1.75-2.41C16.05 7.26 14.3 6 12 6S7.95 7.26 6.74 8.59C6.74 8.59 5 10.5 5 11c0 .5 1.74 2.41 1.74 2.41z"/><path d="M12 13a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm0 1a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/></g><g fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" class="crossed-eye"><path d="M8.85 16.27c.92.44 1.97.73 3.15.73 2.7 0 4.7-1.49 6-2.91 0 0 2-2.09 2-3.09s-2-3.09-2-3.09l-.39-.4-.7.7.34.38S19 10.5 19 11c0 .5-1.75 2.41-1.75 2.41C16.05 14.74 14.3 16 12 16c-.88 0-1.68-.18-2.4-.48l-.75.75zM7.1 13.78l-.36-.37S5 11.5 5 11c0-.5 1.74-2.41 1.74-2.41C7.94 7.26 9.7 6 12 6c.88 0 1.68.18 2.4.48l.75-.75A7.17 7.17 0 0 0 12 5C9.3 5 7.3 6.49 6 7.91 6 7.91 4 10 4 11s2 3.09 2 3.09l.39.4.7-.7z"/><path d="M11.22 13.9a3 3 0 0 0 3.68-3.68l-.9.9A2 2 0 0 1 12.13 13l-.9.9zm.66-4.9A2 2 0 0 0 10 10.88l-.9.9a3 3 0 0 1 3.68-3.68l-.9.9zM5.65 16.65l12-12 .7.7-12 12-.7-.7z"/></g><g class="loading-eye"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M18 7.91C16.7 6.5 14.7 5 12 5S7.3 6.49 6 7.91C6 7.91 4 10 4 11s2 3.09 2 3.09C7.3 15.5 9.3 17 12 17s4.7-1.49 6-2.91c0 0 2-2.09 2-3.09s-2-3.09-2-3.09zm-11.26 5.5C7.94 14.74 9.7 16 12 16s4.05-1.26 5.25-2.59c0 0 1.75-1.91 1.75-2.41 0-.5-1.75-2.41-1.75-2.41C16.05 7.26 14.3 6 12 6S7.95 7.26 6.74 8.59C6.74 8.59 5 10.5 5 11c0 .5 1.74 2.41 1.74 2.41z"/></g><g class="animated-loading-eye"><path stroke="currentColor" stroke-linecap="round" d="M14.5 11a2.5 2.5 0 1 0-2.5 2.5"/></g></svg>'},"6dGu":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 15" width="15" height="15"><path fill="currentColor" d="M3.5 5.58c.24-.28.65-.3.92-.07L7.5 8.14l3.08-2.63a.65.65 0 1 1 .84.98L7.5 9.86 3.58 6.49a.65.65 0 0 1-.07-.91z"/></svg>'},"94TV":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14" height="14"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M6.5 1.5l5 5.5-5 5.5M3 4l2.5 3L3 10"/></svg>'},"956S":function(e,t){
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><circle fill="currentColor" cx="15" cy="9" r="1.5"/><circle fill="currentColor" cx="9" cy="9" r="1.5"/><circle fill="currentColor" cx="3" cy="9" r="1.5"/></svg>'},"9Crk":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M13.4 5.9c-.41 1.62-1.16 2.43-2.25 2.43-.52 0-1.25-.15-2.2-.45-.93-.3-1.58-.45-1.96-.45-.55 0-.98.3-1.27.9H4.66c.1-.67.36-1.24.76-1.71.4-.48.86-.72 1.4-.72.56 0 1.31.16 2.27.46.95.3 1.62.45 2.01.45.64 0 1.06-.3 1.27-.9h1.03zm0 3.87c-.41 1.62-1.16 2.43-2.25 2.43-.52 0-1.25-.15-2.2-.45-.93-.3-1.58-.46-1.96-.46-.55 0-.98.3-1.27.9H4.66c.1-.67.36-1.24.76-1.7.4-.48.86-.72 1.4-.72.56 0 1.31.15 2.27.46.95.3 1.62.44 2.01.44.64 0 1.06-.3 1.27-.9h1.03z"/></svg>'},"9lPX":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><rect width="10" height="4" fill="currentColor" rx="2" x="4" y="7"/></svg>'},AH3n:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M13 12.78V5.22a.3.3 0 0 0-.51-.2L8.7 8.78a.3.3 0 0 0 0 .42L12.5 13a.3.3 0 0 0 .51-.21zM8 12.78V5.22a.3.3 0 0 0-.51-.2L3.7 8.78a.3.3 0 0 0 0 .42L7.5 13a.3.3 0 0 0 .51-.21z"/></svg>'},AvGy:function(e,t,s){"use strict";s.d(t,"a",(function(){return o}));var i=s("qFKp");const o=i.CheckMobile.any()},D8x7:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 22" width="24" height="22" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M17.35 6.35l-10 10-.7-.7 10-10 .7.7z"/><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M6.65 6.35l10 10 .7-.7-10-10-.7.7z"/></svg>'},EV8o:function(e,t,s){"use strict";s.d(t,"a",(function(){return o}));var i=s("ogJP");function o(e,t,s,o,l){const n=document.createElement("div");n.className=t,n.classList.toggle(o,!e.visible.value()),Object.assign(n.dataset,e.dataset),void 0!==e.className&&n.classList.add(e.className),void 0!==e.title&&(n.classList.add("apply-common-tooltip"),n.setAttribute("title",e.title.value()),void 0!==e.hotKeyTitle&&(n.dataset.tooltipHotkey=e.hotKeyTitle)),n.addEventListener("touchend",e.action),n.addEventListener("mousedown",t=>{0===t.button&&e.action(t)});const a=document.createElement("div");a.classList.add(s);const r=e.iconMap.get(l)||"";return Object(i.isString)(r)?a.innerHTML=r:a.appendChild(r),n.appendChild(a),n}},G2LI:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M12.57 5.5h-.07a3.5 3.5 0 1 0 .07 7A4.98 4.98 0 0 1 4 9a5 5 0 0 1 8.57-3.5z"/></svg>'},GOhO:function(e,t,s){"use strict";var i=s("+DwS");s("tc+8");var o=s("m/cY");function l(e,...t){const s=()=>e(...t.map(e=>e.value())),i=Object(o.a)(s()),l=()=>i.setValue(s()),n={};for(const e of t)e.subscribe(n,l);return i.destroy=()=>{t.forEach(e=>e.unsubscribeAll(n))},i}
s.d(t,"b",(function(){return i.a})),s.d(t,"a",(function(){return l}))},"Gp/h":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M12.22 11.78A3.47 3.47 0 0 0 9 6.98a3.48 3.48 0 0 0-3.22 4.8 6.97 6.97 0 0 1 6.44 0zM4.18 9.83L2.1 9.28l.33-1.24 2.07.55-.33 1.24zM6.38 6.36l-.9-1.94 1.16-.54.9 1.94-1.16.54zM10.46 5.82l.9-1.94 1.16.54-.9 1.94-1.16-.54zM13.49 8.6l2.07-.56.33 1.24-2.07.55-.33-1.24z"/></svg>'},Jjb7:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M13.5 4.5l-9 9M4.5 4.5l9 9"/></svg>'},JmzL:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M9 4c-.79 0-1.38.7-1.25 1.48l.67 4.03a.59.59 0 0 0 1.16 0l.67-4.03A1.27 1.27 0 0 0 9 4zm0 8a1 1 0 1 0 0 2 1 1 0 0 0 0-2z"/></svg>'},LIcf:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M7.9 5.87v2.17h3.07v1.4H7.9v2.8h4.22v1.46H6.25V4.4h5.94v1.47H7.9z"/></svg>'},LVLx:function(e,t,s){"use strict";(e=>{function t(e){return e instanceof Node?e:document.createTextNode(String(e))}for(const s of e){if(s.hasOwnProperty("append"))return;Object.defineProperty(s,"append",{configurable:!0,enumerable:!0,writable:!0,value:function(...e){if(1===e.length)return void this.appendChild(t(e[0]));const s=document.createDocumentFragment();for(const i of e)s.appendChild(t(i));this.appendChild(s)}})}})([Element.prototype,Document.prototype,DocumentFragment.prototype])},M3mX:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M9 2.5c-1.06 0-1.88.93-1.75 1.98l.63 5.03a1.13 1.13 0 0 0 2.25 0l.62-5.03A1.77 1.77 0 0 0 9 2.5zm0 10a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3z"/></svg>'},MQEA:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 9 7" width="9" height="7"><path fill="currentColor" d="M8.5 3.5L5 0v3H0v1h5v3z"/></svg>'},MjtL:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14" height="14"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M8.5 1.5L3.5 7l5 5.5"/></svg>'},"MyT/":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><circle fill="currentColor" cx="9" cy="9" r="4"/></svg>'},OJSF:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 15" width="15" height="15"><path fill="currentColor" d="M11.65 12.35l-9-9 .7-.7 9 9-.7.7z"/><path fill="currentColor" d="M2.65 11.65l9-9 .7.7-9 9-.7-.7z"/></svg>'},OcaN:function(e,t,s){e.exports={loader:"loader-1pOK1lo2",loaderItem:"loaderItem-1pOK1lo2","loader-animation":"loader-animation-1pOK1lo2",
touchMode:"touchMode-1pOK1lo2"}},PXSR:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 15" width="15" height="15"><path fill="currentColor" d="M11.83 6.12l-.66.76L8 4.1V12H7V4.1L3.83 6.88l-.66-.76L7.5 2.34l4.33 3.78z"/></svg>'},QEZv:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 15" width="15" height="15"><path fill="currentColor" d="M11.83 8.88l-.66-.76L8 10.9V3H7v7.9L3.83 8.12l-.66.76 4.33 3.78 4.33-3.78z"/></svg>'},QkND:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M13.29 4.8h-.09a4.2 4.2 0 1 0 .09 8.4 6 6 0 1 1 0-8.4z"/></svg>'},R5JZ:function(e,t,s){"use strict";function i(e,t,s,i,o){function l(o){if(e>o.timeStamp)return;const l=o.target;void 0!==s&&null!==t&&null!==l&&l.ownerDocument===i&&(t.contains(l)||s(o))}return o.click&&i.addEventListener("click",l,!1),o.mouseDown&&i.addEventListener("mousedown",l,!1),o.touchEnd&&i.addEventListener("touchend",l,!1),o.touchStart&&i.addEventListener("touchstart",l,!1),()=>{i.removeEventListener("click",l,!1),i.removeEventListener("mousedown",l,!1),i.removeEventListener("touchend",l,!1),i.removeEventListener("touchstart",l,!1)}}s.d(t,"a",(function(){return i}))},RgOa:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14" height="14" fill="none"><path fill="currentColor" fill-rule="evenodd" d="M8.63 1.08a2.04 2.04 0 0 1-3.26 0c-.51.14-1 .35-1.45.6l.01.2A2.05 2.05 0 0 1 1.7 3.93a6.1 6.1 0 0 0-.6 1.45 2.04 2.04 0 0 1 0 3.26c.13.51.34 1 .6 1.45l.2-.01a2.05 2.05 0 0 1 2.03 2.24c.45.26.94.47 1.45.6a2.04 2.04 0 0 1 3.26 0c.51-.13 1-.34 1.45-.6l-.01-.2a2.05 2.05 0 0 1 2.24-2.03c.26-.45.47-.94.6-1.45a2.04 2.04 0 0 1 0-3.26 6.1 6.1 0 0 0-.6-1.45 2.05 2.05 0 0 1-2.23-2.23 6.1 6.1 0 0 0-1.45-.6zM7.84.42c.17-.24.43-.47.72-.4.84.18 1.62.5 2.32.96.23.15.26.48.22.76a1.03 1.03 0 0 0 1.16 1.16c.28-.04.6-.01.76.22.45.7.78 1.48.97 2.32.06.29-.17.55-.41.72a1.02 1.02 0 0 0 0 1.68c.24.17.47.43.4.72a7.12 7.12 0 0 1-.96 2.32c-.15.23-.48.26-.76.22a1.03 1.03 0 0 0-1.17 1.01l.01.15c.04.28.01.6-.22.76-.7.45-1.48.78-2.32.97-.29.06-.55-.17-.72-.41a1.02 1.02 0 0 0-1.68 0c-.17.24-.43.47-.72.4a7.12 7.12 0 0 1-2.32-.96c-.23-.15-.26-.48-.22-.76v-.15a1.02 1.02 0 0 0-1.16-1c-.28.03-.6 0-.76-.23A7.12 7.12 0 0 1 0 8.56c-.06-.29.17-.55.41-.72a1.02 1.02 0 0 0 0-1.68c-.24-.17-.47-.43-.4-.72.18-.84.5-1.62.96-2.32.15-.23.48-.26.76-.22h.15a1.02 1.02 0 0 0 1-1.16c-.03-.28 0-.6.23-.76C3.82.53 4.6.2 5.44 0c.29-.06.55.17.72.41a1.02 1.02 0 0 0 1.68 0zM9 7a2 2 0 1 1-4 0 2 2 0 0 1 4 0zm1 0a3 3 0 1 1-6 0 3 3 0 0 1 6 0z"/></svg>'},S48P:function(e,t){
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M12.58 12.1A3.86 3.86 0 0 0 9 6.75a3.87 3.87 0 0 0-3.58 5.33 7.74 7.74 0 0 1 7.16 0zM3.64 9.93l-2.3-.62.37-1.38 2.3.62-.37 1.38zM6.1 6.07L5.07 3.92l1.3-.6 1 2.15-1.29.6zM10.62 5.47l1-2.16 1.3.6-1.01 2.16-1.3-.6zM13.99 8.55l2.3-.62.36 1.38-2.3.62L14 8.55z"/></svg>'},TGRH:function(e,t,s){"use strict";s.r(t),s.d(t,"ControlBarNavigation",(function(){return A}));var i=s("Eyy1"),o=(s("YFKU"),s("8+VR")),l=s("Kxc7"),n=s("1ANp"),a=s("Ialn"),r=s("gWrr"),d=s("/DW5"),h=s("qFKp"),u=(s("JWMC"),s("MjtL")),c=s("e8Rm"),_=s("e2QN"),p=s("vg09"),m=s("/NcV"),b=s("94TV"),g=s("qfuz"),w=s("MQEA"),v=s("1INk"),y=s("+xKI"),S=s("eYcT"),M=s("nFx7");s("jrhZ");const f=Object(d.b)({keys:["Alt","R"],text:"{0} + {1}"}),E=Object(d.b)({keys:["Alt","Click","Alt","Enter"],text:"{0} + {1}, {2} + {3}"}),C=Object(d.b)({keys:[g],text:"{0}"}),k=Object(d.b)({keys:[w],text:"{0}"}),V=Object(d.b)({keys:["Ctrl",v],text:"{0} + {1}"}),x=Object(d.b)({keys:["Ctrl",y],text:"{0} + {1}"}),W=`<div class="control-bar-wrapper">\n\t<div class="control-bar control-bar--hidden">\n\t\t<div class="control-bar__group js-btn-group js-btn-group-zoom">\n\t\t\t<div class="control-bar__btn control-bar__btn--zoom-out apply-common-tooltip" title="${window.t("Zoom Out")}" data-tooltip-hotkey="${x}">\n\t\t\t\t${c}\n\t\t\t</div>\n\t\t\t<div class="control-bar__btn control-bar__btn--zoom-in apply-common-tooltip" title="${window.t("Zoom In")}" data-tooltip-hotkey="${V}">\n\t\t\t\t${p}\n\t\t\t</div>\n\t\t</div>\n\t\t<div class="control-bar__group js-btn-group js-btn-group-maximize">\n\t\t\t<div class="control-bar__btn control-bar__btn--maximize apply-common-tooltip" title="${window.t("Maximize chart")}" data-tooltip-hotkey="${E}">\n\t\t\t\t${S}\n\t\t\t</div>\n\t\t\t<div class="control-bar__btn control-bar__btn--minimize js-hidden apply-common-tooltip" title="${window.t("Restore chart")}" data-tooltip-hotkey="${E}">\n\t\t\t\t${M}\n\t\t\t</div>\n\t\t</div>\n\t\t<div class="control-bar__group js-btn-group js-btn-group-scroll">\n\t\t\t<div class="control-bar__btn control-bar__btn--move-left apply-common-tooltip" title="${window.t("Scroll to the Left")}" data-tooltip-hotkey="${C}">\n\t\t\t\t${u}\n\t\t\t</div>\n\t\t\t<div class="control-bar__btn control-bar__btn--move-right apply-common-tooltip" title="${window.t("Scroll to the Right")}" data-tooltip-hotkey="${k}">\n\t\t\t\t${m}\n\t\t\t</div>\n\t\t</div>\n\t\t<div class="control-bar__group js-btn-group js-btn-group-reset-scale">\n\t\t\t<div class="control-bar__btn control-bar__btn--turn-button control-bar__btn--btn-hidden apply-common-tooltip js-btn-reset" title="${window.t("Reset Chart")}" data-tooltip-hotkey="${f}">\n\t\t\t\t${_}\n\t\t\t</div>\n\t\t</div>\n\t</div>\n</div>`,T=`<div class="control-bar control-bar__btn control-bar__btn--back-present control-bar__btn--btn-hidden apply-common-tooltip" title="${window.t("Scroll to the Most Recent Bar")}">\n\t${b}\n</div>`,L=h.CheckMobile.any(),O={zoomInOut:!0,maximize:!0,
scrollLeftRight:!0,resetScale:!0,goToRealtime:!0};class A{constructor(e,t,s){this._widget=Object(i.ensureNotNull)(Object(r.a)(W).querySelector(".control-bar-wrapper")),this._controlBar=Object(i.ensureNotNull)(this._widget.querySelector(".control-bar")),this._back=Object(i.ensureNotNull)(Object(r.a)(T).querySelector(".control-bar__btn--back-present")),this._btnGroups=Array.from(this._controlBar.querySelectorAll(".js-btn-group")),this._backButtonVisible=!1,this._boundMouseHandler=null,this._chartModel=null,this._checkIntervalId=0,this._controlBarVisible=!1,this._priceAxisChanged=null,this._resetAvailabilityChanged=null,this._priceAxisName="right",this._rafId=0,this._visibilityTypeProperty=null,this._boundUpdateMaximizeButtonsVisibility=this._updateMaximizeButtonsVisibility.bind(this),this._boundToggleFullscreenButtons=this._toggleFullscreenButtons.bind(this),this._paneWidth=0,this._leftPriceScaleWidth=0,this._rightPriceScaleWidth=0,this._chart=e,this._parent=t,this._options=Object.assign({},O,s),this._visibilityPrioritizedGroups=this._initGroupDescriptions(),this._init(),this._initHandlers(),this.updatePosition()}destroy(){if(null!==this._visibilityTypeProperty&&(this._visibilityTypeProperty.unsubscribe(this,this._onVisibilityTypeChange),this._visibilityTypeProperty=null),null!==this._boundMouseHandler&&(this._parent.removeEventListener("mousemove",this._boundMouseHandler,!1),this._parent.removeEventListener("mouseleave",this._boundMouseHandler,!1),this._boundMouseHandler=null),null!==this._priceAxisChanged&&(this._priceAxisChanged.unsubscribe(this,this._updateBackBtnPosition),this._priceAxisChanged=null),clearInterval(this._checkIntervalId),null!==this._resetAvailabilityChanged){this._resetAvailabilityChanged.unsubscribe(this,this._updateResetScalesButtonVisibility);const e=this._chart.getResizerDetacher();e.fullscreenable.unsubscribe(this._boundUpdateMaximizeButtonsVisibility),e.fullscreen.unsubscribe(this._boundToggleFullscreenButtons),this._resetAvailabilityChanged=null}this._chart=null}updatePosition(){const e=this._chart.paneWidgets();if(0===e.length)return;this._paneWidth=e[0].width(),this._leftPriceScaleWidth=this._chart.getPriceAxisMaxWidthByName("left"),this._rightPriceScaleWidth=this._chart.getPriceAxisMaxWidthByName("right");const t=this._chart.timeAxisHeight()+this._bottomMargin();this._widget.style.bottom=t+"px",this._back.style.bottom=t+"px",this._updateBtnGroupVisibility()}_bottomMargin(){var e;const t=this._chart.paneWidgets();return(null!==(e=this._chart.maximizedPaneWidget())&&void 0!==e?e:t[t.length-1]).containsMainSeries()?32:5}_init(){if(h.CheckMobile.any())for(const e of this._btnGroups)e.classList.add("js-hidden");this._buttons={zoomIn:this._widget.querySelector(".control-bar__btn--zoom-in"),zoomOut:this._widget.querySelector(".control-bar__btn--zoom-out"),moveLeft:this._widget.querySelector(".control-bar__btn--move-left"),moveRight:this._widget.querySelector(".control-bar__btn--move-right"),turn:this._widget.querySelector(".control-bar__btn--turn-button"),
maximize:this._widget.querySelector(".control-bar__btn--maximize"),minimize:this._widget.querySelector(".control-bar__btn--minimize")},this._initVisibility(),this._parent.appendChild(this._widget),this._parent.appendChild(this._back),this._backButtonVisible=!1,this._priceAxisName=Object(a.isRtl)()?"left":"right",this._chart.withModel(this,()=>{this._chartModel=this._chart.model(),this._priceAxisChanged=this._chart.getPriceAxisWidthChangedByName(this._priceAxisName),this._resetAvailabilityChanged=this._chartModel.model().isScalesResetAvailableChanged(),this._priceAxisChanged.subscribe(this,this._updateBackBtnPosition),this._resetAvailabilityChanged.subscribe(this,this._updateResetScalesButtonVisibility);const e=this._chart.getResizerDetacher();e.fullscreenable.subscribe(this._boundUpdateMaximizeButtonsVisibility),e.fullscreen.subscribe(this._boundToggleFullscreenButtons),this._updateMaximizeButtonsVisibility(),this._updateBackBtnPosition(),this._back.addEventListener("click",()=>{null!==this._chartModel&&this._chartModel.timeScale().scrollToRealtime(!0)}),this._checkIntervalId=setInterval(()=>this._check(),1e3)})}_initHandlers(){const e=o.mobiletouch?"touchstart":"mousedown",t=o.mobiletouch?["touchend"]:["mouseup","mouseout"];this._buttons.moveLeft.addEventListener(e,e=>{e.preventDefault(),this._chart.scrollHelper().moveByBar(1),this._trackEvent("Move Left")}),this._buttons.moveRight.addEventListener(e,e=>{e.preventDefault(),this._chart.scrollHelper().moveByBar(-1),this._trackEvent("Move Right")});for(const e of t)this._buttons.moveLeft.addEventListener(e,()=>this._chart.scrollHelper().stopMoveByBar()),this._buttons.moveRight.addEventListener(e,()=>this._chart.scrollHelper().stopMoveByBar());this._buttons.turn.addEventListener("click",e=>{e.preventDefault(),this._chart.GUIResetScales(),this._trackEvent("Reset to Default Settings")}),this._buttons.zoomOut.addEventListener("click",e=>{e.preventDefault(),null!==this._chartModel&&this._chartModel.zoomOut(),this._trackEvent("Zoom Out")}),this._buttons.zoomIn.addEventListener("click",e=>{e.preventDefault(),null!==this._chartModel&&this._chartModel.zoomIn(),this._trackEvent("Zoom In")}),this._buttons.maximize.addEventListener("click",e=>{e.preventDefault(),this._chart.setActive(!0),this._chart.getResizerDetacher().requestFullscreen(),this._trackEvent(" Maximize Chart")}),this._buttons.minimize.addEventListener("click",e=>{e.preventDefault(),this._chart.getResizerDetacher().exitFullscreen(),this._trackEvent(" Restore Chart")});const s=e=>e.addEventListener("contextmenu",e=>e.preventDefault());s(this._buttons.moveLeft),s(this._buttons.moveRight),s(this._buttons.turn),s(this._buttons.zoomOut),s(this._buttons.zoomIn),s(this._buttons.minimize),s(this._buttons.maximize)}_initGroupDescriptions(){return[{shouldBeHiddenOnMobile:!1,available:this._isMaximizeButtonAvailable.bind(this),className:"js-btn-group-maximize",element:this._getBtnGroup("js-btn-group-maximize"),totalWidth:50},{shouldBeHiddenOnMobile:!1,available:()=>this._options.resetScale,className:"js-btn-group-reset-scale",
element:this._getBtnGroup("js-btn-group-reset-scale"),totalWidth:50},{shouldBeHiddenOnMobile:!l.enabled("show_zoom_and_move_buttons_on_touch"),available:()=>this._options.zoomInOut,className:"js-btn-group-zoom",element:this._getBtnGroup("js-btn-group-zoom"),totalWidth:86},{shouldBeHiddenOnMobile:!l.enabled("show_zoom_and_move_buttons_on_touch"),available:()=>this._options.scrollLeftRight,className:"js-btn-group-scroll",element:this._getBtnGroup("js-btn-group-scroll"),totalWidth:86}]}_check(){if(null===this._chartModel||!this._options.goToRealtime)return;const e=this._chartModel.timeScale().rightOffset()<0;e!==this._backButtonVisible&&(this._backButtonVisible=e,this._back.classList.toggle("control-bar__btn--btn-hidden",!this._backButtonVisible))}_initVisibility(){this._visibilityTypeProperty=Object(n.actualBehavior)(),this._visibilityTypeProperty.subscribe(this,this._onVisibilityTypeChange),this._onVisibilityTypeChange()}_onVisibilityTypeChange(){if(null===this._visibilityTypeProperty)return;const e=this._visibilityTypeProperty.value();"alwaysOn"===e||"alwaysOff"===e?(this._controlBarVisible="alwaysOn"===e,null!==this._boundMouseHandler&&(this._parent.removeEventListener("mousemove",this._boundMouseHandler,!1),this._parent.removeEventListener("mouseleave",this._boundMouseHandler,!1),this._boundMouseHandler=null)):(this._controlBarVisible=!1,this._boundMouseHandler||(this._boundMouseHandler=this._visibilityMouseHandler.bind(this),this._parent.addEventListener("mousemove",this._boundMouseHandler),this._parent.addEventListener("mouseleave",this._boundMouseHandler))),this._updateControlBarVisibility()}_visibilityMouseHandler(e){if(e.buttons)return;if(null!==this._chartModel&&this._chartModel.lineBeingCreated())return;let t="mouseleave"!==e.type;if("mousemove"===e.type){const s=this._widget.getBoundingClientRect(),i=100-this._bottomMargin();t=e.clientX>=s.left-100&&e.clientX<=s.right+100&&e.clientY>=s.top-i&&e.clientY<=s.bottom+100}this._controlBarVisible!==t&&(this._controlBarVisible=t,null===this._rafId&&(this._rafId=this._controlBar.ownerDocument.defaultView.requestAnimationFrame(this._updateControlBarVisibility.bind(this))))}_updateControlBarVisibility(){this._rafId=null,this._controlBar.classList.toggle("control-bar--hidden",!this._controlBarVisible)}_updateBackBtnPosition(){if("left"===this._priceAxisName||"right"===this._priceAxisName){const e=this._chart.getPriceAxisMaxWidthByName(this._priceAxisName)+14;e&&(this._back.style.marginRight=e+"px")}}_updateBtnGroupVisibility(){const e=this._leftPriceScaleWidth+this._paneWidth,t=(e+this._rightPriceScaleWidth)/2;let s=2*Math.min(e-t,t-this._leftPriceScaleWidth)-50-50,i=!1;for(const e of this._visibilityPrioritizedGroups){e.enoughSpaceForGroup=!1;e.available()&&(!L||!e.shouldBeHiddenOnMobile)&&(s-=e.totalWidth,e.enoughSpaceForGroup=s>=0&&!i,i=i||!e.enoughSpaceForGroup),!e.enoughSpaceForGroup!==e.element.classList.contains("js-hidden")&&e.element.classList.toggle("js-hidden",!e.enoughSpaceForGroup)}this._updateControlBarPosition()}_getBtnGroup(e){
return Object(i.ensureDefined)(this._btnGroups.find(t=>t.classList.contains(e)))}_updateControlBarPosition(){const e=this._visibilityPrioritizedGroups.reduce((e,t)=>e+(t.enoughSpaceForGroup?t.totalWidth:0),0),t=(this._paneWidth+this._leftPriceScaleWidth+this._rightPriceScaleWidth)/2-Math.ceil(e/2);this._widget.style.left=t+"px"}_updateResetScalesButtonVisibility(){if(null===this._chartModel)return;const e=this._chartModel.model().isScalesResetAvailable();this._buttons.turn.classList.toggle("control-bar__btn--btn-hidden",!e)}_updateMaximizeButtonsVisibility(){this._updateBtnGroupVisibility()}_toggleFullscreenButtons(){const e=this._chart.getResizerDetacher().fullscreen.value();this._buttons.maximize.classList.toggle("js-hidden",e),this._buttons.minimize.classList.toggle("js-hidden",!e)}_isMaximizeButtonAvailable(){return this._options.maximize,!1}_trackEvent(e){0}}},Tq3g:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M14.48 5.1c-.52 2.03-1.46 3.04-2.82 3.04-.64 0-1.55-.19-2.74-.56-1.17-.38-1.99-.57-2.46-.57-.69 0-1.22.37-1.58 1.13H3.55A4.3 4.3 0 0 1 4.5 6c.5-.6 1.08-.9 1.74-.9.7 0 1.65.2 2.84.58 1.2.37 2.04.55 2.52.55.8 0 1.32-.37 1.59-1.13h1.29zm0 4.84c-.52 2.02-1.46 3.03-2.82 3.03-.64 0-1.55-.19-2.74-.56-1.17-.38-1.99-.57-2.46-.57-.69 0-1.22.38-1.58 1.13H3.55a4.3 4.3 0 0 1 .95-2.14c.5-.6 1.08-.9 1.74-.9.7 0 1.65.2 2.84.58 1.2.37 2.04.56 2.52.56.8 0 1.32-.38 1.59-1.13h1.29z"/></svg>'},Uua9:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M7.65 5.16v2.68h3.78v1.73H7.65V13h5.19v1.8H5.62V3.35h7.3v1.8H7.65z"/></svg>'},VrXG:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M7.57 14.8H5.03V3.36c1.62-.05 2.64-.08 3.06-.08 1.66 0 2.98.49 3.96 1.47a5.23 5.23 0 0 1 1.47 3.88c0 4.11-1.99 6.17-5.95 6.17zm-.5-9.66v7.8c.32.04.67.06 1.05.06 1.03 0 1.83-.38 2.41-1.12.58-.75.88-1.79.88-3.13 0-2.44-1.14-3.67-3.42-3.67-.22 0-.53.02-.93.06z"/></svg>'},VrrN:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M7.84 13.7H5.78V4.4l2.48-.06c1.35 0 2.42.4 3.22 1.2.8.78 1.19 1.83 1.19 3.15 0 3.34-1.61 5.01-4.83 5.01zm-.41-7.85v6.35c.26.02.55.03.86.03.83 0 1.48-.3 1.95-.9.48-.6.72-1.46.72-2.54 0-2-.93-2.99-2.78-2.99-.18 0-.43.02-.75.05z"/></svg>'},WYzw:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M19.32 6H8.68A2.68 2.68 0 0 0 6 8.68V11h1V8.68C7 7.75 7.75 7 8.68 7h10.64c.93 0 1.68.75 1.68 1.68V11h1V8.68C22 7.2 20.8 6 19.32 6zM7 19.32c0 .93.75 1.68 1.68 1.68h10.64c.93 0 1.68-.75 1.68-1.68V17h1v2.32C22 20.8 20.8 22 19.32 22H8.68A2.68 2.68 0 0 1 6 19.32V17h1v2.32z"/></svg>'},"Y+EN":function(e,t){
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 15" width="15" height="15"><path fill="currentColor" d="M7.23 2.58a.5.5 0 0 1 .54 0l5.5 3.5a.5.5 0 0 1 0 .84l-5.5 3.5a.5.5 0 0 1-.54 0l-5.5-3.5a.5.5 0 0 1 0-.84l5.5-3.5zM2.93 6.5L7.5 9.4l4.57-2.9L7.5 3.6 2.93 6.5z"/><path fill="currentColor" d="M1.58 9.23a.5.5 0 0 1 .69-.15L7.5 12.4l5.23-3.33a.5.5 0 0 1 .54.84l-5.5 3.5a.5.5 0 0 1-.54 0l-5.5-3.5a.5.5 0 0 1-.15-.69z"/></svg>'},YGQl:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.7" d="M12.5 5.5l-7 7m0-7l7 7"/></svg>'},ZKFq:function(e,t,s){e.exports={"css-value-pane-controls-padding-left":"1px","css-value-pane-controls-padding-right":"5px",paneControls:"paneControls-2fnY2ZKI",hasTopMargin:"hasTopMargin-2fnY2ZKI",hidden:"hidden-2fnY2ZKI",forceHidden:"forceHidden-2fnY2ZKI",button:"button-2fnY2ZKI pane-button-xPfK7aM7",buttonIcon:"buttonIcon-2fnY2ZKI",minimize:"minimize-2fnY2ZKI",newButton:"newButton-2fnY2ZKI",touchMode:"touchMode-2fnY2ZKI",maximize:"maximize-2fnY2ZKI","maximize-animation-up-bracket":"maximize-animation-up-bracket-2fnY2ZKI","maximize-animation-down-bracket":"maximize-animation-down-bracket-2fnY2ZKI","minimize-animation-up-bracket":"minimize-animation-up-bracket-2fnY2ZKI","minimize-animation-down-bracket":"minimize-animation-down-bracket-2fnY2ZKI",up:"up-2fnY2ZKI","up-animation":"up-animation-2fnY2ZKI",down:"down-2fnY2ZKI","down-animation":"down-animation-2fnY2ZKI",buttonsWrapper:"buttonsWrapper-2fnY2ZKI"}},bNWL:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 2 30 24" width="30" height="24" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M15.82 14l5.36-5.36-.82-.82L15 13.18 9.64 7.82l-.82.82L14.18 14l-5.36 5.36.82.82L15 14.82l5.36 5.36.82-.82L15.82 14z"/></svg>'},cbig:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M9.3 9l.9-4.53a1.23 1.23 0 1 0-2.4 0L8.7 9l-.9 4.53a1.23 1.23 0 1 0 2.4 0L9.3 9z"/><path fill="currentColor" d="M9.15 9.26l4.38-1.48a1.23 1.23 0 1 0-1.21-2.09L8.85 8.74l-4.38 1.48a1.23 1.23 0 1 0 1.21 2.09l3.47-3.05z"/><path fill="currentColor" d="M9.15 8.74L5.68 5.69a1.23 1.23 0 1 0-1.2 2.09l4.37 1.48 3.47 3.05a1.23 1.23 0 1 0 1.2-2.09L9.16 8.74z"/></svg>'},e2QN:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 18" width="14" height="18"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-width="2"><path d="M1 10a6 6 0 1 0 6-6H3"/><path d="M5 1L2.5 4 5 7"/></g></svg>'},e8Rm:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14" height="14"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M2 7h10"/></svg>'},eYcT:function(e,t){
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><g class="corner-left-top"><path fill="currentColor" d="M6 9C6 7.89543 6.89543 7 8 7H10C10.5523 7 11 7.44772 11 8C11 8.55228 10.5523 9 10 9H8V11C8 11.5523 7.55228 12 7 12C6.44772 12 6 11.5523 6 11V9Z"/></g><g class="corner-right-top"><path fill="currentColor" d="M17 8C17 7.44772 17.4477 7 18 7H20C21.1046 7 22 7.89543 22 9V11C22 11.5523 21.5523 12 21 12C20.4477 12 20 11.5523 20 11V9H18C17.4477 9 17 8.55228 17 8Z"/></g><g class="corner-right-bottom"><path fill="currentColor" d="M21 16C21.5523 16 22 16.4477 22 17V19C22 20.1046 21.1046 21 20 21H18C17.4477 21 17 20.5523 17 20C17 19.4477 17.4477 19 18 19H20V17C20 16.4477 20.4477 16 21 16Z"/></g><g class="corner-left-bottom"><path fill="currentColor" d="M7 16C7.55228 16 8 16.4477 8 17V19H10C10.5523 19 11 19.4477 11 20C11 20.5523 10.5523 21 10 21H8C6.89543 21 6 20.1046 6 19V17C6 16.4477 6.44772 16 7 16Z"/></g></svg>'},fk9O:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M13.5 6.35l6.32 5.27-.64.76L14 8.07V21h-1V8.07l-5.18 4.31-.64-.76 6.32-5.27z"/></svg>'},gKdq:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M14 7v12.93l5.18-4.31.64.76-6.32 5.27-6.32-5.27.64-.76L13 19.93V7h1z"/></svg>'},i9xP:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 15" width="15" height="15"><circle fill="currentColor" cx="12.75" cy="7.5" r="1.25"/><circle fill="currentColor" cx="7.5" cy="7.5" r="1.25"/><circle fill="currentColor" cx="2.25" cy="7.5" r="1.25"/></svg>'},jXu8:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><circle fill="currentColor" cx="9" cy="9" r="5"/></svg>'},jrhZ:function(e,t,s){e.exports={"animation-minimize-corner-left-top":"animation-minimize-corner-left-top-1yUqeyiZ","animation-minimize-corner-right-top":"animation-minimize-corner-right-top-1yUqeyiZ","animation-minimize-corner-right-bottom":"animation-minimize-corner-right-bottom-1yUqeyiZ","animation-minimize-corner-left-bottom":"animation-minimize-corner-left-bottom-1yUqeyiZ","animation-maximize-corner-left-top":"animation-maximize-corner-left-top-1yUqeyiZ","animation-maximize-corner-right-top":"animation-maximize-corner-right-top-1yUqeyiZ","animation-maximize-corner-right-bottom":"animation-maximize-corner-right-bottom-1yUqeyiZ","animation-maximize-corner-left-bottom":"animation-maximize-corner-left-bottom-1yUqeyiZ"}},ku84:function(e,t){
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 15" width="15" height="15" fill="none"><path fill="currentColor" d="M4.5 12A1.5 1.5 0 0 1 3 10.5V9H2v1.5A2.5 2.5 0 0 0 4.5 13h6a2.5 2.5 0 0 0 2.5-2.5V9h-1v1.5c0 .83-.67 1.5-1.5 1.5h-6z" class="bracket-up"/><path fill="currentColor" d="M4.5 3C3.67 3 3 3.67 3 4.5V6H2V4.5A2.5 2.5 0 0 1 4.5 2h6A2.5 2.5 0 0 1 13 4.5V6h-1V4.5c0-.83-.67-1.5-1.5-1.5h-6z" class="bracket-down"/></svg>'},lvAK:function(e,t,s){"use strict";s.r(t);var i=s("8+VR"),o=s("RspR"),l=s("3ClC"),n=(s("LVLx"),s("Eyy1")),a=s("eJTA"),r=s("AvGy"),d=s("EV8o"),h=s("ZKFq"),u=s("0jws");class c{constructor(e,t,s){this._parentEl=document.createElement("div"),this._listActionsWrapperEl=null,this._listActionsElements={},this._actionsSpawns={},this._onMouseEnterLeaveEventHandler=null,this._mouseOverWidget=!1,this._wrapEl=e,this._onMouseEnterLeaveEventHandler=this._onMouseEnterLeaveEvent.bind(this),this._wrapEl.addEventListener("mouseenter",this._onMouseEnterLeaveEventHandler),this._wrapEl.addEventListener("mouseleave",this._onMouseEnterLeaveEventHandler),this._actions=t,this._globalVisibility=s.globalVisibility.spawn(),this._globalVisibility.subscribe(this._updatePaneControlsWidgetVisibility.bind(this)),this._visibilityType=s.visibilityType.spawn(),this._visibilityType.subscribe(this._updatePaneControlsWidgetVisibility.bind(this)),this._doNotSwitchToContextMenuMode=s.doNotSwitchToContextMenuMode,this._themedColor=s.themedColor.spawn(),this._themedColor.subscribe(this._updateThemedColor.bind(this));for(const[e,t]of Object.entries(this._actions)){const s=e;this._actionsSpawns[s]={visible:t.visible.spawn(),title:void 0===t.title?null:t.title.spawn()},this._actionsSpawns[s].visible.subscribe(this._updateActionVisibilities.bind(this,s));const i=this._actionsSpawns[s].title;null!==i&&i.subscribe(this._updateActionTitle.bind(this,s))}this._render(),this._updatePaneControlsWidgetVisibility(),this._updateThemedColor(this._themedColor.value()),this._parentEl.classList.toggle(h.touchMode,r.a),this._parentEl.addEventListener("contextmenu",e=>e.preventDefault())}destroy(){this._visibilityType.destroy(),this._themedColor.destroy();for(const e of Object.keys(this._actionsSpawns)){const t=e;this._actionsSpawns[t].visible.destroy();const s=this._actionsSpawns[t].title;null!==s&&s.destroy()}null!==this._onMouseEnterLeaveEventHandler&&(this._wrapEl.removeEventListener("mouseenter",this._onMouseEnterLeaveEventHandler),this._wrapEl.removeEventListener("mouseleave",this._onMouseEnterLeaveEventHandler),this._onMouseEnterLeaveEventHandler=null),this._parentEl.innerHTML="",delete this._parentEl}getElement(){return this._parentEl}updateWidgetModeByWidth(e){const t=!this._doNotSwitchToContextMenuMode.value()&&e<356,s=!this._doNotSwitchToContextMenuMode.value()&&e<666.65,i=Object(n.ensureNotNull)(this._listActionsWrapperEl),o=Object(n.ensureNotNull)(this._listActionsElements.more);i.classList.toggle(u.blockHidden,t||s),o.classList.toggle(u.blockHidden,t||!s||!this._actions.more.visible.value())}_render(){this._renderActions(),
this._parentEl.classList.add(h.paneControls),this._wrapEl.append(this._parentEl)}_renderActions(){null===this._listActionsWrapperEl&&(this._listActionsWrapperEl=document.createElement("div"),this._listActionsWrapperEl.classList.add(h.buttonsWrapper),this._parentEl.append(this._listActionsWrapperEl));const e=r.a?"large":"small";this._listActionsElements.up=Object(d.a)(this._actions.up,h.button,h.buttonIcon,u.blockHidden,e),this._listActionsElements.down=Object(d.a)(this._actions.down,h.button,h.buttonIcon,u.blockHidden,e),this._listActionsElements.close=Object(d.a)(this._actions.close,h.button,h.buttonIcon,u.blockHidden,e),this._listActionsElements.maximize=Object(d.a)(this._actions.maximize,h.button,h.buttonIcon,u.blockHidden,e),this._listActionsElements.minimize=Object(d.a)(this._actions.minimize,h.button,h.buttonIcon,u.blockHidden,e),this._listActionsWrapperEl.append(this._listActionsElements.up,this._listActionsElements.down,this._listActionsElements.close,this._listActionsElements.maximize,this._listActionsElements.minimize),this._listActionsElements.more=Object(d.a)(this._actions.more,h.button,h.buttonIcon,u.blockHidden,e);for(const e of Object.keys(this._listActionsElements))Object(n.ensureNotNull)(this._listActionsElements[e]).classList.add(h.newButton);this._parentEl.append(this._listActionsElements.more)}_updateActionVisibilities(e,t){Object(n.ensureNotNull)(this._listActionsElements[e]).classList.toggle(u.blockHidden,!t)}_updateActionTitle(e,t){Object(n.ensureNotNull)(this._listActionsElements[e]).setAttribute("title",t)}_onMouseEnterLeaveEvent(e){this._mouseOverWidget="mouseenter"===e.type,"visibleOnMouseOver"===this._visibilityType.value()&&this._updatePaneControlsWidgetVisibility()}_updatePaneControlsWidgetVisibility(){let e,t=!1;switch(this._visibilityType.value()){case"alwaysOff":e=!1,t=!0;break;case"alwaysOn":e=this._globalVisibility.value();break;case"visibleOnMouseOver":e=this._globalVisibility.value()&&this._mouseOverWidget}this._parentEl.classList.toggle(h.hidden,!e),this._parentEl.classList.toggle(h.forceHidden,!this._globalVisibility.value()||t)}_updateThemedColor(e){if(e.length>0){const[t,s,i]=Object(a.parseRgb)(e);this._parentEl.style.color=Object(a.rgbaToString)([t,s,i,Object(a.normalizeAlphaComponent)(.8)])}else this._parentEl.style.removeProperty("color")}}var _=s("7KDR"),p=s("5VQP"),m=s("obM5"),b=s("fk9O"),g=s("gKdq"),w=s("WYzw");function v(e,t){const s=Object(n.ensureNotNull)(t.target);return function(e){const t=[];if(e.maximize.visible.value()){const s=Object(n.ensure)(e.maximize.title),i=Object(n.ensureNotNull)(e.maximize.action);t.push(new _.Action({icon:w,label:s.value(),statName:"Maximize Pane",shortcutHint:y,onExecute:()=>i()}))}else if(e.minimize.visible.value()){const s=Object(n.ensure)(e.minimize.title),i=Object(n.ensureNotNull)(e.minimize.action);t.push(new _.Action({icon:w,label:s.value(),statName:"Minimize Pane",shortcutHint:y,onExecute:()=>i()}))}if(e.up.visible.value()){const s=Object(n.ensure)(e.up.title),i=Object(n.ensureNotNull)(e.up.action);t.push(new _.Action({icon:b,
label:s.value(),statName:"Move pane up",onExecute:()=>i()}))}if(e.down.visible.value()){const s=Object(n.ensure)(e.down.title),i=Object(n.ensureNotNull)(e.down.action);t.push(new _.Action({icon:g,label:s.value(),statName:"Move pane down",onExecute:()=>i()}))}if(e.close.visible.value()){const s=Object(n.ensure)(e.close.title),i=Object(n.ensureNotNull)(e.close.action);t.push(new _.Action({icon:m,label:s.value(),statName:"Delete pane",onExecute:()=>i()}))}return p.ContextMenuManager.createMenu(t)}(e).then(e=>(e.show(e=>{const t=s.getBoundingClientRect();return{clientX:t.right-e,clientY:t.top+t.height+3}}),e))}const y=window.t("Double click");var S=s("hY0g"),M=s.n(S),f=s("EsvI"),E=s("/DW5"),C=s("OJSF"),k=s("PXSR"),V=s("QEZv"),x=s("ku84"),W=s("i9xP"),T=s("1Wf8"),L=s("956S");s.d(t,"PaneControlsWidget",(function(){return I}));const O=i.mobiletouch,A=window.t("Delete pane"),D=window.t("Move pane up"),H=window.t("Move pane down"),B=window.t("Maximize pane"),P=window.t("Restore pane"),z=window.t("Manage panes"),j=window.t("Double click"),N=Object(E.b)({keys:[""],text:j});class I{constructor(e,t,s,i,l){this._actions={},this._moreActionCM=null,this._themedColor=new M.a(""),this._model=e,this._paneWidget=t,this._callbacks=i,this._closeButtonVisibility=new M.a(this._getCloseButtonVisibility()),this._upButtonVisibility=new M.a(this._getUpButtonVisibility()),this._downButtonVisibility=new M.a(this._getDownButtonVisibility()),this._maximizeButtonVisibility=new M.a(this._getMaximizeButtonVisibility()),this._minimizeButtonVisibility=new M.a(this._getMinimizeButtonVisibility()),this._createActions(),this._visibilityTypeProperty=Object(o.actualBehavior)(),this._visibilityTypeProperty.subscribe(this,e=>{this._visibilityType.setValue(e.value())}),this._visibilityType=new M.a(this._visibilityTypeProperty.value()),this._isPaneMaximize=new M.a(this._getIsPaneMaximizeValue()),this._isWidgetShow=new M.a(this._getIsWidgetShow()),this._backgroundThemeName=s.backgroundThemeName,this._renderer=new c(l,this._actions,{visibilityType:this._visibilityType.readonly(),globalVisibility:this._isWidgetShow.readonly(),doNotSwitchToContextMenuMode:this._isPaneMaximize.readonly(),themedColor:this._themedColor.readonly()})}destroy(){this._visibilityTypeProperty.unsubscribeAll(this),this._renderer.destroy()}getElement(){return this._renderer.getElement()}action(){return this._actions}update(){this._updateButtonsVisibility(),this._isPaneMaximize.setValue(this._getIsPaneMaximizeValue()),this._isWidgetShow.setValue(this._getIsWidgetShow())}updateWidgetModeByWidth(e){this._renderer.updateWidgetModeByWidth(e)}updateThemedColors(e){null===e&&(e=Object(f.getStdThemedValue)("chartProperties.paneProperties.background",this._backgroundThemeName.value())),this._themedColor.setValue(e||"")}_updateButtonsVisibility(){this._closeButtonVisibility.setValue(this._getCloseButtonVisibility()),this._upButtonVisibility.setValue(this._getUpButtonVisibility()),this._downButtonVisibility.setValue(this._getDownButtonVisibility()),
this._maximizeButtonVisibility.setValue(this._getMaximizeButtonVisibility()),this._minimizeButtonVisibility.setValue(this._getMinimizeButtonVisibility())}_createActions(){this._actions.up={iconMap:new Map([["large",k],["small",k]]),action:this._onUpDownButton.bind(this,"up"),visible:this._upButtonVisibility,title:new M.a(D),className:h.up,dataset:{name:"pane-button-up"}},this._actions.down={iconMap:new Map([["large",V],["small",V]]),action:this._onUpDownButton.bind(this,"down"),visible:this._downButtonVisibility,title:new M.a(H),className:h.down,dataset:{name:"pane-button-down"}},this._actions.close={iconMap:new Map([["large",C],["small",C]]),action:this._onCloseButton.bind(this),visible:this._closeButtonVisibility,title:new M.a(A),dataset:{name:"pane-button-close"}},this._actions.maximize={iconMap:new Map([["large",T],["small",x]]),action:this._onToggleMaximizeButton.bind(this),visible:this._maximizeButtonVisibility,title:new M.a(B),hotKeyTitle:N,className:h.maximize,dataset:{name:"pane-button-maximize"}},this._actions.minimize={iconMap:new Map([["large",T],["small",x]]),action:this._onToggleMaximizeButton.bind(this),visible:this._minimizeButtonVisibility,title:new M.a(P),hotKeyTitle:N,className:h.minimize,dataset:{name:"pane-button-minimize"}},this._actions.more={iconMap:new Map([["large",L],["small",W]]),action:this._showButtonsInContextMenu.bind(this),visible:new M.a(!O),title:new M.a(z),dataset:{name:"pane-button-more"}}}_getCloseButtonVisibility(){const e=this._paneWidget.state();let t=!1;return e.containsMainSeries()||e.isMaximized()||O||(t=e.dataSources().some(e=>Object(l.isStudy)(e))),t}_onCloseButton(){const e=this._model.model().panes().indexOf(this._paneWidget.state());this._model.removePane(e)}_getUpButtonVisibility(){const e=this._paneWidget.state();return this._model.model().panes().indexOf(e)>0&&!e.isMaximized()&&!O}_getDownButtonVisibility(){const e=this._paneWidget.state(),t=this._model.model().panes();return t.indexOf(e)<t.length-1&&!e.isMaximized()&&!O}_onUpDownButton(e){const t=this._model.model().panes().indexOf(this._paneWidget.state());this._model.rearrangePanes(t,e)}_getMaximizeButtonVisibility(){const e=this._paneWidget.state();return this._model.model().panes().length>1&&!e.isMaximized()&&!O}_getMinimizeButtonVisibility(){const e=this._paneWidget.state();return this._model.model().panes().length>1&&e.isMaximized()}_onToggleMaximizeButton(){this._callbacks.toggleMaximizePane(this._paneWidget)}_showButtonsInContextMenu(e){e.preventDefault(),null!==this._moreActionCM&&this._moreActionCM.isShown()?this._moreActionCM=null:v(this._actions,e).then(e=>{this._moreActionCM=e})}_getIsPaneMaximizeValue(){return this._paneWidget.state().isMaximized()}_getIsWidgetShow(){return this._model.model().panes().length>1}}},nFx7:function(e,t){
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><g class="corner-left-top"><path fill="currentColor" d="M10 7C10.5523 7 11 7.44772 11 8V10C11 11.1046 10.1046 12 9 12H7C6.44772 12 6 11.5523 6 11C6 10.4477 6.44772 10 7 10H9V8C9 7.44772 9.44772 7 10 7Z"/></g><g class="corner-right-top"><path fill="currentColor" d="M18 7C18.5523 7 19 7.44772 19 8V10H21C21.5523 10 22 10.4477 22 11C22 11.5523 21.5523 12 21 12H19C17.8954 12 17 11.1046 17 10V8C17 7.44772 17.4477 7 18 7Z"/></g><g class="corner-right-bottom"><path fill="currentColor" d="M17 18C17 16.8954 17.8954 16 19 16H21C21.5523 16 22 16.4477 22 17C22 17.5523 21.5523 18 21 18H19V20C19 20.5523 18.5523 21 18 21C17.4477 21 17 20.5523 17 20V18Z"/></g><g class="corner-left-bottom"><path fill="currentColor" d="M6 17C6 16.4477 6.44772 16 7 16H9C10.1046 16 11 16.8954 11 18V20C11 20.5523 10.5523 21 10 21C9.44772 21 9 20.5523 9 20V18H7C6.44772 18 6 17.5523 6 17Z"/></g></svg>'},obM5:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M7.65 8.35l.7-.7 6.15 6.14 6.15-6.14.7.7-6.14 6.15 6.14 6.15-.7.7-6.15-6.14-6.15 6.14-.7-.7 6.14-6.15-6.14-6.15z"/></svg>'},qfuz:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 9 7" width="9" height="7"><path fill="currentColor" d="M.5 3.5L4 0v3h5v1H4v3z"/></svg>'},rGGD:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" width="16" height="16"><path fill="currentColor" d="M2.4 5.46a.8.8 0 0 1 1.14-.05L8 9.42l4.46-4.01a.8.8 0 0 1 1.08 1.18L8 11.58 2.47 6.59a.8.8 0 0 1-.06-1.13z"/></svg>'},rh3U:function(e,t,s){e.exports={"css-value-small-size":"18px","css-value-border-radius-small-size":"9px","css-value-large-size":"22px","css-value-border-radius-large-size":"11px",statuses:"statuses-1Ho_ylkC",statusItem:"statusItem-1Ho_ylkC",small:"small-1Ho_ylkC",large:"large-1Ho_ylkC",blinking:"blinking-1Ho_ylkC",oneWidgetsVisible:"oneWidgetsVisible-1Ho_ylkC",twoWidgetsVisible:"twoWidgetsVisible-1Ho_ylkC",threeWidgetsVisible:"threeWidgetsVisible-1Ho_ylkC","blinking-animation":"blinking-animation-1Ho_ylkC",marketStatusOpen:"marketStatusOpen-1Ho_ylkC",marketStatusClose:"marketStatusClose-1Ho_ylkC",marketStatusPre:"marketStatusPre-1Ho_ylkC",marketStatusPost:"marketStatusPost-1Ho_ylkC",marketStatusHoliday:"marketStatusHoliday-1Ho_ylkC",invalidSymbol:"invalidSymbol-1Ho_ylkC",replayMode:"replayMode-1Ho_ylkC",notAccurate:"notAccurate-1Ho_ylkC",delay:"delay-1Ho_ylkC",eod:"eod-1Ho_ylkC",dataProblemHigh:"dataProblemHigh-1Ho_ylkC",dataProblemLow:"dataProblemLow-1Ho_ylkC"}},sAH5:function(e,t,s){"use strict";s.r(t);var i=s("Eyy1"),o=s("hY0g"),l=s.n(o),n=s("ogJP"),a=s("ikwP"),r=s("eJTA"),d=s("Kxc7"),h=s("AvGy"),u=(s("LVLx"),s("MjAr")),c=s("EV8o"),_=s("S8xo"),p=s("GUQs"),m=s("0jws");var b=s("OcaN");class g extends class{constructor(e,t={}){this._loadingEl=document.createElement("span"),this._renderLoading(t),this.toggleVisibility(!1),e.appendChild(this._loadingEl)}toggleVisibility(e){
this._loadingEl.classList.toggle(m.blockHidden,!e)}_renderLoading(e){const{className:t}=e;t&&this._loadingEl.classList.add(t)}}{_renderLoading(e){super._renderLoading(e),this._loadingEl.innerHTML=`\n\t\t\t<span class="${b.loaderItem}"></span>\n\t\t\t<span class="${b.loaderItem}"></span>\n\t\t\t<span class="${b.loaderItem}"></span>\n\t\t`,this._loadingEl.classList.add(b.loader)}}var w=s("qFKp");function v(e,t){null===e.firstChild?e.textContent=t:e.firstChild.nodeValue=t}var y=s("5Alx");const S=w.CheckMobile.any(),M=[y.title1st,y.title2nd,y.title3rd,y.title4th];class f{constructor(e,t,s){this._el=null,this._firstBlockWrapper=null,this._titleParentEl=null,this._titleElements=[],this._valuesParentEl=null,this._valuesAdditionalWrapperEl=null,this._valuesElements=[],this._actionsParentEl=null,this._actionAdditionalWrapperEl=null,this._stayInHoveredMode=!1,this._mode=4,this._statusesWrapper=null,this._resizeObserver=null,this._hideInvisibleHover=null,this._hideValues=null,this._allButtonsWidth=null,this._lastStatusesWrapperWidth=null,this._lastActionsWrapperWidth=null,this._showActionsHandler=null,this._hideActionsHandler=null,this._selectedSourceHandler=null,this._mouseEventHandlers=[],this._disableTimeout=null,this._loader=null,this._model=e,this._parentEl=t,this._disabled=this._model.disabled().spawn(),this._disabled.subscribe(this._updateDisabledState.bind(this)),this._disabledOnInterval=this._model.disabledOnInterval().spawn(),this._disabledOnInterval.subscribe(this._updateDisabledOnIntervalState.bind(this)),this._selected=this._model.selected().spawn(),this._selected.subscribe(this._updateSelectedState.bind(this)),this._loading=this._model.loading().spawn(),this._loading.subscribe(function(e,t){let s=0;return i=>{clearTimeout(s),Boolean(i)?e():s=setTimeout(e,t,!1)}}(this._updateLoadingState.bind(this),700)),this._isTitleHidden=this._model.isTitleHidden().spawn(),this._isValuesHidden=this._model.isValuesHidden().spawn(),this._isRowHidden=this._model.isRowHidden().spawn(),this._isTitleHidden.subscribe(this._updateShowTitles.bind(this)),this._isValuesHidden.subscribe(this._updateShowValues.bind(this)),this._isRowHidden.subscribe(this._updateShowLine.bind(this)),this._titlesSpawns=this._model.titles().map(e=>e.spawn());for(let e=0;e<this._titlesSpawns.length;e++)this._titlesSpawns[e].subscribe(this._updateTitlesHandler.bind(this,e));this._values=this._model.values().spawn(),this._values.subscribe(this._updateValues.bind(this)),this._createValuesSpawns(),this._addValuesSpawnsSubscriptions(),this._actionsSpawnArray=this._model.actions().map(e=>({visible:e.visible.spawn(),title:void 0===e.title?null:e.title.spawn()}));for(let e=0;e<this._actionsSpawnArray.length;e++){this._actionsSpawnArray[e].visible.subscribe(this._updateActionVisibilities.bind(this,e));const t=this._actionsSpawnArray[e].title;null!==t&&t.subscribe(this._updateActionTitle.bind(this,e))}this._withActions=s.withActions,this._render(),this._updateStates(),this._updateShowTitles(),this._updateShowValues(),this._updateShowLine(),
null!==this._valuesParentEl&&(this._loader=new g(this._valuesParentEl,{className:y.loader})),this._customTextColor=s.customTextColor.spawn(),this._customTextColor.subscribe(this._updateCustomTextColor.bind(this)),this._updateCustomTextColor(),this._withActions&&(this._showActionsHandler=Object(p.c)(this._showActions.bind(this)),this._hideActionsHandler=Object(p.c)(this._hideActions.bind(this)),this._selectedSourceHandler=Object(p.c)(this._model.setSourceSelected.bind(this._model)),null!==this._titleParentEl&&(this._titleParentEl.addEventListener("touchend",this._selectedSourceHandler),this._titleParentEl.addEventListener("mousedown",this._selectedSourceHandler),S||(this._titleParentEl.addEventListener("mouseenter",this._showActionsHandler),this._titleParentEl.addEventListener("mouseleave",this._hideActionsHandler)),this._mouseEventHandlers.push(new _.MouseEventHandler(this._titleParentEl,{mouseDoubleClickEvent:this._model.onShowSettings.bind(this._model),doubleTapEvent:this._model.onShowSettings.bind(this._model)}))),null===this._actionAdditionalWrapperEl||null===this._actionsParentEl||S||(this._actionAdditionalWrapperEl.addEventListener("mouseenter",this._showActionsHandler),this._actionAdditionalWrapperEl.addEventListener("mouseleave",this._hideActionsHandler),this._actionsParentEl.addEventListener("contextmenu",e=>{e.preventDefault(),e.stopPropagation()})))}destroy(){var e,t;this._disabled.destroy(),this._disabledOnInterval.destroy(),this._selected.destroy(),this._loading.destroy(),this._isTitleHidden.destroy(),this._isValuesHidden.destroy(),this._isRowHidden.destroy(),this._customTextColor.destroy(),null!==this._disableTimeout&&clearTimeout(this._disableTimeout);for(const e of this._titlesSpawns)e.destroy();if(null!==this._titleParentEl){for(const e of this._mouseEventHandlers)e.destroy();this._titleElements=[],this._withActions&&null!==this._selectedSourceHandler&&null!==this._showActionsHandler&&null!==this._hideActionsHandler&&(this._titleParentEl.removeEventListener("touchend",this._selectedSourceHandler),this._titleParentEl.removeEventListener("mousedown",this._selectedSourceHandler),S||(this._titleParentEl.removeEventListener("mouseenter",this._showActionsHandler),this._titleParentEl.removeEventListener("mouseleave",this._hideActionsHandler))),this._titleParentEl=null}for(const e of this._actionsSpawnArray){e.visible.destroy();const t=e.title;null!==t&&t.destroy()}if(null!==this._actionAdditionalWrapperEl&&(this._withActions&&null!==this._showActionsHandler&&null!==this._hideActionsHandler&&!S&&(this._actionAdditionalWrapperEl.removeEventListener("mouseenter",this._showActionsHandler),this._actionAdditionalWrapperEl.removeEventListener("mouseleave",this._hideActionsHandler)),this._actionAdditionalWrapperEl=null),this._actionsParentEl=null,this._removeValuesSpawnsSubscriptions(),this._values.destroy(),null!==this._valuesParentEl&&(this._valuesElements=[],this._valuesParentEl=null),null===(e=this._hideInvisibleHover)||void 0===e||e.destroy(),null===(t=this._hideValues)||void 0===t||t.destroy(),
null!==this._resizeObserver&&(this._resizeObserver.disconnect(),this._resizeObserver=null),null!==this._el){Object(i.ensureNotNull)(this._el.parentNode).removeChild(this._el),this._el=null}}getHeight(){return null===this._el?null:24}updateMode(e){this._mode===e&&null!==this._allButtonsWidth||(this._mode=e,this._updateAllButtonsWidth())}_render(){this._renderTitle(),this._renderActions(),this._renderValues(),this._el=document.createElement("div"),this._firstBlockWrapper=document.createElement("div"),this._firstBlockWrapper.classList.add(y.noWrapWrapper),this._firstBlockWrapper.appendChild(Object(i.ensureNotNull)(this._titleParentEl)),null!==this._actionsParentEl&&this._firstBlockWrapper.appendChild(this._actionsParentEl),this._el.appendChild(this._firstBlockWrapper),this._el.appendChild(Object(i.ensureNotNull)(this._valuesParentEl)),this._parentEl.append(this._el)}_renderTitle(){null===this._titleParentEl&&(this._titleParentEl=document.createElement("div"),this._titleParentEl.classList.add(y.titleWrapper));const e=this._titleParentEl;for(let t=0;t<this._titlesSpawns.length;t++){const s=document.createElement("div");s.classList.add(y.title,M[t],"apply-overflow-tooltip"),s.dataset.name="legend-source-title";const i=this._titlesSpawns[t].value();i.length>0?(s.appendChild(document.createTextNode(i)),s.classList.add(y.withDot)):s.classList.add(m.blockHidden),e.appendChild(s),this._titleElements.push(s)}}_renderActions(){if(!this._withActions)return;null===this._actionsParentEl&&(this._actionsParentEl=document.createElement("div"),this._actionsParentEl.classList.add(y.buttonsWrapper),this._parentEl.append(this._actionsParentEl),this._actionAdditionalWrapperEl=document.createElement("div"),this._actionAdditionalWrapperEl.classList.add(y.buttons),this._actionsParentEl.appendChild(this._actionAdditionalWrapperEl));const e=Object(i.ensureNotNull)(this._actionAdditionalWrapperEl),t=h.a?"large":"small";for(const s of this._model.actions()){const i=Object(c.a)(s,y.button,y.buttonIcon,m.blockHidden,t);e.appendChild(i)}}_isWidthButtonsMode(){return null!==this._el&&(this._el.classList.contains(y.withAction)||this._disabled.value()||this._selected.value()||this._stayInHoveredMode)}_updateTitlesHandler(e,t){const s=Object(i.ensureNotNull)(this._titleElements[e]),o=0===t.length;s.classList.toggle(m.blockHidden,o),s.classList.toggle(y.withDot,!o),v(s,t)}_updateStates(e){this._updateDisabledState(),this._updateDisabledOnIntervalState(),this._updateSelectedState(),this._updateLoadingState(),e&&this._clearDisableState()}_updateValuesHTMLElHandler(e,t){v(Object(i.ensure)(this._valuesElements[e].value),t),this._updateShowValues()}_updateValueColorHandler(e,t=""){Object(i.ensure)(this._valuesElements[e].value).style.color=t}_updateValueVisibleHandler(e,t){const s=Object(i.ensure)(this._valuesElements[e].value).closest("."+y.valueItem);null!==s&&s.classList.toggle(m.blockHidden,!t)}_updateShowLine(){null!==this._el&&this._el.classList.toggle(m.blockHidden,this._isRowHidden.value())}_createValuesSpawns(){
this._valuesSpawnArray=this._values.value().map(e=>({value:e.value.spawn(),color:e.color.spawn(),visible:e.visible.spawn()}))}_removeValuesSpawnsSubscriptions(){for(const e of this._valuesSpawnArray)e.value.destroy(),e.color.destroy(),e.visible.destroy();this._valuesSpawnArray=[]}_addValuesSpawnsSubscriptions(){for(let e=0;e<this._valuesSpawnArray.length;e++){const t=this._valuesSpawnArray[e];t.value.subscribe(this._updateValuesHTMLElHandler.bind(this,e)),t.color.subscribe(this._updateValueColorHandler.bind(this,e)),t.visible.subscribe(this._updateValueVisibleHandler.bind(this,e))}}_updateShowValues(){null!==this._valuesAdditionalWrapperEl&&this._valuesAdditionalWrapperEl.classList.toggle(m.blockHidden,this._isValuesShouldBeHidden())}_isValuesShouldBeHidden(){return!this._valuesSpawnArray.some(e=>e.value.value().length>0)}_addStatusesWidget(e,t,s){this._statusesWrapper=document.createElement("div"),this._statusesWrapper.classList.add(y.statusesWrapper),this._statusesWrapper.appendChild(e),Object(i.ensureNotNull)(this._firstBlockWrapper).appendChild(this._statusesWrapper),this._hideInvisibleHover=t.spawn(),this._hideInvisibleHover.subscribe(this._updateInvisibleHoverMode.bind(this),{callWithLast:!0}),this._hideValues=s.spawn(),this._hideValues.subscribe(this._updateHideValuesMode.bind(this),{callWithLast:!0}),this._updateStatusWidgetVisibility(this._disabled.value()),this._resizeObserver=new u.default(this._handlerRestrictTitleWidth.bind(this)),null!==this._actionsParentEl&&this._resizeObserver.observe(this._actionsParentEl),this._resizeObserver.observe(this._statusesWrapper)}_updateTitleMaxWidth(){if(null===this._firstBlockWrapper)return;const e=this._allButtonsWidth||0,t=(this._lastActionsWrapperWidth||0)+(this._lastStatusesWrapperWidth||0);this._isWidthButtonsMode()?this._firstBlockWrapper.style.maxWidth=`calc(100% - ${Math.max(e,t)}px)`:this._firstBlockWrapper.style.maxWidth=t>0?`calc(100% - ${t}px)`:""}_updateAllButtonsWidth(){this._allButtonsWidth=this._getButtonsCount()*N+1,this._updateTitleMaxWidth()}_updateInvisibleHoverMode(e){null!==this._el&&this._el.classList.toggle(y.invisibleHover,!e)}_updateHideValuesMode(e){null!==this._el&&this._el.classList.toggle(y.hideValues,e)}_showActions(){if(null===this._el||!this._withActions)return;this._el.classList.add(y.withAction);const e=null!==this._valuesParentEl&&null!==this._titleParentEl&&this._valuesParentEl.offsetTop===this._titleParentEl.offsetTop;this._el.classList.toggle(y.withTail,e),this._updateTitleMaxWidth()}_hideActions(){null!==this._el&&this._withActions&&!this._stayInHoveredMode&&(this._el.classList.remove(y.withAction),null!==this._valuesParentEl&&this._valuesParentEl.classList.remove(y.withTail),this._updateTitleMaxWidth())}_handlerRestrictTitleWidth(e){if(null===this._actionsParentEl||null===this._firstBlockWrapper)return;let t=null,s=null;for(const i of e)i.target===this._statusesWrapper&&(t=i.contentRect.width),i.target===this._actionsParentEl&&(s=i.contentRect.width)
;t===this._lastStatusesWrapperWidth&&s===this._lastActionsWrapperWidth||(null!==t&&(this._lastStatusesWrapperWidth=t),null!==s&&(this._lastActionsWrapperWidth=s),this._updateTitleMaxWidth())}_clearDesabledTimeout(){null!==this._disableTimeout&&(clearTimeout(this._disableTimeout),this._disableTimeout=null)}_updateDisabledState(){null!==this._el&&(this._el.classList.remove("tempDisabled"),this._el.classList.remove(y.hiddenLoading),this._clearDesabledTimeout(),this._disabled.value()?(this._el.classList.add(y.disabled),this._updateStatusWidgetVisibility(!0)):(this._el.classList.add("tempDisabled"),this._el.classList.add(y.hiddenLoading),this._disableTimeout=setTimeout(()=>{null!==this._el&&(!this._loading.value()&&this._el.classList.contains("tempDisabled")&&(this._el.classList.remove("tempDisabled"),this._el.classList.remove(y.hiddenLoading),this._el.classList.toggle(y.disabled,this._disabled.value()),this._updateStatusWidgetVisibility(!1)),this._clearDesabledTimeout())},1e3)),this._updateTitleMaxWidth())}_clearDisableState(){null!==this._el&&(this._el.classList.remove(y.hiddenLoading),this._el.classList.remove(y.disabled),this._el.classList.remove("tempDisabled"),this._updateStatusWidgetVisibility(this._disabled.value()),this._updateTitleMaxWidth())}_updateDisabledOnIntervalState(){var e;null===(e=this._el)||void 0===e||e.classList.toggle(y.disabledOnInterval,this._disabledOnInterval.value())}_updateSelectedState(){null!==this._el&&this._withActions&&this._el.classList.toggle(y.selected,this._selected.value())}_updateLoadingState(){if(null===this._el)return;this._el.classList.contains("tempDisabled")&&(this._loading.value()?this._el.classList.add(y.hiddenLoading):this._clearDisableState());const e=this._loading.value();this._el.classList.toggle(y.loading,e),null!==this._loader&&this._loader.toggleVisibility(e)}_updateShowTitles(){null!==this._titleParentEl&&(this._titleParentEl.classList.toggle(m.blockHidden,this._isTitleHidden.value()),null!==this._actionsParentEl&&this._actionsParentEl.classList.toggle(m.blockHidden,this._isTitleHidden.value()))}_updateValues(){this._removeValuesSpawnsSubscriptions(),this._createValuesSpawns(),null!==this._valuesParentEl&&null!==this._valuesAdditionalWrapperEl&&(this._valuesElements=[],this._valuesAdditionalWrapperEl.innerHTML=""),this._renderValues(),this._addValuesSpawnsSubscriptions(),this._updateShowValues()}_updateActionVisibilities(e){null!==this._actionsParentEl&&this._actionsParentEl.querySelectorAll("."+y.button)[e].classList.toggle(m.blockHidden,!this._actionsSpawnArray[e].visible.value())}_updateActionTitle(e){const t=this._actionsSpawnArray[e].title;null!==this._actionsParentEl&&null!==t&&this._actionsParentEl.querySelectorAll("."+y.button)[e].setAttribute("title",t.value())}_updateCustomTextColor(){const e=this._customTextColor.value()||"";for(const t of this._titleElements)null!==t&&(t.style.color=e);const t=Object(i.ensureNotNull)(this._valuesParentEl).querySelectorAll("."+y.valueTitle);for(let s=0;s<t.length;s++)t[s].style.color=e
;Object(i.ensureNotNull)(this._el).classList.toggle(y.withCustomTextColor,Boolean(e))}_updateStatusWidgetVisibility(e){null!==this._statusesWrapper&&this._statusesWrapper.classList.toggle(m.blockHidden,e)}}class E extends f{constructor(e,t,s){super(e,t,s),this._clientHeight=null,this._flagged=this._model.flagged().spawn(),this._flagged.subscribe(this._updateFlaggedState.bind(this)),this._updateStates(),s.statusWidgetEl&&this._addStatusesWidget(s.statusWidgetEl,s.hideInvisibleHover,s.hideValues),this._selected.subscribe(this._updateTitleMaxWidth.bind(this))}destroy(){super.destroy(),void 0!==this._flagged&&this._flagged.destroy()}getHeight(){return null===this._el?null:(null===this._clientHeight&&(this._clientHeight=this._el.clientHeight,0===this._clientHeight&&(this._clientHeight=null)),this._clientHeight)}_getButtonsCount(){return 1===this._mode?1:3}_render(){super._render();const e=Object(i.ensureNotNull)(this._el);e.classList.add(y.item,y.series),e.classList.toggle(y.onlyOneButtonCanBeStick,this._model.isOneButtonCanBeStick()),e.dataset.name="legend-series-item"}_updateStates(){super._updateStates(),this._updateFlaggedState()}_renderValues(){null===this._valuesParentEl&&(this._valuesParentEl=document.createElement("div"),this._valuesParentEl.classList.add(y.valuesWrapper),this._valuesAdditionalWrapperEl=document.createElement("div"),this._valuesAdditionalWrapperEl.classList.add(y.valuesAdditionalWrapper),this._valuesParentEl.appendChild(this._valuesAdditionalWrapperEl));const e=Object(i.ensureNotNull)(this._valuesAdditionalWrapperEl),t=this._values.value();for(const s of t){const t=document.createElement("div");t.classList.add(y.valueItem),t.classList.toggle(m.blockHidden,!s.visible.value());const i=document.createElement("div"),o=s.title.value()||"";i.classList.add(y.valueTitle),i.classList.toggle(m.blockHidden,0===o.length),i.appendChild(document.createTextNode(o)),t.appendChild(i);const l=document.createElement("div");l.classList.add(y.valueValue),l.style.color=s.color.value()||"",l.appendChild(document.createTextNode(s.value.value())),t.appendChild(l),this._valuesElements.push({title:i,value:l}),e.appendChild(t)}}_createValuesSpawns(){super._createValuesSpawns(),this._values.value().forEach((e,t)=>{this._valuesSpawnArray[t].title=e.title.spawn()})}_removeValuesSpawnsSubscriptions(){for(const e of this._valuesSpawnArray)e.title.destroy();super._removeValuesSpawnsSubscriptions()}_addValuesSpawnsSubscriptions(){super._addValuesSpawnsSubscriptions();for(let e=0;e<this._valuesSpawnArray.length;e++)this._valuesSpawnArray[e].title.subscribe(this._updateValuesTitleHTMLElHandler.bind(this,e))}_isValuesShouldBeHidden(){return!this._valuesSpawnArray.some(e=>e.value.value().length>0||(e.title.value()||"").length>0)}_updateValuesTitleHTMLElHandler(e,t=""){const s=Object(i.ensure)(this._valuesElements[e].title);v(s,t),s.classList.toggle(m.blockHidden,0===t.length),this._updateShowValues()}_isWidthButtonsMode(){return null!==this._el&&(void 0!==this._flagged&&Boolean(this._flagged.value())||super._isWidthButtonsMode())}
_updateFlaggedState(){if(void 0===this._flagged)return;Object(i.ensureNotNull)(this._el).classList.toggle(y.flagged,Boolean(this._flagged.value())),this._updateTitleMaxWidth()}}const C=w.isSafari?"click":"auxclick";class k extends f{constructor(e,t,s){super(e,t,s),this._wheelClickHandler=null,this._canUpdateRowVisibility=!0,this._globalRowVisibility=this._model.globalVisibility().spawn(),this._globalRowVisibility.subscribe(this._updateShowLine.bind(this),{callWithLast:!0}),this._has5Buttons=this._model.isPineScriptDataSource().spawn(),this._has5Buttons.subscribe(this._update5ButtonsStyles.bind(this)),this._updateStates(!this._disabled.value()),s.statusWidgetEl&&this._addStatusesWidget(s.statusWidgetEl,s.hideInvisibleHover,s.hideValues),this._selected.subscribe(this._updateTitleMaxWidth.bind(this)),s.withActions&&(this._wheelClickHandler=this._onWheelClicked.bind(this),null!==this._titleParentEl&&this._titleParentEl.addEventListener(C,this._wheelClickHandler))}destroy(){super.destroy(),this._has5Buttons.destroy(),this._globalRowVisibility&&this._globalRowVisibility.destroy(),null!==this._wheelClickHandler&&null!==this._titleParentEl&&this._titleParentEl.removeEventListener(C,this._wheelClickHandler)}_updateShowLine(){if(null===this._el||!this._canUpdateRowVisibility)return;const e=!this._globalRowVisibility.value();e?this._el.classList.toggle(m.blockHidden,e):super._updateShowLine()}_getButtonsCount(){switch(this._mode){case 4:return this._has5Buttons.value()?5:4;case 3:return 3;default:return 2}}_render(){super._render();const e=Object(i.ensureNotNull)(this._el);e.classList.add(y.item,y.study),e.dataset.name="legend-source-item"}_renderValues(){null===this._valuesParentEl&&(this._valuesParentEl=document.createElement("div"),this._valuesParentEl.classList.add(y.valuesWrapper),this._valuesAdditionalWrapperEl=document.createElement("div"),this._valuesAdditionalWrapperEl.classList.add(y.valuesAdditionalWrapper),this._valuesParentEl.appendChild(this._valuesAdditionalWrapperEl));const e=Object(i.ensureNotNull)(this._valuesAdditionalWrapperEl),t=this._values.value();for(const s of t){const t=document.createElement("div");t.classList.add(y.valueItem),t.classList.toggle(m.blockHidden,!s.visible.value());const i=document.createElement("div");i.classList.add(y.valueValue),i.style.color=s.color.value()||"",i.appendChild(document.createTextNode(s.value.value())),t.appendChild(i),this._valuesElements.push({value:i}),e.appendChild(t)}}_update5ButtonsStyles(e){null!==this._el&&(this._el.classList.toggle(y.has5Buttons,e),this._updateAllButtonsWidth())}_onWheelClicked(e){1===e.button&&this._model.onRemoveSource()}}var V=s("JWMC");function x(e){Object(V.trackEvent)("GUI","Legend action",e)}var W=s("6dGu"),T=s("rGGD"),L=s("Y+EN"),O=s("vYP1");const A=window.t("Hide Indicator Legend"),D=window.t("Show Indicator Legend"),H=window.t("Show Object Tree");class B{constructor(e,t,s){this._el=null,this._counterEl=null,this._arrowIconEL=null,this._objectTreeEl=null,this._mode=0,this._parentEl=e,this._themedColor=t.spawn(),
this._themedColor.subscribe(this._updateThemedColor.bind(this)),this._sourceCount=s.visibleDataSourceCount.spawn(),this._sourceCount.subscribe(this._updateSourceCount.bind(this)),this._isStateOpen=s.isDataSourcesCollapsed.spawn(),this._isStateOpen.subscribe(this._updateState.bind(this)),this._showObjectsTree=s.showObjectsTree.spawn(),this._showObjectsTree.subscribe(this._updateObjectTreeVisibility.bind(this)),this._render(),this._updateState(),this._updateThemedColor(this._themedColor.value()),this._updateObjectTreeVisibility(this._showObjectsTree.value()),this._toggleStateHandler=Object(p.c)(s.onCollapseDataSources),this._showObjectTreeHandler=Object(p.c)(s.onShowObjectsTreeDialog),null!==this._el&&(this._el.addEventListener("touchend",this._toggleStateHandler),this._el.addEventListener("click",this._toggleStateHandler),this._el.addEventListener("contextmenu",e=>{e.preventDefault(),e.stopPropagation()})),null!==this._objectTreeEl&&(this._objectTreeEl.addEventListener("touchend",this._showObjectTreeHandler),this._objectTreeEl.addEventListener("click",this._showObjectTreeHandler))}destroy(){this._sourceCount.destroy(),this._isStateOpen.destroy(),null!==this._objectTreeEl&&(this._objectTreeEl.removeEventListener("touchend",this._showObjectTreeHandler),this._objectTreeEl.removeEventListener("click",this._showObjectTreeHandler),this._objectTreeEl=null),this._arrowIconEL=null,this._counterEl=null,null!==this._el&&(this._el.removeEventListener("touchend",this._toggleStateHandler),this._el.removeEventListener("click",this._toggleStateHandler),this._el.innerHTML="",this._el=null)}setMode(e){this._mode=e?1:0,this._updateTooltip()}_render(){this._el=document.createElement("div"),this._el.className=y.toggler+" apply-common-tooltip",this._arrowIconEL=document.createElement("div"),this._arrowIconEL.classList.add(y.iconArrow),this._arrowIconEL.innerHTML=h.a?T:W,this._el.appendChild(this._arrowIconEL),this._objectTreeEl=document.createElement("div"),this._objectTreeEl.classList.add(y.objectTree),this._objectTreeEl.innerHTML=h.a?O:L,this._el.appendChild(this._objectTreeEl),this._counterEl=document.createElement("div"),this._counterEl.classList.add(y.counter),this._counterEl.appendChild(document.createTextNode(String(this._sourceCount.value()))),this._el.appendChild(this._counterEl),this._parentEl.appendChild(this._el)}_updateThemedColor(e){if(null!==this._el)if(e.length>0){const[t,s,i]=Object(r.parseRgb)(e);this._el.style.backgroundColor=Object(r.rgbaToString)([t,s,i,Object(r.normalizeAlphaComponent)(.8)])}else this._el.style.removeProperty("background-color")}_updateSourceCount(e){v(Object(i.ensureNotNull)(this._counterEl),String(e));{const t=Object(i.ensureNotNull)(this._el),s=e<1;t.classList.toggle(m.blockHidden,s);const o=1===e;t.classList.toggle(y.onlyOneSourceShown,o)}}_updateCounterVisibility(e){if(null===this._counterEl)return;const t=1===e;this._counterEl.classList.toggle(m.blockHidden,t)}_updateState(){const e=!this._isStateOpen.value();this._parentEl.classList.toggle(y.closed,e),this._updateTooltip(),
x((e?"Hide":"Show")+" not main sources")}_tooltip(){return 1===this._mode?H:this._isStateOpen.value()?A:D}_updateTooltip(){null!==this._el&&this._el.setAttribute("title",this._tooltip())}_updateObjectTreeVisibility(e){Object(i.ensureNotNull)(this._el).classList.toggle(y.objectsTreeCanBeShown,e)}}var P=s("+6II"),z=s("R5JZ");function j(e,t){const s=new l.a(t(e.value()));e.subscribe(e=>{s.setValue(t(e))});return s.readonly().spawn(()=>e.unsubscribe())}const N=h.a?44:28,I=d.enabled("object_tree_legend_mode");class R{constructor(e,t){this._renderToggler=null,this._mainDataSourceRenderer=null,this._dataSourceRenderers=[],this._wrapText=null,this._parentEl=document.createElement("div"),this._mainDataSourceEl=null,this._dataSourcesEl=null,this._dataSourcesAdditionalWrapperEl=null,this._collapsedDataSourcesWrapperEl=null,this._collapsedDataSourcesEl=null,this._outsideEventForCollapsedTooltip=null,this._options=e,this._togglerOptions=t,this._isStudiesLegendHidden=e.isStudiesLegendHidden.spawn(),this._isStudiesLegendHidden.subscribe(this._updateLegendVisibility.bind(this)),this._isAllLegendHidden=e.isAllLegendHidden.spawn(),this._isAllLegendHidden.subscribe(this._updateLegendVisibility.bind(this)),this._updateLegendVisibility(),this._themedColor=e.themedColor.spawn(),this._themedColor.subscribe(this._setCustomBg.bind(this)),this._showBackground=e.showBackground.spawn(),this._showBackground.subscribe(this._setCustomBg.bind(this)),this._backgroundTransparency=e.backgroundTransparency.spawn(),this._backgroundTransparency.subscribe(this._setCustomBg.bind(this)),this._collapsedDataSourcesCountSpawn=e.collapsedDataSourcesCount.spawn(),this._collapsedDataSourcesCountSpawn.subscribe(this._updateCollapsedSourcesCount.bind(this)),this._showCollapsedDataSourcesTooltipHandler=this._showCollapsedDataSourcesTooltip.bind(this),h.a||(this._wrapText=e.wrapText.spawn(),this._wrapText.subscribe(this._updateWrapText.bind(this)),this._updateWrapText(this._wrapText.value())),this._parentEl.classList.add(y.legend),this._parentEl.classList.toggle(y.noActions,!this._options.withActions),this._parentEl.classList.toggle(y.touchMode,h.a),this._parentEl.classList.toggle(y.newCollapser,!0),this._parentEl.dataset.name="legend",this._parentEl.style.setProperty("--legend-source-item-button-width",N+"px"),this._parentEl.addEventListener("contextmenu",t=>{t.preventDefault(),e.showLegendWidgetContextMenu(t)})}destroy(){if(this._isStudiesLegendHidden.destroy(),this._isAllLegendHidden.destroy(),this._themedColor.destroy(),this._showBackground.destroy(),this._backgroundTransparency.destroy(),this._collapsedDataSourcesCountSpawn.destroy(),h.a&&null!==this._collapsedDataSourcesWrapperEl&&this._collapsedDataSourcesWrapperEl.removeEventListener("touchend",this._showCollapsedDataSourcesTooltipHandler),this._outsideEventForCollapsedTooltip&&this._outsideEventForCollapsedTooltip(),null!==this._wrapText&&this._wrapText.destroy(),null!==this._dataSourcesAdditionalWrapperEl&&(this._dataSourcesAdditionalWrapperEl.innerHTML="",this._dataSourcesAdditionalWrapperEl=null),
null!==this._dataSourcesEl&&(this._dataSourcesEl.innerHTML="",this._dataSourcesEl=null),null!==this._renderToggler&&(this._renderToggler.destroy(),this._renderToggler=null),null!==this._mainDataSourceRenderer&&(this._mainDataSourceRenderer.destroy(),this._mainDataSourceRenderer=null),0!==this._dataSourceRenderers.length){for(const e of this._dataSourceRenderers)e.destroy();this._dataSourceRenderers=[]}this._parentEl.innerHTML="",delete this._parentEl}addMainDataSource(e,t){this._renderMainDataSourceEl(),this._mainDataSourceRenderer=new E(e,Object(i.ensureNotNull)(this._mainDataSourceEl),{withActions:this._options.withActions,customTextColor:this._options.customTextColor,statusWidgetEl:t.getElement(),hideInvisibleHover:j(t.visibleWidgetsCount,e=>Boolean(e)),hideValues:t.errorWidgetIsShown}),this._updateLegendVisibility(),e.onDestroy().subscribe(this,()=>{null!==this._mainDataSourceRenderer&&(this._mainDataSourceRenderer.destroy(),this._mainDataSourceRenderer=null)},!0)}addDataSources(e,t){this._renderDataSourcesEl();const s=Object(i.ensureNotNull)(this._dataSourcesAdditionalWrapperEl);for(let i=0;i<e.length;i++){const o=e[i],l=new k(o,s,{withActions:this._options.withActions,customTextColor:this._options.customTextColor,statusWidgetEl:t[i].getElement(),hideInvisibleHover:j(t[i].visibleWidgetsCount,e=>Boolean(e)),hideValues:t[i].errorWidgetIsShown});this._dataSourceRenderers.push(l),this._updateLegendVisibility(),o.onDestroy().subscribe(this,()=>{const e=this._dataSourceRenderers.indexOf(l);-1!==e&&(this._dataSourceRenderers[e].destroy(),this._dataSourceRenderers.splice(e,1))},!0)}}addCustomWidget(e,t){if(0===t.block){this._renderMainDataSourceEl();const s=Object(i.ensureNotNull)(this._mainDataSourceEl);1===t.position&&e.renderTo(s,s.firstChild),0===t.position&&e.renderTo(s)}if(1===t.block){this._renderDataSourcesEl();const s=Object(i.ensureNotNull)(this._dataSourcesAdditionalWrapperEl);1===t.position&&e.renderTo(s,s.firstChild),0===t.position&&e.renderTo(s)}}firstTitle(){return this._parentEl.firstElementChild}getElement(){return this._parentEl}updateMode(e){const t=I&&e<112?1:e<205?2:e<222?3:4;null!==this._mainDataSourceRenderer&&this._mainDataSourceRenderer.updateMode(t);for(const e of this._dataSourceRenderers)e.updateMode(t);this._parentEl.classList.toggle(y.medium,3===t),this._parentEl.classList.toggle(y.minimized,2===t),this._parentEl.classList.toggle(y.micro,1===t),null!==this._renderToggler&&this._renderToggler.setMode(1===t);const s=h.a||e<542;this._parentEl.classList.toggle(y.directionColumn,s)}getMainSourceHeight(){return null===this._mainDataSourceRenderer?0:this._mainDataSourceRenderer.getHeight()}getDataSourceHeight(){return 0===this._dataSourceRenderers.length?0:this._dataSourceRenderers[0].getHeight()}_renderMainDataSourceEl(){null===this._mainDataSourceEl&&(this._mainDataSourceEl=document.createElement("div"),this._parentEl.insertBefore(this._mainDataSourceEl,this._dataSourcesEl))}_renderDataSourcesEl(){null===this._dataSourcesEl&&(this._dataSourcesEl=document.createElement("div"),
this._dataSourcesEl.classList.add(y.sourcesWrapper),this._renderToggle(this._dataSourcesEl),this._dataSourcesAdditionalWrapperEl=document.createElement("div"),this._dataSourcesAdditionalWrapperEl.classList.add(y.sources),this._dataSourcesEl.appendChild(this._dataSourcesAdditionalWrapperEl),this._renderCollapsedCounter(this._dataSourcesAdditionalWrapperEl),this._parentEl.appendChild(this._dataSourcesEl))}_renderToggle(e){this._options.showToggleButton&&(this._renderToggler=new B(e,this._options.themedColor,this._togglerOptions))}_renderCollapsedCounter(e){this._collapsedDataSourcesWrapperEl=document.createElement("div"),this._collapsedDataSourcesWrapperEl.className=`${y.item} ${y.last}`,this._collapsedDataSourcesEl=document.createElement("span"),this._collapsedDataSourcesEl.className=y.text+" apply-common-tooltip",this._collapsedDataSourcesWrapperEl.append(this._collapsedDataSourcesEl),e.append(this._collapsedDataSourcesWrapperEl),h.a&&this._collapsedDataSourcesWrapperEl.addEventListener("touchend",this._showCollapsedDataSourcesTooltipHandler),this._updateCollapsedSourcesCount(this._collapsedDataSourcesCountSpawn.value())}_showCollapsedDataSourcesTooltip(){Object(P.c)(this._collapsedDataSourcesEl,{text:this._options.collapsedDataSourcesTitle.value()}),this._addOutsideEventForHideTooltip()}_addOutsideEventForHideTooltip(){null!==this._outsideEventForCollapsedTooltip&&this._outsideEventForCollapsedTooltip(),this._outsideEventForCollapsedTooltip=Object(z.a)(new CustomEvent("timestamp").timeStamp,this._collapsedDataSourcesWrapperEl,()=>{null!==this._outsideEventForCollapsedTooltip&&this._outsideEventForCollapsedTooltip(),Object(P.a)()},window.document,{touchEnd:!0})}_updateCollapsedSourcesCount(e){if(null===this._collapsedDataSourcesWrapperEl||null===this._collapsedDataSourcesEl)return;const t=0===e;this._collapsedDataSourcesWrapperEl.classList.toggle(m.blockHidden,t),t||(v(this._collapsedDataSourcesEl,"+"+e),this._collapsedDataSourcesEl.setAttribute("title",this._options.collapsedDataSourcesTitle.value()))}_updateLegendVisibility(){this._parentEl.classList.toggle(m.blockHidden,this._isAllLegendHidden.value()),null!==this._dataSourcesEl&&this._dataSourcesEl.classList.toggle(m.blockHidden,this._isStudiesLegendHidden.value())}_setCustomBg(){const e=this._showBackground.value(),t=this._themedColor.value(),s=this._backgroundTransparency.value();let i="";if(e){const[e,o,l]=Object(r.parseRgb)(t);i=Object(r.rgbaToString)([e,o,l,Object(r.normalizeAlphaComponent)(1-s/100)])}this._parentEl.style.color=i}_updateWrapText(e){this._parentEl.classList.toggle(y.noWrap,!e)}}var F=s("Tmoa"),K=s("aIyQ"),U=s.n(K),Y=s("Ialn"),G=s("QloM"),Z=s("1AAW"),X=s("CLNU");function $(e){return void 0!==e?F.resetTransparency(e):e}const q=window.t("Show"),J=window.t("Hide");class Q{constructor(e,t,s,i,o){this._values=new l.a([]),this._actions=[],this._onDestroy=new U.a,this._loading=new l.a(!1),this._moreActionCM=null,this._model=e,this._source=t,this._options=s,this._callbacks=i,this._contextMenuOptions=o,this._disabled=new l.a(this._getDisabledState()),
this._disabledOnInterval=new l.a(this._getDisabledOnIntervalState()),this._selected=new l.a(!1),this._isTitleHidden=new l.a(this._getTitleHiddenValue()),this._isValuesHidden=new l.a(this._getValuesHiddenValue()),this._isRowHidden=new l.a(this._getRowHiddenValue()),Object(Z.a)(()=>({}),this._isTitleHidden,this._isValuesHidden,this._disabled).subscribe(this._updateRowVisibilities.bind(this)),this._values.subscribe(()=>{this._isValuesHidden.setValue(this._getValuesHiddenValue())})}destroy(){}onDestroy(){return this._onDestroy}titles(){return this._titles.map(e=>e.readonly())}values(){return this._values.readonly()}actions(){return this._actions}disabled(){return this._disabled.readonly()}disabledOnInterval(){return this._disabledOnInterval.readonly()}selected(){return this._selected.readonly()}loading(){return this._loading.readonly()}isTitleHidden(){return this._isTitleHidden.readonly()}isValuesHidden(){return this._isValuesHidden.readonly()}isRowHidden(){return this._isRowHidden.readonly()}update(){this._updateTitle(),this._updateValues(),this._updateStates()}updateSource(e){this._source!==e&&(this._source=e,this.update(),this._isTitleHidden.setValue(this._getTitleHiddenValue()),this._isValuesHidden.setValue(this._getValuesHiddenValue()))}onToggleDisabled(){const e=this._source.properties().childs().visible,t=!e.value();this._model.setProperty(e,t,`${t?"Show":"Hide"} ${this._source.title()}`),x((t?"Show":"Hide")+" source")}onShowSettings(){this._source.userEditEnabled()&&(this.setSourceSelected(),this._callbacks.showChartPropertiesForSource(this._source,G.TabNames.style),x("Settings for source"))}onShowMoreActions(e){return this._options.readOnlyMode?Promise.resolve(null):(this._callbacks.updateActions(),x("Show source context menu"),this._callbacks.showContextMenuForSources([this._source],this._calcNewPosition(e),this._contextMenuOptions))}setSourceSelected(){this._model.selectionMacro(e=>{e.clearSelection(),e.addSourceToSelection(this._source)})}_moreActionHandler(e){e.preventDefault(),null!==this._moreActionCM&&this._moreActionCM.isShown()?this._moreActionCM=null:(this.setSourceSelected(),this.onShowMoreActions(e).then(e=>{this._moreActionCM=e}))}_updateTitle(){const e=this._source.statusView();if(null===e)return;const t=e.getSplitTitle();for(let e=0;e<this._titles.length;e++){const s=t[e],i=Object(X.clean)(Array.isArray(s)?s.join(" "):s||"",!0);this._titles[e].setValue(i)}}_updateStates(){this._disabled.setValue(this._getDisabledState()),this._disabledOnInterval.setValue(this._getDisabledOnIntervalState()),this._selected.setValue(this._model.selection().isSelected(this._source)),this._loading.setValue(Boolean(this._source.isLoading()))}_hasValues(){return this._values.value().length>0}_getEyeTitle(){return this._disabled.value()?q:J}_getDisabledState(){return!this._source.properties().visible.value()}_updateRowVisibilities(){this._isRowHidden.setValue(this._getRowHiddenValue())}_getRowHiddenValue(){
return this._options.readOnlyMode&&this._disabled.value()||this._isTitleHidden.value()&&(this._isValuesHidden.value()||this._disabled.value())}_calcNewPosition(e){let t={};if(e.hasOwnProperty("touches")&&e.touches.length>0)t={clientX:e.touches[0].clientX,clientY:e.touches[0].clientY};else if(null!==e.target){const s=e.target.getBoundingClientRect();t={clientX:Object(Y.isRtl)()?s.right:s.left,clientY:s.top+s.height+3}}else{const s=e;t={clientX:s.clientX,clientY:s.clientY}}return t}}var ee=s("z4c1"),te=s("61S9"),se=s("vWJB"),ie=s("wZiV");const oe=window.t("More"),le=window.t("Flag Symbol"),ne=window.t("Unflag Symbol"),ae=d.enabled("show_hide_button_in_legend");class re extends Q{constructor(e,t,s,i,o){super(e,t,s,i,o),this._titles=[new l.a(""),new l.a(""),new l.a(""),new l.a("")],this._symbolMarker=null,this._symbolMarkerIcon=null,this._flagged=new l.a(null),this._symbolAction=null,this._symbol=null,this._isOneButtonCanBeStick=!1,this._createActions(),this._updateSymbolMarker(),this._model.model().properties().paneProperties.legendProperties.showSeriesTitle.subscribe(this,()=>{this._isTitleHidden.setValue(this._getTitleHiddenValue())});const n=[this._model.model().properties().paneProperties.legendProperties.showSeriesOHLC,this._model.model().properties().paneProperties.legendProperties.showBarChange];for(const e of n)e.subscribe(this,()=>{this._isValuesHidden.setValue(this._getValuesHiddenValue())});this.update(),this._source.onStatusChanged().subscribe(this,()=>{this._loading.setValue(this._source.isLoading())})}destroy(){super.destroy(),this._model.model().properties().paneProperties.legendProperties.showSeriesTitle.unsubscribeAll(this),this._model.model().properties().paneProperties.legendProperties.showSeriesOHLC.unsubscribeAll(this),this._model.model().properties().paneProperties.legendProperties.showBarChange.unsubscribeAll(this),this._source.onStatusChanged().unsubscribeAll(this),this._onDestroy.fire()}flagged(){return this._flagged.readonly()}onShowSettings(){this._source.userEditEnabled()&&this._callbacks.showGeneralChartProperties(G.TabNames.symbol)}isOneButtonCanBeStick(){return this._isOneButtonCanBeStick}_updateValues(){const e=this._source.legendView(),t=this._values.value(),s=e.marketTitle(),i=e.marketTitle().length>0;if(0===t.length){const t={value:new l.a(""),color:new l.a(""),visible:new l.a(i),title:new l.a(s)},o=e.items().map(e=>({value:new l.a(e.value()),color:new l.a($(e.color())),visible:new l.a(e.visible()),title:new l.a(e.title())}));this._values.setValue([t].concat(o))}else{t[0].title.setValue(s),t[0].visible.setValue(i);const o=e.items();for(let e=0;e<o.length;e++){const s=o[e];t[e+1].value.setValue(s.value()),t[e+1].color.setValue($(s.color())),t[e+1].visible.setValue(s.visible()),t[e+1].title.setValue(s.title())}}}_updateStates(){super._updateStates(),this._updateSymbolMarker()}_getDisabledOnIntervalState(){return!1}_getTitleHiddenValue(){return!this._model.model().properties().paneProperties.legendProperties.showSeriesTitle.value()}_getValuesHiddenValue(){
return!this._hasValues()||!this._model.model().properties().paneProperties.legendProperties.showSeriesOHLC.value()&&!this._model.model().properties().paneProperties.legendProperties.showBarChange.value()}_createActions(){if(ae){const e={iconMap:new Map([["large",te],["small",ee]]),action:Object(p.c)(this.onToggleDisabled.bind(this)),visible:new l.a(!0),className:y.eye,title:new l.a(this._getEyeTitle()),dataset:{name:"legend-show-hide-action"}};this._actions.push(e),this._disabled.subscribe(()=>{e.title.setValue(this._getEyeTitle())})}this._actions.push({iconMap:new Map([["large",ie],["small",se]]),action:this._moreActionHandler.bind(this),visible:new l.a(!0),title:new l.a(oe),dataset:{name:"legend-more-action"}})}_getMarkerTitle(){return null!==this._symbolMarker?this._symbolMarker.isMarked()?ne:le:""}_symbolActionHandler(){null!==this._symbolMarker&&(this._updateSymbolMarker(),x("Change flag state"))}_updateSymbolMarker(){this._isOneButtonCanBeStick=!0}}var de=s("3ClC"),he=s("fZEr");var ue=s("z61+"),ce=s("txPx"),_e=s("RgOa"),pe=s("2CEX"),me=s("D8x7"),be=s("bNWL"),ge=s("cKqi"),we=s("YzC7");Object(ce.getLogger)("Chart.LegendWidget");const ve=window.t("Settings"),ye=window.t("Source code"),Se=window.t("Remove"),Me=window.t("More"),fe=(window.t("Error"),window.t("Could not get Pine source code."),d.enabled("study_buttons_in_legend"),d.enabled("show_hide_button_in_legend")),Ee=d.enabled("property_pages"),Ce=d.enabled("format_button_in_legend"),ke=d.enabled("delete_button_in_legend");class Ve extends Q{constructor(e,t,s,i,o){super(e,t,s,i,o),this._titles=[new l.a(""),new l.a("")],this._error=new l.a(!1),this._isPineScriptDataSource=new l.a(!1),this._pineAction=null,this._globalVisibility=new l.a(!0),this._createActions();const n=[this._model.model().properties().paneProperties.legendProperties.showSeriesTitle,this._model.model().properties().paneProperties.legendProperties.showStudyTitles];for(const e of n)e.subscribe(this,()=>{this._isTitleHidden.setValue(this._getTitleHiddenValue())});const a=[this._model.model().properties().paneProperties.legendProperties.showSeriesOHLC,this._model.model().properties().paneProperties.legendProperties.showBarChange,this._model.model().properties().paneProperties.legendProperties.showStudyValues];for(const e of a)e.subscribe(this,()=>{this._isValuesHidden.setValue(this._getValuesHiddenValue())});this.update()}destroy(){super.destroy();const e=this._model.model().properties().paneProperties.legendProperties;e.showSeriesTitle.unsubscribeAll(this),e.showStudyTitles.unsubscribeAll(this),e.showSeriesOHLC.unsubscribeAll(this),e.showBarChange.unsubscribeAll(this),e.showStudyValues.unsubscribeAll(this),this._onDestroy.fire()}error(){return this._error.readonly()}isPineScriptDataSource(){return this._isPineScriptDataSource.readonly()}updateSource(e){this._source!==e&&(this._values.setValue([]),super.updateSource(e),this._updateAbleShowSourceCode())}onRemoveSource(){var e;this._source.isUserDeletable()&&(this._source.hasChildren()?(e=this._model.removeSource.bind(this._model,this._source,!1),
Object(he.showConfirm)({title:window.t("Confirm Remove Study Tree"),text:window.t("Do you really want to delete study and all of it's children?"),onConfirm:({dialogClose:t})=>{e(),t()}})):this._model.removeSource(this._source,!1),x("Remove sources"))}onShowSourceCode(){0}setGlobalVisibility(e){this._globalVisibility.setValue(e)}globalVisibility(){return this._globalVisibility.readonly()}getFullTitle(){return this._titles.map(e=>e.value()).join(" ")}_updateValues(){const e=this._source.legendView();if(null===e)return;if(0===e.items().length)return;const t=this._values.value();if(0===t.length){const t=e.items().map(e=>({value:new l.a(e.value()),color:new l.a($(e.color())),visible:new l.a(e.visible())}));this._values.setValue(t)}else{const s=e.items();for(let e=0;e<s.length;e++){const i=t[e],o=s[e];i.value.setValue(o.value()),i.color.setValue($(o.color())),i.visible.setValue(o.visible())}}}_updateStates(){super._updateStates(),void 0!==this._error&&this._error.setValue(Boolean(this._source.isFailed()))}_getTitleHiddenValue(){return this._isSymbolLikeStudy()?!this._model.model().properties().paneProperties.legendProperties.showSeriesTitle.value():!this._model.model().properties().paneProperties.legendProperties.showStudyTitles.value()}_getDisabledOnIntervalState(){return!(!Object(de.isStudy)(this._source)&&!Object(de.isStudyStub)(this._source))&&!this._source.isActualInterval()}_getValuesHiddenValue(){return!this._hasValues()||(this._isSymbolLikeStudy()?!this._model.model().properties().paneProperties.legendProperties.showSeriesOHLC.value()&&!this._model.model().properties().paneProperties.legendProperties.showBarChange.value():!this._model.model().properties().paneProperties.legendProperties.showStudyValues.value())}_isSymbolLikeStudy(){return this._source instanceof ge.study_Overlay||this._source instanceof we.a}_updateAbleShowSourceCode(){0}_updateVisibilityPineAction(e){null!==this._pineAction&&(this._pineAction.visible.setValue(e),this._isPineScriptDataSource.setValue(e))}_createActions(){if(!this._options.readOnlyMode){if(this._pineAction={iconMap:new Map([["large",pe],["small",pe]]),action:Object(p.c)(this.onShowSourceCode.bind(this)),visible:new l.a(!1),title:new l.a(ye),dataset:{name:"legend-pine-action"}},fe){const e={iconMap:new Map([["large",te],["small",ee]]),action:Object(p.c)(this.onToggleDisabled.bind(this)),visible:new l.a(!0),className:y.eye,title:new l.a(this._getEyeTitle()),dataset:{name:"legend-show-hide-action"}};this._actions.push(e),this._disabled.subscribe(()=>{e.title.setValue(this._getEyeTitle())})}Ee&&Ce&&(!Object(de.isStudy)(this._source)||new ue.a(this._source.metaInfo()).hasUserEditableOptions())&&this._actions.push({iconMap:new Map([["large",_e],["small",_e]]),action:Object(p.c)(this.onShowSettings.bind(this)),visible:new l.a(!0),title:new l.a(ve),dataset:{name:"legend-settings-action"}}),ke&&this._actions.push({iconMap:new Map([["large",be],["small",me]]),action:Object(p.c)(this.onRemoveSource.bind(this)),visible:new l.a(!0),title:new l.a(Se),dataset:{name:"legend-delete-action"}}),
this._actions.push({iconMap:new Map([["large",ie],["small",se]]),action:this._moreActionHandler.bind(this),visible:new l.a(!0),title:new l.a(Me),dataset:{name:"legend-more-action"}})}}}var xe=s("7KDR"),We=s("5VQP"),Te=s("x2L+"),Le=s("2uTr"),Oe=s("MXV9");function Ae(e,t,s){e.setProperty(t,!t.value(),s)}function De(e,t,s,i,o,l){return function(e,t,s,i,o){const l=[],n=i.get(0);if(void 0!==n){const e=n.get(1);void 0!==e&&e.length>0&&(l.push(...e),l.push(new xe.Separator))}const a=e.model().properties().paneProperties.legendProperties;l.push(new xe.Action({checkable:!0,checked:a.showSeriesTitle.value(),label:He,statName:"Show Symbol",onExecute:()=>Ae(e,a.showSeriesTitle,"Change Symbol Description Visibility")})),t.showOpenMarketStatus&&l.push(new xe.Action({checkable:!0,checked:Te.b.value(),label:Be,statName:"Show Open market status",onExecute:()=>Ae(e,Te.b,"Change open market status visibility")}));if(l.push(new xe.Action({checkable:!0,checked:a.showSeriesOHLC.value(),label:Pe,statName:"Show OHLC Values",onExecute:()=>Ae(e,a.showSeriesOHLC,"Change OHLC Values Visibility")})),l.push(new xe.Action({checkable:!0,checked:a.showBarChange.value(),label:ze,statName:"Show Bar Change Values",onExecute:()=>Ae(e,a.showBarChange,"Change Bar Change Visibility")})),l.push(new xe.Separator),void 0!==n){const e=n.get(0);void 0!==e&&e.length>0&&(l.push(...e),l.push(new xe.Separator))}const r=i.get(1);if(void 0!==r){const e=r.get(1);void 0!==e&&e.length>0&&(l.push(...e),l.push(new xe.Separator))}if(l.push(new xe.Action({checkable:!0,checked:a.showStudyTitles.value(),label:je,statName:"Show Indicator Titles",onExecute:()=>Ae(e,a.showStudyTitles,"Change Indicator Titles Visibility")})),l.push(new xe.Action({checkable:!0,checked:a.showStudyArguments.value(),label:Ne,statName:"Show Indicator Arguments",onExecute:()=>Ae(e,a.showStudyArguments,"Change Indicator Arguments Visibility")})),l.push(new xe.Action({checkable:!0,checked:a.showStudyValues.value(),label:Ie,statName:"Show Indicator Values",onExecute:()=>Ae(e,a.showStudyValues,"Change Indicator Values Visibility")})),void 0!==r){const e=r.get(0);void 0!==e&&e.length>0&&(l.push(...e),l.push(new xe.Separator))}0;t.settings&&(l.push(new xe.Separator),l.push(new xe.Action({label:Object(Le.appendEllipsis)(window.t("Settings")),icon:Oe,statName:"Settings...",onExecute:()=>s(G.TabNames.legend)})));return We.ContextMenuManager.createMenu(l,{},o)}(e,t,s,i,l).then(e=>(e.show(o),e))}const He=window.t("Show Symbol"),Be=window.t("Show Open market status"),Pe=window.t("Show OHLC Values"),ze=window.t("Show Bar Change Values"),je=window.t("Show Indicator Titles"),Ne=window.t("Show Indicator Arguments"),Ie=window.t("Show Indicator Values");window.t("Wrap text"),w.CheckMobile.any();var Re=s("+DwS"),Fe=s("Y7w9"),Ke=s("+6ja"),Ue=s("kcTO"),Ye=s("4nwx"),Ge=s("99ZO"),Ze=s("jCNj"),Xe=s("GOhO"),$e=s("5mo2");function qe(e){return e===Ge.WeekDays.SUNDAY?Ge.WeekDays.SATURDAY:e-1}function Je(e){return e===Ge.WeekDays.SATURDAY?Ge.WeekDays.SUNDAY:e+1}function Qe(e,t){const s=qe(t)
;return 0===e[s].entries.length?Qe(e,s):{dayIndex:s,entries:e[s].entries}}function et(e,t){const s=Je(t);return 0===e[s].entries.length?et(e,s):{dayIndex:s,entries:e[s].entries}}function tt(e){for(;e>Ze.minutesPerDay;)e-=Ze.minutesPerDay;const t=e%60,s=(e-t)/60;return Object(Ue.numberToStringWithLeadingZero)(s,2)+":"+Object(Ue.numberToStringWithLeadingZero)(t,2)}const st={[Ge.WeekDays.MONDAY]:{title:Ye.weekDaysShortNames[Ge.WeekDays.MONDAY],isActive:!1,entries:[]},[Ge.WeekDays.TUESDAY]:{title:Ye.weekDaysShortNames[Ge.WeekDays.TUESDAY],isActive:!1,entries:[]},[Ge.WeekDays.WEDNESDAY]:{title:Ye.weekDaysShortNames[Ge.WeekDays.WEDNESDAY],isActive:!1,entries:[]},[Ge.WeekDays.THURSDAY]:{title:Ye.weekDaysShortNames[Ge.WeekDays.THURSDAY],isActive:!1,entries:[]},[Ge.WeekDays.FRIDAY]:{title:Ye.weekDaysShortNames[Ge.WeekDays.FRIDAY],isActive:!1,entries:[]},[Ge.WeekDays.SATURDAY]:{title:Ye.weekDaysShortNames[Ge.WeekDays.SATURDAY],isActive:!1,entries:[]},[Ge.WeekDays.SUNDAY]:{title:Ye.weekDaysShortNames[Ge.WeekDays.SUNDAY],isActive:!1,entries:[]}};function it(e,t){return e.start.value===t.start.value&&e.end.value===t.end.value}function ot(e,t){return it(e[0],t[0])}function lt(e){const t=e.start(),s=e.length(),i=e.sessionStartDayOfWeek(),o=e.sessionEndDayOfWeek(),l=tt(t),n=tt(t+s);return s>Ze.minutesPerDay?`${Ye.weekDaysShortNames[i]} ${l} — ${Ye.weekDaysShortNames[o]} ${n}`:`${l} — ${n}`}function nt(e,t,s,i){return Math.abs(i-s)>1?`${Ye.weekDaysShortNames[s]} ${e.title} — ${Ye.weekDaysShortNames[i]} ${t.title}`:`${e.title} — ${t.title}`}function at(e,t,s){for(const i of t){let t=i.sessionStartDayOfWeek();const o=i.start(),l=i.length(),n=o+l,a=[];if(n<=Ze.minutesPerDay)a.push([o,n]);else{const e=Math.min(Ze.minutesPerDay-o,l);a.push([o,o+e]),a.push([0,l-e])}for(let o=0;o<a.length;o++){const l=a[o],[n,r]=l,d=n/Ze.minutesPerDay,h=Object(Fe.lowerbound)(e[t].entries,d,(e,t)=>e.start.value<t),u={start:{value:d,title:tt(n),isFirstOrLastPoint:n===i.start()},end:{value:r/Ze.minutesPerDay,title:tt(r),isFirstOrLastPoint:a.length-1===o},type:s,tooltip:lt(i),showStartForLastEntry:!1,showEndForFirstEntry:!1};e[t].entries.splice(h,0,u),t=Je(t)}}}class rt{constructor(e){this.sessionsDays=new l.a(Object(n.clone)(st)),this.todaySession=new l.a(Object(n.clone)({entries:[]})),this._todayInExchangeTime=null;const t=e;this._symbolInfo=Object(Xe.b)(t.symbolInfo.bind(t),t.dataEvents().symbolResolved()),this._symbolInfo.subscribe(this._updateEntriesBySubSessions.bind(this),{callWithLast:!0})}destroy(){this._symbolInfo.destroy()}currentTimeValue(){return null===this._todayInExchangeTime?-1:Ze.get_minutes_from_midnight(this._todayInExchangeTime)/Ze.minutesPerDay}timezone(){const e=this._symbolInfo.value();return null===e?"":Object(Ke.timezoneTitle)(e.timezone)}_updateEntriesBySubSessions(e){var t;this._updateTodayWithOffsets(e);const s=this._createSubSessionSpecs(e),o=(l=(null===(t=this._todayInExchangeTime)||void 0===t?void 0:t.getUTCDay())||(new Date).getDay())<Ge.WeekDays.SATURDAY?l+1:Ge.WeekDays.SUNDAY;var l;const a=Object(n.clone)(st);a[o].isActive=!0
;for(const e of Array.from(s.keys())){at(a,Object(i.ensureDefined)(s.get(e)).getEntriesForWeekByCalendar(Object(i.ensureNotNull)(this._todayInExchangeTime)),e)}Object.values(a).some(e=>0!==e.entries.length)&&(function(e){const t=tt(0),s=tt(Ze.minutesPerDay),o=Object(n.clone)(e);for(const l of Object.keys(e)){const n=parseInt(l),a=e[n].entries;if(0===a.length){a.push({start:{value:0,title:t,isFirstOrLastPoint:!0},end:{value:1,title:s,isFirstOrLastPoint:!0},type:3,tooltip:`${t} — ${s}`,showStartForLastEntry:!1,showEndForFirstEntry:!1});continue}const r=qe(n),d=Je(n),h=o[r].entries,u=o[d].entries,c=Qe(o,n),_=et(o,n),p=a.length;let m=0;for(let e=0;e<p;e++){const s=a[e],o=e>0?a[e-1]:null,l=0===e;if(0===s.start.value||0===s.start.value&&1===s.end.value||null!==o&&s.start.value===o.end.value){m++;continue}const r=l?c.entries[c.entries.length-1]:Object(i.ensureNotNull)(o),d={start:{value:l?0:a[e-1].end.value,title:l?t:a[e-1].end.title,isFirstOrLastPoint:!(l&&h.length>0)||1===h[h.length-1].end.value},end:{value:s.start.value,title:s.start.title,isFirstOrLastPoint:!0},type:3,tooltip:nt(r.end,s.start,c.dayIndex,n),showStartForLastEntry:!1,showEndForFirstEntry:!1};a.splice(m,0,d),m=e+2}const b=a[a.length-1];1!==b.end.value&&a.push({start:{value:b.end.value,title:b.end.title,isFirstOrLastPoint:!0},end:{value:1,title:tt(Ze.minutesPerDay),isFirstOrLastPoint:!(u.length>0)||0===u[0].start.value},type:3,tooltip:nt(b.end,_.entries[0].start,n,_.dayIndex),showStartForLastEntry:!1,showEndForFirstEntry:!1})}}(a),function(e){for(const i of Object.keys(e)){const o=parseInt(i),l=e[o].entries;if(1===l.length)continue;const n=qe(o),a=Je(o),r=l[0],d=l[l.length-1];r.start.isFirstOrLastPoint||(r.showStartForLastEntry=(t=l,s=e[n].entries,it(t[t.length-1],s[s.length-1]))),d.end.isFirstOrLastPoint||(d.showEndForFirstEntry=ot(l,e[a].entries))}var t,s}(a)),this.sessionsDays.setValue(a),this.todaySession.setValue(a[o])}_createSubSessionSpecs(e){if(null===e)return new Map;if(void 0===e.subsessions)return new Map([[0,new $e.SessionSpec(e.timezone,e.session,e.session_holidays,e.corrections)]]);const t=["regular","premarket","postmarket"],s=new Map;for(const i of t){let t=null;switch(i){case"regular":t=0;break;case"premarket":t=1;break;case"postmarket":t=2}if(null!==t){const o=e.subsessions.find(e=>e.id===i);void 0!==o&&s.set(t,new $e.SessionSpec(e.timezone,o["session-display"]||o.session,"",o["session-correction"]))}}return s}_updateTodayWithOffsets(e){if(null===e)return void(this._todayInExchangeTime=null);const t=1e3*window.ChartApiInstance.serverTimeOffset();this._todayInExchangeTime=Ze.get_cal_from_unix_timestamp_ms(Ze.get_timezone(e.timezone),Date.now()+t)}}var dt=s("q1tI"),ht=s.n(dt),ut=s("TSYQ"),ct=s("YFKU"),_t=s("vqb8"),pt=s("e3/o"),mt=s("tfDh");const bt=new WeakMap;function gt(e){return bt.has(e)||bt.set(e,Object(pt.randomHash)()),Object(i.ensureDefined)(bt.get(e))}
const wt=new Map([[0,mt.green],[1,mt.orange],[2,mt.blue],[3,mt.gray]]),vt=new Map([[0,Object(ct.t)("Market open")],[1,Object(ct.t)("Pre-market")],[2,Object(ct.t)("Post-market")],[3,Object(ct.t)("Market closed")]]);function yt(e){const{segment:t,forceStart:s,forceEnd:i}=e,o=t.end.value-t.start.value,l=o<.03,n={left:100*t.start.value+"%",width:`calc(${100*o}% + ${l?2:0}px)`},a=ut(mt.segment,wt.get(t.type),(s||t.start.isFirstOrLastPoint)&&mt.start,(i||t.end.isFirstOrLastPoint)&&mt.end,l&&mt.small,"common-tooltip-html","apply-common-tooltip"),r=function(e,t){return`<div class="${mt.tooltip}">\n\t\t<span class="${wt.get(t)}">${vt.get(t)}</span>\n\t\t<span class="${mt.time}">${e}</span>\n\t</div>`}(t.tooltip,t.type);return ht.a.createElement("div",{className:a,style:n,"data-tooltip":r})}function St(e){const{sessionDay:t}=e,s=t.entries.map((e,s)=>ht.a.createElement(yt,{key:gt(e)+"Segment",segment:e,forceStart:0===s&&3===e.type,forceEnd:s===t.entries.length-1&&3===e.type})),i=ut(mt.sessionDay,t.isActive&&mt.active);return ht.a.createElement("div",{className:i},ht.a.createElement("div",{className:mt.weekDay},t.title),ht.a.createElement("div",{className:mt.sessionDaySegments},s))}function Mt(e){const{sessionDays:t,currentTimeMark:s}=e,i=[],o=parseInt(Object.keys(t).filter(e=>t[parseInt(e)].isActive)[0]),l=t[o],n=l.entries.filter(e=>e.start.value<=s&&e.end.value>=s)[0];!n.start.isFirstOrLastPoint&&n.showStartForLastEntry?i.push(l.entries[l.entries.length-1].start):i.push(n.start);const a=!n.end.isFirstOrLastPoint&&n.showEndForFirstEntry?l.entries[0].end:n.end;if(i[0].value!==a.value&&i.push(a),0===i.length)return null;i.sort((e,t)=>e.value-t.value);const r=i.map(e=>ht.a.createElement("div",{key:gt(e),className:mt.timeMark},e.title)),d=100*(2===i.length?i[1].value-i[0].value:0),h=ut(d>12&&mt.timeMarkSegmentAlignByEnds,mt.timeMarkSegment);return ht.a.createElement("div",{className:mt.sessionDay},ht.a.createElement("div",{className:mt.weekDay}),ht.a.createElement("div",{className:mt.timeMarkWrapper},ht.a.createElement("div",{className:h,style:{left:100*i[0].value+"%",width:d+"%"}},r)))}class ft{constructor(e){this._fullSessionScheduleViewModel=new rt(e)}destroy(){this._fullSessionScheduleViewModel.destroy()}renderer(e,t){return function(e){const{key:t,className:s,now:i,timezone:o}=e,l=Object(_t.a)({watchedValue:e.sessionDays}),n=Object.values(l).filter(e=>e.isActive)[0],a=ut(s,mt.wrapper);return ht.a.createElement("div",{key:t,className:a},ht.a.createElement("div",{className:mt.timezone},o),ht.a.createElement("div",{className:mt.sessionDayWrapper},ht.a.createElement(St,{sessionDay:n}),ht.a.createElement("div",{className:mt.nowWrapper},ht.a.createElement("div",{className:mt.now,style:{left:100*i+"%"}}))),ht.a.createElement(Mt,{sessionDays:l,currentTimeMark:i}))}({key:e,className:t,sessionDays:this._fullSessionScheduleViewModel.sessionsDays,now:this._fullSessionScheduleViewModel.currentTimeValue(),timezone:this._fullSessionScheduleViewModel.timezone()})}updateSource(e){}}var Et=s("HGP3"),Ct=s("25b6");function kt(e,t){
const s=new l.a(e()),i={};t.forEach(t=>t.subscribe(i,()=>{s.setValue(e())}));return s.readonly().spawn(()=>t.forEach(e=>e.unsubscribeAll(i)))}var Vt=s("rh3U");class xt{constructor(e){this.isBlinkingMode=new l.a(!1),this._status=new l.a(null),this._fullTooltip=new l.a(null),this._iconClassNames=new l.a(null),this._visible=new l.a(!1),this._tooltip=new l.a(null),this._icon=new l.a(null),this._className=new l.a(null),this._infoMaps=e,this._size=e.size||"small",this._status.subscribe(this._updateByStatus.bind(this),{callWithLast:!0}),this._className.subscribe(this._updateIconClassName.bind(this))}turnOffBlinkingMode(){}status(){return this._status}tooltip(){return this._tooltip}icon(){return this._icon}className(){return this._className}visible(){return this._visible}size(){return this._size}fullInfo(){return this._fullTooltip}_getTooltip(e){var t,s;return null!==(s=null===(t=this._infoMaps.tooltipMap)||void 0===t?void 0:t.get(e))&&void 0!==s?s:null}_getIcon(e){let t;const s=this._infoMaps.iconMap.get(e);return void 0!==s&&(t=s.get(this._size)),t||null}_getClassName(e){return this._infoMaps.classNameMap.get(e)||null}_getTitle(e){var t,s;return null!==(s=null===(t=this._infoMaps.titleMap)||void 0===t?void 0:t.get(e))&&void 0!==s?s:null}_getTitleColor(e){var t,s;return null!==(s=null===(t=this._infoMaps.titleColorMap)||void 0===t?void 0:t.get(e))&&void 0!==s?s:null}async _getHtml(e){var t,s,i;return null!==(i=null===(s=null===(t=this._infoMaps.htmlMap)||void 0===t?void 0:t.get(e))||void 0===s?void 0:s.map(Ct.b))&&void 0!==i?i:null}_getAction(e){var t,s;return null!==(s=null===(t=this._infoMaps.actionMap)||void 0===t?void 0:t.get(e))&&void 0!==s?s:null}async _updateFullTooltipByStatus(e){const t=await this._getHtml(e);this._status.value()===e&&this._fullTooltip.setValue([{icon:this._icon.value(),iconClassName:this._iconClassNames.value(),title:this._getTitle(e),titleColor:this._getTitleColor(e),html:t,size:this._size,action:this._getAction(e)}])}_updateByStatus(e){if(null===e)return this._icon.setValue(null),this._tooltip.setValue(null),void this._visible.setValue(!1);this._icon.setValue(this._getIcon(e)),this._className.setValue(this._getClassName(e)),this._tooltip.setValue(this._getTooltip(e)),this._visible.setValue(!0),this._updateFullTooltipByStatus(e)}_updateIconClassName(e){null!==e?this._iconClassNames.setValue([Vt.statusItem,e]):this._iconClassNames.setValue(null)}}var Wt=s("Vdly");const Tt=[];function Lt(){return Wt.getJSON("tv.alreadyBlinkedStatuses",Tt)}const Ot=new l.a(Lt());function At(e){const t=Wt.getJSON("tv.alreadyBlinkedStatuses",Tt);t.includes(e)||(t.push(e),Wt.setJSON("tv.alreadyBlinkedStatuses",t),Ot.setValue(Lt()))}Wt.onSync.subscribe(null,()=>Ot.setValue(Lt()));const Dt=Ot;var Ht=s("Cf1E"),Bt=s("VrrN"),Pt=s("VrXG"),zt=s("LIcf"),jt=s("Uua9"),Nt=s("9Crk"),It=s("Tq3g")
;const Rt=Object(ce.getLogger)("Chart.LegendWidget"),Ft=["TFEXDelayForGuest","MOEXDelayForGuest","CHIXAuDelayForGuest","NGMDelayForGuest","DEForGuest","ICESGDelayForGuest"],Kt=new Map([["DelayToRealtime",new Map([["small",Bt],["large",Pt]])],["DelayNoRealtime",new Map([["small",Bt],["large",Pt]])],["TFEXDelayForGuest",new Map([["small",Bt],["large",Pt]])],["MOEXDelayForGuest",new Map([["small",Bt],["large",Pt]])],["CHIXAuDelayForGuest",new Map([["small",Bt],["large",Pt]])],["NGMDelayForGuest",new Map([["small",Bt],["large",Pt]])],["ICESGDelayForGuest",new Map([["small",Bt],["large",Pt]])],["DEForGuest",new Map([["small",Bt],["large",Pt]])],["EOD",new Map([["small",zt],["large",jt]])],["TickByTick",new Map([["small",Nt],["large",It]])],["BATSToRealtime",new Map([["small",Nt],["large",It]])],["DelayWithoutMarketAgreement",new Map([["small",Bt],["large",Pt]])]]),Ut=new Map([["DelayToRealtime",Vt.delay],["DelayNoRealtime",Vt.delay],["TFEXDelayForGuest",Vt.delay],["MOEXDelayForGuest",Vt.delay],["CHIXAuDelayForGuest",Vt.delay],["NGMDelayForGuest",Vt.delay],["ICESGDelayForGuest",Vt.delay],["DEForGuest",Vt.delay],["EOD",Vt.eod],["TickByTick",Vt.notAccurate],["BATSToRealtime",Vt.notAccurate],["DelayWithoutMarketAgreement",Vt.delay]]),Yt=new Map([["DelayToRealtime",Et.colorsPalette["color-delay-mode"]],["DelayNoRealtime",Et.colorsPalette["color-delay-mode"]],["TFEXDelayForGuest",Et.colorsPalette["color-delay-mode"]],["MOEXDelayForGuest",Et.colorsPalette["color-delay-mode"]],["CHIXAuDelayForGuest",Et.colorsPalette["color-delay-mode"]],["NGMDelayForGuest",Et.colorsPalette["color-delay-mode"]],["ICESGDelayForGuest",Et.colorsPalette["color-delay-mode"]],["DEForGuest",Et.colorsPalette["color-delay-mode"]],["EOD",Et.colorsPalette["color-eod-mode"]],["TickByTick",Et.colorsPalette["color-notaccurate-mode"]],["BATSToRealtime",Et.colorsPalette["color-notaccurate-mode"]],["DelayWithoutMarketAgreement",Et.colorsPalette["color-delay-mode"]]]),Gt=window.t("Data is delayed"),Zt=window.t("End of day data"),Xt=window.t("One update per second"),$t=window.t("Cboe BZX"),qt=window.t("{exchange} by {originalExchange}"),Jt=Object(Ct.b)(window.t("{symbolName} data is delayed by {time} minutes.")),Qt=Object(Ct.b)(window.t("{listedExchange} real-time data is available for free to registered users.")),es=Object(Ct.b)(window.t("To get real-time data for {description}, please buy the real-time data package.")),ts=Object(Ct.b)(window.t("Real-time data for {description} is not supported right now. We may support it in the future.")),ss=Object(Ct.b)(window.t("Data is updated once a day.")),is=Object(Ct.b)(window.t("Data on our Basic plan is updated once per second, even if there are more updates on the market.")),os=Object(Ct.b)(window.t("Data is updated once per second, even if there are more updates on the market.")),ls=Object(Ct.b)(window.t("Paid plans feature faster data updates.")),ns=Object(Ct.b)(window.t("Real-time data for {symbolName} is provided by {exchange} exchange.")),as=Object(Ct.b)(window.t("This data is real-time, but it’s slightly different to its official counterpart coming from primary exchanges.")),rs=Object(Ct.b)(window.t("This data is real-time, but it’s slightly different to its official counterpart coming from {exchange}.")),ds=Object(Ct.b)(window.t("If you'd like {listedExchange} real-time data you'll need to complete an Exchange Agreement. Don't worry, it only takes a few clicks"))
;window.t("Create a free account"),window.t("Learn more"),Object(Ct.b)(window.t("Fill out Exchange Agreements"));class hs extends xt{constructor(e,t,s){super({iconMap:Kt,classNameMap:Ut,titleColorMap:Yt,size:t}),this._dataUpdatedMode=new l.a(null).spawn(),this._options=s,this._model=e,this._dataModeBlinkingStatuses=Dt.spawn(),this._dataModeBlinkingStatuses.subscribe(this._updateBlinkingMode.bind(this)),this.turnOffBlinkingMode=this._turnOffBlinking.bind(this),this.setModel(e)}destroy(){this._dataUpdatedMode.destroy(),this._dataModeBlinkingStatuses.destroy()}setModel(e){if(this._dataUpdatedMode.destroy(),null===e)return this._model=e,void(this._dataUpdatedMode=new l.a(null).spawn());this._dataUpdatedMode=e.status().spawn(),this._dataUpdatedMode.subscribe(this._updateStatus.bind(this),{callWithLast:!0})}_getTooltip(){const e=this._getShortTexts();return null===e?null:Object.values(e).join(" · ")}async _updateFullTooltipByStatus(){const e=this._dataUpdatedMode.value();if(null===e)return void this._fullTooltip.setValue(null);const t=this._getShortTexts(),s=await this._getHtmls(),i=await this._getActions();if(e!==this._dataUpdatedMode.value())return;const o=[];for(const l of e)o.push({icon:this._getIcon(l),iconClassName:this._iconClassNames.value(),title:t&&t[l],titleColor:this._getTitleColor(l),html:s&&s[l],size:this._size,action:i&&i[l]});this._fullTooltip.setValue(o)}_updateStatus(e){const t=null!==e?e[0]:null;super._updateByStatus(t),this._updateBlinkingMode()}async _getHtmls(){const e=this._dataUpdatedMode.value();if(null===e||null===this._model)return Promise.resolve(null);const t={},s=this._model.symbolName();let i=null,o=null;try{i=await this._model.description(),o=this._model.exchange()}catch(e){Rt.logError("Can't get exchange description, reason: "+Object(Ht.a)(e))}for(const l of e)if(t[l]=[],["DelayToRealtime","DelayNoRealtime","DelayWithoutMarketAgreement",...Ft].includes(l)&&(t[l].push(Jt.format({symbolName:s,time:this._model.time().toString()})),this._options.subscriptionFullInfo&&null!==i&&"DelayToRealtime"===l&&t[l].push(es.format({description:`<b>${i}</b>`})),null!==i&&"DelayNoRealtime"===l&&t[l].push(ts.format({description:`<b>${i}</b>`})),"DelayWithoutMarketAgreement"===l&&t[l].push(ds.format({listedExchange:this._model.listedExchange()})),this._options.subscriptionFullInfo&&Ft.includes(l)&&t[l].push(Qt.format({listedExchange:this._model.listedExchange()}))),"EOD"===l&&(t[l]=[ss]),"TickByTick"===l&&(t[l].push(this._options.subscriptionFullInfo?is:os),this._options.subscriptionFullInfo&&t[l].push(ls)),null!==o&&"BATSToRealtime"===l){let e=this._model.listedExchange();0,t[l].push(ns.format({symbolName:s,exchange:o}),""!==e?rs.format({exchange:e}):as)}return Object.keys(t).length>0?t:null}async _getActions(){if(null===this._dataUpdatedMode.value()||null===this._model)return null;const e={};return Object.keys(e).length>0?e:null}_getShortTexts(){var e;const t=this._dataUpdatedMode.value();if(null===t||null===this._model)return null;const s={}
;for(const i of t)if(["DelayToRealtime","DelayNoRealtime",...Ft,"DelayWithoutMarketAgreement"].includes(i)&&(s[i]=Gt),"EOD"===i&&(s[i]=Zt),"TickByTick"===i&&(s[i]=Xt),"BATSToRealtime"===i){let t=null!==(e=this._model.firstReplacedByBatsExchange())&&void 0!==e?e:"";0,s[i]=""!==t?qt.format({exchange:t,originalExchange:$t}):$t}return Object.keys(s).length>0?s:null}_updateBlinkingMode(){const e=this._dataUpdatedMode.value();if(null===e)return;const t=this._dataModeBlinkingStatuses.value();for(const s of e)if(!t.includes(s))return void this.isBlinkingMode.setValue(!0);this.isBlinkingMode.setValue(!1)}_turnOffBlinking(){const e=this._dataUpdatedMode.value();if(null!==e)for(const t of e)At(t)}}var us=s("JmzL"),cs=s("M3mX");const _s=window.t("Study Error"),ps=new Map([[!0,new Map([["small",us],["large",cs]])],[!1,new Map([["small",""],["large",""]])]]),ms=new Map([[!0,Vt.dataProblemLow],[!1,null]]),bs=new Map([[!0,_s],[!1,null]]),gs=new Map([[!0,_s],[!1,null]]),ws=new Map([[!0,Et.colorsPalette["color-data-problem"]],[!1,null]]);class vs extends xt{constructor(e,t,s,i){super({iconMap:ps,classNameMap:ms,tooltipMap:bs,titleMap:gs,titleColorMap:ws,size:s}),this._dataSourceErrorStatus=new l.a(null).spawn(),this._lastError=null,this._options=i,this.setSource(e,t)}destroy(){this._dataSourceErrorStatus.destroy()}setSource(e,t){this._dataSourceErrorStatus.destroy(),this._dataSourceErrorStatus=kt(()=>e.statusProvider({}).errorStatus(),t),this._dataSourceErrorStatus.subscribe(this._updateStatus.bind(this),{callWithLast:!0})}_getTooltip(e){const t=this._dataSourceErrorStatus.value();if(e&&null!==t){const e=t.title;if(void 0!==e)return e}return super._getTooltip(e)}_getTitle(e){const t=this._dataSourceErrorStatus.value();if(e&&null!==t){const e=t.title;if(void 0!==e)return e}return super._getTitle(e)}async _getHtml(e){const t=this._dataSourceErrorStatus.value();return null!==t?[Object(Ct.b)(t.error)]:null}_getAction(e){this._dataSourceErrorStatus.value();return null}_updateStatus(e){const t=this._status.value();null!==e?(this._status.setValue(!0),t&&this._lastError!==e.error&&this._updateByStatus(!0),this._lastError=e.error):(this._status.setValue(null),this._lastError=null)}}const ys=window.t("Data error"),Ss=new Map([["high",new Map([["small",us],["large",cs]])],["low",new Map([["small",us],["large",cs]])]]),Ms=new Map([["high",Vt.dataProblemHigh],["low",Vt.dataProblemLow]]),fs=new Map([["high",ys],["low",ys]]),Es=new Map([["high",Et.colorsPalette["color-data-problem"]],["low",Et.colorsPalette["color-data-problem"]]]);class Cs extends xt{constructor(e,t){super({tooltipMap:fs,iconMap:Ss,classNameMap:Ms,titleMap:fs,titleColorMap:Es,size:t}),this._dataProblem=new l.a(null).spawn(),this._isDataProblemCritical=new l.a(!1),this.setModel(e)}destroy(){this._dataProblem.destroy()}isDataProblemCritical(){return this._isDataProblemCritical}setModel(e){this._dataProblem.destroy(),null!==e?(this._dataProblem=e.status().spawn(),this._dataProblem.subscribe(this._updateStatus.bind(this),{callWithLast:!0})):this._dataProblem=new l.a(null).spawn()}
async _getHtml(e){const t=this._dataProblem.value();return null===t?null:[Object(Ct.b)(t.text)]}_updateStatus(e){var t;const s=null!==(t=null==e?void 0:e.severity)&&void 0!==t?t:null;this._status.setValue(s),this._isDataProblemCritical.setValue(function(e){return"high"===e}(s))}}class ks extends xt{constructor(e,t){super(t),this._booleanStatus=new l.a(!1).spawn(),this.updateStatus(e)}destroy(){this._booleanStatus.destroy()}updateStatus(e){this._booleanStatus.destroy(),this._booleanStatus=e.spawn(),this._booleanStatus.subscribe(this._updateStatus.bind(this),{callWithLast:!0})}_updateStatus(e){e?this._status.setValue(!0):this._status.setValue(null)}}var Vs=s("YGQl"),xs=s("Jjb7");const Ws=window.t("Invalid Symbol"),Ts=window.t("This symbol doesn't exist, please pick another one."),Ls=new Map([[!0,new Map([["small",Vs],["large",xs]])],[!1,new Map([["small",""],["large",""]])]]),Os=new Map([[!0,Vt.invalidSymbol],[!1,null]]),As=new Map([[!0,Ws],[!1,null]]),Ds=new Map([[!0,Ws],[!1,null]]),Hs=new Map([[!0,Et.colorsPalette["color-invalid-symbol"]],[!1,null]]),Bs=new Map([[!0,[Ts]],[!1,null]]),Ps=new Map([[!0,null],[!1,null]]);class zs{constructor(e){this._el=document.createElement("div"),this._prevCustomClass=null,this._icon=e.icon.spawn(),this._icon.subscribe(this._updateIcon.bind(this),{callWithLast:!0}),this._className=e.className.spawn(),this._className.subscribe(this._updateClassName.bind(this),{callWithLast:!0}),this._visible=e.visible.spawn(),this._visible.subscribe(this._updateVisibility.bind(this),{callWithLast:!0}),this._size=e.size||"small",this._render(e.parentEl),e.isBlinking&&(this._isBlinking=e.isBlinking.spawn(),this._isBlinking.subscribe(this._updateBlinkingMode.bind(this),{callWithLast:!0}),this._turnOffBlinking=e.turnOffBlinking)}destroy(){this._visible.destroy(),this._icon.destroy(),this._isBlinking&&this._isBlinking.destroy(),this._el.remove()}onClick(){this._turnOffBlinking&&this._turnOffBlinking()}visible(){return this._visible}_render(e){this._el.classList.add(Vt.statusItem,Vt[this._size]),e.appendChild(this._el)}_updateVisibility(e){this._el.classList.toggle("js-hidden",!e)}_updateIcon(e){this._el.innerHTML=e||""}_updateClassName(e){this._prevCustomClass!==e&&(null!==this._prevCustomClass&&this._el.classList.remove(this._prevCustomClass),null!==e&&this._el.classList.add(e),this._prevCustomClass=e)}_updateBlinkingMode(e){this._el.classList.toggle(Vt.blinking,e)}}class js{constructor(e,t,s,i){this.element=document.createElement("div"),this._blinkingSpawns=[],this._iconsRenderers=[];const o=[Vt.statuses,"apply-common-tooltip","common-tooltip-wide"];h.a&&o.push(Vt.touchMode),this.element.classList.add(...o,Vt[e]),this._visibleWidgetsCount=t.spawn(),this._visibleWidgetsCount.subscribe(this._updateSpecialClassAndTooltip.bind(this)),this._tooltips=s.spawn(),this._tooltips.subscribe(this._updateTooltip.bind(this)),this._onClickCallback=i.onClick,this._onClickHandler=this._onClick.bind(this),this.element.addEventListener("click",this._onClickHandler)}destroy(){for(const e of this._iconsRenderers)e.destroy()
;for(const e of this._blinkingSpawns)e.destroy();this._visibleWidgetsCount.destroy(),this._tooltips.destroy(),this.element.removeEventListener("click",this._onClickHandler),this.element.remove()}addStatusModel(e){this._iconsRenderers.push(new zs({visible:e.visible,icon:e.model.icon(),className:e.model.className(),size:e.model.size(),parentEl:this.element,isBlinking:e.model.isBlinkingMode,turnOffBlinking:e.model.turnOffBlinkingMode}));const t=e.model.isBlinkingMode.spawn();t.subscribe(this._updateBlinkingMode.bind(this)),this._blinkingSpawns.push(t),this._updateBlinkingMode()}_onClick(e){e.preventDefault();const t=this._iconsRenderers.filter(e=>e.visible().value());for(const e of t)e.onClick();let s=14;t.length>1&&(s-=2);const i=this.element.getBoundingClientRect(),o={x:i.left-s,y:i.bottom+4};this._onClickCallback(o)}_updateTooltip(){this.element.setAttribute("title",this._tooltips.value().join(" · "))}_updateSpecialClassAndTooltip(){const e=this._visibleWidgetsCount.value();this.element.classList.toggle(Vt.oneWidgetsVisible,1===e),this.element.classList.toggle(Vt.twoWidgetsVisible,2===e),this.element.classList.toggle(Vt.threeWidgetsVisible,3===e),this._updateTooltip()}_updateBlinkingMode(){const e=this._blinkingSpawns.some(e=>e.value());this.element.classList.toggle(Vt.blinking,e)}}var Ns=s("9lPX"),Is=s("MyT/"),Rs=s("jXu8"),Fs=s("cbig"),Ks=s("G2LI"),Us=s("QkND"),Ys=s("Gp/h"),Gs=s("S48P");const Zs=window.t("Market open"),Xs=window.t("Pre-market"),$s=window.t("Post-market"),qs=window.t("Market closed"),Js=window.t("Holiday"),Qs=window.t("All's well — Market is open."),ei=window.t("Morning. Market is open for pre-market trading."),ti=window.t("Evening. Market is open for post-market trading."),si=window.t("Time for a walk — this market is closed."),ii=window.t("Market is currently on holiday. Lucky them."),oi=new Map([["market",new Map([["small",Is],["large",Rs]])],["pre_market",new Map([["small",Ys],["large",Gs]])],["post_market",new Map([["small",Ks],["large",Us]])],["out_of_session",new Map([["small",Ns],["large",Ns]])],["holiday",new Map([["small",Fs],["large",Fs]])]]),li=new Map([["market",Vt.marketStatusOpen],["pre_market",Vt.marketStatusPre],["post_market",Vt.marketStatusPost],["out_of_session",Vt.marketStatusClose],["holiday",Vt.marketStatusHoliday]]),ni=new Map([["market",Zs],["pre_market",Xs],["post_market",$s],["out_of_session",qs],["holiday",Js]]),ai=new Map([["market",Zs],["pre_market",Xs],["post_market",$s],["out_of_session",qs],["holiday",Js]]),ri=new Map([["market",Et.colorsPalette["color-market-open"]],["pre_market",Et.colorsPalette["color-pre-market"]],["post_market",Et.colorsPalette["color-post-market"]],["out_of_session",Et.colorsPalette["color-market-closed"]],["holiday",Et.colorsPalette["color-market-holiday"]]]),di=new Map([["market",[Qs]],["pre_market",[ei]],["post_market",[ti]],["out_of_session",[si]],["holiday",[ii]]]),hi=new Map([["market",null],["pre_market",null],["post_market",null],["out_of_session",null],["holiday",null]]),ui=window.t("You can turn this data on or off.")
;class ci extends xt{constructor(e,t,s){super({tooltipMap:ni,iconMap:oi,classNameMap:li,titleMap:ai,titleColorMap:ri,htmlMap:di,actionMap:hi,size:t}),this._marketStatus=new l.a(null).spawn(),this._options=s,this.setModel(e)}destroy(){this._marketStatus.destroy()}setModel(e){this._marketStatus.destroy(),null!==e?(this._marketStatus=e.status().spawn(),this._marketStatus.subscribe(this._updateStatus.bind(this),{callWithLast:!0})):this._marketStatus=new l.a(null).spawn()}async _getHtml(e){const t=await super._getHtml(e);return null!==t&&(this._options.preMarketSolution||this._options.postMarketSolution)&&("pre_market"===e&&this._options.preMarketSolution&&t.push(ui),"post_market"===e&&this._options.postMarketSolution&&t.push(ui)),t}_getAction(e){return super._getAction(e)}_updateStatus(e){this._status.setValue(e)}}class _i{constructor(e,t,s){this.visibleWidgetsCount=new l.a(0),this.errorWidgetIsShown=new l.a(!1),this._size=h.a?"large":"small",this._tooltips=new l.a([]),this._visibilitySpawns=[],this._tooltipSpawns=[],this._statusWidgetInfos=[],this._renderer=new js(this._size,this.visibleWidgetsCount,this._tooltips,{onClick:this._handleToggleDropdown.bind(this)}),this._symbolInvalidViewModel=null,this._dataSourceErrorStatusViewModel=null,this._marketStatusViewModel=null,this._dataUpdatedModeViewModel=null,this._dataProblemViewModel=null,this._sessionWidget=null,this._isSymbolInvalid=null,this._dataSourceHasErrorVisible=null,this._dataSourceErrorCanBeShown=new l.a(!1),this._marketStatusCanBeShown=new l.a(!1),this._dataUpdatedModeCanBeShown=new l.a(!1),this._dataProblemCanBeShown=new l.a(!1),this._isDataProblemCritical=null,this._container=document.createElement("div"),this._menuOpened=!1,this._menuPosition=null,this._source=e,this._model=t,this._options=s,this._recreateWidgets(),this._addSubscriptionForSymbolInvalid(),null!==this._dataSourceHasErrorVisible&&(this._dataSourceHasErrorVisible.subscribe(this._updateStatusWidgetsVisibilities.bind(this)),this._dataSourceHasErrorVisible.subscribe(this._updateErrorWidgetIsShown.bind(this))),this._options.dataProblemEnabled&&null!==this._isDataProblemCritical&&this._isDataProblemCritical.subscribe(this._updateStatusWidgetsVisibilities.bind(this));for(const e of this._tooltipSpawns)e.subscribe(this._updateTooltips.bind(this));for(const e of this._visibilitySpawns)e.subscribe(this._updateVisibleWidgetsCount.bind(this)),e.subscribe(this._updateTooltips.bind(this));this._updateErrorWidgetIsShown(),this._updateStatusWidgetsVisibilities(),this._updateVisibleWidgetsCount(),this._updateTooltips()}destroy(){var e;this._source.properties().hasChild("symbol")&&this._source.properties().symbol.listeners().unsubscribeAll(this),this._options.sourceStatusesEnabled&&null!==this._isSymbolInvalid&&this._isSymbolInvalid.destroy(),null===(e=this._isDataProblemCritical)||void 0===e||e.destroy();for(const e of this._tooltipSpawns)e.destroy();for(const e of this._visibilitySpawns)e.destroy();this.visibleWidgetsCount.unsubscribe();for(const e of this._statusWidgetInfos)e.model.destroy()
;this._renderer.destroy()}getElement(){return this._renderer.element}updateSource(e){this._source!==e&&(this._source.properties().hasChild("symbol")&&this._source.properties().symbol.listeners().unsubscribeAll(this),this._source=e,this._recreateWidgets(),this._updateStatusWidgetsVisibilities(),this._updateErrorWidgetIsShown(),this._updateVisibleWidgetsCount(),this._updateTooltips())}_updateStatusWidgetsVisibilities(){const e=this._isForceStatusActive();this._dataSourceErrorCanBeShown.setValue(!e),this._marketStatusCanBeShown.setValue(!e),this._dataUpdatedModeCanBeShown.setValue(!e),this._dataProblemCanBeShown.setValue(!this._isPrimaryWidgetShown())}_isPrimaryWidgetShown(){var e,t;return null!==(t=null===(e=this._isSymbolInvalid)||void 0===e?void 0:e.value())&&void 0!==t&&t}_isForceStatusActive(){var e,t;return this._isPrimaryWidgetShown()||null!==(t=null===(e=this._isDataProblemCritical)||void 0===e?void 0:e.value())&&void 0!==t&&t}_updateVisibleWidgetsCount(){const e=this._statusWidgetInfos.filter(e=>e.visible.value());this.visibleWidgetsCount.setValue(e.length)}_updateTooltips(){const e=[];for(let t=0;t<this._tooltipSpawns.length;t++){if(!this._visibilitySpawns[t].value())continue;const s=this._tooltipSpawns[t].value();null!==s&&s.length>0&&e.push(s)}this._tooltips.setValue(e)}_recreateWidgets(){var e,t;if(this._options.sourceStatusesEnabled){if(Object(de.isStudy)(this._source)||Object(de.isStudyStub)(this._source)||this._source===this._model.mainSeries()){null===(e=this._isSymbolInvalid)||void 0===e||e.destroy();const t=this._source;if(Object(de.isStudy)(t)||Object(de.isStudyStub)(t)?this._isSymbolInvalid=kt(()=>t.isSymbolInvalid()&&t.isActualInterval(),[t.onStatusChanged(),t.onIsActualIntervalChange()]):this._isSymbolInvalid=Object(Re.a)(()=>t.isSymbolInvalid(),t.onStatusChanged()),null===this._symbolInvalidViewModel){this._symbolInvalidViewModel=new ks(this._isSymbolInvalid,{tooltipMap:As,iconMap:Ls,classNameMap:Os,titleMap:Ds,titleColorMap:Hs,htmlMap:Bs,actionMap:Ps,size:this._size});const e=this._symbolInvalidViewModel.visible().spawn();this._visibilitySpawns.push(e),this._tooltipSpawns.push(this._symbolInvalidViewModel.tooltip().spawn());const t={visible:e,model:this._symbolInvalidViewModel};this._statusWidgetInfos.push(t),this._renderer.addStatusModel(t)}else this._symbolInvalidViewModel.updateStatus(this._isSymbolInvalid),this._addSubscriptionForSymbolInvalid()}if(Object(de.isStudy)(this._source)||Object(de.isStudyStub)(this._source)||this._source===this._model.mainSeries()){const e=this._source;let t=[];if(t=Object(de.isStudy)(e)||Object(de.isStudyStub)(e)?[e.onStatusChanged(),e.onIsActualIntervalChange()]:[e.onStatusChanged()],null===this._dataSourceErrorStatusViewModel){this._dataSourceErrorStatusViewModel=new vs(e,t,this._size,this._options.sourceStatuses),this._dataSourceHasErrorVisible=Object(Z.a)(()=>this._dataSourceErrorCanBeShown.value()&&Object(i.ensureNotNull)(this._dataSourceErrorStatusViewModel).visible().value(),this._dataSourceErrorCanBeShown,this._dataSourceErrorStatusViewModel.visible()),
this._visibilitySpawns.push(this._dataSourceHasErrorVisible),this._tooltipSpawns.push(this._dataSourceErrorStatusViewModel.tooltip().spawn());const s={visible:this._dataSourceHasErrorVisible,model:this._dataSourceErrorStatusViewModel};this._statusWidgetInfos.push(s),this._renderer.addStatusModel(s)}else this._dataSourceErrorStatusViewModel.setSource(e,t)}}if(this._options.marketStatusEnabled){const e=this._source.marketStatusModel();if(null===this._marketStatusViewModel){this._marketStatusViewModel=new ci(e,this._size,this._options.marketStatus);const t=Object(Z.a)(()=>this._marketStatusCanBeShown.value()&&Object(i.ensureNotNull)(this._marketStatusViewModel).visible().value(),this._marketStatusCanBeShown,this._marketStatusViewModel.visible());this._visibilitySpawns.push(t),this._tooltipSpawns.push(this._marketStatusViewModel.tooltip().spawn());const s={visible:t,model:this._marketStatusViewModel};null!==e&&(this._sessionWidget=new ft(this._source),s.additionalWidgets=[this._sessionWidget]),this._statusWidgetInfos.push(s),this._renderer.addStatusModel(s)}else this._marketStatusViewModel.setModel(e),null===(t=this._sessionWidget)||void 0===t||t.updateSource(this._source)}if(this._options.dataUpdateModeEnabled){const e=this._source.dataUpdatedModeModel();if(null===this._dataUpdatedModeViewModel){this._dataUpdatedModeViewModel=new hs(e,this._size,this._options.dataUpdateMode);const t=Object(Z.a)(()=>this._dataUpdatedModeCanBeShown.value()&&Object(i.ensureNotNull)(this._dataUpdatedModeViewModel).visible().value(),this._dataUpdatedModeCanBeShown,this._dataUpdatedModeViewModel.visible());this._visibilitySpawns.push(t),this._tooltipSpawns.push(this._dataUpdatedModeViewModel.tooltip().spawn());const s={visible:t,model:this._dataUpdatedModeViewModel};this._statusWidgetInfos.push(s),this._renderer.addStatusModel(s)}else this._dataUpdatedModeViewModel.setModel(e)}if(this._options.dataProblemEnabled){const e=this._source.dataProblemModel();if(null===this._dataProblemViewModel){this._dataProblemViewModel=new Cs(e,this._size),this._isDataProblemCritical=this._dataProblemViewModel.isDataProblemCritical().spawn();const t=Object(Z.a)(()=>this._dataProblemCanBeShown.value()&&Object(i.ensureNotNull)(this._dataProblemViewModel).visible().value(),this._dataProblemCanBeShown,this._dataProblemViewModel.visible());this._visibilitySpawns.push(t),this._tooltipSpawns.push(this._dataProblemViewModel.tooltip().spawn());const s={visible:t,model:this._dataProblemViewModel};this._statusWidgetInfos.push(s),this._renderer.addStatusModel(s)}else this._dataProblemViewModel.setModel(e)}}_addSubscriptionForSymbolInvalid(){this._options.sourceStatusesEnabled&&null!==this._isSymbolInvalid&&(this._isSymbolInvalid.subscribe(this._updateStatusWidgetsVisibilities.bind(this)),this._isSymbolInvalid.subscribe(this._updateErrorWidgetIsShown.bind(this),{callWithLast:!0}))}_updateErrorWidgetIsShown(){var e,t,s,i
;const o=null!==(t=null===(e=this._isSymbolInvalid)||void 0===e?void 0:e.value())&&void 0!==t&&t,l=null!==(i=null===(s=this._dataSourceHasErrorVisible)||void 0===s?void 0:s.value())&&void 0!==i&&i;this.errorWidgetIsShown.setValue(o||l)}_handleToggleDropdown(e){var t;this._menuPosition=e,this._menuOpened=!this._menuOpened,this._menuOpened&&(this._source.properties().hasChild("symbol")&&this._source.properties().symbol.listeners().subscribe(this,this._handleDropdownMenuClose),t="Open full tooltip for statuses: "+this._tooltips.value().join(", "),Object(V.trackEvent)("GUI","Statuses widget's action",t)),this._updateDropdownMenu()}_handleDropdownMenuClose(){this._menuOpened=!1,this._source.properties().hasChild("symbol")&&this._source.properties().symbol.listeners().unsubscribeAll(this),this._updateDropdownMenu()}_updateDropdownMenu(){Promise.all([s.e("react"),s.e(6),s.e(11),s.e(92),s.e(0),s.e(5),s.e("full-tooltips-popup")]).then(s.bind(null,"vR7+")).then(e=>{e.render(this._menuOpened,this._container,this._renderer.element,this._statusWidgetInfos,this._handleDropdownMenuClose.bind(this),Object(i.ensureNotNull)(this._menuPosition))})}}var pi=s("AH3n");const mi=window.t("Replay mode"),bi=window.t("You're in Replay mode. You're in Replay mode. You're in Replay mode.");new Map([[!0,new Map([["small",pi],["large",pi]])],[!1,new Map([["small",""],["large",""]])]]),new Map([[!0,Vt.replayMode],[!1,null]]),new Map([[!0,mi],[!1,null]]),new Map([[!0,mi],[!1,null]]),new Map([[!0,Et.colorsPalette["color-replay-mode"]],[!1,null]]),new Map([[!0,[bi]],[!1,null]]),new Map([[!0,null],[!1,null]]);class gi extends _i{constructor(e,t,s){super(e,t,s),this._isInReplay=new l.a(!1).readonly().spawn(),this._isInReplayCanBeShown=null,this._inited=!1}destroy(){super.destroy()}_updateStatusWidgetsVisibilities(){super._updateStatusWidgetsVisibilities()}_isPrimaryWidgetShown(){var e,t;return super._isPrimaryWidgetShown()||null!==(t=null===(e=this._isInReplay)||void 0===e?void 0:e.value())&&void 0!==t&&t}}var wi=s("EsvI"),vi=s("UXvI");s.d(t,"LegendWidget",(function(){return Ei}));const yi={readOnlyMode:!1,contextMenu:{settings:!0,mainSeries:!0,studies:!0,showOpenMarketStatus:!1},symbolMarkerEnabled:!1,showToggleButton:!0,canShowSourceCode:!1,statusesWidgets:{sourceStatusesEnabled:!1,sourceStatuses:{errorSolution:!0},marketStatusEnabled:!1,marketStatus:{preMarketSolution:!0,postMarketSolution:!0},dataUpdateModeEnabled:!1,dataUpdateMode:{subscriptionFullInfo:!0},dataProblemEnabled:!1}},Si=(d.enabled("hide_legend_by_default"),d.enabled("fundamental_widget")),Mi=d.enabled("legend_context_menu"),fi=2*parseInt(y.marginlegendhoriz);class Ei{constructor(e,t,s,i,o,a,r){this._mainSeriesViewModel=null,this._dataSourceViewModels=[],this._visibleDataSourceCount=new l.a(0),this._themedColor=new l.a(""),this._mainSeriesRowHidden=null,this._dataSourceRowsHidden=[],this._customWidgetsVisibilities=[],this._allLegendHidden=new l.a(!1),this._studiesLegendHidden=new l.a(!1),this._customWidgetsHeights=[],this._onLegendVisibilityToggled=null,this._availableHeight=0,
this._collapsedDataSourcesCount=new l.a(0),this._collapsedDataSourcesTitle=new l.a(""),this._mainSeriesStatusWidget=null,this._dataSourcesStatusesWidgets=[],this._size=null,this._customLegendWidgetsFactoriesMap=new Map,this._customLegendWidgetsMap=new Map,this._margin=0,this._model=e,this._paneWidget=t,this._options=Object(n.merge)(Object(n.clone)(yi),a),this._callbacks=r,this._mainSeriesViewModelsOptions={readOnlyMode:this._options.readOnlyMode,symbolMarkerEnabled:this._options.symbolMarkerEnabled},this._dataSourceViewModelsOptions={...this._mainSeriesViewModelsOptions,canShowSourceCode:this._options.canShowSourceCode},this._backgroundThemeName=s;const d=this._showLegendCalculatedProperty();this._isDataSourcesCollapsed=new l.a(d.value()),d.subscribe(this,()=>{this._isDataSourcesCollapsed.setValue(d.value())});const h=new l.a(this._getCustomTextColorValue());this._model.model().properties().scalesProperties.textColor.subscribe(this,()=>{h.setValue(this._getCustomTextColorValue())});const u=this._model.model().properties().paneProperties.legendProperties.showBackground,c=new l.a(u.value());u.subscribe(this,()=>{c.setValue(u.value())});const _=this._model.model().properties().paneProperties.legendProperties.backgroundTransparency,p=new l.a(_.value());_.subscribe(this,()=>{p.setValue(_.value())}),this._wrapText=new l.a(!1),this._hideNotMainSources=i.spawn(),this._hideNotMainSources.subscribe(this._updateLegendVisibilities.bind(this)),this._hideWholeLegend=o.spawn(),this._hideWholeLegend.subscribe(this._updateLegendVisibilities.bind(this)),this._isPaneMain=new l.a(this._getIsPaneMainValue()),this._updateCollapsedSourcesModeThrottle=Object(vi.default)(this._updateCollapsedSourcesMode.bind(this),100),this._renderer=new R({withActions:!this._options.readOnlyMode,showToggleButton:this._options.showToggleButton,isStudiesLegendHidden:this._studiesLegendHidden.readonly(),isAllLegendHidden:this._allLegendHidden.readonly(),customTextColor:h.readonly(),themedColor:this._themedColor.readonly(),showBackground:c.readonly(),backgroundTransparency:p.readonly(),wrapText:this._wrapText.readonly(),collapsedDataSourcesCount:this._collapsedDataSourcesCount.readonly(),collapsedDataSourcesTitle:this._collapsedDataSourcesTitle.readonly(),showLegendWidgetContextMenu:this.onShowLegendWidgetContextMenu.bind(this)},{visibleDataSourceCount:this._visibleDataSourceCount.readonly(),isDataSourcesCollapsed:this._isDataSourcesCollapsed.readonly(),showObjectsTree:this._isPaneMain.readonly(),onCollapseDataSources:this.onCollapseDataSources.bind(this),onShowObjectsTreeDialog:this._callbacks.showObjectsTreeDialog})}destroy(){this._hideNotMainSources.destroy(),this._hideWholeLegend.destroy(),null!==this._mainSeriesViewModel&&this._destroyMainDataSource();for(const e of this._dataSourceViewModels)e.destroy();for(const e of this._dataSourcesStatusesWidgets)e.destroy();this._clearSubscriptions();for(const e of Array.from(this._customLegendWidgetsMap.keys()))this._destroyCustomWidgetFromLayerBlock(e);this._customLegendWidgetsMap.clear(),this._renderer.destroy(),
delete this._renderer,this._showLegendCalculatedProperty().unsubscribeAll(this),this._showLegendOriginalProperty().unsubscribeAll(this),this._model.model().properties().scalesProperties.textColor.unsubscribeAll(this),this._model.model().properties().paneProperties.legendProperties.showBackground.unsubscribeAll(this),this._model.model().properties().paneProperties.legendProperties.backgroundTransparency.unsubscribeAll(this)}addCustomWidgetToLegend(e,t){const s=this._customLegendWidgetsFactoriesMap.get(t.block)||new Map,i=s.get(t.position)||[];i.push(e),s.set(t.position,i),this._customLegendWidgetsFactoriesMap.set(t.block,s),this.updateLayout(),this._updateCustomWidgetModeBySize()}onShowLegendWidgetContextMenu(e,t){if(this._options.readOnlyMode||!Mi)return Promise.resolve(null);x("Show legend context menu");const s=new Map;for(const e of Array.from(this._customLegendWidgetsMap.keys())){const t=Object(i.ensureDefined)(this._customLegendWidgetsMap.get(e)),o=new Map;for(const e of Array.from(t.keys())){const s=Object(i.ensureDefined)(t.get(e)),l=o.get(e)||[];for(const e of s)l.push(...e.contextMenuActions());o.set(e,l)}s.set(e,o)}return De(this._model,this._options.contextMenu,this._callbacks.showGeneralChartProperties,s,e,t)}onCollapseDataSources(){const e=this._showLegendOriginalProperty();e.setValue(!e.value())}updateLayout(){const e=this._paneWidget.state().sourcesByGroup().all().filter(e=>null!==e.statusView());if(0===e.length)return;const t=this._model.mainSeries(),s=e.indexOf(t);s>-1?(e.splice(s,1),Si||null!==this._mainSeriesViewModel||(this._mainSeriesViewModel=new re(this._model,t,this._mainSeriesViewModelsOptions,this._callbacks,this._options.contextMenu),this._mainSeriesStatusWidget=new gi(t,this._model.model(),this._options.statusesWidgets),this._renderer.addMainDataSource(this._mainSeriesViewModel,this._mainSeriesStatusWidget)),this._addCustomWidgetForLayerBlock(0)):null!==this._mainSeriesViewModel&&(this._destroyMainDataSource(),this._destroyCustomWidgetFromLayerBlock(0));const o=[],l=[],n=this._dataSourceViewModels.length;if(0===n)for(let t=e.length-1;t>=0;t--)o.push(new Ve(this._model,e[t],this._dataSourceViewModelsOptions,this._callbacks,this._options.contextMenu)),l.push(new _i(e[t],this._model.model(),this._options.statusesWidgets));else{let t=0;for(let s=e.length-1;s>=0;s--)this._dataSourceViewModels[t]?(this._dataSourceViewModels[t].updateSource(e[s]),this._dataSourcesStatusesWidgets[t].updateSource(e[s])):(o.push(new Ve(this._model,e[s],this._dataSourceViewModelsOptions,this._callbacks,this._options.contextMenu)),l.push(new _i(e[s],this._model.model(),this._options.statusesWidgets))),t++;for(;this._dataSourceViewModels.length>t;)Object(i.ensureDefined)(this._dataSourceViewModels.pop()).destroy();for(;this._dataSourcesStatusesWidgets.length>t;)Object(i.ensureDefined)(this._dataSourcesStatusesWidgets.pop()).destroy()}0!==o.length&&(this._renderer.addDataSources(o,l),this._dataSourceViewModels.push(...o),this._dataSourcesStatusesWidgets.push(...l)),
n!==this._dataSourceViewModels.length&&this._updateCollapsedSourcesMode(),this._dataSourceViewModels.length>0?this._addCustomWidgetForLayerBlock(1):this._destroyCustomWidgetFromLayerBlock(1),this._recreateSubscriptions(),this._isPaneMain.setValue(this._getIsPaneMainValue()),this.update(),this._updateWidgetModeByWidth()}update(){null!==this._mainSeriesViewModel&&this._mainSeriesViewModel.update();for(const e of this._dataSourceViewModels)e.update()}updateThemedColors(e){null===e&&(e=Object(wi.getStdThemedValue)("chartProperties.paneProperties.background",this._backgroundThemeName.value())),this._themedColor.setValue(e||"")}firstTitle(){return this._renderer.firstTitle()}getElement(){return this._renderer.getElement()}addMargin(e){if(this._margin===e)return;this._margin=e;this._renderer.getElement().style.maxWidth=0===this._margin?"":`calc(100% - ${this._margin+fi}px)`,this._updateWidgetModeBySize()}updateWidgetModeBySize(e){this._size=e,this._updateWidgetModeBySize()}_updateWidgetModeBySize(){this._updateWidgetModeByWidth(),this._updateWidgetModeByHeight(),this._updateCustomWidgetModeBySize()}_updateWidgetModeByWidth(){null!==this._size&&this._renderer.updateMode(this._availableWidth())}_updateWidgetModeByHeight(){null!==this._size&&(this._availableHeight=.8*this._size.h,this._updateCollapsedSourcesModeThrottle())}_updateCustomWidgetModeBySize(){if(null===this._size)return;const e=new a.Size(this._availableWidth(),this._size.h);for(const t of Array.from(this._customLegendWidgetsMap.values()))for(const s of Array.from(t.values()))for(const t of s)t.updateWidgetModeBySize(e)}_destroyMainDataSource(){Object(i.ensureNotNull)(this._mainSeriesStatusWidget).destroy(),this._mainSeriesStatusWidget=null,Object(i.ensureNotNull)(this._mainSeriesViewModel).destroy(),this._mainSeriesViewModel=null}_updateCollapsedSourcesMode(){const e=this._dataSourceViewModels.length;if(!this._wrapText.value()&&this._availableHeight>0&&e>2){const t=this._renderer.getMainSourceHeight(),s=this._renderer.getDataSourceHeight(),i=this._getCustomWidgetsHeight();if(null!==t&&null!==s){const o=Math.floor((this._availableHeight-t-i)/s),l=Math.max(o,2)-1;if(e>l+1){let t="";for(let s=0;s<e;s++){const e=s<l;this._dataSourceViewModels[s].setGlobalVisibility(e),e||(t+=`${0===t.length?"":", "}${this._dataSourceViewModels[s].getFullTitle()}`)}return this._collapsedDataSourcesTitle.setValue(t),void this._collapsedDataSourcesCount.setValue(e-l)}}}for(const e of this._dataSourceViewModels)e.setGlobalVisibility(!0);this._collapsedDataSourcesCount.setValue(0),this._collapsedDataSourcesTitle.setValue("")}_getCustomWidgetsHeight(){let e=0;for(const t of Array.from(this._customLegendWidgetsMap.values()))for(const s of Array.from(t.values()))for(const t of s)e+=t.height().value();return e}_getCustomTextColorValue(){const e=this._model.model().properties().scalesProperties.textColor.value();return Object(wi.isStdThemedDefaultValue)("chartProperties.scalesProperties.textColor",e,Object(wi.getCurrentTheme)().name)?null:e}_clearSubscriptions(){
null!==this._mainSeriesRowHidden&&(this._mainSeriesRowHidden.destroy(),this._mainSeriesRowHidden=null);for(const e of this._dataSourceRowsHidden)e.destroy();this._dataSourceRowsHidden=[];for(const e of this._customWidgetsVisibilities)e.destroy();this._customWidgetsVisibilities=[];for(const e of this._customWidgetsHeights)e.destroy();this._customWidgetsHeights=[]}_recreateSubscriptions(){this._clearSubscriptions(),null!==this._mainSeriesViewModel&&(this._mainSeriesRowHidden=this._mainSeriesViewModel.isRowHidden().spawn(),this._mainSeriesRowHidden.subscribe(this._updateLegendVisibilities.bind(this)));for(const e of this._dataSourceViewModels){const t=e.isRowHidden().spawn();this._dataSourceRowsHidden.push(t),t.subscribe(this._updateVisibleDataSourceCount.bind(this)),t.subscribe(this._updateLegendVisibilities.bind(this))}for(const e of Array.from(this._customLegendWidgetsMap.values()))for(const t of Array.from(e.values()))for(const e of t){const t=e.visibility().spawn();this._customWidgetsVisibilities.push(t),t.subscribe(this._updateLegendVisibilities.bind(this));const s=e.height().spawn();this._customWidgetsHeights.push(s),s.subscribe(this._updateCollapsedSourcesMode.bind(this))}this._updateVisibleDataSourceCount(),this._updateLegendVisibilities()}_updateLegendVisibilities(){if(this._hideWholeLegend.value())return void this._allLegendHidden.setValue(!0);const e=this._dataSourceRowsHidden.every(e=>e.value()),t=this._hideNotMainSources.value()||e;this._studiesLegendHidden.setValue(t);const s=null===this._mainSeriesRowHidden||this._mainSeriesRowHidden.value(),i=this._customWidgetsVisibilities.some(e=>e.value());this._allLegendHidden.setValue(e&&s&&!i)}_updateVisibleDataSourceCount(){const e=this._dataSourceRowsHidden.filter(e=>!e.value()).length;this._visibleDataSourceCount.setValue(e)}_setLegendVisibilityToggled(){0}_getIsPaneMainValue(){return this._paneWidget.containsMainSeries()}_showLegendCalculatedProperty(){return this._model.model().showLegend()}_showLegendOriginalProperty(){return this._model.model().properties().paneProperties.legendProperties.showLegend}_addCustomWidgetForLayerBlock(e){const t=this._customLegendWidgetsFactoriesMap.get(e);if(void 0===t)return;const s=this._customLegendWidgetsMap.get(e)||new Map;let i=!1;for(const o of Array.from(t.keys())){const l=s.get(o)||[],n=t.get(o)||[];for(let t=l.length;t<n.length;t++){const s=n[t](this._model.model(),this._backgroundThemeName);0===e&&0===o&&s.setGlobalVisibility(this._hideNotMainSources.opposite()),l.push(s),this._renderer.addCustomWidget(s,{block:e,position:o}),i=!0}i&&s.set(o,l)}i&&this._customLegendWidgetsMap.set(e,s)}_destroyCustomWidgetFromLayerBlock(e){const t=this._customLegendWidgetsMap.get(e);if(void 0!==t){for(const e of Array.from(t.values()))for(const t of e)t.destroy();t.clear(),this._customLegendWidgetsMap.delete(e)}}_availableWidth(){return null===this._size?0:this._size.w-this._margin-fi}}},tfDh:function(e,t,s){e.exports={wrapper:"wrapper-2-RXze0M",timezone:"timezone-2-RXze0M",sessionDayWrapper:"sessionDayWrapper-2-RXze0M",
nowWrapper:"nowWrapper-2-RXze0M",now:"now-2-RXze0M",sessionDay:"sessionDay-2-RXze0M",weekDay:"weekDay-2-RXze0M",sessionDaySegments:"sessionDaySegments-2-RXze0M",timeMarkWrapper:"timeMarkWrapper-2-RXze0M",timeMarkSegment:"timeMarkSegment-2-RXze0M",timeMark:"timeMark-2-RXze0M",timeMarkSegmentAlignByEnds:"timeMarkSegmentAlignByEnds-2-RXze0M",segment:"segment-2-RXze0M",small:"small-2-RXze0M",start:"start-2-RXze0M",end:"end-2-RXze0M",active:"active-2-RXze0M",green:"green-2-RXze0M",orange:"orange-2-RXze0M",blue:"blue-2-RXze0M",gray:"gray-2-RXze0M",tooltip:"tooltip-2-RXze0M",time:"time-2-RXze0M"}},vWJB:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 4" width="16" height="4" fill="none"><circle stroke="currentColor" cx="2" cy="2" r="1.5"/><circle stroke="currentColor" cx="8" cy="2" r="1.5"/><circle stroke="currentColor" cx="14" cy="2" r="1.5"/></svg>'},vYP1:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M8.54.84a.8.8 0 0 1 .92 0l7.5 5.25a.8.8 0 0 1 0 1.32l-7.5 5.25a.8.8 0 0 1-.92 0L1.04 7.4a.8.8 0 0 1 0-1.32L8.54.84zM2.9 6.75L9 11.02l6.1-4.27L9 2.48 2.9 6.75z"/><path fill="currentColor" d="M.84 10.8a.8.8 0 0 1 1.12-.2L9 15.51l7.04-4.93a.8.8 0 0 1 .92 1.32l-7.5 5.25a.8.8 0 0 1-.92 0l-7.5-5.25a.8.8 0 0 1-.2-1.12z"/></svg>'},vg09:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14" height="14"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M7 2v10M2 7h10"/></svg>'},vqb8:function(e,t,s){"use strict";s.d(t,"a",(function(){return o}));var i=s("q1tI");const o=e=>{const t="watchedValue"in e?e.watchedValue:void 0,s="defaultValue"in e?e.defaultValue:e.watchedValue.value(),[o,l]=Object(i.useState)(t?t.value():s);return Object(i.useEffect)(()=>{if(t){l(t.value());const e=e=>l(e);return t.subscribe(e),()=>t.unsubscribe(e)}return()=>{}},[t]),o}},wZiV:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 2 30 24" width="30" height="24" fill="none"><circle stroke="currentColor" stroke-width="1.15" cx="8.08" cy="14" r="1.73"/><circle stroke="currentColor" stroke-width="1.15" cx="15" cy="14" r="1.73"/><circle stroke="currentColor" stroke-width="1.15" cx="21.92" cy="14" r="1.73"/></svg>'},z4c1:function(e,t){
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 22" width="24" height="22" fill="none"><g class="normal-eye"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M17.9948 7.91366C16.6965 6.48549 14.6975 5 11.9999 5C9.30225 5 7.30322 6.48549 6.00488 7.91366C6.00488 7.91366 4 10 4 11C4 12 6.00488 14.0863 6.00488 14.0863C7.30322 15.5145 9.30225 17 11.9999 17C14.6975 17 16.6965 15.5145 17.9948 14.0863C17.9948 14.0863 20 12 20 11C20 10 17.9948 7.91366 17.9948 7.91366ZM6.74482 13.4137C7.94648 14.7355 9.69746 16 11.9999 16C14.3022 16 16.0532 14.7355 17.2549 13.4137C17.2549 13.4137 19 11.5 19 11C19 10.5 17.2549 8.58634 17.2549 8.58634C16.0532 7.26451 14.3022 6 11.9999 6C9.69746 6 7.94648 7.26451 6.74482 8.58634C6.74482 8.58634 5 10.5 5 11C5 11.5 6.74482 13.4137 6.74482 13.4137Z"/><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M12 13C13.1046 13 14 12.1046 14 11C14 9.89543 13.1046 9 12 9C10.8954 9 10 9.89543 10 11C10 12.1046 10.8954 13 12 13ZM12 14C13.6569 14 15 12.6569 15 11C15 9.34315 13.6569 8 12 8C10.3431 8 9 9.34315 9 11C9 12.6569 10.3431 14 12 14Z"/></g><g class="crossed-eye"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M8.8503 16.2712C9.76531 16.7135 10.8152 17 11.9999 17C14.6975 17 16.6965 15.5145 17.9948 14.0863C17.9948 14.0863 20 12 20 11C20 10 17.9948 7.91366 17.9948 7.91366C17.8729 7.77954 17.7448 7.64491 17.6105 7.51105L16.9035 8.2181C17.0254 8.33968 17.1425 8.46276 17.2549 8.58634C17.2549 8.58634 19 10.5 19 11C19 11.5 17.2549 13.4137 17.2549 13.4137C16.0532 14.7355 14.3022 16 11.9999 16C11.1218 16 10.324 15.8161 9.60627 15.5153L8.8503 16.2712ZM7.09663 13.7823C6.97455 13.6606 6.85728 13.5374 6.74482 13.4137C6.74482 13.4137 5 11.5 5 11C5 10.5 6.74482 8.58634 6.74482 8.58634C7.94648 7.26451 9.69746 6 11.9999 6C12.8781 6 13.6761 6.18398 14.394 6.48495L15.1499 5.729C14.2348 5.28657 13.1847 5 11.9999 5C9.30225 5 7.30322 6.48549 6.00488 7.91366C6.00488 7.91366 4 10 4 11C4 12 6.00488 14.0863 6.00488 14.0863C6.12693 14.2206 6.25516 14.3553 6.38959 14.4893L7.09663 13.7823Z"/><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M11.2231 13.8984C11.4709 13.9647 11.7313 14 12 14C13.6569 14 15 12.6569 15 11C15 10.7313 14.9647 10.4709 14.8984 10.2231L13.9961 11.1254C13.934 12.1301 13.1301 12.934 12.1254 12.9961L11.2231 13.8984ZM11.8751 9.00384C10.87 9.06578 10.0658 9.87001 10.0038 10.8751L9.10166 11.7772C9.03535 11.5294 9 11.2688 9 11C9 9.34315 10.3431 8 12 8C12.2688 8 12.5294 8.03535 12.7772 8.10166L11.8751 9.00384Z"/><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M5.64648 16.6465L17.6465 4.64648L18.3536 5.35359L6.35359 17.3536L5.64648 16.6465Z"/></g><g class="loading-eye"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M17.9948 7.91366C16.6965 6.48549 14.6975 5 11.9999 5C9.30225 5 7.30322 6.48549 6.00488 7.91366C6.00488 7.91366 4 10 4 11C4 12 6.00488 14.0863 6.00488 14.0863C7.30322 15.5145 9.30225 17 11.9999 17C14.6975 17 16.6965 15.5145 17.9948 14.0863C17.9948 14.0863 20 12 20 11C20 10 17.9948 7.91366 17.9948 7.91366ZM6.74482 13.4137C7.94648 14.7355 9.69746 16 11.9999 16C14.3022 16 16.0532 14.7355 17.2549 13.4137C17.2549 13.4137 19 11.5 19 11C19 10.5 17.2549 8.58634 17.2549 8.58634C16.0532 7.26451 14.3022 6 11.9999 6C9.69746 6 7.94648 7.26451 6.74482 8.58634C6.74482 8.58634 5 10.5 5 11C5 11.5 6.74482 13.4137 6.74482 13.4137Z"/></g><g class="animated-loading-eye"><path stroke="currentColor" stroke-linecap="round" d="M14.5 11C14.5 9.61929 13.3807 8.5 12 8.5C10.6193 8.5 9.5 9.61929 9.5 11C9.5 12.3807 10.6193 13.5 12 13.5"/></g></svg>'
}}]);