(window.webpackJsonp=window.webpackJsonp||[]).push([[30],{"+l/S":function(e,t,n){},"1Kfe":function(e,t,n){e.exports={container:"container-TZggBcGZ",sectionTitle:"sectionTitle-TZggBcGZ",separator:"separator-TZggBcGZ",customButton:"customButton-TZggBcGZ"}},"2A9e":function(e){e.exports=JSON.parse('{"button":"button-1iktpaT1","content":"content-2PGssb8d","noOutline":"noOutline-d9Yp4qvi","grouped":"grouped-2NxOpIxM","adjust-position":"adjust-position-2zd-ooQC","first-row":"first-row-11wXF7aC","first-col":"first-col-pbJu53tK","no-corner-top-left":"no-corner-top-left-3ZsS65Fk","no-corner-top-right":"no-corner-top-right-3MYQOwk_","no-corner-bottom-right":"no-corner-bottom-right-3II18BAU","no-corner-bottom-left":"no-corner-bottom-left-3KZuX8tv","appearance-default":"appearance-default-dMjF_2Hu","intent-primary":"intent-primary-1-IOYcbg","intent-success":"intent-success-25a4XZXM","intent-default":"intent-default-2ZbSqQDs","intent-warning":"intent-warning-24j5HMi0","intent-danger":"intent-danger-1EETHCla","appearance-stroke":"appearance-stroke-12lxiUSM","appearance-text":"appearance-text-DqKJVT3U","appearance-inverse":"appearance-inverse-r1Y2JQg_","size-s":"size-s-3mait84m","size-m":"size-m-2G7L7Qat","size-l":"size-l-2NEs9_xt","full-width":"full-width-1wU8ljjC","with-icon":"with-icon-yumghDr-","icon":"icon-1grlgNdV"}')},"9dlw":function(e,t,n){"use strict";n.d(t,"a",(function(){return h}));var o=n("q1tI"),i=n.n(o),s=n("i8i4"),r=n.n(s),a=n("AiMB"),c=n("DTHj"),l=n("X0gx"),u=n("8Rai");function h(e){const{controller:t,children:n,isOpened:s,closeOnClickOutside:h=!0,doNotCloseOn:d,onClickOutside:p,onClose:m,...v}=e,f=Object(o.useContext)(l.a),b=Object(u.a)({handler:function(e){p&&p(e);if(!h)return;if(d&&e.target instanceof Node){const t=r.a.findDOMNode(d);if(t instanceof Node&&t.contains(e.target))return}m()},mouseDown:!0,touchStart:!0});return s?i.a.createElement(a.a,{top:"0",left:"0",right:"0",bottom:"0",pointerEvents:"none"},i.a.createElement("span",{ref:b,style:{pointerEvents:"auto"}},i.a.createElement(c.b,{...v,onClose:m,onScroll:function(t){const{onScroll:n}=e;n&&n(t)},customCloseDelegate:f,ref:t},n))):null}},DXuF:function(e,t,n){e.exports={swatches:"swatches-vBKBthtD",swatch:"swatch-vBKBthtD",hover:"hover-vBKBthtD",empty:"empty-vBKBthtD",white:"white-vBKBthtD",selected:"selected-vBKBthtD",contextItem:"contextItem-vBKBthtD"}},F0Qt:function(e){e.exports=JSON.parse('{"wrapper":"wrapper-21v50zE8","input":"input-24iGIobO","box":"box-3574HVnv","icon":"icon-2jsUbtec","noOutline":"noOutline-3VoWuntz","intent-danger":"intent-danger-1Sr9dowC","check":"check-382c8Fu1","dot":"dot-3gRd-7Qt"}')},N5tr:function(e,t,n){"use strict";n.d(t,"a",(function(){return u})),n.d(t,"b",(function(){return p}));var o=n("q1tI"),i=n.n(o),s=n("TSYQ"),r=n("tWVy"),a=n("JWMC"),c=n("ijHL"),l=n("v1bN");const u=l;function h(e){const{reference:t,...n}=e,o={...n,ref:t};return i.a.createElement(e.href?"a":"div",o)}function d(e){e.stopPropagation()}function p(e){
const{id:t,role:n,"aria-selected":u,className:p,title:m,labelRowClassName:v,labelClassName:f,shortcut:b,forceShowShortcuts:g,icon:w,isActive:C,isDisabled:_,isHovered:E,appearAsDisabled:y,label:O,link:S,showToolboxOnHover:N,target:k,rel:I,toolbox:T,reference:x,onMouseOut:j,onMouseOver:P,suppressToolboxClick:M=!0,theme:U=l}=e,D=Object(c.b)(e),B=Object(o.useRef)(null);return i.a.createElement(h,{...D,id:t,role:n,"aria-selected":u,className:s(p,U.item,w&&U.withIcon,{[U.isActive]:C,[U.isDisabled]:_||y,[U.hovered]:E}),title:m,href:S,target:k,rel:I,reference:function(e){B.current=e,"function"==typeof x&&x(e);"object"==typeof x&&(x.current=e)},onClick:function(t){const{dontClosePopup:n,onClick:o,onClickArg:i,trackEventObject:s}=e;if(_)return;s&&Object(a.trackEvent)(s.category,s.event,s.label);o&&o(i,t);n||Object(r.b)()},onContextMenu:function(t){const{trackEventObject:n,trackRightClick:o}=e;n&&o&&Object(a.trackEvent)(n.category,n.event,n.label+"_rightClick")},onMouseUp:function(t){const{trackEventObject:n,trackMouseWheelClick:o}=e;if(1===t.button&&S&&n){let e=n.label;o&&(e+="_mouseWheelClick"),Object(a.trackEvent)(n.category,n.event,e)}},onMouseOver:P,onMouseOut:j},void 0!==w&&i.a.createElement("div",{className:U.icon,dangerouslySetInnerHTML:{__html:w}}),i.a.createElement("div",{className:s(U.labelRow,v)},i.a.createElement("div",{className:s(U.label,f)},O)),(void 0!==b||g)&&i.a.createElement("div",{className:U.shortcut},(A=b)&&A.split("+").join(" + ")),void 0!==T&&i.a.createElement("div",{onClick:M?d:void 0,className:s(U.toolbox,{[U.showOnHover]:N})},T));var A}},Oqo1:function(e,t,n){e.exports={opacity:"opacity-2UqCUhku",opacitySlider:"opacitySlider-2UqCUhku",opacitySliderGradient:"opacitySliderGradient-2UqCUhku",pointer:"pointer-2UqCUhku",dragged:"dragged-2UqCUhku",opacityPointerWrap:"opacityPointerWrap-2UqCUhku",opacityInputWrap:"opacityInputWrap-2UqCUhku",opacityInput:"opacityInput-2UqCUhku",opacityInputPercent:"opacityInputPercent-2UqCUhku"}},"P4l+":function(e,t,n){},U1eG:function(e,t,n){"use strict";n.d(t,"a",(function(){return c}));var o=n("q1tI"),i=n("TSYQ"),s=n("Eyy1"),r=n("Hr11"),a=n("Oqo1");class c extends o.PureComponent{constructor(e){super(e),this._container=null,this._pointer=null,this._raf=null,this._refContainer=e=>{this._container=e},this._refPointer=e=>{this._pointer=e},this._handlePosition=e=>{null===this._raf&&(this._raf=requestAnimationFrame(()=>{const t=Object(s.ensureNotNull)(this._container),n=Object(s.ensureNotNull)(this._pointer),o=t.getBoundingClientRect(),i=n.offsetWidth,a=e.clientX-i/2-o.left,c=Object(r.clamp)(a/(o.width-i),0,1);this.setState({inputOpacity:Math.round(100*c).toString()}),this.props.onChange(c),this._raf=null}))},this._onSliderClick=e=>{this._handlePosition(e.nativeEvent),this._dragSubscribe()},this._mouseUp=e=>{this.setState({isPointerDragged:!1}),this._dragUnsubscribe(),this._handlePosition(e)},this._mouseMove=e=>{this.setState({isPointerDragged:!0}),this._handlePosition(e)},this._onTouchStart=e=>{this._handlePosition(e.nativeEvent.touches[0])},this._handleTouch=e=>{
this.setState({isPointerDragged:!0}),this._handlePosition(e.nativeEvent.touches[0])},this._handleTouchEnd=()=>{this.setState({isPointerDragged:!1})},this._handleInput=e=>{const t=e.currentTarget.value,n=Number(t)/100;this.setState({inputOpacity:t}),Number.isNaN(n)||n>1||this.props.onChange(n)},this.state={inputOpacity:Math.round(100*e.opacity).toString(),isPointerDragged:!1}}componentWillUnmount(){null!==this._raf&&(cancelAnimationFrame(this._raf),this._raf=null),this._dragUnsubscribe()}render(){const{color:e,opacity:t,hideInput:n}=this.props,{inputOpacity:s,isPointerDragged:r}=this.state,c={color:e||void 0};return o.createElement("div",{className:a.opacity},o.createElement("div",{className:a.opacitySlider,style:c,ref:this._refContainer,onMouseDown:this._onSliderClick,onTouchStart:this._onTouchStart,onTouchMove:this._handleTouch,onTouchEnd:this._handleTouchEnd},o.createElement("div",{className:a.opacitySliderGradient,style:{backgroundImage:`linear-gradient(90deg, transparent, ${e})`}}),o.createElement("div",{className:a.opacityPointerWrap},o.createElement("div",{className:i(a.pointer,r&&a.dragged),style:{left:100*t+"%"},ref:this._refPointer}))),!n&&o.createElement("div",{className:a.opacityInputWrap},o.createElement("input",{type:"text",className:a.opacityInput,value:s,onChange:this._handleInput}),o.createElement("span",{className:a.opacityInputPercent},"%")))}_dragSubscribe(){const e=Object(s.ensureNotNull)(this._container).ownerDocument;e&&(e.addEventListener("mouseup",this._mouseUp),e.addEventListener("mousemove",this._mouseMove))}_dragUnsubscribe(){const e=Object(s.ensureNotNull)(this._container).ownerDocument;e&&(e.removeEventListener("mousemove",this._mouseMove),e.removeEventListener("mouseup",this._mouseUp))}}},V3OP:function(e,t,n){"use strict";var o=n("q1tI"),i=n("Vdly"),s=n("FQhm");function r(e,t){Object(o.useEffect)(()=>(s.subscribe(e,t,null),()=>{s.unsubscribe(e,t,null)}),[e,t])}var a=n("eJTA");function c(){const[e,t]=Object(o.useState)(Object(i.getJSON)("pickerCustomColors",[]));r("add_new_custom_color",n=>t(l(n,e))),r("remove_custom_color",n=>t(u(n,e)));const n=Object(o.useCallback)(t=>{const n=t?Object(a.parseRgb)(t):null;e.some(e=>null!==e&&null!==n&&Object(a.areEqualRgb)(Object(a.parseRgb)(e),n))||(s.emit("add_new_custom_color",t),Object(i.setJSON)("pickerCustomColors",l(t,e)))},[e]),c=Object(o.useCallback)(t=>{(t>=0||t<e.length)&&(s.emit("remove_custom_color",t),Object(i.setJSON)("pickerCustomColors",u(t,e)))},[e]);return[e,n,c]}function l(e,t){const n=t.slice();return n.push(e),n.length>29&&n.shift(),n}function u(e,t){return t.filter((t,n)=>e!==n)}n.d(t,"a",(function(){return c}))},htM8:function(e,t,n){"use strict";var o=n("YFKU"),i=n("q1tI"),s=n.n(i),r=n("TSYQ"),a=n.n(r),c=n("eJTA"),l=n("Eyy1"),u=n("qFKp"),h=n("9dlw"),d=n("N5tr");const p=i.createContext(void 0);var m=n("wLjq"),v=n("aVjL"),f=n("DXuF");function b(e){const{index:t,color:n,selected:a,onSelect:c}=e,[b,g]=Object(i.useState)(!1),w=Object(i.useContext)(p),C=Object(i.useRef)(null),_=Boolean(w)&&!u.CheckMobile.any()
;return s.a.createElement(s.a.Fragment,null,s.a.createElement("div",{ref:C,style:n?{color:n}:void 0,className:r(f.swatch,b&&f.hover,a&&f.selected,!n&&f.empty,String(n).toLowerCase()===m.c&&f.white),onClick:function(){c(n)},onContextMenu:_?E:void 0}),_&&s.a.createElement(h.a,{isOpened:b,onClose:E,position:function(){const e=Object(l.ensureNotNull)(C.current).getBoundingClientRect();return{x:e.left,y:e.top+e.height+4}},onClickOutside:E},s.a.createElement(d.b,{className:f.contextItem,label:Object(o.t)("Remove color"),icon:v,onClick:function(){E(),Object(l.ensureDefined)(w)(t)},dontClosePopup:!0})));function E(){g(!b)}}class g extends i.PureComponent{constructor(){super(...arguments),this._onSelect=e=>{const{onSelect:t}=this.props;t&&t(e)}}render(){const{colors:e,color:t,children:n}=this.props;if(!e)return null;const o=t?Object(c.parseRgb)(String(t)):void 0;return i.createElement("div",{className:f.swatches},e.map((e,t)=>i.createElement(b,{key:String(e)+t,index:t,color:e,selected:o&&Object(c.areEqualRgb)(o,Object(c.parseRgb)(String(e))),onSelect:this._onSelect})),n)}}var w=n("U1eG"),C=n("mwqF");function _(e){const t="Invalid RGB color: "+e;if(null===e)throw new Error(t);const n=e.match(/^#?([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})$/i);if(null===n)throw new Error(t);const[,o,i,s]=n;if(!o||!i||!s)throw new Error(t);const r=parseInt(o,16)/255,a=parseInt(i,16)/255,c=parseInt(s,16)/255,l=Math.max(r,a,c),u=Math.min(r,a,c);let h;const d=l,p=l-u,m=0===l?0:p/l;if(l===u)h=0;else{switch(l){case r:h=(a-c)/p+(a<c?6:0);break;case a:h=(c-r)/p+2;break;case c:h=(r-a)/p+4;break;default:h=0}h/=6}return{h,s:m,v:d}}var E=n("UXvI"),y=n("lY1a");class O extends i.PureComponent{constructor(){super(...arguments),this._container=null,this._refContainer=e=>{this._container=e},this._handlePosition=e=>{const{hsv:{h:t},onChange:n}=this.props;if(!n)return;const o=Object(l.ensureNotNull)(this._container).getBoundingClientRect(),i=e.clientX-o.left,s=e.clientY-o.top;let r=i/o.width;r<0?r=0:r>1&&(r=1);let a=1-s/o.height;a<0?a=0:a>1&&(a=1),n({h:t,s:r,v:a})},this._mouseDown=e=>{window.addEventListener("mouseup",this._mouseUp),window.addEventListener("mousemove",this._mouseMove)},this._mouseUp=e=>{window.removeEventListener("mousemove",this._mouseMove),window.removeEventListener("mouseup",this._mouseUp),this._handlePosition(e)},this._mouseMove=Object(E.default)(this._handlePosition,100),this._handleTouch=e=>{this._handlePosition(e.nativeEvent.touches[0])}}render(){const{className:e,hsv:{h:t,s:n,v:o}}=this.props,s=`hsl(${360*t}, 100%, 50%)`;return i.createElement("div",{className:a()(y.saturation,e),style:{backgroundColor:s},ref:this._refContainer,onMouseDown:this._mouseDown,onTouchStart:this._handleTouch,onTouchMove:this._handleTouch},i.createElement("div",{className:y.pointer,style:{left:100*n+"%",top:100*(1-o)+"%"}}))}}var S=n("jpE+");class N extends i.PureComponent{constructor(){super(...arguments),this._container=null,this._refContainer=e=>{this._container=e},this._handlePosition=e=>{const{hsv:{s:t,v:n},onChange:o}=this.props;if(!o)return
;const i=Object(l.ensureNotNull)(this._container).getBoundingClientRect();let s=(e.clientY-i.top)/i.height;s<0?s=0:s>1&&(s=1),o({h:s,s:t,v:n})},this._mouseDown=e=>{window.addEventListener("mouseup",this._mouseUp),window.addEventListener("mousemove",this._mouseMove)},this._mouseUp=e=>{window.removeEventListener("mousemove",this._mouseMove),window.removeEventListener("mouseup",this._mouseUp),this._handlePosition(e)},this._mouseMove=Object(E.default)(this._handlePosition,100),this._handleTouch=e=>{this._handlePosition(e.nativeEvent.touches[0])}}render(){const{className:e,hsv:{h:t}}=this.props;return i.createElement("div",{className:a()(S.hue,e)},i.createElement("div",{className:S.pointerContainer,ref:this._refContainer,onMouseDown:this._mouseDown,onTouchStart:this._handleTouch,onTouchMove:this._handleTouch},i.createElement("div",{className:S.pointer,style:{top:100*t+"%"}})))}}var k=n("uJfL");const I=window.t("Add",{context:"Color Picker"});class T extends i.PureComponent{constructor(e){super(e),this._handleHSV=e=>{const t=function(e){const{h:t,s:n,v:o}=e;let i,s,r;const a=Math.floor(6*t),c=6*t-a,l=o*(1-n),u=o*(1-c*n),h=o*(1-(1-c)*n);switch(a%6){case 0:i=o,s=h,r=l;break;case 1:i=u,s=o,r=l;break;case 2:i=l,s=o,r=h;break;case 3:i=l,s=u,r=o;break;case 4:i=h,s=l,r=o;break;case 5:i=o,s=l,r=u;break;default:i=0,s=0,r=0}return"#"+[255*i,255*s,255*r].map(e=>("0"+Math.round(e).toString(16)).replace(/.+?([a-f0-9]{2})$/i,"$1")).join("")}(e)||"#000000";this.setState({color:t,inputColor:t.replace(/^#/,""),hsv:e}),this.props.onSelect(t)},this._handleInput=e=>{const t=e.currentTarget.value;try{const e=_(t),n="#"+t;this.setState({color:n,inputColor:t,hsv:e}),this.props.onSelect(n)}catch(e){this.setState({inputColor:t})}},this._handleAddColor=()=>this.props.onAdd(this.state.color);const t=e.color||"#000000";this.state={color:t,inputColor:t.replace(/^#/,""),hsv:_(t)}}render(){const{color:e,hsv:t,inputColor:n}=this.state;return i.createElement("div",{className:k.container},i.createElement("div",{className:k.form},i.createElement("div",{className:k.swatch,style:{backgroundColor:e}}),i.createElement("div",{className:k.inputWrap},i.createElement("span",{className:k.inputHash},"#"),i.createElement("input",{type:"text",className:k.input,value:n,onChange:this._handleInput})),i.createElement("div",{className:k.buttonWrap},i.createElement(C.a,{size:"s",onClick:this._handleAddColor},I))),i.createElement("div",{className:k.hueSaturationWrap},i.createElement(O,{className:k.saturation,hsv:t,onChange:this._handleHSV}),i.createElement(N,{className:k.hue,hsv:t,onChange:this._handleHSV})))}}var x=n("1Kfe");n.d(t,"a",(function(){return M}));const j=window.t("Add Custom Color",{context:"Color Picker"}),P=window.t("Opacity",{context:"Color Picker"});class M extends i.PureComponent{constructor(e){super(e),this._handleAddColor=e=>{this.setState({isCustom:!1}),this._onToggleCustom(!1);const{onAddColor:t}=this.props;t&&t(e)},this._handleSelectColor=e=>{const{onColorChange:t}=this.props,{isCustom:n}=this.state;t&&t(e,n)},this._handleCustomClick=()=>{
this.setState({isCustom:!0}),this._onToggleCustom(!0)},this._handleOpacity=e=>{const{onOpacityChange:t}=this.props;t&&t(e)},this.state={isCustom:!1}}componentDidUpdate(e,t){e.selectOpacity!==this.props.selectOpacity&&this.props.menu&&this.props.menu.update()}render(){const{color:e,opacity:t,selectCustom:n,selectOpacity:o,customColors:s,onRemoveCustomColor:r}=this.props,{isCustom:c}=this.state,l="number"==typeof t?t:1;return c?i.createElement(T,{color:e,onSelect:this._handleSelectColor,onAdd:this._handleAddColor}):i.createElement("div",{className:x.container},i.createElement(g,{colors:m.a,color:e,onSelect:this._handleSelectColor}),i.createElement(g,{colors:m.b,color:e,onSelect:this._handleSelectColor}),i.createElement("div",{className:x.separator}),i.createElement(p.Provider,{value:r},i.createElement(g,{colors:s,color:e,onSelect:this._handleSelectColor},n&&i.createElement("div",{className:a()(x.customButton,"apply-common-tooltip"),onClick:this._handleCustomClick,title:j}))),o&&i.createElement(i.Fragment,null,i.createElement("div",{className:x.sectionTitle},P),i.createElement(w.a,{color:e,opacity:l,onChange:this._handleOpacity})))}_onToggleCustom(e){const{onToggleCustom:t}=this.props;t&&t(e)}}},"jpE+":function(e,t,n){e.exports={hue:"hue-1Mi0KyO-",pointer:"pointer-1Mi0KyO-",pointerContainer:"pointerContainer-1Mi0KyO-"}},lY1a:function(e,t,n){e.exports={saturation:"saturation-2uNV-KY0",pointer:"pointer-2uNV-KY0"}},mwqF:function(e,t,n){"use strict";var o=n("q1tI"),i=n.n(o),s=n("TSYQ"),r=n("wwkJ"),a=n("ZWNO");function c(e,t){const{intent:n="primary",size:o="m",appearance:i="default",useFullWidth:r=!1,tabIndex:c=0,icon:l,className:u,isGrouped:h,cellState:d,disablePositionAdjustment:p=!1}=t,m=function(e,t){let n="";return 0!==e&&(1&e&&(n=s(n,t["no-corner-top-left"])),2&e&&(n=s(n,t["no-corner-top-right"])),4&e&&(n=s(n,t["no-corner-bottom-right"])),8&e&&(n=s(n,t["no-corner-bottom-left"]))),n}(Object(a.a)(d),e);return s(u,e.button,e["size-"+o],e["intent-"+n],e["appearance-"+i],r&&e["full-width"],-1===c&&e.noOutline,l&&"s"!==o&&e["with-icon"],m,h&&e.grouped,!p&&e["adjust-position"],d.isTop&&e["first-row"],d.isLeft&&e["first-col"])}var l=n("2A9e");n("+l/S");function u(e){const{className:t,intent:n,size:a,appearance:u,disabled:h,useFullWidth:d,reference:p,icon:m,children:v,tabIndex:f,...b}=e,{isGrouped:g,cellState:w,disablePositionAdjustment:C}=Object(o.useContext)(r.a),_=c(l,{intent:n,size:a,appearance:u,disabled:h,useFullWidth:d,tabIndex:f,icon:m,isGrouped:g,cellState:w,disablePositionAdjustment:C});return i.a.createElement("button",{className:s(_,t),disabled:h,ref:p,tabIndex:f,...b},m&&"s"!==a&&i.a.createElement("span",{className:l.icon},m),i.a.createElement("span",{className:l.content},v))}n.d(t,"a",(function(){return u}))},tUxN:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 11 9" width="11" height="9" fill="none"><path stroke-width="2" d="M0.999878 4L3.99988 7L9.99988 1"/></svg>'},uJfL:function(e,t,n){e.exports={container:"container-1r82-bI2",form:"form-1r82-bI2",swatch:"swatch-1r82-bI2",
inputWrap:"inputWrap-1r82-bI2",inputHash:"inputHash-1r82-bI2",input:"input-1r82-bI2",buttonWrap:"buttonWrap-1r82-bI2",hueSaturationWrap:"hueSaturationWrap-1r82-bI2",saturation:"saturation-1r82-bI2",hue:"hue-1r82-bI2"}},v1bN:function(e,t,n){e.exports={"tablet-small-breakpoint":"screen and (max-width: 428px)",item:"item-2IihgTnv",hovered:"hovered-2IihgTnv",isDisabled:"isDisabled-2IihgTnv",isActive:"isActive-2IihgTnv",shortcut:"shortcut-2IihgTnv",toolbox:"toolbox-2IihgTnv",withIcon:"withIcon-2IihgTnv",icon:"icon-2IihgTnv",labelRow:"labelRow-2IihgTnv",label:"label-2IihgTnv",showOnHover:"showOnHover-2IihgTnv"}},vCF3:function(e,t,n){"use strict";n.d(t,"a",(function(){return c}));var o=n("q1tI"),i=n("TSYQ"),s=n("Iivm"),r=n("tUxN"),a=n("F0Qt");n("P4l+");function c(e){const t=i(a.box,a["intent-"+e.intent],{[a.check]:!Boolean(e.indeterminate),[a.dot]:Boolean(e.indeterminate),[a.noOutline]:-1===e.tabIndex}),n=i(a.wrapper,e.className);return o.createElement("span",{className:n,title:e.title},o.createElement("input",{id:e.id,tabIndex:e.tabIndex,className:a.input,type:"checkbox",name:e.name,checked:e.checked,disabled:e.disabled,value:e.value,autoFocus:e.autoFocus,role:e.role,onChange:function(){e.onChange&&e.onChange(e.value)},ref:e.reference}),o.createElement("span",{className:t},o.createElement(s.a,{icon:r,className:a.icon})))}},wLjq:function(e,t,n){"use strict";n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return l}));var o=n("HGP3");const i=o.colorsPalette["color-white"],s=["ripe-red","tan-orange","banana-yellow","iguana-green","minty-green","sky-blue","tv-blue","deep-blue","grapes-purple","berry-pink"],r=[200,300,400,500,600,700,800,900].map(e=>"color-cold-gray-"+e);r.unshift("color-white"),r.push("color-black"),s.forEach(e=>{r.push(`color-${e}-500`)});const a=r.map(e=>o.colorsPalette[e]),c=[];[100,200,300,400,700,900].forEach(e=>{s.forEach(t=>{c.push(`color-${t}-${e}`)})});const l=c.map(e=>o.colorsPalette[e])},"x0D+":function(e,t,n){var o,i,s;i=[t],void 0===(s="function"==typeof(o=function(e){"use strict";function t(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}Object.defineProperty(e,"__esModule",{value:!0});var n=!1;if("undefined"!=typeof window){var o={get passive(){n=!0}};window.addEventListener("testPassive",null,o),window.removeEventListener("testPassive",null,o)}var i="undefined"!=typeof window&&window.navigator&&window.navigator.platform&&/iP(ad|hone|od)/.test(window.navigator.platform),s=[],r=!1,a=-1,c=void 0,l=void 0,u=function(e){return s.some((function(t){return!(!t.options.allowTouchMove||!t.options.allowTouchMove(e))}))},h=function(e){var t=e||window.event;return!!u(t.target)||1<t.touches.length||(t.preventDefault&&t.preventDefault(),!1)},d=function(){setTimeout((function(){void 0!==l&&(document.body.style.paddingRight=l,l=void 0),void 0!==c&&(document.body.style.overflow=c,c=void 0)}))};e.disableBodyScroll=function(e,o){if(i){
if(!e)return void console.error("disableBodyScroll unsuccessful - targetElement must be provided when calling disableBodyScroll on IOS devices.");if(e&&!s.some((function(t){return t.targetElement===e}))){var d={targetElement:e,options:o||{}};s=[].concat(t(s),[d]),e.ontouchstart=function(e){1===e.targetTouches.length&&(a=e.targetTouches[0].clientY)},e.ontouchmove=function(t){var n,o,i,s;1===t.targetTouches.length&&(o=e,s=(n=t).targetTouches[0].clientY-a,!u(n.target)&&(o&&0===o.scrollTop&&0<s||(i=o)&&i.scrollHeight-i.scrollTop<=i.clientHeight&&s<0?h(n):n.stopPropagation()))},r||(document.addEventListener("touchmove",h,n?{passive:!1}:void 0),r=!0)}}else{m=o,setTimeout((function(){if(void 0===l){var e=!!m&&!0===m.reserveScrollBarGap,t=window.innerWidth-document.documentElement.clientWidth;e&&0<t&&(l=document.body.style.paddingRight,document.body.style.paddingRight=t+"px")}void 0===c&&(c=document.body.style.overflow,document.body.style.overflow="hidden")}));var p={targetElement:e,options:o||{}};s=[].concat(t(s),[p])}var m},e.clearAllBodyScrollLocks=function(){i?(s.forEach((function(e){e.targetElement.ontouchstart=null,e.targetElement.ontouchmove=null})),r&&(document.removeEventListener("touchmove",h,n?{passive:!1}:void 0),r=!1),s=[],a=-1):(d(),s=[])},e.enableBodyScroll=function(e){if(i){if(!e)return void console.error("enableBodyScroll unsuccessful - targetElement must be provided when calling enableBodyScroll on IOS devices.");e.ontouchstart=null,e.ontouchmove=null,s=s.filter((function(t){return t.targetElement!==e})),r&&0===s.length&&(document.removeEventListener("touchmove",h,n?{passive:!1}:void 0),r=!1)}else 1===s.length&&s[0].targetElement===e?(d(),s=[]):s=s.filter((function(t){return t.targetElement!==e}))}})?o.apply(t,i):o)||(e.exports=s)}}]);