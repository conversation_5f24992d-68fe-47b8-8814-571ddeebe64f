// This file serves as a wrapper to export the TradingView widget
// The actual library is loaded via script tag in index.html

// Function to wait for TradingView library to load
const waitForTradingView = () => {
  return new Promise((resolve) => {
    if (window.TradingView && window.TradingView.widget) {
      resolve(window.TradingView.widget);
    } else {
      const checkInterval = setInterval(() => {
        if (window.TradingView && window.TradingView.widget) {
          clearInterval(checkInterval);
          resolve(window.TradingView.widget);
        }
      }, 100);
    }
  });
};

export const widget = window.TradingView?.widget;
export const getWidget = waitForTradingView;
