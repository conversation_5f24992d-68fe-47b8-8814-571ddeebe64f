{"name": "magic-string", "version": "0.27.0", "description": "Modify strings, generate sourcemaps", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "repository": "https://github.com/rich-harris/magic-string", "license": "MIT", "author": "<PERSON>", "main": "./dist/magic-string.cjs.js", "module": "./dist/magic-string.es.mjs", "jsnext:main": "./dist/magic-string.es.mjs", "types": "./index.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js", "types": "./index.d.ts"}}, "files": ["dist/*", "index.d.ts", "README.md"], "scripts": {"build": "rollup -c", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "lint": "eslint src test", "prepare": "npm run build", "prepublishOnly": "rm -rf dist && npm test", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "pretest": "npm run lint && npm run build", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "watch": "rollup -cw"}, "devDependencies": {"@rollup/plugin-node-resolve": "^14.1.0", "@rollup/plugin-replace": "^4.0.0", "benchmark": "^2.1.4", "bumpp": "^8.2.1", "conventional-changelog-cli": "^2.2.2", "eslint": "^8.23.1", "mocha": "^10.0.0", "prettier": "^2.7.1", "rollup": "^2.79.1", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21"}, "engines": {"node": ">=12"}, "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.13"}}