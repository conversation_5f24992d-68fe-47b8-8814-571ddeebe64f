{"version": 3, "names": ["assignmentExpression", "callExpression", "cloneNode", "expressionStatement", "getOuterBindingIdentifiers", "identifier", "isMemberExpression", "isVariableDeclaration", "jsxIdentifier", "jsxMemberExpression", "memberExpression", "numericLiteral", "sequenceExpression", "stringLiteral", "variableDeclaration", "variableDeclarator", "isInType", "path", "parent", "type", "parentPath", "exportKind", "isStatement", "isExpression", "rewriteLiveReferences", "programPath", "metadata", "imported", "Map", "exported", "requeueInParent", "requeue", "source", "data", "localName", "importName", "imports", "set", "importsNamespace", "local", "exportMeta", "get", "push", "names", "rewriteBindingInitVisitorState", "scope", "traverse", "rewriteBindingInitVisitor", "simplifyAccess", "Set", "Array", "from", "keys", "rewriteReferencesVisitorState", "seen", "WeakSet", "buildImportReference", "identNode", "meta", "referenced", "lazy", "namespace", "name", "interop", "computed", "stringSpecifiers", "has", "rewriteReferencesVisitor", "<PERSON><PERSON>", "skip", "ClassDeclaration", "id", "node", "Error", "exportNames", "length", "statement", "buildBindingExportAssignmentExpression", "_blockHoist", "insertAfter", "VariableDeclaration", "Object", "for<PERSON>ach", "localExpr", "exportsObjectName", "exportName", "currentScope", "hasOwnBinding", "rename", "reduce", "expr", "buildImportThrow", "template", "expression", "ast", "ReferencedIdentifier", "add", "importData", "buildCodeFrameError", "localBinding", "getBinding", "rootBinding", "ref", "loc", "isCallExpression", "callee", "isOptionalCallExpression", "isTaggedTemplateExpression", "tag", "replaceWith", "isJSXIdentifier", "object", "property", "UpdateExpression", "arg", "update", "isIdentifier", "exportedNames", "operator", "prefix", "generateDeclaredUidIdentifier", "AssignmentExpression", "exit", "left", "assert", "assignment", "right", "ids", "programScopeIds", "filter", "find", "items", "isExpressionStatement", "programScope", "didTransformExport", "importConstViolationName", "loopBodyScope", "ensureBlock", "bodyPath", "newLoopId", "generateUidIdentifierBasedOnNode", "registerDeclaration", "unshiftContainer"], "sources": ["../src/rewrite-live-references.ts"], "sourcesContent": ["import assert from \"assert\";\nimport {\n  assignmentExpression,\n  callExpression,\n  cloneNode,\n  expressionStatement,\n  getOuterBindingIdentifiers,\n  identifier,\n  isMemberExpression,\n  isVariableDeclaration,\n  jsxIdentifier,\n  jsxMemberExpression,\n  memberExpression,\n  numericLiteral,\n  sequenceExpression,\n  stringLiteral,\n  variableDeclaration,\n  variableDeclarator,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport template from \"@babel/template\";\nimport type { NodePath, Visitor, Scope } from \"@babel/traverse\";\nimport simplifyAccess from \"@babel/helper-simple-access\";\n\nimport type { ModuleMetadata } from \"./normalize-and-load-metadata\";\n\ninterface RewriteReferencesVisitorState {\n  exported: Map<any, any>;\n  metadata: ModuleMetadata;\n  requeueInParent: (path: NodePath) => void;\n  scope: Scope;\n  imported: Map<any, any>;\n  buildImportReference: (\n    [source, importName, localName]: readonly [string, string, string],\n    identNode: t.Identifier | t.CallExpression | t.JSXIdentifier,\n  ) => any;\n  seen: WeakSet<object>;\n}\n\ninterface RewriteBindingInitVisitorState {\n  exported: Map<any, any>;\n  metadata: ModuleMetadata;\n  requeueInParent: (path: NodePath) => void;\n  scope: Scope;\n}\n\nfunction isInType(path: NodePath) {\n  do {\n    switch (path.parent.type) {\n      case \"TSTypeAnnotation\":\n      case \"TSTypeAliasDeclaration\":\n      case \"TSTypeReference\":\n      case \"TypeAnnotation\":\n      case \"TypeAlias\":\n        return true;\n      case \"ExportSpecifier\":\n        return (\n          (\n            path.parentPath.parent as\n              | t.ExportDefaultDeclaration\n              | t.ExportNamedDeclaration\n          ).exportKind === \"type\"\n        );\n      default:\n        if (path.parentPath.isStatement() || path.parentPath.isExpression()) {\n          return false;\n        }\n    }\n  } while ((path = path.parentPath));\n}\n\nexport default function rewriteLiveReferences(\n  programPath: NodePath<t.Program>,\n  metadata: ModuleMetadata,\n) {\n  const imported = new Map();\n  const exported = new Map();\n  const requeueInParent = (path: NodePath) => {\n    // Manually re-queue `exports.default =` expressions so that the ES3\n    // transform has an opportunity to convert them. Ideally this would\n    // happen automatically from the replaceWith above. See #4140 for\n    // more info.\n    programPath.requeue(path);\n  };\n\n  for (const [source, data] of metadata.source) {\n    for (const [localName, importName] of data.imports) {\n      imported.set(localName, [source, importName, null]);\n    }\n    for (const localName of data.importsNamespace) {\n      imported.set(localName, [source, null, localName]);\n    }\n  }\n\n  for (const [local, data] of metadata.local) {\n    let exportMeta = exported.get(local);\n    if (!exportMeta) {\n      exportMeta = [];\n      exported.set(local, exportMeta);\n    }\n\n    exportMeta.push(...data.names);\n  }\n\n  // Rewrite initialization of bindings to update exports.\n  const rewriteBindingInitVisitorState: RewriteBindingInitVisitorState = {\n    metadata,\n    requeueInParent,\n    scope: programPath.scope,\n    exported, // local name => exported name list\n  };\n  programPath.traverse(\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    rewriteBindingInitVisitor,\n    rewriteBindingInitVisitorState,\n  );\n\n  simplifyAccess(\n    programPath,\n    // NOTE(logan): The 'Array.from' calls are to make this code with in loose mode.\n    new Set([...Array.from(imported.keys()), ...Array.from(exported.keys())]),\n    false,\n  );\n\n  // Rewrite reads/writes from imports and exports to have the correct behavior.\n  const rewriteReferencesVisitorState: RewriteReferencesVisitorState = {\n    seen: new WeakSet(),\n    metadata,\n    requeueInParent,\n    scope: programPath.scope,\n    imported, // local / import\n    exported, // local name => exported name list\n    buildImportReference: ([source, importName, localName], identNode) => {\n      const meta = metadata.source.get(source);\n      meta.referenced = true;\n\n      if (localName) {\n        if (meta.lazy) {\n          identNode = callExpression(\n            // @ts-expect-error Fixme: we should handle the case when identNode is a JSXIdentifier\n            identNode,\n            [],\n          );\n        }\n        return identNode;\n      }\n\n      let namespace: t.Expression = identifier(meta.name);\n      if (meta.lazy) namespace = callExpression(namespace, []);\n\n      if (importName === \"default\" && meta.interop === \"node-default\") {\n        return namespace;\n      }\n\n      const computed = metadata.stringSpecifiers.has(importName);\n\n      return memberExpression(\n        namespace,\n        computed ? stringLiteral(importName) : identifier(importName),\n        computed,\n      );\n    },\n  };\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  programPath.traverse(rewriteReferencesVisitor, rewriteReferencesVisitorState);\n}\n\n/**\n * A visitor to inject export update statements during binding initialization.\n */\nconst rewriteBindingInitVisitor: Visitor<RewriteBindingInitVisitorState> = {\n  Scope(path) {\n    path.skip();\n  },\n  ClassDeclaration(path) {\n    const { requeueInParent, exported, metadata } = this;\n\n    const { id } = path.node;\n    if (!id) throw new Error(\"Expected class to have a name\");\n    const localName = id.name;\n\n    const exportNames = exported.get(localName) || [];\n    if (exportNames.length > 0) {\n      const statement = expressionStatement(\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        buildBindingExportAssignmentExpression(\n          metadata,\n          exportNames,\n          identifier(localName),\n          path.scope,\n        ),\n      );\n      // @ts-expect-error todo(flow->ts): avoid mutations\n      statement._blockHoist = path.node._blockHoist;\n\n      requeueInParent(path.insertAfter(statement)[0]);\n    }\n  },\n  VariableDeclaration(path) {\n    const { requeueInParent, exported, metadata } = this;\n\n    Object.keys(path.getOuterBindingIdentifiers()).forEach(localName => {\n      const exportNames = exported.get(localName) || [];\n\n      if (exportNames.length > 0) {\n        const statement = expressionStatement(\n          // eslint-disable-next-line @typescript-eslint/no-use-before-define\n          buildBindingExportAssignmentExpression(\n            metadata,\n            exportNames,\n            identifier(localName),\n            path.scope,\n          ),\n        );\n        // @ts-expect-error todo(flow->ts): avoid mutations\n        statement._blockHoist = path.node._blockHoist;\n\n        requeueInParent(path.insertAfter(statement)[0]);\n      }\n    });\n  },\n};\n\nconst buildBindingExportAssignmentExpression = (\n  metadata: ModuleMetadata,\n  exportNames: string[],\n  localExpr: t.Expression,\n  scope: Scope,\n) => {\n  const exportsObjectName = metadata.exportName;\n  for (\n    let currentScope = scope;\n    currentScope != null;\n    currentScope = currentScope.parent\n  ) {\n    if (currentScope.hasOwnBinding(exportsObjectName)) {\n      currentScope.rename(exportsObjectName);\n    }\n  }\n  return (exportNames || []).reduce((expr, exportName) => {\n    // class Foo {} export { Foo, Foo as Bar };\n    // as\n    // class Foo {} exports.Foo = exports.Bar = Foo;\n    const { stringSpecifiers } = metadata;\n    const computed = stringSpecifiers.has(exportName);\n    return assignmentExpression(\n      \"=\",\n      memberExpression(\n        identifier(exportsObjectName),\n        computed ? stringLiteral(exportName) : identifier(exportName),\n        /* computed */ computed,\n      ),\n      expr,\n    );\n  }, localExpr);\n};\n\nconst buildImportThrow = (localName: string) => {\n  return template.expression.ast`\n    (function() {\n      throw new Error('\"' + '${localName}' + '\" is read-only.');\n    })()\n  `;\n};\n\nconst rewriteReferencesVisitor: Visitor<RewriteReferencesVisitorState> = {\n  ReferencedIdentifier(path) {\n    const { seen, buildImportReference, scope, imported, requeueInParent } =\n      this;\n    if (seen.has(path.node)) return;\n    seen.add(path.node);\n\n    const localName = path.node.name;\n\n    const importData = imported.get(localName);\n    if (importData) {\n      if (isInType(path)) {\n        throw path.buildCodeFrameError(\n          `Cannot transform the imported binding \"${localName}\" since it's also used in a type annotation. ` +\n            `Please strip type annotations using @babel/preset-typescript or @babel/preset-flow.`,\n        );\n      }\n\n      const localBinding = path.scope.getBinding(localName);\n      const rootBinding = scope.getBinding(localName);\n\n      // redeclared in this scope\n      if (rootBinding !== localBinding) return;\n\n      const ref = buildImportReference(importData, path.node);\n\n      // Preserve the binding location so that sourcemaps are nicer.\n      ref.loc = path.node.loc;\n\n      if (\n        (path.parentPath.isCallExpression({ callee: path.node }) ||\n          path.parentPath.isOptionalCallExpression({ callee: path.node }) ||\n          path.parentPath.isTaggedTemplateExpression({ tag: path.node })) &&\n        isMemberExpression(ref)\n      ) {\n        path.replaceWith(sequenceExpression([numericLiteral(0), ref]));\n      } else if (path.isJSXIdentifier() && isMemberExpression(ref)) {\n        const { object, property } = ref;\n        path.replaceWith(\n          jsxMemberExpression(\n            // @ts-expect-error todo(flow->ts): possible bug `object` might not have a name\n            jsxIdentifier(object.name),\n            // @ts-expect-error todo(flow->ts): possible bug `property` might not have a name\n            jsxIdentifier(property.name),\n          ),\n        );\n      } else {\n        path.replaceWith(ref);\n      }\n\n      requeueInParent(path);\n\n      // The path could have been replaced with an identifier that would\n      // otherwise be re-visited, so we skip processing its children.\n      path.skip();\n    }\n  },\n\n  UpdateExpression(path) {\n    const {\n      scope,\n      seen,\n      imported,\n      exported,\n      requeueInParent,\n      buildImportReference,\n    } = this;\n\n    if (seen.has(path.node)) return;\n\n    seen.add(path.node);\n\n    const arg = path.get(\"argument\");\n\n    // No change needed\n    if (arg.isMemberExpression()) return;\n\n    const update = path.node;\n\n    if (arg.isIdentifier()) {\n      const localName = arg.node.name;\n\n      // redeclared in this scope\n      if (scope.getBinding(localName) !== path.scope.getBinding(localName)) {\n        return;\n      }\n\n      const exportedNames = exported.get(localName);\n      const importData = imported.get(localName);\n\n      if (exportedNames?.length > 0 || importData) {\n        if (importData) {\n          path.replaceWith(\n            assignmentExpression(\n              update.operator[0] + \"=\",\n              buildImportReference(importData, arg.node),\n              buildImportThrow(localName),\n            ),\n          );\n        } else if (update.prefix) {\n          // ++foo\n          // =>   exports.foo = ++foo\n          path.replaceWith(\n            buildBindingExportAssignmentExpression(\n              this.metadata,\n              exportedNames,\n              cloneNode(update),\n              path.scope,\n            ),\n          );\n        } else {\n          // foo++\n          // =>   (ref = i++, exports.i = i, ref)\n          const ref = scope.generateDeclaredUidIdentifier(localName);\n\n          path.replaceWith(\n            sequenceExpression([\n              assignmentExpression(\"=\", cloneNode(ref), cloneNode(update)),\n              buildBindingExportAssignmentExpression(\n                this.metadata,\n                exportedNames,\n                identifier(localName),\n                path.scope,\n              ),\n              cloneNode(ref),\n            ]),\n          );\n        }\n      }\n    }\n\n    requeueInParent(path);\n    path.skip();\n  },\n\n  AssignmentExpression: {\n    exit(path) {\n      const {\n        scope,\n        seen,\n        imported,\n        exported,\n        requeueInParent,\n        buildImportReference,\n      } = this;\n\n      if (seen.has(path.node)) return;\n      seen.add(path.node);\n\n      const left = path.get(\"left\");\n\n      // No change needed\n      if (left.isMemberExpression()) return;\n\n      if (left.isIdentifier()) {\n        // Simple update-assign foo += 1; export { foo };\n        // =>   exports.foo =  (foo += 1);\n        const localName = left.node.name;\n\n        // redeclared in this scope\n        if (scope.getBinding(localName) !== path.scope.getBinding(localName)) {\n          return;\n        }\n\n        const exportedNames = exported.get(localName);\n        const importData = imported.get(localName);\n        if (exportedNames?.length > 0 || importData) {\n          assert(path.node.operator === \"=\", \"Path was not simplified\");\n\n          const assignment = path.node;\n\n          if (importData) {\n            assignment.left = buildImportReference(importData, left.node);\n\n            assignment.right = sequenceExpression([\n              assignment.right,\n              buildImportThrow(localName),\n            ]);\n          }\n\n          path.replaceWith(\n            buildBindingExportAssignmentExpression(\n              this.metadata,\n              exportedNames,\n              assignment,\n              path.scope,\n            ),\n          );\n          requeueInParent(path);\n        }\n      } else {\n        const ids = left.getOuterBindingIdentifiers();\n        const programScopeIds = Object.keys(ids).filter(\n          localName =>\n            scope.getBinding(localName) === path.scope.getBinding(localName),\n        );\n        const id = programScopeIds.find(localName => imported.has(localName));\n\n        if (id) {\n          path.node.right = sequenceExpression([\n            path.node.right,\n            buildImportThrow(id),\n          ]);\n        }\n\n        // Complex ({a, b, c} = {}); export { a, c };\n        // =>   ({a, b, c} = {}), (exports.a = a, exports.c = c);\n        const items: t.Expression[] = [];\n        programScopeIds.forEach(localName => {\n          const exportedNames = exported.get(localName) || [];\n          if (exportedNames.length > 0) {\n            items.push(\n              buildBindingExportAssignmentExpression(\n                this.metadata,\n                exportedNames,\n                identifier(localName),\n                path.scope,\n              ),\n            );\n          }\n        });\n\n        if (items.length > 0) {\n          let node: t.Node = sequenceExpression(items);\n          if (path.parentPath.isExpressionStatement()) {\n            node = expressionStatement(node);\n            // @ts-expect-error todo(flow->ts): avoid mutations\n            node._blockHoist = path.parentPath.node._blockHoist;\n          }\n\n          const statement = path.insertAfter(node)[0];\n          requeueInParent(statement);\n        }\n      }\n    },\n  },\n  \"ForOfStatement|ForInStatement\"(\n    path: NodePath<t.ForOfStatement | t.ForInStatement>,\n  ) {\n    const { scope, node } = path;\n    const { left } = node;\n    const { exported, imported, scope: programScope } = this;\n\n    if (!isVariableDeclaration(left)) {\n      let didTransformExport = false,\n        importConstViolationName;\n      const loopBodyScope = path.get(\"body\").scope;\n      for (const name of Object.keys(getOuterBindingIdentifiers(left))) {\n        if (programScope.getBinding(name) === scope.getBinding(name)) {\n          if (exported.has(name)) {\n            didTransformExport = true;\n            if (loopBodyScope.hasOwnBinding(name)) {\n              loopBodyScope.rename(name);\n            }\n          }\n          if (imported.has(name) && !importConstViolationName) {\n            importConstViolationName = name;\n          }\n        }\n      }\n      if (!didTransformExport && !importConstViolationName) {\n        return;\n      }\n\n      path.ensureBlock();\n      const bodyPath = path.get(\"body\");\n\n      const newLoopId = scope.generateUidIdentifierBasedOnNode(left);\n      path\n        .get(\"left\")\n        .replaceWith(\n          variableDeclaration(\"let\", [\n            variableDeclarator(cloneNode(newLoopId)),\n          ]),\n        );\n      scope.registerDeclaration(path.get(\"left\"));\n\n      if (didTransformExport) {\n        bodyPath.unshiftContainer(\n          \"body\",\n          expressionStatement(assignmentExpression(\"=\", left, newLoopId)),\n        );\n      }\n      if (importConstViolationName) {\n        bodyPath.unshiftContainer(\n          \"body\",\n          expressionStatement(buildImportThrow(importConstViolationName)),\n        );\n      }\n    }\n  },\n};\n"], "mappings": ";;;;;;AAAA;AACA;AAmBA;AAEA;AAAyD;EApBvDA,oBAAoB;EACpBC,cAAc;EACdC,SAAS;EACTC,mBAAmB;EACnBC,0BAA0B;EAC1BC,UAAU;EACVC,kBAAkB;EAClBC,qBAAqB;EACrBC,aAAa;EACbC,mBAAmB;EACnBC,gBAAgB;EAChBC,cAAc;EACdC,kBAAkB;EAClBC,aAAa;EACbC,mBAAmB;EACnBC;AAAkB;AA6BpB,SAASC,QAAQ,CAACC,IAAc,EAAE;EAChC,GAAG;IACD,QAAQA,IAAI,CAACC,MAAM,CAACC,IAAI;MACtB,KAAK,kBAAkB;MACvB,KAAK,wBAAwB;MAC7B,KAAK,iBAAiB;MACtB,KAAK,gBAAgB;MACrB,KAAK,WAAW;QACd,OAAO,IAAI;MACb,KAAK,iBAAiB;QACpB,OAEIF,IAAI,CAACG,UAAU,CAACF,MAAM,CAGtBG,UAAU,KAAK,MAAM;MAE3B;QACE,IAAIJ,IAAI,CAACG,UAAU,CAACE,WAAW,EAAE,IAAIL,IAAI,CAACG,UAAU,CAACG,YAAY,EAAE,EAAE;UACnE,OAAO,KAAK;QACd;IAAC;EAEP,CAAC,QAASN,IAAI,GAAGA,IAAI,CAACG,UAAU;AAClC;AAEe,SAASI,qBAAqB,CAC3CC,WAAgC,EAChCC,QAAwB,EACxB;EACA,MAAMC,QAAQ,GAAG,IAAIC,GAAG,EAAE;EAC1B,MAAMC,QAAQ,GAAG,IAAID,GAAG,EAAE;EAC1B,MAAME,eAAe,GAAIb,IAAc,IAAK;IAK1CQ,WAAW,CAACM,OAAO,CAACd,IAAI,CAAC;EAC3B,CAAC;EAED,KAAK,MAAM,CAACe,MAAM,EAAEC,IAAI,CAAC,IAAIP,QAAQ,CAACM,MAAM,EAAE;IAC5C,KAAK,MAAM,CAACE,SAAS,EAAEC,UAAU,CAAC,IAAIF,IAAI,CAACG,OAAO,EAAE;MAClDT,QAAQ,CAACU,GAAG,CAACH,SAAS,EAAE,CAACF,MAAM,EAAEG,UAAU,EAAE,IAAI,CAAC,CAAC;IACrD;IACA,KAAK,MAAMD,SAAS,IAAID,IAAI,CAACK,gBAAgB,EAAE;MAC7CX,QAAQ,CAACU,GAAG,CAACH,SAAS,EAAE,CAACF,MAAM,EAAE,IAAI,EAAEE,SAAS,CAAC,CAAC;IACpD;EACF;EAEA,KAAK,MAAM,CAACK,KAAK,EAAEN,IAAI,CAAC,IAAIP,QAAQ,CAACa,KAAK,EAAE;IAC1C,IAAIC,UAAU,GAAGX,QAAQ,CAACY,GAAG,CAACF,KAAK,CAAC;IACpC,IAAI,CAACC,UAAU,EAAE;MACfA,UAAU,GAAG,EAAE;MACfX,QAAQ,CAACQ,GAAG,CAACE,KAAK,EAAEC,UAAU,CAAC;IACjC;IAEAA,UAAU,CAACE,IAAI,CAAC,GAAGT,IAAI,CAACU,KAAK,CAAC;EAChC;EAGA,MAAMC,8BAA8D,GAAG;IACrElB,QAAQ;IACRI,eAAe;IACfe,KAAK,EAAEpB,WAAW,CAACoB,KAAK;IACxBhB;EACF,CAAC;EACDJ,WAAW,CAACqB,QAAQ,CAElBC,yBAAyB,EACzBH,8BAA8B,CAC/B;EAED,IAAAI,2BAAc,EACZvB,WAAW,EAEX,IAAIwB,GAAG,CAAC,CAAC,GAAGC,KAAK,CAACC,IAAI,CAACxB,QAAQ,CAACyB,IAAI,EAAE,CAAC,EAAE,GAAGF,KAAK,CAACC,IAAI,CAACtB,QAAQ,CAACuB,IAAI,EAAE,CAAC,CAAC,CAAC,EACzE,KAAK,CACN;EAGD,MAAMC,6BAA4D,GAAG;IACnEC,IAAI,EAAE,IAAIC,OAAO,EAAE;IACnB7B,QAAQ;IACRI,eAAe;IACfe,KAAK,EAAEpB,WAAW,CAACoB,KAAK;IACxBlB,QAAQ;IACRE,QAAQ;IACR2B,oBAAoB,EAAE,CAAC,CAACxB,MAAM,EAAEG,UAAU,EAAED,SAAS,CAAC,EAAEuB,SAAS,KAAK;MACpE,MAAMC,IAAI,GAAGhC,QAAQ,CAACM,MAAM,CAACS,GAAG,CAACT,MAAM,CAAC;MACxC0B,IAAI,CAACC,UAAU,GAAG,IAAI;MAEtB,IAAIzB,SAAS,EAAE;QACb,IAAIwB,IAAI,CAACE,IAAI,EAAE;UACbH,SAAS,GAAGxD,cAAc,CAExBwD,SAAS,EACT,EAAE,CACH;QACH;QACA,OAAOA,SAAS;MAClB;MAEA,IAAII,SAAuB,GAAGxD,UAAU,CAACqD,IAAI,CAACI,IAAI,CAAC;MACnD,IAAIJ,IAAI,CAACE,IAAI,EAAEC,SAAS,GAAG5D,cAAc,CAAC4D,SAAS,EAAE,EAAE,CAAC;MAExD,IAAI1B,UAAU,KAAK,SAAS,IAAIuB,IAAI,CAACK,OAAO,KAAK,cAAc,EAAE;QAC/D,OAAOF,SAAS;MAClB;MAEA,MAAMG,QAAQ,GAAGtC,QAAQ,CAACuC,gBAAgB,CAACC,GAAG,CAAC/B,UAAU,CAAC;MAE1D,OAAOzB,gBAAgB,CACrBmD,SAAS,EACTG,QAAQ,GAAGnD,aAAa,CAACsB,UAAU,CAAC,GAAG9B,UAAU,CAAC8B,UAAU,CAAC,EAC7D6B,QAAQ,CACT;IACH;EACF,CAAC;EAEDvC,WAAW,CAACqB,QAAQ,CAACqB,wBAAwB,EAAEd,6BAA6B,CAAC;AAC/E;AAKA,MAAMN,yBAAkE,GAAG;EACzEqB,KAAK,CAACnD,IAAI,EAAE;IACVA,IAAI,CAACoD,IAAI,EAAE;EACb,CAAC;EACDC,gBAAgB,CAACrD,IAAI,EAAE;IACrB,MAAM;MAAEa,eAAe;MAAED,QAAQ;MAAEH;IAAS,CAAC,GAAG,IAAI;IAEpD,MAAM;MAAE6C;IAAG,CAAC,GAAGtD,IAAI,CAACuD,IAAI;IACxB,IAAI,CAACD,EAAE,EAAE,MAAM,IAAIE,KAAK,CAAC,+BAA+B,CAAC;IACzD,MAAMvC,SAAS,GAAGqC,EAAE,CAACT,IAAI;IAEzB,MAAMY,WAAW,GAAG7C,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC,IAAI,EAAE;IACjD,IAAIwC,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MAC1B,MAAMC,SAAS,GAAGzE,mBAAmB,CAEnC0E,sCAAsC,CACpCnD,QAAQ,EACRgD,WAAW,EACXrE,UAAU,CAAC6B,SAAS,CAAC,EACrBjB,IAAI,CAAC4B,KAAK,CACX,CACF;MAED+B,SAAS,CAACE,WAAW,GAAG7D,IAAI,CAACuD,IAAI,CAACM,WAAW;MAE7ChD,eAAe,CAACb,IAAI,CAAC8D,WAAW,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD;EACF,CAAC;EACDI,mBAAmB,CAAC/D,IAAI,EAAE;IACxB,MAAM;MAAEa,eAAe;MAAED,QAAQ;MAAEH;IAAS,CAAC,GAAG,IAAI;IAEpDuD,MAAM,CAAC7B,IAAI,CAACnC,IAAI,CAACb,0BAA0B,EAAE,CAAC,CAAC8E,OAAO,CAAChD,SAAS,IAAI;MAClE,MAAMwC,WAAW,GAAG7C,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC,IAAI,EAAE;MAEjD,IAAIwC,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;QAC1B,MAAMC,SAAS,GAAGzE,mBAAmB,CAEnC0E,sCAAsC,CACpCnD,QAAQ,EACRgD,WAAW,EACXrE,UAAU,CAAC6B,SAAS,CAAC,EACrBjB,IAAI,CAAC4B,KAAK,CACX,CACF;QAED+B,SAAS,CAACE,WAAW,GAAG7D,IAAI,CAACuD,IAAI,CAACM,WAAW;QAE7ChD,eAAe,CAACb,IAAI,CAAC8D,WAAW,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AAED,MAAMC,sCAAsC,GAAG,CAC7CnD,QAAwB,EACxBgD,WAAqB,EACrBS,SAAuB,EACvBtC,KAAY,KACT;EACH,MAAMuC,iBAAiB,GAAG1D,QAAQ,CAAC2D,UAAU;EAC7C,KACE,IAAIC,YAAY,GAAGzC,KAAK,EACxByC,YAAY,IAAI,IAAI,EACpBA,YAAY,GAAGA,YAAY,CAACpE,MAAM,EAClC;IACA,IAAIoE,YAAY,CAACC,aAAa,CAACH,iBAAiB,CAAC,EAAE;MACjDE,YAAY,CAACE,MAAM,CAACJ,iBAAiB,CAAC;IACxC;EACF;EACA,OAAO,CAACV,WAAW,IAAI,EAAE,EAAEe,MAAM,CAAC,CAACC,IAAI,EAAEL,UAAU,KAAK;IAItD,MAAM;MAAEpB;IAAiB,CAAC,GAAGvC,QAAQ;IACrC,MAAMsC,QAAQ,GAAGC,gBAAgB,CAACC,GAAG,CAACmB,UAAU,CAAC;IACjD,OAAOrF,oBAAoB,CACzB,GAAG,EACHU,gBAAgB,CACdL,UAAU,CAAC+E,iBAAiB,CAAC,EAC7BpB,QAAQ,GAAGnD,aAAa,CAACwE,UAAU,CAAC,GAAGhF,UAAU,CAACgF,UAAU,CAAC,EAC9CrB,QAAQ,CACxB,EACD0B,IAAI,CACL;EACH,CAAC,EAAEP,SAAS,CAAC;AACf,CAAC;AAED,MAAMQ,gBAAgB,GAAIzD,SAAiB,IAAK;EAC9C,OAAO0D,iBAAQ,CAACC,UAAU,CAACC,GAAI;AACjC;AACA,+BAA+B5D,SAAU;AACzC;AACA,GAAG;AACH,CAAC;AAED,MAAMiC,wBAAgE,GAAG;EACvE4B,oBAAoB,CAAC9E,IAAI,EAAE;IACzB,MAAM;MAAEqC,IAAI;MAAEE,oBAAoB;MAAEX,KAAK;MAAElB,QAAQ;MAAEG;IAAgB,CAAC,GACpE,IAAI;IACN,IAAIwB,IAAI,CAACY,GAAG,CAACjD,IAAI,CAACuD,IAAI,CAAC,EAAE;IACzBlB,IAAI,CAAC0C,GAAG,CAAC/E,IAAI,CAACuD,IAAI,CAAC;IAEnB,MAAMtC,SAAS,GAAGjB,IAAI,CAACuD,IAAI,CAACV,IAAI;IAEhC,MAAMmC,UAAU,GAAGtE,QAAQ,CAACc,GAAG,CAACP,SAAS,CAAC;IAC1C,IAAI+D,UAAU,EAAE;MACd,IAAIjF,QAAQ,CAACC,IAAI,CAAC,EAAE;QAClB,MAAMA,IAAI,CAACiF,mBAAmB,CAC3B,0CAAyChE,SAAU,+CAA8C,GAC/F,qFAAoF,CACxF;MACH;MAEA,MAAMiE,YAAY,GAAGlF,IAAI,CAAC4B,KAAK,CAACuD,UAAU,CAAClE,SAAS,CAAC;MACrD,MAAMmE,WAAW,GAAGxD,KAAK,CAACuD,UAAU,CAAClE,SAAS,CAAC;MAG/C,IAAImE,WAAW,KAAKF,YAAY,EAAE;MAElC,MAAMG,GAAG,GAAG9C,oBAAoB,CAACyC,UAAU,EAAEhF,IAAI,CAACuD,IAAI,CAAC;MAGvD8B,GAAG,CAACC,GAAG,GAAGtF,IAAI,CAACuD,IAAI,CAAC+B,GAAG;MAEvB,IACE,CAACtF,IAAI,CAACG,UAAU,CAACoF,gBAAgB,CAAC;QAAEC,MAAM,EAAExF,IAAI,CAACuD;MAAK,CAAC,CAAC,IACtDvD,IAAI,CAACG,UAAU,CAACsF,wBAAwB,CAAC;QAAED,MAAM,EAAExF,IAAI,CAACuD;MAAK,CAAC,CAAC,IAC/DvD,IAAI,CAACG,UAAU,CAACuF,0BAA0B,CAAC;QAAEC,GAAG,EAAE3F,IAAI,CAACuD;MAAK,CAAC,CAAC,KAChElE,kBAAkB,CAACgG,GAAG,CAAC,EACvB;QACArF,IAAI,CAAC4F,WAAW,CAACjG,kBAAkB,CAAC,CAACD,cAAc,CAAC,CAAC,CAAC,EAAE2F,GAAG,CAAC,CAAC,CAAC;MAChE,CAAC,MAAM,IAAIrF,IAAI,CAAC6F,eAAe,EAAE,IAAIxG,kBAAkB,CAACgG,GAAG,CAAC,EAAE;QAC5D,MAAM;UAAES,MAAM;UAAEC;QAAS,CAAC,GAAGV,GAAG;QAChCrF,IAAI,CAAC4F,WAAW,CACdpG,mBAAmB,CAEjBD,aAAa,CAACuG,MAAM,CAACjD,IAAI,CAAC,EAE1BtD,aAAa,CAACwG,QAAQ,CAAClD,IAAI,CAAC,CAC7B,CACF;MACH,CAAC,MAAM;QACL7C,IAAI,CAAC4F,WAAW,CAACP,GAAG,CAAC;MACvB;MAEAxE,eAAe,CAACb,IAAI,CAAC;MAIrBA,IAAI,CAACoD,IAAI,EAAE;IACb;EACF,CAAC;EAED4C,gBAAgB,CAAChG,IAAI,EAAE;IACrB,MAAM;MACJ4B,KAAK;MACLS,IAAI;MACJ3B,QAAQ;MACRE,QAAQ;MACRC,eAAe;MACf0B;IACF,CAAC,GAAG,IAAI;IAER,IAAIF,IAAI,CAACY,GAAG,CAACjD,IAAI,CAACuD,IAAI,CAAC,EAAE;IAEzBlB,IAAI,CAAC0C,GAAG,CAAC/E,IAAI,CAACuD,IAAI,CAAC;IAEnB,MAAM0C,GAAG,GAAGjG,IAAI,CAACwB,GAAG,CAAC,UAAU,CAAC;IAGhC,IAAIyE,GAAG,CAAC5G,kBAAkB,EAAE,EAAE;IAE9B,MAAM6G,MAAM,GAAGlG,IAAI,CAACuD,IAAI;IAExB,IAAI0C,GAAG,CAACE,YAAY,EAAE,EAAE;MACtB,MAAMlF,SAAS,GAAGgF,GAAG,CAAC1C,IAAI,CAACV,IAAI;MAG/B,IAAIjB,KAAK,CAACuD,UAAU,CAAClE,SAAS,CAAC,KAAKjB,IAAI,CAAC4B,KAAK,CAACuD,UAAU,CAAClE,SAAS,CAAC,EAAE;QACpE;MACF;MAEA,MAAMmF,aAAa,GAAGxF,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC;MAC7C,MAAM+D,UAAU,GAAGtE,QAAQ,CAACc,GAAG,CAACP,SAAS,CAAC;MAE1C,IAAI,CAAAmF,aAAa,oBAAbA,aAAa,CAAE1C,MAAM,IAAG,CAAC,IAAIsB,UAAU,EAAE;QAC3C,IAAIA,UAAU,EAAE;UACdhF,IAAI,CAAC4F,WAAW,CACd7G,oBAAoB,CAClBmH,MAAM,CAACG,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,EACxB9D,oBAAoB,CAACyC,UAAU,EAAEiB,GAAG,CAAC1C,IAAI,CAAC,EAC1CmB,gBAAgB,CAACzD,SAAS,CAAC,CAC5B,CACF;QACH,CAAC,MAAM,IAAIiF,MAAM,CAACI,MAAM,EAAE;UAGxBtG,IAAI,CAAC4F,WAAW,CACdhC,sCAAsC,CACpC,IAAI,CAACnD,QAAQ,EACb2F,aAAa,EACbnH,SAAS,CAACiH,MAAM,CAAC,EACjBlG,IAAI,CAAC4B,KAAK,CACX,CACF;QACH,CAAC,MAAM;UAGL,MAAMyD,GAAG,GAAGzD,KAAK,CAAC2E,6BAA6B,CAACtF,SAAS,CAAC;UAE1DjB,IAAI,CAAC4F,WAAW,CACdjG,kBAAkB,CAAC,CACjBZ,oBAAoB,CAAC,GAAG,EAAEE,SAAS,CAACoG,GAAG,CAAC,EAAEpG,SAAS,CAACiH,MAAM,CAAC,CAAC,EAC5DtC,sCAAsC,CACpC,IAAI,CAACnD,QAAQ,EACb2F,aAAa,EACbhH,UAAU,CAAC6B,SAAS,CAAC,EACrBjB,IAAI,CAAC4B,KAAK,CACX,EACD3C,SAAS,CAACoG,GAAG,CAAC,CACf,CAAC,CACH;QACH;MACF;IACF;IAEAxE,eAAe,CAACb,IAAI,CAAC;IACrBA,IAAI,CAACoD,IAAI,EAAE;EACb,CAAC;EAEDoD,oBAAoB,EAAE;IACpBC,IAAI,CAACzG,IAAI,EAAE;MACT,MAAM;QACJ4B,KAAK;QACLS,IAAI;QACJ3B,QAAQ;QACRE,QAAQ;QACRC,eAAe;QACf0B;MACF,CAAC,GAAG,IAAI;MAER,IAAIF,IAAI,CAACY,GAAG,CAACjD,IAAI,CAACuD,IAAI,CAAC,EAAE;MACzBlB,IAAI,CAAC0C,GAAG,CAAC/E,IAAI,CAACuD,IAAI,CAAC;MAEnB,MAAMmD,IAAI,GAAG1G,IAAI,CAACwB,GAAG,CAAC,MAAM,CAAC;MAG7B,IAAIkF,IAAI,CAACrH,kBAAkB,EAAE,EAAE;MAE/B,IAAIqH,IAAI,CAACP,YAAY,EAAE,EAAE;QAGvB,MAAMlF,SAAS,GAAGyF,IAAI,CAACnD,IAAI,CAACV,IAAI;QAGhC,IAAIjB,KAAK,CAACuD,UAAU,CAAClE,SAAS,CAAC,KAAKjB,IAAI,CAAC4B,KAAK,CAACuD,UAAU,CAAClE,SAAS,CAAC,EAAE;UACpE;QACF;QAEA,MAAMmF,aAAa,GAAGxF,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC;QAC7C,MAAM+D,UAAU,GAAGtE,QAAQ,CAACc,GAAG,CAACP,SAAS,CAAC;QAC1C,IAAI,CAAAmF,aAAa,oBAAbA,aAAa,CAAE1C,MAAM,IAAG,CAAC,IAAIsB,UAAU,EAAE;UAC3C2B,OAAM,CAAC3G,IAAI,CAACuD,IAAI,CAAC8C,QAAQ,KAAK,GAAG,EAAE,yBAAyB,CAAC;UAE7D,MAAMO,UAAU,GAAG5G,IAAI,CAACuD,IAAI;UAE5B,IAAIyB,UAAU,EAAE;YACd4B,UAAU,CAACF,IAAI,GAAGnE,oBAAoB,CAACyC,UAAU,EAAE0B,IAAI,CAACnD,IAAI,CAAC;YAE7DqD,UAAU,CAACC,KAAK,GAAGlH,kBAAkB,CAAC,CACpCiH,UAAU,CAACC,KAAK,EAChBnC,gBAAgB,CAACzD,SAAS,CAAC,CAC5B,CAAC;UACJ;UAEAjB,IAAI,CAAC4F,WAAW,CACdhC,sCAAsC,CACpC,IAAI,CAACnD,QAAQ,EACb2F,aAAa,EACbQ,UAAU,EACV5G,IAAI,CAAC4B,KAAK,CACX,CACF;UACDf,eAAe,CAACb,IAAI,CAAC;QACvB;MACF,CAAC,MAAM;QACL,MAAM8G,GAAG,GAAGJ,IAAI,CAACvH,0BAA0B,EAAE;QAC7C,MAAM4H,eAAe,GAAG/C,MAAM,CAAC7B,IAAI,CAAC2E,GAAG,CAAC,CAACE,MAAM,CAC7C/F,SAAS,IACPW,KAAK,CAACuD,UAAU,CAAClE,SAAS,CAAC,KAAKjB,IAAI,CAAC4B,KAAK,CAACuD,UAAU,CAAClE,SAAS,CAAC,CACnE;QACD,MAAMqC,EAAE,GAAGyD,eAAe,CAACE,IAAI,CAAChG,SAAS,IAAIP,QAAQ,CAACuC,GAAG,CAAChC,SAAS,CAAC,CAAC;QAErE,IAAIqC,EAAE,EAAE;UACNtD,IAAI,CAACuD,IAAI,CAACsD,KAAK,GAAGlH,kBAAkB,CAAC,CACnCK,IAAI,CAACuD,IAAI,CAACsD,KAAK,EACfnC,gBAAgB,CAACpB,EAAE,CAAC,CACrB,CAAC;QACJ;QAIA,MAAM4D,KAAqB,GAAG,EAAE;QAChCH,eAAe,CAAC9C,OAAO,CAAChD,SAAS,IAAI;UACnC,MAAMmF,aAAa,GAAGxF,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC,IAAI,EAAE;UACnD,IAAImF,aAAa,CAAC1C,MAAM,GAAG,CAAC,EAAE;YAC5BwD,KAAK,CAACzF,IAAI,CACRmC,sCAAsC,CACpC,IAAI,CAACnD,QAAQ,EACb2F,aAAa,EACbhH,UAAU,CAAC6B,SAAS,CAAC,EACrBjB,IAAI,CAAC4B,KAAK,CACX,CACF;UACH;QACF,CAAC,CAAC;QAEF,IAAIsF,KAAK,CAACxD,MAAM,GAAG,CAAC,EAAE;UACpB,IAAIH,IAAY,GAAG5D,kBAAkB,CAACuH,KAAK,CAAC;UAC5C,IAAIlH,IAAI,CAACG,UAAU,CAACgH,qBAAqB,EAAE,EAAE;YAC3C5D,IAAI,GAAGrE,mBAAmB,CAACqE,IAAI,CAAC;YAEhCA,IAAI,CAACM,WAAW,GAAG7D,IAAI,CAACG,UAAU,CAACoD,IAAI,CAACM,WAAW;UACrD;UAEA,MAAMF,SAAS,GAAG3D,IAAI,CAAC8D,WAAW,CAACP,IAAI,CAAC,CAAC,CAAC,CAAC;UAC3C1C,eAAe,CAAC8C,SAAS,CAAC;QAC5B;MACF;IACF;EACF,CAAC;EACD,+BAA+B,CAC7B3D,IAAmD,EACnD;IACA,MAAM;MAAE4B,KAAK;MAAE2B;IAAK,CAAC,GAAGvD,IAAI;IAC5B,MAAM;MAAE0G;IAAK,CAAC,GAAGnD,IAAI;IACrB,MAAM;MAAE3C,QAAQ;MAAEF,QAAQ;MAAEkB,KAAK,EAAEwF;IAAa,CAAC,GAAG,IAAI;IAExD,IAAI,CAAC9H,qBAAqB,CAACoH,IAAI,CAAC,EAAE;MAChC,IAAIW,kBAAkB,GAAG,KAAK;QAC5BC,wBAAwB;MAC1B,MAAMC,aAAa,GAAGvH,IAAI,CAACwB,GAAG,CAAC,MAAM,CAAC,CAACI,KAAK;MAC5C,KAAK,MAAMiB,IAAI,IAAImB,MAAM,CAAC7B,IAAI,CAAChD,0BAA0B,CAACuH,IAAI,CAAC,CAAC,EAAE;QAChE,IAAIU,YAAY,CAACjC,UAAU,CAACtC,IAAI,CAAC,KAAKjB,KAAK,CAACuD,UAAU,CAACtC,IAAI,CAAC,EAAE;UAC5D,IAAIjC,QAAQ,CAACqC,GAAG,CAACJ,IAAI,CAAC,EAAE;YACtBwE,kBAAkB,GAAG,IAAI;YACzB,IAAIE,aAAa,CAACjD,aAAa,CAACzB,IAAI,CAAC,EAAE;cACrC0E,aAAa,CAAChD,MAAM,CAAC1B,IAAI,CAAC;YAC5B;UACF;UACA,IAAInC,QAAQ,CAACuC,GAAG,CAACJ,IAAI,CAAC,IAAI,CAACyE,wBAAwB,EAAE;YACnDA,wBAAwB,GAAGzE,IAAI;UACjC;QACF;MACF;MACA,IAAI,CAACwE,kBAAkB,IAAI,CAACC,wBAAwB,EAAE;QACpD;MACF;MAEAtH,IAAI,CAACwH,WAAW,EAAE;MAClB,MAAMC,QAAQ,GAAGzH,IAAI,CAACwB,GAAG,CAAC,MAAM,CAAC;MAEjC,MAAMkG,SAAS,GAAG9F,KAAK,CAAC+F,gCAAgC,CAACjB,IAAI,CAAC;MAC9D1G,IAAI,CACDwB,GAAG,CAAC,MAAM,CAAC,CACXoE,WAAW,CACV/F,mBAAmB,CAAC,KAAK,EAAE,CACzBC,kBAAkB,CAACb,SAAS,CAACyI,SAAS,CAAC,CAAC,CACzC,CAAC,CACH;MACH9F,KAAK,CAACgG,mBAAmB,CAAC5H,IAAI,CAACwB,GAAG,CAAC,MAAM,CAAC,CAAC;MAE3C,IAAI6F,kBAAkB,EAAE;QACtBI,QAAQ,CAACI,gBAAgB,CACvB,MAAM,EACN3I,mBAAmB,CAACH,oBAAoB,CAAC,GAAG,EAAE2H,IAAI,EAAEgB,SAAS,CAAC,CAAC,CAChE;MACH;MACA,IAAIJ,wBAAwB,EAAE;QAC5BG,QAAQ,CAACI,gBAAgB,CACvB,MAAM,EACN3I,mBAAmB,CAACwF,gBAAgB,CAAC4C,wBAAwB,CAAC,CAAC,CAChE;MACH;IACF;EACF;AACF,CAAC"}