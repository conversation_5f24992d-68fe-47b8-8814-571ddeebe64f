// Generated by dts-bundle-generator v5.3.0

declare const dateFormatFunctions: {
	"dd MMM 'yy": (date: Date, local: boolean) => string;
	"MMM dd, yyyy": (date: Date, local: boolean) => string;
	"MMM dd": (date: Date, local: boolean) => string;
	"yyyy-MM-dd": (date: Date, local: boolean) => string;
	"yy-MM-dd": (date: Date, local: boolean) => string;
	"yy/MM/dd": (date: Date, local: boolean) => string;
	"yyyy/MM/dd": (date: Date, local: boolean) => string;
	"dd-MM-yyyy": (date: Date, local: boolean) => string;
	"dd-MM-yy": (date: Date, local: boolean) => string;
	"dd/MM/yy": (date: Date, local: boolean) => string;
	"dd/MM/yyyy": (date: Date, local: boolean) => string;
	"MM/dd/yy": (date: Date, local: boolean) => string;
	"MM/dd/yyyy": (date: Date, local: boolean) => string;
};
declare enum OrderOrPositionMessageType {
	Information = "information",
	Warning = "warning",
	Error = "error"
}
declare enum StopType {
	StopLoss = 0,
	TrailingStop = 1
}
export declare const enum ChartStyle {
	Bar = 0,
	Candle = 1,
	Line = 2,
	Area = 3,
	Renko = 4,
	Kagi = 5,
	PnF = 6,
	LineBreak = 7,
	HeikinAshi = 8,
	HollowCandle = 9,
	Baseline = 10,
	HiLo = 12
}
export declare const enum ConnectionStatus {
	Connected = 1,
	Connecting = 2,
	Disconnected = 3,
	Error = 4
}
export declare const enum NotificationType {
	Error = 0,
	Success = 1
}
export declare const enum OrderStatus {
	Canceled = 1,
	Filled = 2,
	Inactive = 3,
	Placing = 4,
	Rejected = 5,
	Working = 6
}
export declare const enum OrderStatusFilter {
	All = 0,
	Canceled = 1,
	Filled = 2,
	Inactive = 3,
	Rejected = 5,
	Working = 6
}
export declare const enum OrderTicketFocusControl {
	LimitPrice = 1,
	StopPrice = 2,
	TakeProfit = 3,
	StopLoss = 4,
	Quantity = 5
}
export declare const enum OrderType {
	Limit = 1,
	Market = 2,
	Stop = 3,
	StopLimit = 4
}
export declare const enum ParentType {
	Order = 1,
	Position = 2,
	Trade = 3
}
export declare const enum PriceScaleMode {
	Normal = 0,
	Log = 1,
	Percentage = 2,
	IndexedTo100 = 3
}
export declare const enum SeriesStyle {
	Bars = 0,
	Candles = 1,
	Line = 2,
	Area = 3,
	HeikenAshi = 8,
	HollowCandles = 9,
	Baseline = 10,
	HiLo = 12,
	Renko = 4,
	Kagi = 5,
	PointAndFigure = 6,
	LineBreak = 7
}
export declare const enum Side {
	Buy = 1,
	Sell = -1
}
export declare const enum StandardFormatterName {
	Date = "date",
	DateOrDateTime = "dateOrDateTime",
	Default = "default",
	Fixed = "fixed",
	VariablePrecision = "variablePrecision",
	FormatQuantity = "formatQuantity",
	FormatPrice = "formatPrice",
	FormatPriceForexSup = "formatPriceForexSup",
	IntegerSeparated = "integerSeparated",
	LocalDate = "localDate",
	LocalDateOrDateTime = "localDateOrDateTime",
	OrderSettings = "orderSettings",
	Percentage = "percentage",
	Pips = "pips",
	Profit = "profit",
	ProfitInInstrumentCurrency = "profitInInstrumentCurrency",
	PosSettings = "posSettings",
	Side = "side",
	PositionSide = "positionSide",
	Status = "status",
	Symbol = "symbol",
	Text = "text",
	TextNoWrap = "textNoWrap",
	TradeSettings = "tradeSettings",
	Type = "type",
	MarginPercent = "marginPercent"
}
export declare const enum TimeFrameType {
	PeriodBack = "period-back",
	TimeRange = "time-range"
}
export declare const enum VisibilityType {
	AlwaysOn = "alwaysOn",
	VisibleOnMouseOver = "visibleOnMouseOver",
	AlwaysOff = "alwaysOff"
}
export declare const widget: ChartingLibraryWidgetConstructor;
export declare function version(): string;
export declare type AccountId = Nominal<string, "AccountID">;
export declare type ActionMetaInfo = ActionDescriptionWithCallback | MenuSeparator;
export declare type AvailableSaveloadVersions = "1.0" | "1.1";
export declare type CellAlignment = "left" | "right";
export declare type ChartActionId = "chartProperties" | "compareOrAdd" | "scalesProperties" | "paneObjectTree" | "insertIndicator" | "symbolSearch" | "changeInterval" | "timeScaleReset" | "chartReset" | "seriesHide" | "studyHide" | "lineToggleLock" | "lineHide" | "scaleSeriesOnly" | "drawingToolbarAction" | "stayInDrawingModeAction" | "hideAllMarks" | "showCountdown" | "showSeriesLastValue" | "showSymbolLabelsAction" | "showStudyLastValue" | "showStudyPlotNamesAction" | "undo" | "redo" | "paneRemoveAllStudiesDrawingTools" | "showSymbolInfoDialog";
export declare type DateFormat = keyof typeof dateFormatFunctions;
export declare type Direction = "buy" | "sell";
export declare type DomeCallback = (data: DOMData) => void;
export declare type DrawingEventType = "click" | "move" | "remove" | "hide" | "show";
export declare type DropdownUpdateParams = Partial<Omit<DropdownParams, "align">>;
export declare type EditObjectDialogObjectType = "mainSeries" | "drawing" | "study" | "other";
export declare type EmptyCallback = () => void;
export declare type EntityId = Nominal<string, "EntityId">;
export declare type ErrorCallback = (reason: string) => void;
export declare type FieldDescriptor = TimeFieldDescriptor | UserTimeFieldDescriptor | SeriesFieldDescriptor | StudyFieldDescriptor;
export declare type GetMarksCallback<T> = (marks: T[]) => void;
export declare type GetNewsFunction = (symbol: string, callback: (response: GetNewsResponse) => void) => void;
export declare type GroupLockState = "Locked" | "Unlocked" | "Partial";
export declare type GroupVisibilityState = "Visible" | "Invisible" | "Partial";
export declare type HeaderWidgetButtonsMode = "fullsize" | "compact" | "adaptive";
export declare type HistoryCallback = (bars: Bar[], meta?: HistoryMetadata) => void;
export declare type IBasicDataFeed = IDatafeedChartApi & IExternalDatafeed;
export declare type INumberFormatter = IFormatter<number>;
export declare type InputFieldValidator = (value: any) => InputFieldValidatorResult;
export declare type InputFieldValidatorResult = PositiveBaseInputFieldValidatorResult | NegativeBaseInputFieldValidatorResult;
export declare type LanguageCode = "ar" | "zh" | "cs" | "da_DK" | "nl_NL" | "en" | "et_EE" | "fr" | "de" | "el" | "he_IL" | "hu_HU" | "id_ID" | "it" | "ja" | "ko" | "fa" | "pl" | "pt" | "ro" | "ru" | "sk_SK" | "es" | "sv" | "th" | "tr" | "vi" | "no" | "ms_MY" | "zh_TW";
export declare type LayoutType = SingleChartLayoutType | MultipleChartsLayoutType;
export declare type LegendMode = "horizontal" | "vertical";
export declare type MarkConstColors = "red" | "green" | "blue" | "yellow";
export declare type MultipleChartsLayoutType = "2h" | "2v" | "2-1" | "3s" | "3h" | "3v" | "4" | "6" | "8" | "1-2" | "3r" | "4h" | "4v" | "4s" | "1-3" | "2-2" | "1-4" | "5s" | "6c" | "8c";
/**
 * This is the generic type useful for declaring a nominal type,
 * which does not structurally matches with the base type and
 * the other types declared over the same base type
 *
 * Usage:
 * @example
 * type Index = Nominal<number, 'Index'>;
 * // let i: Index = 42; // this fails to compile
 * let i: Index = 42 as Index; // OK
 * @example
 * type TagName = Nominal<string, 'TagName'>;
 */
export declare type Nominal<T, Name extends string> = T & {
	[Symbol.species]: Name;
};
export declare type OnReadyCallback = (configuration: DatafeedConfiguration) => void;
export declare type Order = PlacedOrder | BracketOrder;
export declare type PineJS = any;
export declare type QuoteData = QuoteOkData | QuoteErrorData;
export declare type QuotesCallback = (data: QuoteData[]) => void;
export declare type ResolutionString = Nominal<string, "ResolutionString">;
export declare type ResolveCallback = (symbolInfo: LibrarySymbolInfo) => void;
export declare type RssNewsFeedItem = RssNewsFeedInfo | RssNewsFeedInfo[];
export declare type SearchSymbolsCallback = (items: SearchSymbolResultItem[]) => void;
export declare type SeriesEventType = "price_scale_changed";
export declare type SeriesFormat = "price" | "volume";
export declare type SeriesPriceScale = "new-left" | "new-right" | "no-scale" | EntityId;
export declare type ServerTimeCallback = (serverTime: number) => void;
export declare type SetVisibleTimeRange = Omit<VisibleTimeRange, "to"> & Partial<Pick<VisibleTimeRange, "to">>;
export declare type ShapePoint = StickedPoint | PricedPoint | TimePoint;
export declare type ShapesGroupId = Nominal<string, "ShapesGroupId">;
export declare type SingleChartLayoutType = "s";
export declare type StudyEventType = "remove" | "price_scale_changed";
export declare type StudyInputId = Nominal<string, "StudyInputId">;
export declare type StudyInputValue = string | number | boolean;
export declare type StudyOverrideValueType = string | number | boolean;
export declare type StudyPriceScale = "new-left" | "new-right" | "no-scale" | "as-series";
export declare type SubscribeBarsCallback = (bar: Bar) => void;
export declare type SupportedLineTools = "text" | "anchored_text" | "note" | "anchored_note" | "signpost" | "double_curve" | "arc" | "icon" | "arrow_up" | "arrow_down" | "arrow_left" | "arrow_right" | "price_label" | "price_note" | "arrow_marker" | "flag" | "vertical_line" | "horizontal_line" | "cross_line" | "horizontal_ray" | "trend_line" | "info_line" | "trend_angle" | "arrow" | "ray" | "extended" | "parallel_channel" | "disjoint_angle" | "flat_bottom" | "pitchfork" | "schiff_pitchfork_modified" | "schiff_pitchfork" | "balloon" | "inside_pitchfork" | "pitchfan" | "gannbox" | "gannbox_square" | "gannbox_fixed" | "gannbox_fan" | "fib_retracement" | "fib_trend_ext" | "fib_speed_resist_fan" | "fib_timezone" | "fib_trend_time" | "fib_circles" | "fib_spiral" | "fib_speed_resist_arcs" | "fib_channel" | "xabcd_pattern" | "cypher_pattern" | "abcd_pattern" | "callout" | "triangle_pattern" | "3divers_pattern" | "head_and_shoulders" | "fib_wedge" | "elliott_impulse_wave" | "elliott_triangle_wave" | "elliott_triple_combo" | "elliott_correction" | "elliott_double_combo" | "cyclic_lines" | "time_cycles" | "sine_line" | "long_position" | "short_position" | "forecast" | "date_range" | "price_range" | "date_and_price_range" | "bars_pattern" | "ghost_feed" | "projection" | "rectangle" | "rotated_rectangle" | "ellipse" | "triangle" | "polyline" | "path" | "curve" | "cursor" | "dot" | "arrow_cursor" | "eraser" | "measure" | "zoom" | "brush" | "highlighter" | "regression_trend" | "fixed_range_volume_profile";
export declare type SymbolSearchCompleteOverrideFunction = (symbol: string) => Promise<string>;
export declare type SymbolType = "stock" | "index" | "forex" | "futures" | "bitcoin" | "crypto" | "undefined" | "expression" | "spread" | "cfd" | "economic" | "equity" | "dr" | "bond" | "right" | "warrant" | "fund" | "structured";
export declare type TextInputFieldValidator = (value: string) => InputFieldValidatorResult;
export declare type ThemeName = "Light" | "Dark";
export declare type TickMarkType = 
/**
 * The start of the year (e.g. it's the first tick mark in a year).
 */
"Year"
/**
 * The start of the month (e.g. it's the first tick mark in a month).
 */
 | "Month"
/**
 * A day of the month.
 */
 | "DayOfMonth"
/**
 * A time without seconds.
 */
 | "Time"
/**
 * A time with seconds.
 */
 | "TimeWithSeconds";
export declare type TimeFrameValue = TimeFramePeriodBack | TimeFrameTimeRange;
export declare type Timezone = "Etc/UTC" | CustomTimezones;
export declare type TradableSolutions = ChangeAccountSolution | ChangeSymbolSolution;
export declare type TradingDialogCustomField = TextWithCheckboxFieldMetaInfo | CustomComboBoxMetaInfo;
export declare type WatchListSymbolListAddedCallback = (listId: string, symbols: string[]) => void;
export declare type WatchListSymbolListChangedCallback = (listId: string) => void;
export declare type WatchListSymbolListRemovedCallback = (listId: string) => void;
export declare type WatchListSymbolListRenamedCallback = (listId: string, oldName: string, newName: string) => void;
export declare type WatchedValueCallback<T> = (value: T) => void;
export interface AccessList {
	type: "black" | "white";
	tools: AccessListItem[];
}
export interface AccessListItem {
	name: string;
	grayed?: boolean;
}
export interface AccountManagerColumn {
	label: string;
	alignment?: CellAlignment;
	formatter?: StandardFormatterName | "orderSettings" | "posSettings" | string;
	property: string;
	sortProp?: string;
	notSortable?: boolean;
	help?: string;
	highlightDiff?: boolean;
	notHideable?: boolean;
	hideByDefault?: boolean;
	tooltipProperty?: string;
	isCapitalize?: boolean;
	showZeroValues?: boolean;
}
export interface AccountManagerInfo {
	accountTitle: string;
	summary: AccountManagerSummaryField[];
	orderColumns: OrderTableColumn[];
	orderColumnsSorting?: SortingParameters;
	historyColumns?: AccountManagerColumn[];
	historyColumnsSorting?: SortingParameters;
	positionColumns?: AccountManagerColumn[];
	tradeColumns?: AccountManagerColumn[];
	pages: AccountManagerPage[];
	possibleOrderStatuses?: OrderStatus[];
	marginUsed?: IWatchedValue<number>;
	contextMenuActions?(contextMenuEvent: MouseEvent | TouchEvent, activePageActions: ActionMetaInfo[]): Promise<ActionMetaInfo[]>;
}
export interface AccountManagerPage {
	id: string;
	title: string;
	tables: AccountManagerTable[];
}
export interface AccountManagerSummaryField {
	text: string;
	wValue: IWatchedValueReadonly<any>;
	formatter?: string;
}
export interface AccountManagerTable {
	id: string;
	title?: string;
	columns: AccountManagerColumn[];
	initialSorting?: SortingParameters;
	changeDelegate: ISubscription<(data: {}) => void>;
	flags?: AccountManagerTableFlags;
	getData(paginationLastId?: string | number): Promise<{}[]>;
}
export interface AccountManagerTableFlags {
	supportPagination?: boolean;
}
export interface ActionDescription {
	text?: "-" | string;
	separator?: boolean;
	shortcut?: string;
	tooltip?: string;
	checked?: boolean;
	checkedStateSource?: () => boolean;
	checkable?: boolean;
	enabled?: boolean;
	externalLink?: boolean;
	icon?: string;
}
export interface ActionDescriptionWithCallback extends ActionDescription {
	action: (a?: ActionDescription) => void;
}
export interface AdditionalSymbolInfoField {
	title: string;
	propertyName: string;
}
export interface AreaStylePreferences {
	color1: string;
	color2: string;
	linecolor: string;
	linestyle: number;
	linewidth: number;
	transparency: number;
}
export interface AvailableZOrderOperations {
	bringForwardEnabled: boolean;
	bringToFrontEnabled: boolean;
	sendBackwardEnabled: boolean;
	sendToBackEnabled: boolean;
}
export interface Bar {
	time: number;
	open: number;
	high: number;
	low: number;
	close: number;
	volume?: number;
}
export interface BarStylePreferences {
	upColor: string;
	downColor: string;
	barColorsOnPrevClose: boolean;
	dontDrawOpen: boolean;
	thinBars: boolean;
}
export interface BaseInputFieldValidatorResult {
	valid: boolean;
}
export interface BaselineStylePreferences {
	topFillColor1: string;
	topFillColor2: string;
	bottomFillColor1: string;
	bottomFillColor2: string;
	topLineColor: string;
	bottomLineColor: string;
	topLineWidth: number;
	bottomLineWidth: number;
	transparency: number;
	baseLevelPercentage: number;
}
export interface BracketOrder extends PlacedOrder {
	parentId: string;
	parentType: ParentType;
}
export interface Brackets {
	stopLoss?: number;
	takeProfit?: number;
	trailingStopPips?: number;
}
export interface BrokerConfigFlags {
	supportDisplayBrokerNameInSymbolSearch?: boolean;
	showQuantityInsteadOfAmount?: boolean;
	supportOrderBrackets?: boolean;
	supportTrailingStop?: boolean;
	supportPositions?: boolean;
	supportPositionBrackets?: boolean;
	supportTradeBrackets?: boolean;
	supportTrades?: boolean;
	supportClosePosition?: boolean;
	supportCloseTrade?: boolean;
	supportEditAmount?: boolean;
	supportLevel2Data?: boolean;
	supportDOM?: boolean;
	supportMultiposition?: boolean;
	supportPLUpdate?: boolean;
	supportReversePosition?: boolean;
	supportNativeReversePosition?: boolean;
	supportMarketOrders?: boolean;
	supportLimitOrders?: boolean;
	supportStopOrders?: boolean;
	supportStopLimitOrders?: boolean;
	supportDemoLiveSwitcher?: boolean;
	supportMarketBrackets?: boolean;
	supportSymbolSearch?: boolean;
	supportModifyDuration?: boolean;
	supportModifyOrder?: boolean;
	supportModifyTrailingStop?: boolean;
	supportMargin?: boolean;
	calculatePLUsingLast?: boolean;
	supportPlaceOrderPreview?: boolean;
	supportModifyOrderPreview?: boolean;
	supportOrdersHistory?: boolean;
	supportAddBracketsToExistingOrder?: boolean;
	supportBalances?: boolean;
	closePositionCancelsOrders?: boolean;
	supportOnlyPairPositionBrackets?: boolean;
	supportCryptoExchangeOrderTicket?: boolean;
	supportConfirmations?: boolean;
	/** Does broker need to display position's PL in instrument's currency */
	positionPLInInstrumentCurrency?: boolean;
	/** Does broker support partial position closing */
	supportPartialClosePosition?: boolean;
	/** Does broker support partial trade closing */
	supportPartialCloseTrade?: boolean;
	/** Cancelling a bracket cancels it's oco pair */
	supportCancellingBothBracketsOnly?: boolean;
	/** Does broker support crypto brackets */
	supportCryptoBrackets?: boolean;
	/** Does broker need to show notifications log */
	showNotificationsLog?: boolean;
	/**
	 * Whether stop orders should behave like Market-if-touched in both directions.
	 * Enabling this flag removes the direction check from the order dialog.
	 */
	supportStopOrdersInBothDirections?: boolean;
	/** Does broker support executions */
	supportExecutions?: boolean;
	requiresFIFOCloseTrades?: boolean;
	/**
	 * @deprecated
	 */
	supportBrackets?: boolean;
}
export interface BrokerCustomUI {
	showOrderDialog?: (order: OrderTemplate | Order, focus?: OrderTicketFocusControl) => Promise<boolean>;
	showPositionDialog?: (position: Position | Trade, brackets: Brackets, focus?: OrderTicketFocusControl) => Promise<boolean>;
	showCancelOrderDialog?: (order: Order) => Promise<boolean>;
	showClosePositionDialog?: (position: Position) => Promise<boolean>;
}
export interface CandleStylePreferences {
	upColor: string;
	downColor: string;
	drawWick: boolean;
	drawBorder: boolean;
	drawBody: boolean;
	borderColor: string;
	borderUpColor: string;
	borderDownColor: string;
	wickColor: string;
	wickUpColor: string;
	wickDownColor: string;
	barColorsOnPrevClose: boolean;
}
export interface ChangeAccountSolution {
	changeAccount: AccountId;
}
export interface ChangeSymbolSolution {
	changeSymbol: string;
}
export interface ChangeThemeOptions {
	disableUndo: boolean;
}
export interface ChartData {
	id: string;
	name: string;
	symbol: string;
	resolution: ResolutionString;
	content: string;
}
export interface ChartMetaInfo {
	id: number;
	name: string;
	symbol: string;
	resolution: ResolutionString;
	timestamp: number;
}
export interface ChartingLibraryWidgetConstructor {
	new (options: ChartingLibraryWidgetOptions | TradingTerminalWidgetOptions): IChartingLibraryWidget;
}
export interface ChartingLibraryWidgetOptions {
	/** @deprecated */
	container_id?: string;
	container: HTMLElement | string;
	datafeed: IBasicDataFeed | (IBasicDataFeed & IDatafeedQuotesApi);
	interval: ResolutionString;
	symbol?: string;
	auto_save_delay?: number;
	autosize?: boolean;
	debug?: boolean;
	disabled_features?: string[];
	drawings_access?: AccessList;
	enabled_features?: string[];
	fullscreen?: boolean;
	height?: number;
	library_path?: string;
	locale: LanguageCode;
	numeric_formatting?: NumericFormattingParams;
	saved_data?: object;
	saved_data_meta_info?: SavedStateMetaInfo;
	studies_access?: AccessList;
	study_count_limit?: number;
	symbol_search_request_delay?: number;
	timeframe?: string;
	timezone?: "exchange" | Timezone;
	toolbar_bg?: string;
	width?: number;
	charts_storage_url?: string;
	charts_storage_api_version?: AvailableSaveloadVersions;
	client_id?: string;
	user_id?: string;
	load_last_chart?: boolean;
	studies_overrides?: StudyOverrides;
	customFormatters?: CustomFormatters;
	custom_formatters?: CustomFormatters;
	overrides?: Overrides;
	snapshot_url?: string;
	preset?: "mobile";
	time_frames?: TimeFrameItem[];
	custom_css_url?: string;
	favorites?: Favorites;
	save_load_adapter?: IExternalSaveLoadAdapter;
	loading_screen?: LoadingScreenOptions;
	settings_adapter?: ISettingsAdapter;
	theme?: ThemeName;
	compare_symbols?: CompareSymbol[];
	custom_indicators_getter?: (PineJS: PineJS) => Promise<readonly CustomIndicator[]>;
	additional_symbol_info_fields?: AdditionalSymbolInfoField[];
	header_widget_buttons_mode?: HeaderWidgetButtonsMode;
	time_scale?: TimeScaleOptions;
	symbol_search_complete?: SymbolSearchCompleteOverrideFunction;
}
export interface ClientSnapshotOptions {
	backgroundColor: string;
	borderColor: string;
	font: string;
	fontSize: number;
	legendMode: LegendMode;
	hideResolution: boolean;
}
export interface CompareSymbol {
	symbol: string;
	title: string;
}
export interface ContextMenuItem {
	position: "top" | "bottom";
	text: string;
	click: EmptyCallback;
}
export interface CreateButtonOptions {
	align: "right" | "left";
}
export interface CreateMultipointShapeOptions<TOverrides extends object> extends CreateShapeOptionsBase<TOverrides> {
	shape?: Exclude<SupportedLineTools, "cursor" | "dot" | "arrow_cursor" | "eraser" | "measure" | "zoom">;
}
export interface CreateShapeOptions<TOverrides extends object> extends CreateShapeOptionsBase<TOverrides> {
	shape?: "arrow_up" | "arrow_down" | "flag" | "vertical_line" | "horizontal_line" | "long_position" | "short_position";
	ownerStudyId?: EntityId;
}
export interface CreateShapeOptionsBase<TOverrides extends object> {
	text?: string;
	lock?: boolean;
	disableSelection?: boolean;
	disableSave?: boolean;
	disableUndo?: boolean;
	overrides?: TOverrides;
	zOrder?: "top" | "bottom";
	showInObjectsTree?: boolean;
	ownerStudyId?: EntityId;
}
export interface CreateStudyOptions {
	checkLimit?: boolean;
	priceScale?: StudyPriceScale;
	allowChangeCurrency?: boolean;
	allowChangeUnit?: boolean;
	disableUndo?: boolean;
}
export interface CreateStudyTemplateOptions {
	saveSymbol?: boolean;
	saveInterval?: boolean;
}
export interface CrossHairMovedEventParams {
	time: number;
	price: number;
}
export interface CryptoBalance {
	symbol: string;
	total: number;
	available: number;
	reserved?: number;
	value?: number;
	valueCurrency?: string;
	longName?: string;
	btcValue?: number;
}
export interface CustomComboBoxItem {
	text: string;
	value: string;
}
export interface CustomComboBoxMetaInfo extends CustomInputFieldMetaInfo {
	inputType: "ComboBox";
	items: CustomComboBoxItem[];
}
export interface CustomFields {
	[key: string]: any;
}
export interface CustomFormatter {
	format(date: Date): string;
	formatLocal(date: Date): string;
}
export interface CustomFormatters {
	timeFormatter: CustomFormatter;
	dateFormatter: CustomFormatter;
	tickMarkFormatter?: (date: Date, tickMarkType: TickMarkType) => string;
}
export interface CustomIndicator {
	readonly name: string;
	readonly metainfo: any;
	readonly constructor: any;
}
export interface CustomInputFieldMetaInfo {
	inputType: string;
	id: string;
	title: string;
	preventModify?: boolean;
	placeHolder?: string;
	value?: any;
	validator?: InputFieldValidator;
	customInfo?: any;
	saveToSettings?: boolean;
}
export interface CustomInputFieldsValues {
	[fieldId: string]: TextWithCheckboxValue | string | any;
}
export interface DOMData {
	snapshot: boolean;
	asks: DOMLevel[];
	bids: DOMLevel[];
}
export interface DOMLevel {
	price: number;
	volume: number;
}
export interface DatafeedConfiguration {
	exchanges?: Exchange[];
	supported_resolutions?: ResolutionString[];
	units?: Record<string, Unit[]>;
	currency_codes?: string[];
	supports_marks?: boolean;
	supports_time?: boolean;
	supports_timescale_marks?: boolean;
	symbols_types?: DatafeedSymbolType[];
}
export interface DatafeedQuoteValues {
	ch?: number;
	chp?: number;
	short_name?: string;
	exchange?: string;
	description?: string;
	lp?: number;
	ask?: number;
	bid?: number;
	spread?: number;
	open_price?: number;
	high_price?: number;
	low_price?: number;
	prev_close_price?: number;
	volume?: number;
	original_name?: string;
	[valueName: string]: string | number | undefined;
}
export interface DatafeedSymbolType {
	name: string;
	value: string;
}
export interface DefaultContextMenuActionsParams {
}
export interface DefaultDropdownActionsParams {
	tradingProperties?: boolean;
	showHowToUse?: boolean;
	restoreConfirmations?: boolean;
}
export interface DialogParams<CallbackType> {
	title: string;
	body: string;
	callback: CallbackType;
}
export interface DropdownItem {
	title: string;
	onSelect: () => void;
}
export interface DropdownParams {
	title: string;
	items: DropdownItem[];
	tooltip?: string;
	icon?: string;
	align?: "right" | "left";
}
export interface EditObjectDialogEventParams {
	objectType: EditObjectDialogObjectType;
	scriptTitle: string;
}
export interface EntityInfo {
	id: EntityId;
	name: string;
}
export interface ErrorFormatterParseResult extends FormatterParseResult {
	error?: string;
	res: false;
}
export interface Exchange {
	value: string;
	name: string;
	desc: string;
}
export interface Execution extends CustomFields {
	symbol: string;
	price: number;
	qty: number;
	side: Side;
	time: number;
}
export interface ExportDataOptions {
	from?: number;
	to?: number;
	includeTime?: boolean;
	includeUserTime?: boolean;
	includeSeries?: boolean;
	includeDisplayedValues?: boolean;
	includedStudies: readonly string[] | "all";
}
export interface ExportedData {
	schema: FieldDescriptor[];
	data: Float64Array[];
	displayedData: string[][];
}
export interface Favorites {
	intervals: ResolutionString[];
	chartTypes: string[];
}
export interface FormatterParseResult {
	res: boolean;
}
export interface GetNewsResponse {
	title?: string;
	newsItems: NewsItem[];
}
export interface GrayedObject {
	type: "drawing" | "study";
	name: string;
}
export interface HeikinAshiStylePreferences {
	upColor: string;
	downColor: string;
	drawWick: boolean;
	drawBorder: boolean;
	drawBody: boolean;
	borderColor: string;
	borderUpColor: string;
	borderDownColor: string;
	wickColor: string;
	wickUpColor: string;
	wickDownColor: string;
	showRealLastPrice: boolean;
	barColorsOnPrevClose: boolean;
}
export interface HiLoStylePreferences {
	color: string;
	showBorders: boolean;
	borderColor: string;
	showLabels: boolean;
	labelColor: string;
	fontSize: number;
	drawBody: boolean;
}
export interface HistoryMetadata {
	noData?: boolean;
	nextTime?: number | null;
}
export interface HollowCandleStylePreferences {
	upColor: string;
	downColor: string;
	drawWick: boolean;
	drawBorder: boolean;
	drawBody: boolean;
	borderColor: string;
	borderUpColor: string;
	borderDownColor: string;
	wickColor: string;
	wickUpColor: string;
	wickDownColor: string;
}
export interface IBoxedValue<T> extends IBoxedValueReadOnly<T> {
	setValue(value: T): void;
}
export interface IBoxedValueReadOnly<T> {
	value(): T;
}
export interface IBrokerCommon {
	chartContextMenuActions(context: TradeContext, options?: DefaultContextMenuActionsParams): Promise<ActionMetaInfo[]>;
	isTradable(symbol: string): Promise<boolean | IsTradableResult>;
	connectionStatus(): ConnectionStatus;
	orders(): Promise<Order[]>;
	ordersHistory?(): Promise<Order[]>;
	positions?(): Promise<Position[]>;
	trades?(): Promise<Trade[]>;
	executions(symbol: string): Promise<Execution[]>;
	symbolInfo(symbol: string): Promise<InstrumentInfo>;
	accountManagerInfo(): AccountManagerInfo;
	formatter?(symbol: string, alignToMinMove: boolean): Promise<INumberFormatter>;
	spreadFormatter?(symbol: string): Promise<INumberFormatter>;
	quantityFormatter?(symbol: string): Promise<INumberFormatter>;
	getOrderDialogOptions?(symbol: string): Promise<OrderDialogOptions | undefined>;
	getPositionDialogOptions?(): PositionDialogOptions | undefined;
}
export interface IBrokerConnectionAdapterFactory {
	createDelegate<T extends Function>(): IDelegate<T>;
	createWatchedValue<T>(value?: T): IWatchedValue<T>;
	createPriceFormatter(priceScale?: number, minMove?: number, fractional?: boolean, minMove2?: number): IPriceFormatter;
}
export interface IBrokerConnectionAdapterHost {
	factory: IBrokerConnectionAdapterFactory;
	defaultFormatter(symbol: string, alignToMinMove: boolean): Promise<INumberFormatter>;
	numericFormatter(decimalPlaces: number): Promise<INumberFormatter>;
	quantityFormatter(decimalPlaces?: number): Promise<INumberFormatter>;
	defaultContextMenuActions(context: TradeContext, params?: DefaultContextMenuActionsParams): Promise<ActionMetaInfo[]>;
	defaultDropdownMenuActions(options?: Partial<DefaultDropdownActionsParams>): ActionMetaInfo[];
	sellBuyButtonsVisibility(): IWatchedValue<boolean> | null;
	domPanelVisibility(): IWatchedValue<boolean> | null;
	orderPanelVisibility(): IWatchedValue<boolean> | null;
	silentOrdersPlacement(): IWatchedValue<boolean>;
	patchConfig(config: Partial<BrokerConfigFlags>): void;
	setDurations(durations: OrderDurationMetaInfo[]): void;
	orderUpdate(order: Order): void;
	orderPartialUpdate(id: string, orderChanges: Partial<Order>): void;
	positionUpdate(position: Position, isHistoryUpdate?: boolean): void;
	positionPartialUpdate(id: string, positionChanges: Partial<Position>): void;
	tradeUpdate(trade: Trade, isHistoryUpdate?: boolean): void;
	tradePartialUpdate(id: string, tradeChanges: Partial<Trade>): void;
	executionUpdate(execution: Execution): void;
	currentAccountUpdate(): void;
	realtimeUpdate(symbol: string, data: TradingQuotes): void;
	plUpdate(positionId: string, pl: number): void;
	pipValueUpdate(symbol: string, pipValues: PipValues): void;
	tradePLUpdate(tradeId: string, pl: number): void;
	equityUpdate(equity: number): void;
	marginAvailableUpdate(marginAvailable: number): void;
	cryptoBalanceUpdate(symbol: string, balance: CryptoBalance): void;
	domeUpdate(symbol: string, equity: DOMData): void;
	showOrderDialog?<T extends PreOrder>(order: T, focus?: OrderTicketFocusControl): Promise<boolean>;
	showNotification(title: string, text: string, notificationType?: NotificationType): void;
	showCancelOrderDialog(orderId: string, handler: () => Promise<void>): Promise<void>;
	showCancelMultipleOrdersDialog(symbol: string, side: Side, qty: number, handler: () => Promise<void>): Promise<void>;
	showCancelBracketsDialog(orderId: string, handler: () => Promise<void>): Promise<void>;
	showCancelMultipleBracketsDialog(orderId: string, handler: () => Promise<void>): Promise<void>;
	showReversePositionDialog(position: string, handler: () => Promise<boolean>): Promise<boolean>;
	showPositionBracketsDialog(position: Position | Trade, brackets: Brackets, focus: OrderTicketFocusControl): Promise<boolean>;
	setButtonDropdownActions(descriptions: ActionMetaInfo[]): void;
	activateBottomWidget(): Promise<void>;
	showTradingProperties(): void;
	suggestedQty(): SuggestedQuantity;
	getSymbolMinTick(symbol: string): Promise<number>;
	showMessageDialog(title: string, text: string, textHasHTML: boolean): void;
	showConfirmDialog(title: string, content: string | string[], mainButtonText?: string, cancelButtonText?: string, showDisableConfirmationsCheckbox?: boolean): Promise<boolean>;
	showSimpleConfirmDialog(title: string, content: string | string[], mainButtonText?: string, cancelButtonText?: string, showDisableConfirmationsCheckbox?: boolean): Promise<boolean>;
}
export interface IBrokerTerminal extends IBrokerWithoutRealtime {
	subscribeRealtime(symbol: string): void;
	unsubscribeRealtime(symbol: string): void;
}
export interface IBrokerWithoutRealtime extends IBrokerCommon {
	subscribeDOME?(symbol: string): void;
	unsubscribeDOME?(symbol: string): void;
	placeOrder(order: PreOrder, confirmId?: string): Promise<PlaceOrderResult>;
	previewOrder?(order: PreOrder): Promise<OrderPreviewResult>;
	modifyOrder(order: Order, confirmId?: string): Promise<void>;
	cancelOrder(orderId: string): Promise<void>;
	cancelOrders(symbol: string, side: Side | undefined, ordersIds: string[]): Promise<void>;
	reversePosition?(positionId: string): Promise<void>;
	closePosition?(positionId: string, amount?: number): Promise<void>;
	closeTrade?(tradeId: string, amount?: number): Promise<void>;
	editPositionBrackets?(positionId: string, brackets: Brackets, customFields?: CustomInputFieldsValues): Promise<void>;
	editTradeBrackets?(tradeId: string, brackets: Brackets): Promise<void>;
	/**
	 * @deprecated Brokers should always send PL and equity updates
	 */
	subscribePL?(positionId: string): void;
	subscribeEquity?(): void;
	subscribeMarginAvailable?(symbol: string): void;
	subscribePipValue?(symbol: string): void;
	unsubscribePipValue?(symbol: string): void;
	unsubscribeMarginAvailable?(symbol: string): void;
	/**
	 * @deprecated
	 */
	unsubscribePL?(positionId: string): void;
	unsubscribeEquity?(): void;
}
export interface IChartWidgetApi {
	onDataLoaded(): ISubscription<() => void>;
	onSymbolChanged(): ISubscription<() => void>;
	onIntervalChanged(): ISubscription<(interval: ResolutionString, timeFrameParameters: {
		timeframe?: TimeFrameValue;
	}) => void>;
	onVisibleRangeChanged(): ISubscription<(range: VisibleTimeRange) => void>;
	onChartTypeChanged(): ISubscription<(chartType: SeriesStyle) => void>;
	dataReady(callback: () => void): boolean;
	crossHairMoved(): ISubscription<(params: CrossHairMovedEventParams) => void>;
	setVisibleRange(range: SetVisibleTimeRange, options?: SetVisibleRangeOptions): Promise<void>;
	setSymbol(symbol: string, callback?: () => void): void;
	setResolution(resolution: ResolutionString, callback?: () => void): void;
	setChartType(type: SeriesStyle, callback?: () => void): void;
	resetData(): void;
	executeActionById(actionId: ChartActionId): void;
	getCheckableActionState(actionId: ChartActionId): boolean;
	refreshMarks(): void;
	clearMarks(): void;
	getAllShapes(): EntityInfo[];
	getAllStudies(): EntityInfo[];
	getPriceToBarRatio(): number | null;
	setPriceToBarRatio(ratio: number, options?: UndoOptions): void;
	isPriceToBarRatioLocked(): boolean;
	setPriceToBarRatioLocked(value: boolean, options?: UndoOptions): void;
	getAllPanesHeight(): number[];
	setAllPanesHeight(heights: readonly number[]): void;
	availableZOrderOperations(sources: readonly EntityId[]): AvailableZOrderOperations;
	sendToBack(entities: readonly EntityId[]): void;
	bringToFront(sources: readonly EntityId[]): void;
	bringForward(sources: readonly EntityId[]): void;
	sendBackward(sources: readonly EntityId[]): void;
	/**
	 * @deprecated Use shape/study API instead ([getStudyById] / [getShapeById])
	 */
	setEntityVisibility(entityId: EntityId, isVisible: boolean): void;
	createStudy<TStudyInputValue extends StudyInputValue, TOverrides extends StudyOverrides>(name: string, forceOverlay?: boolean, lock?: boolean, inputs?: TStudyInputValue[], overrides?: TOverrides, options?: CreateStudyOptions): Promise<EntityId | null>;
	getStudyById(entityId: EntityId): IStudyApi;
	getSeries(): ISeriesApi;
	createShape<TOverrides extends object>(point: ShapePoint, options: CreateShapeOptions<TOverrides>): EntityId | null;
	createMultipointShape<TOverrides extends object>(points: ShapePoint[], options: CreateMultipointShapeOptions<TOverrides>): EntityId | null;
	getShapeById(entityId: EntityId): ILineDataSourceApi;
	removeEntity(entityId: EntityId, options?: UndoOptions): void;
	removeAllShapes(): void;
	removeAllStudies(): void;
	selection(): ISelectionApi;
	showPropertiesDialog(studyId: EntityId): void;
	createStudyTemplate(options: CreateStudyTemplateOptions): object;
	applyStudyTemplate(template: object): void;
	createOrderLine(options?: UndoOptions): IOrderLineAdapter;
	createPositionLine(options?: UndoOptions): IPositionLineAdapter;
	createExecutionShape(options?: UndoOptions): IExecutionLineAdapter;
	symbol(): string;
	symbolExt(): SymbolExt;
	resolution(): ResolutionString;
	getVisibleRange(): VisibleTimeRange;
	/**
	 * @deprecated Use Price Scale API instead
	 */
	getVisiblePriceRange(): VisiblePriceRange;
	scrollPosition(): number;
	defaultScrollPosition(): number;
	priceFormatter(): INumberFormatter;
	chartType(): SeriesStyle;
	setTimezone(timezone: "exchange" | Timezone): void;
	getTimezone(): "exchange" | Timezone;
	getPanes(): IPaneApi[];
	exportData(options?: Partial<ExportDataOptions>): Promise<ExportedData>;
	canZoomOut(): boolean;
	zoomOut(): void;
	setZoomEnabled(enabled: boolean): void;
	setScrollEnabled(enabled: boolean): void;
	shapesGroupController(): IShapesGroupControllerApi;
	barTimeToEndOfPeriod(unixTime: number): number;
	endOfPeriodToBarTime(unixTime: number): number;
	getTimeScale(): ITimeScaleApi;
	isSelectBarRequested(): boolean;
	requestSelectBar(): Promise<number>;
	cancelSelectBar(): void;
}
export interface IChartingLibraryWidget {
	headerReady(): Promise<void>;
	onChartReady(callback: EmptyCallback): void;
	onGrayedObjectClicked(callback: (obj: GrayedObject) => void): void;
	onShortcut(shortCut: string | number | (string | number)[], callback: EmptyCallback): void;
	subscribe<EventName extends keyof SubscribeEventsMap>(event: EventName, callback: SubscribeEventsMap[EventName]): void;
	unsubscribe<EventName extends keyof SubscribeEventsMap>(event: EventName, callback: SubscribeEventsMap[EventName]): void;
	chart(index?: number): IChartWidgetApi;
	getLanguage(): LanguageCode;
	setSymbol(symbol: string, interval: ResolutionString, callback: EmptyCallback): void;
	remove(): void;
	closePopupsAndDialogs(): void;
	selectLineTool(linetool: SupportedLineTools): void;
	selectedLineTool(): SupportedLineTools;
	save(callback: (state: object) => void): void;
	load(state: object, extendedData?: SavedStateMetaInfo): void;
	getSavedCharts(callback: (chartRecords: SaveLoadChartRecord[]) => void): void;
	loadChartFromServer(chartRecord: SaveLoadChartRecord): void;
	saveChartToServer(onComplete?: EmptyCallback, onFail?: EmptyCallback, options?: SaveChartToServerOptions): void;
	removeChartFromServer(chartId: string, onCompleteCallback: EmptyCallback): void;
	onContextMenu(callback: (unixTime: number, price: number) => ContextMenuItem[]): void;
	createButton(options?: CreateButtonOptions): HTMLElement;
	createDropdown(params: DropdownParams): Promise<IDropdownApi>;
	showNoticeDialog(params: DialogParams<() => void>): void;
	showConfirmDialog(params: DialogParams<(confirmed: boolean) => void>): void;
	showLoadChartDialog(): void;
	showSaveAsChartDialog(): void;
	symbolInterval(): SymbolIntervalResult;
	mainSeriesPriceFormatter(): INumberFormatter;
	getIntervals(): string[];
	getStudiesList(): string[];
	getStudyInputs(studyName: string): StudyInputInfo[];
	addCustomCSSFile(url: string): void;
	applyOverrides<TOverrides extends StudyOverrides>(overrides: TOverrides): void;
	applyStudiesOverrides(overrides: object): void;
	watchList(): Promise<IWatchListApi>;
	news(): Promise<INewsApi>;
	activeChart(): IChartWidgetApi;
	chartsCount(): number;
	layout(): LayoutType;
	setLayout(layout: LayoutType): void;
	layoutName(): string;
	changeTheme(themeName: ThemeName, options?: ChangeThemeOptions): Promise<void>;
	getTheme(): ThemeName;
	takeScreenshot(): void;
	takeClientScreenshot(options?: Partial<ClientSnapshotOptions>): Promise<HTMLCanvasElement>;
	lockAllDrawingTools(): IWatchedValue<boolean>;
	hideAllDrawingTools(): IWatchedValue<boolean>;
	magnetEnabled(): IWatchedValue<boolean>;
	magnetMode(): IWatchedValue<number>;
	symbolSync(): IWatchedValue<boolean>;
	intervalSync(): IWatchedValue<boolean>;
	crosshairSync(): IWatchedValue<boolean>;
	timeSync(): IWatchedValue<boolean>;
	startFullscreen(): void;
	exitFullscreen(): void;
	undoRedoState(): UndoRedoState;
	navigationButtonsVisibility(): IWatchedValue<VisibilityType>;
	paneButtonsVisibility(): IWatchedValue<VisibilityType>;
	dateFormat(): IWatchedValue<DateFormat>;
}
export interface IDatafeedChartApi {
	getMarks?(symbolInfo: LibrarySymbolInfo, from: number, to: number, onDataCallback: GetMarksCallback<Mark>, resolution: ResolutionString): void;
	getTimescaleMarks?(symbolInfo: LibrarySymbolInfo, from: number, to: number, onDataCallback: GetMarksCallback<TimescaleMark>, resolution: ResolutionString): void;
	/**
	 * This function is called if configuration flag supports_time is set to true when chart needs to know the server time.
	 * The charting library expects callback to be called once.
	 * The time is provided without milliseconds. Example: 1445324591. It is used to display Countdown on the price scale.
	 */
	getServerTime?(callback: ServerTimeCallback): void;
	searchSymbols(userInput: string, exchange: string, symbolType: string, onResult: SearchSymbolsCallback): void;
	resolveSymbol(symbolName: string, onResolve: ResolveCallback, onError: ErrorCallback, extension?: SymbolResolveExtension): void;
	getBars(symbolInfo: LibrarySymbolInfo, resolution: ResolutionString, periodParams: PeriodParams, onResult: HistoryCallback, onError: ErrorCallback): void;
	subscribeBars(symbolInfo: LibrarySymbolInfo, resolution: ResolutionString, onTick: SubscribeBarsCallback, listenerGuid: string, onResetCacheNeededCallback: () => void): void;
	unsubscribeBars(listenerGuid: string): void;
	subscribeDepth?(symbol: string, callback: DomeCallback): string;
	unsubscribeDepth?(subscriberUID: string): void;
	getVolumeProfileResolutionForPeriod?(currentResolution: ResolutionString, from: number, to: number, symbolInfo: LibrarySymbolInfo): ResolutionString;
}
export interface IDatafeedQuotesApi {
	getQuotes(symbols: string[], onDataCallback: QuotesCallback, onErrorCallback: (msg: string) => void): void;
	subscribeQuotes(symbols: string[], fastSymbols: string[], onRealtimeCallback: QuotesCallback, listenerGUID: string): void;
	unsubscribeQuotes(listenerGUID: string): void;
}
export interface IDelegate<TFunc extends Function> extends ISubscription<TFunc> {
	fire: TFunc;
}
export interface IDropdownApi {
	applyOptions(options: DropdownUpdateParams): void;
	remove(): void;
}
export interface IExecutionLineAdapter {
	remove(): void;
	getPrice(): number;
	setPrice(value: number): this;
	getTime(): number;
	setTime(value: number): this;
	getDirection(): Direction;
	setDirection(value: Direction): this;
	getText(): string;
	setText(value: string): this;
	getTooltip(): string;
	setTooltip(value: string): this;
	getArrowHeight(): number;
	setArrowHeight(value: number): this;
	getArrowSpacing(): number;
	setArrowSpacing(value: number): this;
	getFont(): string;
	setFont(value: string): this;
	getTextColor(): string;
	setTextColor(value: string): this;
	getArrowColor(): string;
	setArrowColor(value: string): this;
}
export interface IExternalDatafeed {
	onReady(callback: OnReadyCallback): void;
}
export interface IExternalSaveLoadAdapter {
	getAllCharts(): Promise<ChartMetaInfo[]>;
	removeChart<T extends number | string>(id: T): Promise<void>;
	saveChart(chartData: ChartData): Promise<number>;
	getChartContent(chartId: number): Promise<string>;
	getAllStudyTemplates(): Promise<StudyTemplateMetaInfo[]>;
	removeStudyTemplate(studyTemplateInfo: StudyTemplateMetaInfo): Promise<void>;
	saveStudyTemplate(studyTemplateData: StudyTemplateData): Promise<void>;
	getStudyTemplateContent(studyTemplateInfo: StudyTemplateMetaInfo): Promise<string>;
	getDrawingTemplates(toolName: string): Promise<string[]>;
	loadDrawingTemplate(toolName: string, templateName: string): Promise<string>;
	removeDrawingTemplate(toolName: string, templateName: string): Promise<void>;
	saveDrawingTemplate(toolName: string, templateName: string, content: string): Promise<void>;
}
export interface IFormatter<T> {
	format(value?: T): string;
	parse?(value: string): ErrorFormatterParseResult | SuccessFormatterParseResult<T>;
}
export interface ILineDataSourceApi {
	isSelectionEnabled(): boolean;
	setSelectionEnabled(enable: boolean): void;
	isSavingEnabled(): boolean;
	setSavingEnabled(enable: boolean): void;
	isShowInObjectsTreeEnabled(): boolean;
	setShowInObjectsTreeEnabled(enabled: boolean): void;
	isUserEditEnabled(): boolean;
	setUserEditEnabled(enabled: boolean): void;
	bringToFront(): void;
	sendToBack(): void;
	getProperties(): Record<string, any>;
	setProperties(newProperties: object): void;
	getPoints(): PricedPoint[];
	setPoints(points: ShapePoint[]): void;
}
export interface INewsApi {
	refresh(): void;
}
export interface IObservable<T> {
	subscribe(callback: (value: T) => void): void;
	unsubscribe(callback: (value: T) => void): void;
}
export interface IObservableValue<T> extends IBoxedValue<T>, IObservable<T> {
}
export interface IObservableValueReadOnly<T> extends IBoxedValueReadOnly<T>, IObservable<T> {
}
export interface IOrderLineAdapter {
	remove(): void;
	onModify(callback: () => void): this;
	onModify<T>(data: T, callback: (data: T) => void): this;
	onMove(callback: () => void): this;
	onMove<T>(data: T, callback: (data: T) => void): this;
	onCancel(callback: () => void): this;
	onCancel<T>(data: T, callback: (data: T) => void): this;
	getPrice(): number;
	setPrice(value: number): this;
	getText(): string;
	setText(value: string): this;
	getTooltip(): string;
	setTooltip(value: string): this;
	getModifyTooltip(): string;
	setModifyTooltip(value: string): this;
	getCancelTooltip(): string;
	setCancelTooltip(value: string): this;
	getQuantity(): string;
	setQuantity(value: string): this;
	getEditable(): boolean;
	setEditable(value: boolean): this;
	getCancellable(): boolean;
	setCancellable(value: boolean): this;
	getExtendLeft(): boolean;
	setExtendLeft(value: boolean): this;
	getLineLength(): number;
	setLineLength(value: number): this;
	getLineStyle(): number;
	setLineStyle(value: number): this;
	getLineWidth(): number;
	setLineWidth(value: number): this;
	getBodyFont(): string;
	setBodyFont(value: string): this;
	getQuantityFont(): string;
	setQuantityFont(value: string): this;
	getLineColor(): string;
	setLineColor(value: string): this;
	getBodyBorderColor(): string;
	setBodyBorderColor(value: string): this;
	getBodyBackgroundColor(): string;
	setBodyBackgroundColor(value: string): this;
	getBodyTextColor(): string;
	setBodyTextColor(value: string): this;
	getQuantityBorderColor(): string;
	setQuantityBorderColor(value: string): this;
	getQuantityBackgroundColor(): string;
	setQuantityBackgroundColor(value: string): this;
	getQuantityTextColor(): string;
	setQuantityTextColor(value: string): this;
	getCancelButtonBorderColor(): string;
	setCancelButtonBorderColor(value: string): this;
	getCancelButtonBackgroundColor(): string;
	setCancelButtonBackgroundColor(value: string): this;
	getCancelButtonIconColor(): string;
	setCancelButtonIconColor(value: string): this;
}
export interface IPaneApi {
	hasMainSeries(): boolean;
	getLeftPriceScales(): readonly IPriceScaleApi[];
	getRightPriceScales(): readonly IPriceScaleApi[];
	getMainSourcePriceScale(): IPriceScaleApi | null;
	getHeight(): number;
	setHeight(height: number): void;
	moveTo(paneIndex: number): void;
	paneIndex(): number;
}
export interface IPositionLineAdapter {
	remove(): void;
	onClose(callback: () => void): this;
	onClose<T>(data: T, callback: (data: T) => void): this;
	onModify(callback: () => void): this;
	onModify<T>(data: T, callback: (data: T) => void): this;
	onReverse(callback: () => void): this;
	onReverse<T>(data: T, callback: (data: T) => void): this;
	getPrice(): number;
	setPrice(value: number): this;
	getText(): string;
	setText(value: string): this;
	getTooltip(): string;
	setTooltip(value: string): this;
	getProtectTooltip(): string;
	setProtectTooltip(value: string): this;
	getCloseTooltip(): string;
	setCloseTooltip(value: string): this;
	getReverseTooltip(): string;
	setReverseTooltip(value: string): this;
	getQuantity(): string;
	setQuantity(value: string): this;
	getExtendLeft(): boolean;
	setExtendLeft(value: boolean): this;
	getLineLength(): number;
	setLineLength(value: number): this;
	getLineStyle(): number;
	setLineStyle(value: number): this;
	getLineWidth(): number;
	setLineWidth(value: number): this;
	getBodyFont(): string;
	setBodyFont(value: string): this;
	getQuantityFont(): string;
	setQuantityFont(value: string): this;
	getLineColor(): string;
	setLineColor(value: string): this;
	getBodyBorderColor(): string;
	setBodyBorderColor(value: string): this;
	getBodyBackgroundColor(): string;
	setBodyBackgroundColor(value: string): this;
	getBodyTextColor(): string;
	setBodyTextColor(value: string): this;
	getQuantityBorderColor(): string;
	setQuantityBorderColor(value: string): this;
	getQuantityBackgroundColor(): string;
	setQuantityBackgroundColor(value: string): this;
	getQuantityTextColor(): string;
	setQuantityTextColor(value: string): this;
	getReverseButtonBorderColor(): string;
	setReverseButtonBorderColor(value: string): this;
	getReverseButtonBackgroundColor(): string;
	setReverseButtonBackgroundColor(value: string): this;
	getReverseButtonIconColor(): string;
	setReverseButtonIconColor(value: string): this;
	getCloseButtonBorderColor(): string;
	setCloseButtonBorderColor(value: string): this;
	getCloseButtonBackgroundColor(): string;
	setCloseButtonBackgroundColor(value: string): this;
	getCloseButtonIconColor(): string;
	setCloseButtonIconColor(value: string): this;
}
export interface IPriceFormatter extends ISymbolValueFormatter {
	format(price: number, signPositive?: boolean, tailSize?: number, signNegative?: boolean, useRtlFormat?: boolean): string;
}
export interface IPriceScaleApi {
	getMode(): PriceScaleMode;
	setMode(newMode: PriceScaleMode): void;
	isInverted(): boolean;
	setInverted(isInverted: boolean): void;
	getVisiblePriceRange(): VisiblePriceRange | null;
	setVisiblePriceRange(range: VisiblePriceRange): void;
	hasMainSeries(): boolean;
	getStudies(): EntityId[];
}
export interface ISelectionApi {
	add(entities: EntityId[] | EntityId): void;
	set(entities: EntityId[] | EntityId): void;
	remove(entities: EntityId[]): void;
	contains(entity: EntityId): boolean;
	allSources(): EntityId[];
	isEmpty(): boolean;
	clear(): void;
	onChanged(): ISubscription<() => void>;
	canBeAddedToSelection(entity: EntityId): boolean;
}
export interface ISeriesApi {
	isUserEditEnabled(): boolean;
	setUserEditEnabled(enabled: boolean): void;
	mergeUp(): void;
	mergeDown(): void;
	unmergeUp(): void;
	unmergeDown(): void;
	detachToRight(): void;
	detachToLeft(): void;
	detachNoScale(): void;
	changePriceScale(newPriceScale: SeriesPriceScale): void;
	isVisible(): boolean;
	setVisible(visible: boolean): void;
	bringToFront(): void;
	sendToBack(): void;
	entityId(): EntityId;
	chartStyleProperties<T extends ChartStyle>(chartStyle: T): SeriesPreferencesMap[T];
	setChartStyleProperties<T extends ChartStyle>(chartStyle: T, newPrefs: Partial<SeriesPreferencesMap[T]>): void;
}
export interface ISettingsAdapter {
	initialSettings?: InitialSettingsMap;
	setValue(key: string, value: string): void;
	removeValue(key: string): void;
}
export interface IShapesGroupControllerApi {
	createGroupFromSelection(): ShapesGroupId;
	removeGroup(groupId: ShapesGroupId): void;
	groups(): readonly ShapesGroupId[];
	shapesInGroup(groupId: ShapesGroupId): readonly EntityId[];
	excludeShapeFromGroup(groupId: ShapesGroupId, shapeId: EntityId): void;
	availableZOrderOperations(groupId: ShapesGroupId): AvailableZOrderOperations;
	bringToFront(groupId: ShapesGroupId): void;
	sendToBack(groupId: ShapesGroupId): void;
	bringForward(groupId: ShapesGroupId): void;
	sendBackward(groupId: ShapesGroupId): void;
	insertAfter(groupId: ShapesGroupId, target: ShapesGroupId | EntityId): void;
	insertBefore(groupId: ShapesGroupId, target: ShapesGroupId | EntityId): void;
	setGroupVisibility(groupId: ShapesGroupId, value: boolean): void;
	groupVisibility(groupId: ShapesGroupId): GroupVisibilityState;
	setGroupLock(groupId: ShapesGroupId, value: boolean): void;
	groupLock(groupId: ShapesGroupId): GroupLockState;
	getGroupName(groupId: ShapesGroupId): string;
	setGroupName(groupId: ShapesGroupId, name: string): void;
	canBeGroupped(shapes: readonly EntityId[]): boolean;
}
export interface IStudyApi {
	isUserEditEnabled(): boolean;
	setUserEditEnabled(enabled: boolean): void;
	getInputsInfo(): StudyInputInfo[];
	getInputValues(): StudyInputValueItem[];
	setInputValues(values: StudyInputValueItem[]): void;
	mergeUp(): void;
	mergeDown(): void;
	unmergeUp(): void;
	unmergeDown(): void;
	changePriceScale(newPriceScale: StudyPriceScale | EntityId): void;
	isVisible(): boolean;
	setVisible(visible: boolean): void;
	bringToFront(): void;
	sendToBack(): void;
	applyOverrides<TOverrides extends StudyOverrides>(overrides: TOverrides): void;
	applyToEntireLayout(): void;
	onDataLoaded(): ISubscription<() => void>;
	onStudyError(): ISubscription<() => void>;
}
export interface ISubscription<TFunc extends Function> {
	subscribe(obj: object | null, member: TFunc, singleshot?: boolean): void;
	unsubscribe(obj: object | null, member: TFunc): void;
	unsubscribeAll(obj: object | null): void;
}
export interface ISymbolValueFormatter {
	format(price: number, signPositive?: boolean): string;
}
export interface ITimeScaleApi {
	coordinateToTime(x: number): number | null;
	/**
	 * This is to detect when the chart has been zoomed in/out
	 */
	barSpacingChanged(): ISubscription<(newBarSpacing: number) => void>;
	/**
	 * This is to detect when the chart has been scrolled left/right
	 */
	rightOffsetChanged(): ISubscription<(rightOffset: number) => void>;
}
export interface IWatchListApi {
	defaultList(): string[];
	getList(id?: string): string[] | null;
	getAllLists(): WatchListSymbolListMap | null;
	setActiveList(id: string): void;
	getActiveListId(): string | null;
	setList(symbols: string[]): void;
	updateList(listId: string, symbols: string[]): void;
	renameList(listId: string, newName: string): void;
	createList(listName?: string, symbols?: string[]): WatchListSymbolList | null;
	saveList(list: WatchListSymbolList): boolean;
	deleteList(listId: string): void;
	onListChanged(): ISubscription<WatchListSymbolListChangedCallback>;
	onActiveListChanged(): ISubscription<EmptyCallback>;
	onListAdded(): ISubscription<WatchListSymbolListAddedCallback>;
	onListRemoved(): ISubscription<WatchListSymbolListRemovedCallback>;
	onListRenamed(): ISubscription<WatchListSymbolListRenamedCallback>;
}
export interface IWatchedValue<T> extends IWatchedValueReadonly<T>, IObservableValue<T> {
	setValue(value: T, forceUpdate?: boolean): void;
	subscribe(callback: WatchedValueCallback<T>, options?: WatchedValueSubscribeOptions): void;
	unsubscribe(callback?: WatchedValueCallback<T> | null): void;
}
export interface IWatchedValueReadonly<T> extends IObservableValueReadOnly<T> {
	subscribe(callback: (value: T) => void, options?: WatchedValueSubscribeOptions): void;
	unsubscribe(callback?: ((value: T) => void) | null): void;
}
export interface InitialSettingsMap {
	[key: string]: string;
}
export interface InstrumentInfo {
	qty: QuantityMetainfo;
	pipValue: number;
	pipSize: number;
	minTick: number;
	lotSize?: number;
	type?: SymbolType;
	units?: string;
	brokerSymbol?: string;
	description: string;
	domVolumePrecision?: number;
	leverage?: string;
	marginRate?: number;
	limitPriceStep?: number;
	stopPriceStep?: number;
	allowedDurations?: string[];
	currency?: string;
	baseCurrency?: string;
	quoteCurrency?: string;
	bigPointValue?: number;
}
export interface IsTradableResult {
	tradable: boolean;
	reason?: string;
	solutions?: TradableSolutions;
	shortReason?: string;
}
export interface KagiStylePreferences {
	upColor: string;
	downColor: string;
	upColorProjection: string;
	downColorProjection: string;
}
export interface LibrarySymbolInfo {
	/**
	 * Symbol Name
	 */
	name: string;
	full_name: string;
	base_name?: [
		string
	];
	/**
	 * Unique symbol id
	 */
	ticker?: string;
	description: string;
	type: string;
	/**
	 * @example "1700-0200"
	 */
	session: string;
	session_display?: string;
	/** @deprecated Use session_holidays instead */
	holidays?: string;
	/**
	 * @example "20181105,20181107,20181112"
	 */
	session_holidays?: string;
	/**
	 * @example "1900F4-2350F4,1000-1845:20181113;1000-1400:20181114"
	 */
	corrections?: string;
	/**
	 * Traded exchange
	 * @example "NYSE"
	 */
	exchange: string;
	listed_exchange: string;
	timezone: Timezone;
	/**
	 * Prices format: "price" or "volume"
	 */
	format: SeriesFormat;
	/**
	 * Code (Tick)
	 * @example 8/16/.../256 (1/8/100 1/16/100 ... 1/256/100) or 1/10/.../10000000 (1 0.1 ... 0.0000001)
	 */
	pricescale: number;
	/**
	 * The number of units that make up one tick.
	 * @example For example, U.S. equities are quotes in decimals, and tick in decimals, and can go up +/- .01. So the tick increment is 1. But the e-mini S&P futures contract, though quoted in decimals, goes up in .25 increments, so the tick increment is 25. (see also Tick Size)
	 */
	minmov: number;
	fractional?: boolean;
	/**
	 * @example Quarters of 1/32: pricescale=128, minmovement=1, minmovement2=4
	 */
	minmove2?: number;
	/**
	 * false if DWM only
	 */
	has_intraday?: boolean;
	/**
	 * An array of resolutions which should be enabled in resolutions picker for this symbol.
	 */
	supported_resolutions: ResolutionString[];
	/**
	 * @example (for ex.: "1,5,60") - only these resolutions will be requested, all others will be built using them if possible
	 */
	intraday_multipliers?: string[];
	has_seconds?: boolean;
	has_ticks?: boolean;
	/**
	 * It is an array containing seconds resolutions (in seconds without a postfix) the datafeed builds by itself.
	 */
	seconds_multipliers?: string[];
	has_daily?: boolean;
	has_weekly_and_monthly?: boolean;
	has_empty_bars?: boolean;
	has_no_volume?: boolean;
	/**
	 * Integer showing typical volume value decimal places for this symbol
	 */
	volume_precision?: number;
	data_status?: "streaming" | "endofday" | "pulsed" | "delayed_streaming";
	/**
	 * Boolean showing whether this symbol is expired futures contract or not.
	 */
	expired?: boolean;
	/**
	 * Unix timestamp of expiration date.
	 */
	expiration_date?: number;
	sector?: string;
	industry?: string;
	currency_code?: string;
	original_currency_code?: string;
	unit_id?: string;
	original_unit_id?: string;
	unit_conversion_types?: string[];
}
export interface LineBreakStylePreferences {
	upColor: string;
	downColor: string;
	borderUpColor: string;
	borderDownColor: string;
	upColorProjection: string;
	downColorProjection: string;
	borderUpColorProjection: string;
	borderDownColorProjection: string;
}
export interface LineStylePreferences {
	color: string;
	linestyle: number;
	linewidth: number;
	styleType: number;
}
export interface LoadingScreenOptions {
	foregroundColor?: string;
	backgroundColor?: string;
}
export interface Mark {
	id: string | number;
	time: number;
	color: MarkConstColors | MarkCustomColor;
	text: string;
	label: string;
	labelFontColor: string;
	minSize: number;
}
export interface MarkCustomColor {
	color: string;
	background: string;
}
export interface MenuSeparator extends ActionDescription {
	separator: boolean;
}
export interface MouseEventParams {
	clientX: number;
	clientY: number;
	pageX: number;
	pageY: number;
	screenX: number;
	screenY: number;
}
export interface NegativeBaseInputFieldValidatorResult extends BaseInputFieldValidatorResult {
	valid: false;
	errorMessage: string;
}
export interface NewsItem {
	title: string;
	source: string;
	published: number;
	link?: string;
	shortDescription?: string;
	fullDescription?: string;
}
export interface NumericFormattingParams {
	decimal_sign: string;
}
export interface OrderDialogOptions extends TradingDialogOptions {
}
export interface OrderDuration {
	/**
	 * type is OrderDurationMetaInfo.value
	 */
	type: string;
	datetime?: number;
}
export interface OrderDurationMetaInfo {
	hasDatePicker?: boolean;
	hasTimePicker?: boolean;
	default?: boolean;
	name: string;
	value: string;
	supportedOrderTypes?: OrderType[];
}
export interface OrderOrPositionMessage {
	type: OrderOrPositionMessageType;
	text: string;
}
export interface OrderPreviewResult {
	sections: OrderPreviewSection[];
	confirmId?: string;
	warnings?: string[];
	errors?: string[];
}
export interface OrderPreviewSection {
	rows: OrderPreviewSectionRow[];
	header?: string;
}
export interface OrderPreviewSectionRow {
	title: string;
	value: string;
}
export interface OrderRule {
	id: string;
	severity: "warning" | "error";
}
export interface OrderTableColumn extends AccountManagerColumn {
	supportedStatusFilters?: OrderStatusFilter[];
}
/**
 * Input value of the order ticket
 * This info is not sufficient to place an order
 */
export interface OrderTemplate {
	symbol: string;
	type?: OrderType;
	side?: Side;
	qty?: number;
	stopPrice?: number;
	limitPrice?: number;
	takeProfit?: number;
	stopLoss?: number;
	trailingStopPips?: number;
	duration?: OrderDuration;
	customFields?: CustomInputFieldsValues;
}
export interface Overrides {
	[key: string]: string | number | boolean;
}
export interface PeriodParams {
	from: number;
	to: number;
	countBack: number;
	firstDataRequest: boolean;
}
export interface PipValues {
	buyPipValue: number;
	sellPipValue: number;
}
export interface PlaceOrderResult {
	orderId?: string;
}
/**
 * Info about a placed order
 */
export interface PlacedOrder extends CustomFields {
	id: string;
	symbol: string;
	type: OrderType;
	side: Side;
	qty: number;
	status: OrderStatus;
	stopLoss?: number;
	trailingStopPips?: number;
	stopType?: StopType;
	takeProfit?: number;
	duration?: OrderDuration;
	customFields?: CustomInputFieldsValues;
	filledQty?: number;
	avgPrice?: number;
	updateTime?: number; /** unix timestamp in milliseconds */
	limitPrice?: number;
	stopPrice?: number;
	message?: OrderOrPositionMessage;
}
export interface PlusClickParams extends MouseEventParams {
	symbol: string | null;
	price: number;
}
export interface PnFStylePreferences {
	upColor: string;
	downColor: string;
	upColorProjection: string;
	downColorProjection: string;
}
export interface Position {
	id: string;
	symbol: string;
	qty: number;
	shortQty?: number;
	longQty?: number;
	side: Side;
	avgPrice: number;
	message?: OrderOrPositionMessage;
	[key: string]: any;
}
export interface PositionDialogOptions extends TradingDialogOptions {
}
export interface PositiveBaseInputFieldValidatorResult extends BaseInputFieldValidatorResult {
	valid: true;
}
/**
 * Output value of the order ticket and input value of the broker's place order command
 * This info is sufficient to place an order
 */
export interface PreOrder extends OrderTemplate {
	symbol: string;
	type: OrderType;
	side: Side;
	qty: number;
	seenPrice: number | null;
	isClose?: boolean;
}
export interface PricedPoint extends TimePoint {
	price: number;
}
export interface QuantityMetainfo {
	min: number;
	max: number;
	step: number;
	uiStep?: number;
	default?: number;
}
export interface QuoteErrorData {
	s: "error";
	n: string;
	v: object;
}
export interface QuoteOkData {
	s: "ok";
	n: string;
	v: DatafeedQuoteValues;
}
export interface RenkoStylePreferences {
	upColor: string;
	downColor: string;
	borderUpColor: string;
	borderDownColor: string;
	upColorProjection: string;
	downColorProjection: string;
	borderUpColorProjection: string;
	borderDownColorProjection: string;
	wickUpColor: string;
	wickDownColor: string;
}
export interface RestBrokerConnectionInfo {
	url: string;
	access_token: string;
}
export interface RssNewsFeedInfo {
	url: string;
	name: string;
}
export interface RssNewsFeedParams {
	default: RssNewsFeedItem;
	[symbolType: string]: RssNewsFeedItem;
}
export interface SaveChartToServerOptions {
	chartName?: string;
	defaultChartName?: string;
}
export interface SaveLoadChartRecord {
	id: string;
	name: string;
	image_url: string;
	modified_iso: number;
	short_symbol: string;
	interval: ResolutionString;
}
export interface SavedStateMetaInfo {
	uid: number;
	name: string;
	description: string;
}
export interface SearchSymbolResultItem {
	symbol: string;
	full_name: string;
	description: string;
	exchange: string;
	ticker: string;
	type: string;
}
export interface SeriesFieldDescriptor {
	type: "value";
	sourceType: "series";
	plotTitle: string;
	sourceTitle: string;
}
export interface SeriesPreferencesMap {
	[ChartStyle.Bar]: BarStylePreferences;
	[ChartStyle.Candle]: CandleStylePreferences;
	[ChartStyle.Line]: LineStylePreferences;
	[ChartStyle.Area]: AreaStylePreferences;
	[ChartStyle.Renko]: RenkoStylePreferences;
	[ChartStyle.Kagi]: KagiStylePreferences;
	[ChartStyle.PnF]: PnFStylePreferences;
	[ChartStyle.LineBreak]: LineBreakStylePreferences;
	[ChartStyle.HeikinAshi]: HeikinAshiStylePreferences;
	[ChartStyle.HollowCandle]: HollowCandleStylePreferences;
	[ChartStyle.Baseline]: BaselineStylePreferences;
	[ChartStyle.HiLo]: HiLoStylePreferences;
}
export interface SetVisibleRangeOptions {
	applyDefaultRightMargin?: boolean;
	percentRightMargin?: number;
}
export interface SingleBrokerMetaInfo {
	configFlags: BrokerConfigFlags;
	customNotificationFields?: string[];
	durations?: OrderDurationMetaInfo[];
	positionDialogOptions?: PositionDialogOptions;
	orderRules?: OrderRule[];
	customUI?: BrokerCustomUI;
}
export interface SortingParameters {
	columnId: string;
	asc?: boolean;
}
export interface StickedPoint extends TimePoint {
	channel: "open" | "high" | "low" | "close";
}
export interface StudyFieldDescriptor {
	type: "value";
	sourceType: "study";
	sourceId: string;
	sourceTitle: string;
	plotTitle: string;
}
export interface StudyInputInfo {
	id: StudyInputId;
	name: string;
	type: string;
	localizedName: string;
}
export interface StudyInputValueItem {
	id: StudyInputId;
	value: StudyInputValue;
}
export interface StudyOrDrawingAddedToChartEventParams {
	value: string;
}
export interface StudyOverrides {
	[key: string]: StudyOverrideValueType;
}
export interface StudyTemplateData {
	name: string;
	content: string;
}
export interface StudyTemplateMetaInfo {
	name: string;
}
export interface SubscribeEventsMap {
	toggle_sidebar: (isHidden: boolean) => void;
	indicators_dialog: EmptyCallback;
	toggle_header: (isHidden: boolean) => void;
	edit_object_dialog: (params: EditObjectDialogEventParams) => void;
	chart_load_requested: (savedData: object) => void;
	chart_loaded: EmptyCallback;
	mouse_down: (params: MouseEventParams) => void;
	mouse_up: (params: MouseEventParams) => void;
	drawing: (params: StudyOrDrawingAddedToChartEventParams) => void;
	study: (params: StudyOrDrawingAddedToChartEventParams) => void;
	undo: EmptyCallback;
	redo: EmptyCallback;
	undo_redo_state_changed: (state: UndoRedoState) => void;
	reset_scales: EmptyCallback;
	compare_add: EmptyCallback;
	add_compare: EmptyCallback;
	"load_study template": EmptyCallback;
	onTick: (tick: Bar) => void;
	onAutoSaveNeeded: EmptyCallback;
	onScreenshotReady: (url: string) => void;
	onMarkClick: (markId: Mark["id"]) => void;
	onPlusClick: (params: PlusClickParams) => void;
	onTimescaleMarkClick: (markId: TimescaleMark["id"]) => void;
	onSelectedLineToolChanged: EmptyCallback;
	layout_about_to_be_changed: (newLayoutType: LayoutType) => void;
	layout_changed: EmptyCallback;
	activeChartChanged: (chartIndex: number) => void;
	series_event: (seriesEventType: SeriesEventType) => void;
	study_event: (entityId: EntityId, studyEventType: StudyEventType) => void;
	drawing_event: (sourceId: EntityId, drawingEventType: DrawingEventType) => void;
	study_properties_changed: (id: EntityId) => void;
	series_properties_changed: (id: EntityId) => void;
	panes_height_changed: () => void;
	panes_order_changed: () => void;
}
export interface SuccessFormatterParseResult<T> extends FormatterParseResult {
	res: true;
	value: T;
	suggest?: string;
}
export interface SuggestedQuantity {
	changed: IDelegate<(symbol: string) => void>;
	value(symbol: string): Promise<number>;
	setValue(symbol: string, value: number): void;
}
export interface SymbolExt {
	symbol: string;
	full_name: string;
	exchange: string;
	description: string;
	type: string;
	pro_name: string;
}
export interface SymbolIntervalResult {
	symbol: string;
	interval: ResolutionString;
}
export interface SymbolResolveExtension {
	currencyCode?: string;
	unitId?: string;
}
export interface TextWithCheckboxFieldCustomInfo {
	checkboxTitle: string;
	asterix?: boolean;
}
export interface TextWithCheckboxFieldMetaInfo extends CustomInputFieldMetaInfo {
	inputType: "TextWithCheckBox";
	value: TextWithCheckboxValue;
	customInfo: TextWithCheckboxFieldCustomInfo;
	validator?: TextInputFieldValidator;
}
export interface TextWithCheckboxValue {
	text: string;
	checked: boolean;
}
export interface TimeFieldDescriptor {
	type: "time";
}
export interface TimeFrameItem {
	text: string;
	resolution: ResolutionString;
	description?: string;
	title?: string;
}
export interface TimeFramePeriodBack {
	type: TimeFrameType.PeriodBack;
	value: string;
}
export interface TimeFrameTimeRange {
	type: TimeFrameType.TimeRange;
	from: number;
	to: number;
}
export interface TimePoint {
	time: number;
}
export interface TimeScaleOptions {
	min_bar_spacing?: number;
}
export interface TimescaleMark {
	id: string | number;
	time: number;
	color: MarkConstColors | string;
	label: string;
	tooltip: string[];
}
export interface Trade extends CustomFields {
	id: string;
	date: number;
	symbol: string;
	qty: number;
	side: Side;
	price: number;
}
export interface TradeContext {
	symbol: string;
	displaySymbol: string;
	value: number | null;
	formattedValue: string;
	last: number;
}
export interface TradingCustomization {
	position: Overrides;
	order: Overrides;
}
export interface TradingDialogOptions {
	customFields?: TradingDialogCustomField[];
}
export interface TradingQuotes {
	trade?: number;
	size?: number;
	bid?: number;
	bid_size?: number;
	ask?: number;
	ask_size?: number;
	spread?: number;
	isDelayed?: boolean;
}
export interface TradingTerminalWidgetOptions extends ChartingLibraryWidgetOptions {
	brokerConfig?: SingleBrokerMetaInfo;
	broker_config?: SingleBrokerMetaInfo;
	restConfig?: RestBrokerConnectionInfo;
	widgetbar?: WidgetBarParams;
	rss_news_feed?: RssNewsFeedParams;
	news_provider?: GetNewsFunction;
	trading_customization?: TradingCustomization;
	brokerFactory?(host: IBrokerConnectionAdapterHost): IBrokerWithoutRealtime | IBrokerTerminal;
	broker_factory?(host: IBrokerConnectionAdapterHost): IBrokerWithoutRealtime | IBrokerTerminal;
}
export interface UndoOptions {
	disableUndo?: boolean;
}
export interface UndoRedoState {
	readonly enableUndo: boolean;
	readonly undoText: string | undefined;
	readonly enableRedo: boolean;
	readonly redoText: string | undefined;
}
export interface Unit {
	id: string;
	name: string;
	description: string;
}
export interface UserTimeFieldDescriptor {
	type: "userTime";
}
export interface VisiblePriceRange {
	from: number;
	to: number;
}
export interface VisibleTimeRange {
	from: number;
	to: number;
}
export interface WatchListSymbolList extends WatchListSymbolListData {
	id: string;
}
export interface WatchListSymbolListData {
	symbols: string[];
	title: string;
}
export interface WatchListSymbolListMap {
	[listId: string]: WatchListSymbolList;
}
export interface WatchedValueSubscribeOptions {
	once?: boolean;
	callWithLast?: boolean;
}
export interface WidgetBarParams {
	details?: boolean;
	watchlist?: boolean;
	news?: boolean;
	datawindow?: boolean;
	watchlist_settings?: {
		default_symbols: string[];
		readonly?: boolean;
	};
}
export type CustomTimezones = "Africa/Cairo" | "Africa/Johannesburg" | "Africa/Lagos" | "America/Argentina/Buenos_Aires" | "America/Bogota" | "America/Caracas" | "America/Chicago" | "America/El_Salvador" | "America/Juneau" | "America/Lima" | "America/Los_Angeles" | "America/Mexico_City" | "America/New_York" | "America/Phoenix" | "America/Santiago" | "America/Sao_Paulo" | "America/Toronto" | "America/Vancouver" | "Asia/Almaty" | "Asia/Ashkhabad" | "Asia/Bahrain" | "Asia/Bangkok" | "Asia/Chongqing" | "Asia/Dubai" | "Asia/Ho_Chi_Minh" | "Asia/Hong_Kong" | "Asia/Jakarta" | "Asia/Jerusalem" | "Asia/Kathmandu" | "Asia/Kolkata" | "Asia/Kuwait" | "Asia/Muscat" | "Asia/Qatar" | "Asia/Riyadh" | "Asia/Seoul" | "Asia/Shanghai" | "Asia/Singapore" | "Asia/Taipei" | "Asia/Tehran" | "Asia/Tokyo" | "Atlantic/Reykjavik" | "Australia/ACT" | "Australia/Adelaide" | "Australia/Brisbane" | "Australia/Perth" | "Australia/Sydney" | "Europe/Amsterdam" | "Europe/Athens" | "Europe/Belgrade" | "Europe/Berlin" | "Europe/Brussels" | "Europe/Copenhagen" | "Europe/Dublin" | "Europe/Helsinki" | "Europe/Istanbul" | "Europe/Lisbon" | "Europe/London" | "Europe/Luxembourg" | "Europe/Madrid" | "Europe/Malta" | "Europe/Moscow" | "Europe/Oslo" | "Europe/Paris" | "Europe/Riga" | "Europe/Rome" | "Europe/Stockholm" | "Europe/Tallinn" | "Europe/Vilnius" | "Europe/Warsaw" | "Europe/Zurich" | "Pacific/Auckland" | "Pacific/Chatham" | "Pacific/Fakaofo" | "Pacific/Honolulu" | "Pacific/Norfolk" | "US/Mountain";

export as namespace TradingView;

export {};
